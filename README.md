Dynamic Categories Modul
============
&nbsp;  

Modul pre tvorbu dynamických kategórií produktov E-shopu cez typ stránky dynamic_category.  
Dynamická kategória je kategó<PERSON>, ktorá má definovaný filter na dynamický výber produktov a súčasne môže obsahovať statický zoznam produktov, ktoré sú topované. 

Packagist: [buxus-libs/dynamic-categories](http://packagist.ui42.sk/packages/buxus-libs/dynamic-categories)

### Obsah modulu
- config: nastavenie query typov
 
  ```
    'item_types' => [
    ],

    'queries' => [
        'basic' => [
            'fs_tag' => null,
            'items' => [
                'and',
                'or',
            ],
        ],
    ],

    /*
     * list of filter tags that can be made visible for the dynamic categories
     */
    'filter_mask_tags' => [
        'flags' => 'príznaky produktov',
    ],
  ```
- src

### Pridá do Buxusu
 - migrácia `DynamicCategories.php` :  
               - vytvorí typy stránok 'E-shop' a 'E-shop kategórie' ak ešte neexistujú  
               - vytvorí typ stránky  `Dynamická kategória` (dynamic_category)
 - pridáva dve custom property `DynamicQueryProperty`, `DynamicQueryFilterMaskProperty`
 - publishuje config `dynamic_categories.php`

