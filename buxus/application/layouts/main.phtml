<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML+RDFa 1.0//EN"
    "https://www.w3.org/MarkUp/DTD/xhtml-rdfa-1.dtd">
<html xmlns="https://www.w3.org/1999/xhtml" xml:lang="sk" version="XHTML+RDFa 1.0">
<?php $t = 466; ?>
<head profile="https://www.w3.org/1999/xhtml/vocab">
    <!-- Google Tag Manager -->
    <script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
                new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
            j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
            'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
        })(window,document,'script','dataLayer','GTM-T5T3BTTJ');</script>
    <!-- End Google Tag Manager -->

    <!-- page_id=<?= BuxusMVC::pageId() ?> -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <link rel="shortcut icon" href="favicon.ico" type="image/vnd.microsoft.icon" />

    <!--
    TWITTER
    -->
    <meta property="twitter:title" content="<?= $this->metaTitleCustom() ?>" />
    <meta property="twitter:url" content="<?= \Buxus\Util\Url::page(BuxusMVC::pageId()) ?>" />

    <!--
    Open graph
    -->
    <meta property="og:site_name" content="Vaša lekáreň" />
    <meta property="og:description" content="Vaša lekáreň" />
    <meta property="og:title" content="<?= $this->metaTitleCustom() ?>" />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="<?= \Buxus\Util\Url::page(BuxusMVC::pageId()) ?>" />
    <meta property="og:image" content=" https://portal.vasalekaren.sk/" />

    <? if(\Buxus::isLive()): ?>
            <script src="/buxus/assets/libs/vue/2.6.14/vue.min.js"></script>
            <!-- script src="//unpkg.com/vue@latest/dist/vue.min.js"></script -->
            <script src="/buxus/assets/libs/bootstrap-vue@2.19.0/bootstrap-vue.min.js"></script>
            <script src="/buxus/assets/libs/bootstrap-vue@2.19.0/bootstrap-vue-icons.min.js"></script>
        <? else: ?>

            <script src="https://cdnjs.cloudflare.com/ajax/libs/vue/2.6.14/vue.min.js"></script>
            <!--script src="//unpkg.com/vue@latest/dist/vue.js"></script -->
            <script src="//unpkg.com/bootstrap-vue@2.19.0/dist/bootstrap-vue.js"></script>
            <script src="//unpkg.com/bootstrap-vue@2.19.0/dist/bootstrap-vue-icons.js"></script>
    <? endif; ?>
    <script src="/buxus/assets/libs/portal-vue"></script>

    <title><?= $this->metaTitleCustom() ?></title>

    <?= $this->headMeta() ?>

    <?= $this->headLink() ?>

    <?= $this->headScript() ?>

    <?= $this->headStyle() ?>

    <link rel="stylesheet" href="/buxus/assets/libs/bootstrap-vue@2.19.0/bootstrap.min.css" />

    <!-- Load required Bootstrap and BootstrapVue CSS -->
    <!-- link type="text/css" rel="stylesheet" href="//unpkg.com/bootstrap/dist/css/bootstrap.min.css" /--> <!-- 4.5.0 - homepage problem -->
    <link type="text/css" rel="stylesheet" href="/buxus/assets/libs/bootstrap-vue@2.19.0/bootstrap-vue.min.css" />
    <link type="text/css" rel="stylesheet" href="/buxus/assets/libs/bootstrap-vue@2.19.0/bootstrap-vue-icons.min.css" />
    <!-- Load polyfills to support older browsers -->
    <script src="//polyfill.io/v3/polyfill.min.js?features=es2015%2CIntersectionObserver" crossorigin="anonymous"></script>

    <link rel="stylesheet" href="/buxus/docs/design-2018/rangeslider/rangeslider.css?t=<?= $t ?>" />
    <link rel="stylesheet" href="/buxus/assets/scss/style.css?t=<?= $t ?>" />
    <? if(Site::getHomePageId() == PageIds::getWebPartneri()): ?>
        <link rel="stylesheet" href="/buxus/docs/design-2018/style-firmy.css?t=<?= $t ?>" />
    <? endif; ?>
    <? if(Site::getHomePageId() == PageIds::getWebElearning()): ?>
        <link rel="stylesheet" href="/buxus/docs/design-2018/style-elearning.css?t=<?= $t ?>" />
    <? endif; ?>
    <link href="/buxus/assets/libs/font-awesome/4.7.0/font-awesome.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="/buxus/docs/design-2018/OwlCarousel2-2.3.4/dist/assets/owl.carousel.css" />
    <link rel="stylesheet" href="/buxus/docs/design-2018/lity-2.3.1/dist/lity.css" />
    <link rel="stylesheet" href="/buxus/docs/design-2018/hover/css/hover-min.css" />

    <link rel="stylesheet" href="/buxus/assets/libs/ekko-lightbox/5.3.0/ekko-lightbox.css" />
    <script src="/buxus/assets/libs/axios/0.19.2/axios.min.js"></script>
    <script src="/buxus/assets/libs/js-cookie@rc/js.cookie.min.js"></script>
    <script type="text/javascript">
        var _token = '<?= csrf_token() ?>';
            axios.defaults.headers.common = {
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRF-TOKEN' : _token
        };
    </script>


    <script>
        var cart_vbc_pocet_bodov = <?= floatval(EshopShoppingCartCustom::pocetBodov()) ?>;


        var use_vbc = <?= VbcUser::useVbcEshop() ? 'true':'false'; ?>;
        var vbc_points_total = <?= floatval(EshopShoppingCartCustom::pocetBodov()); ?>;
        var vbc_points_available = <?= floatval(EshopShoppingCartCustom::pocetBodovBezKosiku()); ?>;
        var vbc_price_per_point = <?= floatval(EshopShoppingCartCustom::hodnotaBodu()); ?>;
        var vbc_used_in_cart =  <?= floatval(EshopShoppingCartCustom::usedPoints()); ?>;
        var cart_final_price = <?= floatval(EshopShoppingCartCustom::getFinalPriceIfDefined()) ?>;
    </script>

    <link rel="stylesheet" href="/buxus/assets/libs/fontawesome.com/releases/v5.7.2/all.css" integrity="sha384-fnmOCqbTlWIlj8LyTjo7mOUStjsKC4pOpQbqyi7RrhN7udi9RwhKkMHpvLbHG9Sr" crossorigin="anonymous">
</head>
<body class="<?= auth()->user() ? 'logged-in':'' ?> <?= in_array(BuxusMVC::page()->getPageTypeId(), array(PageTypesConstantsIDs::MAIN_PAGE_ID, PageTypesConstantsIDs::WEB_PAGE_ID)) ? 'homepage':'' ?> <?= Site::getHomePageId() == PageIds::getWebEshop() ? 'eshop':'not-eshop' ?> <?= Site::getHomePageId() == PageIds::getWebClenovia() ? 'clenovia':'' ?> <?= Site::getHomePageId() == PageIds::getHomepageVasalekaren() ? 'vasalekaren-hp':'' ?>">

<!-- Google Tag Manager (noscript) -->
<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-T5T3BTTJ"
                  height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
<!-- End Google Tag Manager (noscript) -->

    <div class="container">
        <header id="header" class="m<?= SubMenu::factory(\BuxusMVC::pageId())->count() ?> <?= (SubMenu::factory(\BuxusMVC::pageId())->count() && BuxusMVC::page()->getPageTypeId() != PageTypesConstantsIDs::ELEARNING_TEST_ID) ? 'with-submenu':'' ?>">
            <div class="row">
                <div class="col-md-12">
                    <nav class="navbar fixed-top navbar-expand-lg"  style="<?= $this->header_color ? ('background:'.$this->header_color.';'):'' ?>">
                        <div class="container <?= strlen($this->header_color) ? 'colored':'' ?>" style="position:relative; padding-left: 0px; <?= $this->header_color ? ('background:'.$this->header_color.';'):'' ?>">
                                <a class="navbar-brand" href="<?= \Buxus\Util\Url::page($this->homepage_id) ?>"><img src="/buxus/assets/images/Logo Vasa Lekaren.png" srcset="/buxus/assets/images/Logo Vasa Lekaren.png 1x, /buxus/assets/images/<NAME_EMAIL> 2x" /></a>
                                <? if(isset($GLOBALS['auth_user'])): ?>
                                    <div class="cart-link-container">
                                        <a href="<?= \Buxus\Util\Url::page(PageIds::getEshopShoppingCart()) ?>" class="nav-link cart-link">
                                            <span class=""><img src="<?= \Buxus\Util\Url::image('design-2018/cart.png') ?>" /></span>
                                            <span class="small-cart-mini-status"><?= EshopShoppingCartCustom::smallCartMiniStatus() ?></span>
                                        </a>
                                        <?= $this->action('small-cart', 'eshop_cart') ?>
                                    </div>
                                <? endif; ?>
                                <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
                                    <span class="navbar-toggler-icon">
                                        <svg xmlns="https://www.w3.org/2000/svg" viewBox="0 0 200 200"><path d="M-.019 162.953v-7.7h200v15.4h-200v-7.7zm0-62.8v-7.7h200v15.4h-200v-7.7zm0-63.1v-7.7h200v15.4h-200v-7.7z" fill="#ffffff"></path></svg>
                                    </span>
                                </button>

                                <div class="collapse navbar-collapse" id="navbarSupportedContent"  style="position:relative; <?= $this->header_color ? ('background:'.$this->header_color.';'):'' ?>">
                                    <?= $this->action('top-menu', $this->controller_name) ?>
                                </div>

                        </div>
                    </nav>

                </div>
            </div>
        </header>
    </div>

        <section>
            <div class="row no-gutters">
                <div class="col-md-12">
                    <div id="slider">
                        <?= $this->action('carousel', $this->controller_name) ?>
                    </div>
                </div>
            </div>
        </section>

    <div class="container">
            <!-- #document:BEGIN //-->
            <div id="document" style="display:none">
                <!-- #out:BEGIN //-->
                <div id="out"><div class="container show grid">

                <!-- #header:BEGIN //-->
                    <div id="centerhack">
                        <div id="header" class="span-12">

                            <div id="nav" class="span-6">

                            </div>

                            <div id="subnav" class="span-5 push-1 last">
                                <?= $this->action('top-menu-right', $this->controller_name) ?>
                            </div>

                            <div id="login">
                                <? if(intval($GLOBALS['auth_user']['user_id'])): ?>
                                    <a href="<?= \Buxus\Util\Url::page(PageIds::getOdhlasenie())?>">Odhlásiť sa</a>
                                <? else: ?>
                                    <a href="<?= \Buxus\Util\Url::page(PageIds::authenticationLogin())?>">Prihlásiť sa</a>
                                <? endif; ?>
                            </div>

                            <div id="search">
                                <form action="<?= \Buxus\Util\Url::page(PageIds::getFulltextSearch()) ?>">
                                    <fieldset>
                                        <input type="text" name="term" value="Hľadať" class="search" onfocus="$(this).val('');" />
                                        <input type="image" class="tlacidlo" src="<?= \Buxus\Util\Url::image('design/empty.png') ?>" />
                                    </fieldset>
                                </form>
                            </div>

                        </div>
                    </div>
                <!-- #header:END //-->



                    </div></div>
                    <!-- #out:END //-->



            </div>
            <!-- #document:END //-->

        <? if(getProperty('page_id') == Site::getHomePageId()): ?>
            <?= $this->layout()->content ?>
        <? else: ?>
            <?= $this->layout()->content ?>
        <? endif; ?>

    </div>

    <!-- #footer:BEGIN //-->
    <footer id="footer" class="<?= strlen($this->header_color) ? 'colored':'' ?>" style="<?= $this->header_color ? ('background:'.$this->header_color.';'):'' ?>">
        <div class="container">
            <div class="row">
                <div class="col-md-12" >
                    <?= $this->action('footer-form', $this->controller_name) ?>
                </div>

                <div class="col-md-12" >
                    <?= $this->action('footer-menu', $this->controller_name) ?>
                </div>

                <!-- div class="col-md-12" id="footer-newsletter">
                        <?= ''  /* $this->action('sign-in-form', 'mailinglist') */ ?>
                    </div -->

                <?= $this->action('social', $this->controller_name) ?>

                <div class="col-md-12" id="footer-top">
                    <a id="go-top" class="back-top" href="#">
                        <i class="fa fa-long-arrow-up"></i>
                        <div>Naspäť hore</div>
                    </a>
                </div>

            </div>
        </div>
        <div class="container">
            <div class="row">
                <div class="col-md-12 buxus-ui42 text-right">
                    <div id="buxus">
                        Generuje <a target="_blank" href="https://www.ui42.sk/cms-buxus.html?page_id=63">redakčný systém</a> BUXUS <a target="_blank" href="https://www.ui42.sk/cms-buxus.html?page_id=63">CMS</a> spoločnosti <a target="_blank" href="https://www.ui42.sk/">ui42</a>.
                    </div>
                </div>

            </div>
        </div>
    </footer>
    <!-- #footer:END //-->

    <div class="modal fade" id="modal">
        <div class="modal-dialog modal-dialog-centered modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title"></h4>
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                </div>
                <div class="modal-body"></div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-danger" data-dismiss="modal">Zatvoriť</button>
                </div>

            </div>
        </div>
    </div>

    <div class="modal fade" id="modal-pharmacy">
        <div class="modal-dialog modal-dialog-centered modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h4>VAŠA LEKÁREŇ</h4>
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="modal-body-title">
                        <h4 class="modal-pharmacy-title"></h4>
                        <div class="kraj-mesto">
                            <span class="modal-pharmacy-kraj"></span> - <span class="modal-pharmacy-mesto"></span>
                        </div>
                    </div>
                    <div class="modal-title-info">
                        <div class="hours"></div>
                        <div class="address"></div>
                    </div>

                    <div id="map-detail"></div>
                </div>
            </div>
        </div>
    </div>

    <script src="/buxus/assets/libs/jquery-3.3.1/jquery-3.3.1.min.js"  integrity="sha256-FgpCb/KJQlLNfOu91ta32o/NMZxltwRo8QtmkMRdAu8="  crossorigin="anonymous"></script>
    <script src="/buxus/assets/libs/popper.js/1.16.1/popper.min.js" crossorigin="anonymous"></script>
    <script src="/buxus/assets/libs/bootstrap/4.5.0/bootstrap.min.js"></script>
    <script src="/buxus/assets/libs/bootstrap-vue@2.19.0/bootstrap-vue-icons.min.js"></script>
    <script src="/buxus/docs/design-2018/OwlCarousel2-2.3.4/dist/owl.carousel.js"></script>
    <script src="/buxus/docs/design-2018/rangeslider/rangeslider.js?t=<?= $t ?>"></script>

    <script src="/buxus/assets/libs/ekko-lightbox/5.3.0/ekko-lightbox.js"></script>

    <script src="/buxus/docs/design-2018/scripts.js?t=<?= $t ?>"></script>
    <script src="/buxus/docs/jscripts/eshop_cart.js?t=<?= $t ?>"></script>
    <script src="/buxus/docs/design-2018/lity-2.3.1/dist/lity.js"></script>

    <script src="/buxus/docs/jscripts/jquery.form.min.js"></script>

    <? if(BuxusMVC::pageId() == PageIds::getZoznamLekarni()): ?>
        <script src="/buxus/docs/design-2018/markercluster/markerclusterer.js"></script>
        <script src="/buxus/docs/design-2018/map.js?t=<?= $t ?>"></script>
        <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyCpxvfYvoMS-4LfSNjxvnHuW4vdanzRTlY&callback=initMap" async defer></script>
    <? endif; ?>



    <? if(Site::getHomePageId() == PageIds::getWebElearning()): ?>
        <script src="/buxus/docs/design-2018/elearning.js?t=<?= $t ?>"></script>
    <? endif; ?>

    <? /* echo app('buxus:toolbar')->render() */ ?>

    <?= \App\PopupForms\PopupFormManager::renderPopup() ?>
	</body>
</html>
