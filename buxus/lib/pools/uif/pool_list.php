<?php
require_once 'includes/functions.php';
require_once 'includes/local_property_functions.php';
require_once 'includes/uif_functions.php';
require_once __DIR__ . '/../pool_functions.php';
require_once 'includes/validate.php';

$user_id = \Buxus::userId();

$page_ids = (isset($_REQUEST["page_id"])) ? $_POST["page_id"] : null;
unset($page_id);

$have_permission = false;

$sql_qry = "SELECT admin_right,pool_right ";
$sql_qry .= " FROM tblUsers  WHERE user_id = $user_id";
$data_row = BuxusDB::get()->fetchRow($sql_qry, array(':user_id' => $user_id));
if ($data_row) {
    if (TRIM($data_row["pool_right"]) == C_True_Char) {
        $have_permission = true;
    }
    if (TRIM($data_row["admin_right"]) == C_True_Char) {
        $have_permission = true;
    }
}

if ($have_permission) {
    $show_list = isset($_REQUEST["show_list"]) ? $_REQUEST["show_list"] : null;
    $filter_state = isset($_REQUEST["filter_state"]) ? $_REQUEST["filter_state"] : null;
    $filter_tag = isset($_REQUEST["filter_tag"]) ? $_REQUEST["filter_tag"] : null;
    $filter_user = isset($_REQUEST["filter_user"]) ? $_REQUEST["filter_user"] : null;
    $filter_pool_type = isset($_REQUEST["filter_pool_type"]) ? $_REQUEST["filter_pool_type"] : null;
    $time_created_from = isset($_REQUEST["time_created_from"]) ? $_REQUEST["time_created_from"] : null;
    $time_created_till = isset($_REQUEST["time_created_till"]) ? $_REQUEST["time_created_till"] : null;
    $subpolls = isset($_REQUEST["subpolls"]) ? $_REQUEST["subpolls"] : null;
    $order_by = isset($_REQUEST["order_by"]) ? $_REQUEST["order_by"] : null;
    $first_item_index = isset($_REQUEST["first_item_index"]) ? $_REQUEST["first_item_index"] : null;
    $C_HTML_ItemListCount = isset($_REQUEST["C_HTML_ItemListCount"]) ? $_REQUEST["C_HTML_ItemListCount"] : null;
    if (!$first_item_index) {
        $first_item_index = 0;
    }
    if ($page_ids) {
        $show_list = 1;
        $subpolls = 1;
    }

    $forms = Array();

    if ($show_list) {
        Session::put('buxus.POLLS_FILTER',
            $C_HTML_ItemListCount . "&" . urlencode($filter_tag) . "&" . urlencode($filter_state) . "&" . urlencode($filter_user) . "&" . urlencode($page_ids) . "&" . urlencode($filter_pool_type) . "&" . urlencode($time_created_from) . "&" . urlencode($time_created_till) . "&" . urlencode($subpolls) . "&" . urlencode($order_by));
    } else {
        if (Session::has('buxus.POLLS_FILTER')) {
            $show_list = 0;
            list($C_HTML_ItemListCount, $filter_tag, $filter_state, $filter_user, $page_ids, $filter_pool_type, $time_created_from, $time_created_till, $subpolls, $order_by) = explode("&",
                Session::get('buxus.POLLS_FILTER'));
            $filter_tag = urldecode($filter_tag);
            $filter_state = urldecode($filter_state);
            $filter_user = urldecode($filter_user);
            $page_ids = urldecode($page_ids);
            $filter_pool_type = urldecode($filter_pool_type);
            $time_created_from = urldecode($time_created_from);
            $time_created_till = urldecode($time_created_till);
            $subpolls = urldecode($subpolls);
            $order_by = urldecode($order_by);
        } else //default values
        {
            $C_HTML_ItemListCount = GetSystemOption("C_list_page_limit");
            $filter_state = C_True_Char;
        }

    }

    if ($show_list) {
        if ($page_ids) {
            $list_pool_ids = "0";
            $tmp_page_ids = explode(',', $page_ids);
            $forms = BuxusDB::get()->fetchAll("SELECT DISTINCT pool_id FROM tblMultiPoolAnswers WHERE page_id IN (" . BuxusDB::get()->quoteInto('?',
                    $tmp_page_ids) . ")");
            while (list ($key, $form) = each($forms)) {
                $list_pool_ids .= "," . BuxusDB::get()->quote($form["pool_id"]);
            }
        }
        if (!$order_by) {
            $order_by = " tblPools.time_created DESC";
        }
        $tmp_page_ids = explode(',', $page_ids);
        $sql_query = "SELECT tblPools.pool_id, tblPools.pool_type, RTRIM(tblPools.pool_tag) AS pool_tag, RTRIM(tblPools.is_active) AS is_active, RTRIM(tblPools.time_created) AS time_created, 
			 RTRIM(tblPools.disalow_multiple_votes_flag) AS disalow_multiple_votes_flag, RTRIM(tblPools.the_question) AS the_question, tblUsers.user_id, RTRIM(tblUsers.user_long_name) AS user_long_name
			  FROM tblPools LEFT JOIN tblUsers ON tblUsers.user_id=tblPools.user_id
			  WHERE (1=1) " . (($filter_state != '') ? " AND (tblPools.is_active=" . BuxusDB::get()->quote($filter_state) . ") " : "") . (($filter_tag != '') ? " AND (tblPools.pool_tag like " . BuxusDB::get()->quote($filter_tag . '%') . ") " : "") . (($filter_user != '') ? " AND (tblPools.user_id=" . BuxusDB::get()->quote($filter_user) . ") " : "") . (($filter_pool_type != '') ? " AND (tblPools.pool_type=" . BuxusDB::get()->quote($filter_pool_type) . ") " : "") . (($time_created_from != '') ? " AND (DATE(tblPools.time_created) >= " . BuxusDB::get()->quote($time_created_from) . ") " : "") . (($time_created_till != '') ? " AND (DATE(tblPools.time_created) <= " . BuxusDB::get()->quote($time_created_till) . ") " : "") . (($page_ids) ? " AND (tblPools.page_id IN (" . BuxusDB::get()->quoteInto('?',
                    $tmp_page_ids) . ") OR tblPools.pool_id IN ($list_pool_ids)) " : "");
        $sql_query .= " ORDER BY " . BuxusDB::get()->addslashes($order_by);
        //			 echo "<BR>$sql_query <BR>";
        $forms = BuxusDB::get()->fetchAll($sql_query);

    }
    $num_all = BuxusDB::get()->fetchOne("SELECT Count(*) FROM tblPools");

    $bm = new \Buxus\Bookmark\PollBookmark(array(
        "show_list" => $show_list,
        "filter_tag" => $filter_tag,
        "filter_pool_type" => $filter_pool_type,
        "subpolls" => $subpolls,
        "filter_user" => $filter_user,
        "filter_state" => $filter_state,
        "time_created_from" => $time_created_from,
        "time_created_till" => $time_created_till,
        "page_id" => $page_ids,
        "order_by" => $order_by,
        "C_HTML_ItemListCount" => $C_HTML_ItemListCount
    ), mb_substr($_SERVER['PHP_SELF'], mb_strlen(config('buxus_core.base_url')) - mb_strlen($_SERVER['PHP_SELF'])));

    DisplayHtmlHeader(__bx('legacy-base::pool.PoolPageTitle'), C_MenuType_Pool, '', '', '', 0, $bm);

    echo HTML_PageTitle(__bx('legacy-base::pool.PageTitle'), __bx('legacy-base::pool.PageAnotation'));

    echo HTML_FormBegin('form_filter', 'pool_list.php');
    echo HTML_HiddenField('show_list', 1);

    echo HTML_TableBegin(__bx('legacy-base::pool.TableHeader1') . "&nbsp;&nbsp;" . Count($forms) . " / $num_all", 4);
    echo HTML_RowBegin();
    echo HTML_HeaderCell(__bx('legacy-base::pool.Tag') . ':', 1, 1, C_HTML_NoStyle, '30%');
    echo HTML_ValueCell(HTML_TextField('filter_tag', $filter_tag), 3);
    echo HTML_RowEnd();

    echo HTML_RowBegin();
    echo HTML_HeaderCell(__bx('legacy-base::pool.PoolType') . ':');
    $pool_type_array = Array('' => __bx('legacy-base::pool.All'), 'single' => __bx('legacy-base::pool.Single'), 'multi' => __bx('legacy-base::pool.Multi'));
    $html_subpolls = HTML_Checkbox("subpolls", "1", $subpolls);
    $html_subpolls .= "&nbsp;" . __bx('legacy-base::pool.View_Subpolls');
    echo HTML_ValueCell(HTML_SelectSimple('filter_pool_type', $pool_type_array, $filter_pool_type), 1, 1, 'left', 193);
    echo HTML_ValueCell($html_subpolls, 2);
    echo HTML_RowEnd();

    echo HTML_RowBegin();
    echo HTML_HeaderCell(__bx('legacy-base::pool.Author') . ':');
    $users_array = Array('' => __bx('legacy-base::pool.Allm'));
    $pom_array = BuxusDB::get()->fetchAll("SELECT user_id, user_long_name FROM tblUsers WHERE pool_right=:pool_right  AND user_id <> :user_id",
        array(':pool_right' => C_True_Char, ':user_id' => C_admin_id));
    for ($i = 0; $i < Count($pom_array); $users_array += Array($pom_array[$i]['user_id'] => $pom_array[$i]['user_long_name']), $i++) {
        ;
    }
    echo HTML_ValueCell(HTML_SelectSimple('filter_user', $users_array, $filter_user), 3);
    echo HTML_RowEnd();

    echo HTML_RowBegin();
    echo HTML_HeaderCell(__bx('legacy-base::pool.State') . ':');
    $html = HTML_SelectBegin('filter_state');
    $html .= HTML_Option('', __bx('legacy-base::pool.All'));
    $html .= HTML_Option(C_True_Char, __bx('legacy-base::pool.Active'), $filter_state == C_True_Char);
    $html .= HTML_Option(C_False_Char, __bx('legacy-base::pool.Passive'), $filter_state == C_False_Char);
    $html .= HTML_SelectEnd();
    echo HTML_ValueCell($html, 3);
    echo HTML_RowEnd();

    echo HTML_RowBegin();
    echo HTML_HeaderCell(__bx('legacy-base::pool.TimeFrom') . ' ' . __bx('legacy-base::pool.DateFormat') . ':');
    echo HTML_ValueCell(HTML_TextField('time_created_from', $time_created_from, 20));
    echo HTML_HeaderCell(__bx('legacy-base::pool.TimeTill') . ' ' . __bx('legacy-base::pool.DateFormat') . ':');
    echo HTML_ValueCell(HTML_TextField('time_created_till', $time_created_till, 20));
    echo HTML_RowEnd();

    echo HTML_RowBegin();
    echo HTML_HeaderCell(__bx('legacy-base::pool.Page_id') . ':', 1, 1, C_HTML_NoStyle);
    echo HTML_ValueCell(HTML_TextField('page_id', $page_ids, 20), 3);
    echo HTML_RowEnd();

    echo HTML_RowBegin();
    echo HTML_HeaderCell(__bx('legacy-base::pool.Order_by') . ':', 1, 1, C_HTML_NoStyle);
    $html = HTML_SelectBegin('order_by');
    $html .= HTML_Option('tblPools.time_created DESC', __bx('legacy-base::pool.ByDate'), $order_by == 'tblPools.time_created DESC');
    $html .= HTML_Option('tblPools.pool_tag', __bx('legacy-base::pool.ByName'), $order_by == 'tblPools.pool_tag');
    $html .= HTML_SelectEnd();
    echo HTML_ValueCell($html, 3);
    echo HTML_RowEnd();

    echo HTML_RowBegin();
    echo HTML_HeaderCell(__bx('legacy-base::pool.Paging_After') . ':');
    echo HTML_ValueCell(HTML_TextField('C_HTML_ItemListCount', $C_HTML_ItemListCount, 20), 3);
    echo HTML_RowEnd();

    echo HTML_TableEnd();

    echo HTML_SubmitButton(__bx('legacy-base::pool.Filter'), __bx('legacy-base::pool.Filter'));

    if ($show_list) {
        echo HTML_HiddenField("operation");

        echo "<h6>" . __bx('legacy-base::pool.Table2Anotation') . " " . Count($forms) . " / $num_all " . __bx('legacy-base::pool.Table2Anotation1') . "</h6>";
        $array_items = Array();
        $index = 0;
        while (list ($key, $form) = each($forms)) {
            if ($form["is_active"] == C_False_Char) {
                $class_href = " class=\"passive\" id=\"passive\" ";
            } else {
                $class_href = '';
            }

            if (!$form["pool_tag"]) {
                $form["pool_tag"] = "_";
            }

            $multi_votes_array = PoolGetMultipleVotesArray();

            $array_items[$index][__bx('legacy-base::pool.Tag')] = HTML_Link("./pool_details.php?pool_id=" . $form["pool_id"],
                htmlspecialchars($form["pool_tag"]), ($form["is_active"] == C_False_Char) ? 'passive' : '');
            $array_items[$index][__bx('legacy-base::pool.PoolType')] = (($form["pool_type"] == 'multi') ? __bx('legacy-base::pool.Multi') : __bx('legacy-base::pool.Single'));
            $array_items[$index][__bx('legacy-base::pool.Author')] = $form["user_long_name"];
            $array_items[$index][__bx('legacy-base::pool.Date_Created')] = $form["time_created"];
            $array_items[$index][__bx('legacy-base::pool.Multiple_Votes_Flag')] = $multi_votes_array[$form["disalow_multiple_votes_flag"]];
            $array_items[$index][__bx('legacy-base::pool.Question')] = htmlspecialchars(mb_substr($form["the_question"], 0, C_pool_displayed_question));
            $array_items[$index][__bx('legacy-base::pool.Selected')] = HTML_Checkbox('pool_checked[]', $form["pool_id"]);
            if ($subpolls) {
                $html_subpolls = "";
                if ($form["pool_type"] == 'multi') {
                    $sql_query = "SELECT DISTINCT RTRIM(P.page_name) as page_name, MP.page_id FROM tblMultiPoolAnswers MP";
                    $sql_query .= " INNER JOIN tblPages P ON P.page_id=MP.page_id";
                    $sql_query .= " WHERE MP.pool_id= :pool_id";
                    if ($page_ids) {
                        $tmp_page_ids = explode(',', $page_ids);
                        $sql_query .= " AND MP.page_id IN (" . BuxusDB::get()->quoteInto('?', $tmp_page_ids) . ")";
                    }
                    $sql_query .= " ORDER BY P.page_name";
                    $subpolls_array = BuxusDB::get()->fetchAll($sql_query, array(':pool_id' => $form["pool_id"]));
                    for ($sp = 0; $sp < Count($subpolls_array); $sp++) {
                        $html_subpolls .= HTML_Link("./pool_details.php?pool_id=" . $form["pool_id"] . "&page_id=" . $subpolls_array[$sp]["page_id"],
                                $subpolls_array[$sp]["page_name"]) . "&nbsp;&nbsp;&nbsp;";
                    }
                }
                $array_items[$index][__bx('legacy-base::pool.Subpolls')] = $html_subpolls;
            }
            $index++;
        }

        $filter = array(
            "show_list" => $show_list,
            "filter_tag" => $filter_tag,
            "filter_pool_type" => $filter_pool_type,
            "subpolls" => $subpolls,
            "filter_user" => $filter_user,
            "filter_state" => $filter_state,
            "time_created_from" => $time_created_from,
            "time_created_till" => $time_created_till,
            "page_id" => $page_ids,
            "order_by" => $order_by,
            "C_HTML_ItemListCount" => $C_HTML_ItemListCount
        );

        $baseUrl = $_SERVER['PHP_SELF'] . '?' . implodeAssociativeArray($filter);
        DisplayEntityList($array_items, $C_HTML_ItemListCount, $first_item_index, $baseUrl);

        echo "<BR><BR>" . HTML_Button(__bx('legacy-base::pool.DeleteSelected'), __bx('legacy-base::pool.DeleteSelected'),
                'this.form.operation.value="delete"; this.form.action="pool_operations.php?first_item_index=' . $first_item_index . '";this.form.submit();');

    }
    echo HTML_FormEnd();
} else {
    echo __bx('legacy-base::pool.NoAccessRights');
}
DisplayHtmlFooter();
