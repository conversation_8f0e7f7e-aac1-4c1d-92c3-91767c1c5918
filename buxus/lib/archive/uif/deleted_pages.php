<?php
	
	
	
	require_once 'includes/functions.php';
	require_once 'includes/local_property_functions.php';
	require_once 'includes/uif_functions.php';

	
	require_once 'includes/validate.php';
	
	

	// Ak je archiv vypnuty, nic nebudeme zobrazovat!
	if (GetSystemOption('C_mod_archive') != '1')
	{
		ResultMessage::warnMessage(__bx('legacy-base::deleted_pages.module_archive_is_not_allowed_you_have_been_redirected_to_main_page'));
        return DisplayHtmlRedirectPage(__bx('legacy-base::deleted_pages.DeletedPagesTitle'), config('buxus_core.base_url') . "system/main.php", __bx('legacy-base::deleted_pages.Processing'));
	}

    if(isset($_REQUEST['selected_pages']) && is_array($_REQUEST['selected_pages']) && count($_REQUEST['selected_pages']) > 0 && \Buxus\User\UserUtils::getGlobalUserRight(\Buxus::userId(), 'admin_right') != C_True_Char) {
        foreach($_REQUEST['selected_pages'] as $key => $selected_page) {
            $page_rights = getUserRightsForPage(\Buxus::userId(), $selected_page);

            if($page_rights['write_right'] != C_True_Char) {
                unset($_REQUEST['selected_pages'][$key]);
            }
        }
    }

	if (isset($_REQUEST['action']) && $_REQUEST['action'] == 'delete' && isset($_REQUEST['selected_pages']) && is_array($_REQUEST['selected_pages']) && count($_REQUEST['selected_pages']) > 0)
	{
		$selected_pages = array();
		$removed_pages = array();
		foreach ($_REQUEST['selected_pages'] as $page_id)
		{
			if ((int)$page_id > 0)
			{
				$selected_pages[] = (int)$page_id;
				$removed_pages[] = '<li>' . GetPageName($page_id) . ' (' . $page_id . ')</li>';
			}
		}
		
		// Vyber vsetky archivovane polozky k tejto stranke:
		$sql = "SELECT page_archive_id FROM tblArchivePages WHERE page_id IN (" . BuxusDB::get()->quoteInto('?', $selected_pages) . ")";
		$archive_pages = BuxusDB::get()->fetchCol($sql);
		
		// Zmaz vsetky archivovane polozky pre vybrane stranky:
		BuxusDB::get()->query("DELETE FROM tblArchiveLinkProperties WHERE from_page_archive_id IN (" . BuxusDB::get()->quoteInto('?', $archive_pages) . ")");
		BuxusDB::get()->query("DELETE FROM tblArchivePagePropertyValues WHERE page_archive_id IN (" . BuxusDB::get()->quoteInto('?', $archive_pages) . ")");
		BuxusDB::get()->query("DELETE FROM tblArchivePages WHERE page_archive_id IN (" . BuxusDB::get()->quoteInto('?', $archive_pages) . ")");
		
		// Zmaz vsetky zaznamy o vybranych strankach:
		BuxusDB::get()->query("DELETE FROM tblPages WHERE page_id IN (" . BuxusDB::get()->quoteInto('?', $selected_pages) . ")");
		BuxusDB::get()->query("DELETE FROM tblAccessRightsPageUser WHERE page_id IN (" . BuxusDB::get()->quoteInto('?', $selected_pages) . ")");
		BuxusDB::get()->query("DELETE FROM tblPagePropertyValues WHERE page_id IN (" . BuxusDB::get()->quoteInto('?', $selected_pages) . ")");
		BuxusDB::get()->query("DELETE FROM tblPageTemplates WHERE page_id IN (" . BuxusDB::get()->quoteInto('?', $selected_pages) . ")");
		BuxusDB::get()->query("DELETE FROM tblLinkProperties WHERE from_page_id IN (" . BuxusDB::get()->quoteInto('?', $selected_pages) . ")");
		BuxusDB::get()->query("DELETE FROM tblLinkProperties WHERE to_page_id IN (" . BuxusDB::get()->quoteInto('?', $selected_pages) . ")");
		//delete counters
		$tmp = $selected_pages;
		foreach($tmp as $k=>$v) {
			$tmp[$k] = C_Articles_counter_prefix . $v; 
		}
		BuxusDB::get()->query("DELETE tblCounterDailyCounts FROM tblCounterDailyCounts, tblCounters WHERE tblCounterDailyCounts.counter_id = tblCounters.counter_id AND counter_tag IN  (" . BuxusDB::get()->quoteInto('?', $tmp) . ")");
		BuxusDB::get()->query("DELETE FROM tblCounters WHERE counter_tag IN  (" . BuxusDB::get()->quoteInto('?', $tmp) . ")");
		//forums
		$tmp = $selected_pages;
		foreach($tmp as $k=>$v) {
			$tmp[$k] = C_Articles_forum_prefix . $v; 
		}
		BuxusDB::get()->query("DELETE FROM tblMessages WHERE forum_tag IN  (" . BuxusDB::get()->quoteInto('?', $tmp) . ")");
		//pools (multi)
		BuxusDB::get()->query("DELETE FROM tblMultiPoolAnswers WHERE page_id IN (" . BuxusDB::get()->quoteInto('?', $selected_pages) . ")");
		
		ResultMessage::infoMessage('Úspešne odstránené stránky (' . count($removed_pages) . '): <ul>' . implode("\n", $removed_pages) . '</ul>');
		header('Location: ' . $_SERVER['PHP_SELF']);
		exit();
	}
	elseif (isset($_REQUEST['action']) && $_REQUEST['action'] == 'restore' && isset($_REQUEST['selected_pages']) && is_array($_REQUEST['selected_pages']) && count($_REQUEST['selected_pages']) > 0)
	{
		$restored_pages = array();
		foreach ($_REQUEST['selected_pages'] as $page_id)
		{
			$res = Archive::restoreDeletedPage($page_id);
			if ($res !== false)
			{
				$restored_pages[] = '<li><a href="' . config('buxus_core.base_url') . 'system/page_details.php?page_id=' . $page_id . '">' . GetPageName($page_id) . ' (' . $page_id . ')</a></li>';
			}
		}

		ResultMessage::infoMessage(__bx('legacy-base::deleted_pages.successfully_restored_pages'). '(' . count($restored_pages) . '): <ul>' . implode("\n", $restored_pages) . '</ul>');
		header('Location: ' . $_SERVER['PHP_SELF']);
		exit();
	}
	elseif (isset($_REQUEST['action']))
	{
		header('Location: ' . $_SERVER['PHP_SELF']);
		exit();
	}
	
	  $javascript='
	  <SCRIPT LANGUAGE=JavaScript>
<!--
function SelectAllFields(theField)
{
	if (theField!=null)
	{
		if (theField.length==undefined) 
		{
			theField.checked=true;
		}
		else
		{
			for (i=0;i<theField.length;i++)
			{
				theField[i].checked=true;
			}
		}
		document.getElementById("delete_submit").disabled = false;
		document.getElementById("restore_submit").disabled = false;
	}
}

function toggleDeleteButton(checkboxes)
{
	var disabled = true;
	if (checkboxes) 
	{
		if (checkboxes.length)
		{
			for (i=0;i<checkboxes.length;i++)
			{
				if (checkboxes[i].checked)
				{
					disabled = false;
					break;
				}
			}
		}
		else
		{
			disabled = !checkboxes.checked;
		}
	}
	
	document.getElementById("delete_submit").disabled = disabled;
	document.getElementById("restore_submit").disabled = disabled;
}

function confirmForm()
{
	var action = document.getElementById("action").value;
	
	if (action == "delete")
	{
		return confirm("' . __bx('legacy-base::deleted_pages.DeleteConfirmation') . '");
	}
	else
	{
		return confirm("' . __bx('legacy-base::deleted_pages.RestoreConfirmation') . '");
	}
}
//-->
</SCRIPT>';
	
  	DisplayHtmlHeader(__bx('legacy-base::deleted_pages.DeletedPagesTitle'), C_MenuType_Pages, $javascript);
  	
	echo HTML_PageTitle(__bx('legacy-base::deleted_pages.PageTitle'), __bx('legacy-base::deleted_pages.PageAnotation'));

	
	/* Subselect zatial asi nepouzivame, zlozime to z dvoch selektov:
	$sql = "
		SELECT 
			tp.`page_id`,
			tp.`page_name`,
			tap.`last_updated_user_id`,
			tap.`archive_time`
		FROM 
			`tblPages` as tp
		LEFT JOIN
			`tblArchivePages` as tap
		ON
			tp.`page_id` = tap.`page_id`
		WHERE
			tp.`author_id` IS NULL AND
			tp.`creation_date` IS NULL AND
			tp.`page_type_id` IS NULL AND
			tp.`page_state_id` IS NULL AND
			tp.`page_class_id` IS NULL AND
			tp.`last_updated` IS NULL AND
			tp.`sort_date_time` IS NULL AND
			tp.`properties` IS NULL AND
			tap.`page_archive_id` in
			(
				SELECT
					max(`page_archive_id`)
				FROM 
					`tblArchivePages`
				GROUP BY
					`page_id`
			)";
	*/
	
	$sql = "
		SELECT
			max(`page_archive_id`)
		FROM 
			`tblArchivePages`
		WHERE
			`page_name` IS NOT NULL
			AND
			`page_name` <> ''
		GROUP BY
			`page_id`
	";
	$maxArchiveIds = BuxusDB::get()->fetchCol($sql);

    $deletedPages = array();
    if(count($maxArchiveIds)) {
        $sql = "
            SELECT
                tp.`page_id`,
                tap.`page_name`,
                tap.`last_updated_user_id`,
                tap.`archive_time`
            FROM
                `tblPages` as tp
            LEFT JOIN
                `tblArchivePages` as tap
            ON
                tp.`page_id` = tap.`page_id`
            WHERE
                tp.`author_id` IS NULL AND
                tp.`creation_date` IS NULL AND
                tp.`page_type_id` IS NULL AND
                tp.`page_state_id` IS NULL AND
                tp.`page_class_id` IS NULL AND
                tp.`last_updated` IS NULL AND
                tp.`sort_date_time` IS NULL AND
                tp.`properties` IS NULL AND
                tap.`page_archive_id` in (" . BuxusDB::get()->quoteInto('?', $maxArchiveIds) . ")
            ORDER BY
                tp.`page_id`";

        $deletedPages = BuxusDB::get()->fetchAll($sql);
    }

    $readOnlyPages = array();
    if(\Buxus\User\UserUtils::getGlobalUserRight(\Buxus::userId(), 'admin_right') != C_True_Char) {
        foreach($deletedPages as $key => $deletedPage) {
            $page_rights = getUserRightsForPage(\Buxus::userId(), $deletedPage['page_id']);

            if($page_rights['write_right'] != C_True_Char) {
                if($page_rights['read_right'] == C_True_Char) {
                    $readOnlyPages[] = $deletedPage['page_id'];
                    continue;
                }
                unset($deletedPages[$key]);
            }
        }
    }

	$html = '';
	$html .= HTML_FormBegin('page_delete_form', $_SERVER['PHP_SELF'], 'return confirmForm()');
		$html .= HTML_HiddenField('action', 'delete', 'action');
		$html .= HTML_TableBegin(__bx('legacy-base::deleted_pages.DeletedPagesList'), 5);
			$html .= HTML_RowBegin();
				$html .= HTML_HeaderCell(__bx('legacy-base::deleted_pages.PageId'));
				$html .= HTML_HeaderCell(__bx('legacy-base::deleted_pages.PageName'));
				$html .= HTML_HeaderCell(__bx('legacy-base::deleted_pages.ArchiveTime'));
				$html .= HTML_HeaderCell(__bx('legacy-base::deleted_pages.UpdateAuthor'));
				$html .= HTML_HeaderCell(__bx('legacy-base::deleted_pages.Selected'));
			$html .= HTML_RowEnd();
		
			foreach ($deletedPages as $deletedPage)
			{
				$html .= HTML_RowBegin();
					$html .= HTML_ValueCell(HTML_Link(config('buxus_core.base_url') . 'lib/archive/uif/page_history.php?page_id=' . $deletedPage['page_id'], $deletedPage['page_id']));
					$html .= HTML_ValueCell(htmlspecialchars($deletedPage['page_name'], ENT_QUOTES));
					$html .= HTML_ValueCell($deletedPage['archive_time']);
					$html .= HTML_ValueCell(GetUserLongName($deletedPage['last_updated_user_id']));
                    if(in_array($deletedPage['page_id'], $readOnlyPages) === false) {
                        $disabled = '';
                    }
                    else {
                        $disabled = ' disabled="disabled" ';
                    }
                    $html .= HTML_ValueCell(HTML_Checkbox('selected_pages[]', $deletedPage['page_id'], false, 'toggleDeleteButton(this.form["selected_pages[]"])', $disabled));
				$html .= HTML_RowEnd();
			}
			
		$html .= HTML_TableEnd();
		$html .= HTML_SubmitButton('restore_submit', __bx('legacy-base::deleted_pages.RestoreSelected'), 'document.getElementById("action").value="restore";', null, ' disabled');
		$html .= '&nbsp; &nbsp;' . HTML_SubmitButton('delete_submit', __bx('legacy-base::deleted_pages.DeleteSelected'), 'document.getElementById("action").value="delete";', null, ' disabled');
		$html .= '&nbsp; &nbsp;' . HTML_Button('select_all_button', __bx('legacy-base::deleted_pages.SelectAll'), 'SelectAllFields(this.form["selected_pages[]"]);');
	$html .= HTML_FormEnd();
	
	echo $html;
	DisplayHtmlFooter();
