<?php

require_once 'includes/functions.php';
require_once 'includes/validate.php';
require_once 'includes/functions/tf_toupperwithdiacritic.php';
require_once 'lib/shop/shop.inc.php';

$have_permission = false;
$user_id = \Buxus\User\Facades\BuxusUserManager::getCurrentUserId();

$data_row = BuxusDB::get()->fetchRow("SELECT admin_right, eshop_right FROM tblUsers  WHERE user_id = :user_id", array(':user_id' => $user_id));
if ($data_row) {
    if (trim($data_row['eshop_right']) == C_True_Char) {
        $have_permission = true;
    }
    if (trim($data_row['admin_right']) == C_True_Char) {
        $have_permission = true;
    }
}

$_MYPOST = $_POST;

if (!isset($_POST['order_state']) || !isset($_POST['order_id']) || $have_permission == false) {
    $result = array('result' => array('success' => 0));
} else {
    $order_id = (int)$_MYPOST['order_id'];

    $order_object = \OrderFactory::getById($order_id);
    \BuxusSite::pushSite($order_object->getSite());

    if (!isset($_GET['notif'])) {
        $order_state = (int)$_MYPOST['order_state'];
    } else {
        $order_state = $order_object->getOrderState();
    }

    /**
     * @var \Buxus\Eshop\Contracts\ShopOrderState $order_state
     */
    $order_state = \Buxus\Eshop\Facades\OrderStateManager::getStateById($order_state);

    $order_properties = array();

    $item_index = 0;
    $order_items = array();
    while (isset($_MYPOST['order_item_id_' . $item_index]))
    {
        $order_item_index = $_MYPOST['order_item_id_' . $item_index];

        $order_items[$order_item_index] = array(
            'ORDER_ITEM_ID' => $order_item_index,
            'ORDER_ITEM_NAME' => $_MYPOST['item_custom_name_' . $item_index],
            'ORDER_ID' => $order_id, 'PAGE_ID' => $_MYPOST['page_id_' . $item_index],
            'NR_OF_ITEMS' => $_MYPOST['nr_of_items_' . $item_index],
            'PRICE_PER_ITEM' => str_replace(',', '.', $_MYPOST['price_per_item_' . $item_index]),
            'VAT_PER_ITEM' => str_replace(',', '.', $_MYPOST['vat_per_item_' . $item_index]),
            'ITEM_PRICE_TYPE' => $_MYPOST['item_price_type_' . $item_index],
        );

        if (isset($_MYPOST['external_note_' . $item_index])) {
            $order_items[$order_item_index]['EXTERNAL_NOTE'] = $_MYPOST['external_note_' . $item_index];
        }

        $tmp = \BuxusDB::get()->fetchAll("SELECT * FROM tblShopOrderItemOptions WHERE order_item_id = :order_item_id", [':order_item_id' => $order_item_index]);

        if (empty($tmp)) {
            $tmp = [];
        }
        $item_options = [];
        foreach ($tmp as $row) {
            $item_options[$row['option_tag']] = $row['option_value'];
        }


        $props = \PageFactory::get($order_items[$order_item_index]['PAGE_ID']);
        $properties = $props->toArray();
        foreach($properties as $key => $value) {
            $key = mb_strtoupper($key);
            if (!isset($item_options[$key])) {
                $item_options[$key] = $value;
            }
        }
        $order_items[$order_item_index]['OPTIONS'] = $item_options;

        unset($_MYPOST['order_item_id_' . $item_index]);
        unset($_MYPOST['item_custom_name_' . $item_index]);
        unset($_MYPOST['page_id_' . $item_index]);
        unset($_MYPOST['nr_of_items_' . $item_index]);
        unset($_MYPOST['price_per_item_' . $item_index]);
        unset($_MYPOST['vat_per_item_' . $item_index]);
        unset($_MYPOST['nr_of_delivered_items_' . $item_index]);
        unset($_MYPOST['order_item_id_' . $item_index]);
        unset($_MYPOST['item_price_type_' . $item_index]);
        unset($_MYPOST['is_manual_price_' . $item_index]);
        unset($_MYPOST['discount_' . $item_index]);

        $item_index++;
    }

    $order_items_count = count($order_items);

    // Custom properties
    if (is_array($_MYPOST)) {
        foreach ($_MYPOST as $key => $value) {
            $order_properties[mb_strtoupper($key)] = $value;
        }
    }

    $order_properties['HOSTNAME'] = \BuxusSite::getHostname();

    $delivery_type = $order_properties['DELIVERY_TYPE'];
    try {
        $delivery_object = app(\Buxus\Eshop\Contracts\DeliveryTypeFactory::class)->getDeliveryTypeByTag($delivery_type);
    } catch (Exception $e) {
        $delivery_object = null;
    }
    $payment_type = $order_properties['PAYMENT_TYPE'];
    try {
        $payment_object = app(\Buxus\Eshop\Contracts\PaymentTypeFactory::class)->getPaymentTypeByTag($payment_type);
    } catch (Exception $e) {
        $payment_object = null;
    }

    $ignored_properties = [
        'NOTIFICATION_SEND',
        'NOTIFICATION_EMAIL_TO',
        'NOTIFICATION_EMAIL_FROM',
        'NOTIFICATION_EMAIL_SUBJECT',
        'NOTIFICATION_EMAIL_TEXT_HTML',
        'NOTIFICATION_EMAIL_TEXT',
        'ACTIVE_PANEL',
        'DELETE_LIST',
    ];
    foreach ($ignored_properties as $ignored) {
        unset($order_properties[$ignored]);
    }

    /**
     * @var \Buxus\Eshop\Notifications\OrderNotificationEmailResolver $notificationResolver
     */
    $notificationResolver = app('buxus:eshop:order-notification-email-resolver');
    $email_page_id = $notificationResolver->resolveNotificationEmailPageId($order_state, $order_object);

    if ($_GET['notif'] && $_POST['custom_notification']) {
        $buxus_email = \Email::get($_POST['custom_notification']);
    } elseif ($email_page_id) {
        $buxus_email = \Email::get($email_page_id);
    }

    if ($buxus_email) {
        // put the posted order data into the order
        foreach ($order_properties as $key => $value) {
            $order_object->setData($key, $value);
        }

        $order_object->setData('ORDER_ITEMS_COUNT', $order_items_count);
        $order_object->setData('BUXUS_USER', \Buxus\User\Facades\BuxusUserManager::getUserLongName(\Buxus\User\Facades\BuxusUserManager::getCurrentUserId()));

        // add items
        $new_order_items = [];
        foreach ($order_items as $item) {
            /**
             * @var \Buxus\Eshop\Order\OrderItemInterface $order_item
             */
            $order_item = app('buxus.eshop.order-item-object');
            $order_item->setOrderItemName($item['ORDER_ITEM_NAME']);
            $order_item->setPageId($item['PAGE_ID']);
            $order_item->setItemPrice($item['PRICE_PER_ITEM']);
            $order_item->setItemPriceType($item['ITEM_PRICE_TYPE']);
            $order_item->setAmount($item['NR_OF_ITEMS']);
            $order_item->setVatRatePercent($item['VAT_PER_ITEM']);
            $order_item->setOptions($item['OPTIONS']);
            $new_order_items[] = $order_item;
        }
        $order_object->setItems($new_order_items);

        if ($delivery_object) {
            $order_object->setTransportType($delivery_object);
        }
        if ($payment_object) {
            $order_object->setPaymentType($payment_object);
        }

        $buxus_email->addDataProvider($order_object);

        $emailContent = $buxus_email->getEmailContent();

//        $buxus_email->fillBodyAndSubjectProperties();

        // From
        $notification_email_from = $emailContent->getFrom();

        $notification_email_body = $emailContent->getBodyText();
        $notification_email_body_html = $emailContent->getBodyHTML();
        $notification_email_subject = $emailContent->getSubject();

        $users_to_notify = $emailContent->getTo();
        array_unshift($users_to_notify, $order_properties['EMAIL']);
        $users_to_notify = array_unique($users_to_notify);
        $users_to_notify = implode(',', $users_to_notify);

        $attachments = $emailContent->getAttachments();

        $result = array("result" => array(
            'success' => 1,
            'notification_email_body' => $notification_email_body,
            'notification_email_body_html' =>$notification_email_body_html,
            'notification_email_from' => $notification_email_from,
            'notification_email_subject' => $notification_email_subject,
            'notification_embed_images' => ($emailContent->shouldEmbedImages() ? 'T' : 'F'),
            'notification_recipients' => $users_to_notify,
            'attachments' => $attachments,
            'activate_eshop_notification' => !config('buxus_eshop.eshop_detail_do_not_set_email_notification_on', false),
        ));
    } else {
        $result = array('result' => array(
            'success' => 1,
            'notification_email_body' => __bx('eshop::shop_orders_details.message_content'),
            'notification_email_body_html' => __bx('eshop::shop_orders_details.message_content'),
            'notification_email_from' => '',
            'notification_email_subject' => __bx('eshop::shop_orders_details.message_subject'),
            'notification_embed_images' => 'F',
            'notification_recipients' => $order_properties['EMAIL'],
            'attachments' => [],
            'activate_eshop_notification' => false,
        ));
    }
}
header('Content-type:application/json');
echo json_encode($result);
exit;
