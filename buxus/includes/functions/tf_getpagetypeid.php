<?php
/**
 * Functions for page type examinations.
 * @see connect.php
 * @see global.php

 *
 * @copyright ui42 © 2000 - All Rights Reserved
 * @package buxus
 * @subpackage TemplateFunctions
 * @filesource
 */


/**
 * Returns page type ID
 * @param string $page_type_tag tag of the page type
 * @returns int page type ID
 */
function TF_GetPageTypeID($page_type_tag)
{	
	$sql = "SELECT page_type_id
          FROM tblPageTypes 
          WHERE page_type_tag = :page_type_tag";
	
	return BuxusDB::get()->fetchOne($sql, array(':page_type_tag' => $page_type_tag));
}

