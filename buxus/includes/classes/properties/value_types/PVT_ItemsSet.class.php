<?php

use Buxus\Core\Constants;

class PVT_ItemsSet extends PropertyValueType
{
    protected $caption;

    public function __construct()
    {
        parent::__construct();
        $this->caption = __bx('legacy-base::property.ItemsSet');
        $this->tag = constant('PropertyValueTypes::' . __CLASS__);

        $this->addAttribute(new PVTA_Options(Constants::PVTA_options, __bx('legacy-base::property.PVTA_options')));
        $this->addAttribute(new PVTA_Multiple());

        $this->addAttribute(new PVTA_ShowAsList());

        $this->addAttribute(new PVTA_AddEmptyOption());
        $this->addAttribute(new PVTA_LinesCount());

        $this->addAttribute(new PVTA_ColsInSet());
        $this->addAttribute(new PVTA_RowsInSet());
        $this->addAttribute(new PVTA_OnChangeJs());
        $this->addAttribute(new PVTA_Disabled());
        $this->addAttribute(new PVTA_CheckReadRights());
        $this->addAttribute(new PVTA_InheritValue());

        $options = $this->getAttribute(Constants::PVTA_options);
        $options->isArray(true);
    }

    public function loadFromXML($pseudo_xml, $only_raw_values = false)
    {
        $raw_attributes_array = \Buxus\Legacy\LegacyMethods::GParseXMLIntoArray($pseudo_xml);

        // Set options type
        $value_type = $this->getPropertySetting('value_type');
        if (empty($value_type)) { // Old property
            $is_array = $this->getPropertySetting('property_class_id') != Constants::C_ppc_Link;
            $is_array &= $this->getPropertySetting('property_class_id') != Constants::C_ppc_Link_multivalue;
            $is_array &= ((isset($raw_attributes_array['sql'])) && ($raw_attributes_array['sql'] == 'F'));
        } else { // New property
            $is_array = true;
        }
        $this->getAttribute(Constants::PVTA_options)->isArray($is_array);

        parent::loadFromXML($pseudo_xml);

        $cols = $this->getAttribute(Constants::PVTA_cols_in_set)->getValue();
        if (empty($cols)) { // The property is not set
            $this->getAttribute(Constants::PVTA_cols_in_set)->setValue(isset($raw_attributes_array['columns']) ? $raw_attributes_array['columns'] : null);
        }

        if (empty($value_type)) {
            switch ($this->getPropertySetting('show_type_tag')) {
                case Constants::C_BuxusInputSelect:
                    $this->getAttribute(Constants::PVTA_show_as_list)->setValue('T');
                    break;

                case Constants::C_BuxusCheckBoxGroup:
                    $this->getAttribute(Constants::PVTA_multiple)->setValue('T');
                case Constants::C_BuxusRadioGroup:
                    $this->getAttribute(Constants::PVTA_show_as_list)->setValue('F');
            }
        }
    }

    public function renderForm()
    {
        $options = $this->getAttribute(Constants::PVTA_options);
        $multiple = $this->getAttribute(Constants::PVTA_multiple);

        $show_as_list = $this->getAttribute(Constants::PVTA_show_as_list);

        $add_empty_option = $this->getAttribute(Constants::PVTA_add_empty_option);
        $lines_count = $this->getAttribute(Constants::PVTA_lines_count);

        $cols_in_set = $this->getAttribute(Constants::PVTA_cols_in_set);
        $rows_in_set = $this->getAttribute(Constants::PVTA_rows_in_set);

        $onchange_js = $this->getAttribute(Constants::PVTA_onchange_js);
        $disabled = $this->getAttribute(Constants::PVTA_disabled);
        $inherit_value = $this->getAttribute(Constants::PVTA_inherit_value);
        $check_read_rights = $this->getAttribute(Constants::PVTA_check_read_rights);
        $tab = $this->getAttribute(Constants::PVTA_tab);

        if ((string)$show_as_list->getValue() == 'T') {
            $display_list_attributes = '';
            $display_not_list_attributes = 'display: none;';
        } else {
            $display_list_attributes = 'display: none;';
            $display_not_list_attributes = '';
        }

        // Generate options HTML
        $options_controls = $options->getControl();

        $content = '
			<script type="text/javascript">
				function switchShowAsList(elm)
				{
					if (elm.checked)
					{
						$("#add_empty_option").show();
						$("#lines_count").show();
						$("#onchange_js").show();
						$("#disabled").show();
						$("#cols_in_set").hide();
						$("#rows_in_set").hide();
					}
					else
					{
						$("#add_empty_option").hide();
						$("#lines_count").hide();
						$("#onchange_js").hide();
						$("#disabled").hide();
						$("#cols_in_set").show();
						$("#rows_in_set").show();
					}
				}
			</script>' .
            HTML_TableBegin() .
            HTML_RowBegin() .
            HTML_HeaderCell($tab->getCaption() . ':', 1, 1, 'left', 200) .
            HTML_ValueCell($tab->getControl()) .
            HTML_RowEnd() .
            HTML_RowBegin() .
            HTML_HeaderCell($options->getCaption() . ':', 1, 1, 'left', 200) .
            HTML_ValueCell($options_controls) .
            HTML_RowEnd() .
            HTML_RowBegin() .
            HTML_HeaderCell($multiple->getCaption() . ':', 1, 1, 'left', 200) .
            HTML_ValueCell($multiple->getControl()) .
            HTML_RowEnd() .
            HTML_RowBegin() .
            HTML_HeaderCell($show_as_list->getCaption() . ':', 1, 1, 'left', 200) .
            HTML_ValueCell($show_as_list->getControl()) .
            HTML_RowEnd() .
            HTML_RowBegin('add_empty_option', $display_list_attributes) .
            HTML_HeaderCell($add_empty_option->getCaption() . ':', 1, 1, 'left', 200) .
            HTML_ValueCell($add_empty_option->getControl()) .
            HTML_RowEnd() .
            HTML_RowBegin('lines_count', $display_list_attributes) .
            HTML_HeaderCell($lines_count->getCaption() . ':', 1, 1, 'left', 200) .
            HTML_ValueCell($lines_count->getControl()) .
            HTML_RowEnd() .
            HTML_RowBegin('cols_in_set', $display_not_list_attributes) .
            HTML_HeaderCell($cols_in_set->getCaption() . ':', 1, 1, 'left', 200) .
            HTML_ValueCell($cols_in_set->getControl()) .
            HTML_RowEnd() .
            HTML_RowBegin('rows_in_set', $display_not_list_attributes) .
            HTML_HeaderCell($rows_in_set->getCaption() . ':', 1, 1, 'left', 200) .
            HTML_ValueCell($rows_in_set->getControl()) .
            HTML_RowEnd() .
            HTML_RowBegin('disabled', $display_list_attributes) .
            HTML_HeaderCell($disabled->getCaption() . ':', 1, 1, 'left', 200) .
            HTML_ValueCell($disabled->getControl()) .
            HTML_RowEnd() .
            HTML_RowBegin('inherit_value') .
            HTML_HeaderCell($inherit_value->getCaption() . ':', 1, 1, 'left', 200) .
            HTML_ValueCell($inherit_value->getControl()) .
            HTML_RowEnd() .
            HTML_RowBegin() .
            HTML_HeaderCell($check_read_rights->getCaption() . ':', 1, 1, 'left', 200) .
            HTML_ValueCell($check_read_rights->getControl()) .
            HTML_RowEnd() .
            HTML_RowBegin('onchange_js', $display_list_attributes) .
            HTML_HeaderCell($onchange_js->getCaption() . ':', 1, 1, 'left', 200) .
            HTML_ValueCell($onchange_js->getControl()) .
            HTML_RowEnd() .
            HTML_TableEnd();

        return $content;
    }

    public function getDisplayType()
    {
        if ((string)$this->getAttribute(Constants::PVTA_show_as_list)->getValue() == 'T') {
            return Constants::C_BuxusInputSelect;
        } elseif ((string)$this->getAttribute(Constants::PVTA_multiple)->getValue() == 'T') {
            return Constants::C_BuxusCheckBoxGroup;
        } else {
            return Constants::C_BuxusRadioGroup;
        }
    }

    public function getJSValidationClass()
    {
        switch ($this->getDisplayType()) {
            case Constants::C_BuxusCheckBoxGroup:
                return 'BuxusPropertyCheckboxGroup';
                break;

            case Constants::C_BuxusRadioGroup:
                return 'BuxusPropertyRadioGroup';
                break;

            default:
                if ((string)$this->getAttribute(Constants::PVTA_multiple)->getValue() == 'T') {
                    return 'BuxusPropertyMultiSelectbox';
                } else {
                    return parent::getJSValidationClass();
                }
        }
    }

    public function validateValue($value)
    {
        return true;
    }

    public function isSuitableForProperty($property_attributes)
    {
        return in_array($property_attributes['show_type_tag'], array(Constants::C_BuxusInputSelect, Constants::C_BuxusRadioGroup, Constants::C_BuxusCheckBoxGroup));
    }
}
