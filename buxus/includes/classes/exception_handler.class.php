<?php
/**
 * Odchytavanie exception
 *
 * <AUTHOR> <PERSON>
 * @package Buxus
 * @uses Buxus
 */
class ExceptionHandler
{
	/**
	 * <PERSON>cet riadkov kodu, ktore sa maju zobrazovat.
	 */
	public static $source_lines = 6;

	public static function displayUserError()
    {
        $template_path = base_path('buxus/error.phtml');
        if (!file_exists($template_path)) {
            // Show message for user
            echo '<h1>Unrecoverable error occured</h1>';
            echo '<p>Buxus encountered unrecoverable error and the script execution had to be terminated. Error details were reported to the system administrator and written to the system log.<br />';
            echo 'Please try again later. Sorry for the inconvenience.</p>';
        } else {
            include $template_path;
        }
    }

	/**
	 * <PERSON>ler pre excption
	 *
	 * @param Exception $e
	 */
	public static function handleException($e)
	{
		if (config('app.debug')) { // Ide o dev server
			self::displayException($e);
		} else { // Ide o live
			self::logException($e);
            \Buxus\Error\ErrorReporter::reportException($e);
			if (php_sapi_name() != 'cli') {
                self::displayUserError();
            } else {
			    self::displayException($e);
            }

			// Unlock tables
			if (isset($GLOBALS['locked_tables']) && $GLOBALS['locked_tables'] == 'locked')
			{ // There is some loced tables
				BuxusDB::get()->query("UNLOCK TABLES");
			}
		}

		exit();
	}

	/**
	 * Zobrazenie exception
	 *
	 * @param Exception $e
	 */
	public static function displayException($e)
	{
        $_ENV = [];
        $_SERVER = [];
		$whoops = new \Whoops\Run;
		if (php_sapi_name() == 'cli') {
			$whoops->pushHandler(new \Whoops\Handler\PlainTextHandler);
		} else {
			$whoops->pushHandler(new \Whoops\Handler\PrettyPageHandler);
		}
		$whoops->handleException($e);

		//echo self::renderException($e);
	}

	/**
	 * Odoslanie exception emailom
	 *
	 * @param Exception $e
	 */
	public static function mailException($e)
	{
	    /**
         * @var \Buxus\Email\Contract\BaseEmail $email
         */
	    try {
            $email = app()->make(\Buxus\Email\Contract\BaseEmail::class);
        } catch (\Exception $x) {
	        return;
        }

        $email->setSender('<EMAIL>', 'Buxus Automatic Reporter');
        $email->setIsErrorEmail(true);
        $email->setBodyHTML(self::renderException($e));
        $email->setSubject('Buxus: Error [' . C_database_name . ']');
        $email->send(explode(',', \Buxus::config('legacy.C_critical_errors_report_to')));
	}

	/**
	 * Zalogovanie exception do logu
	 *
	 * @param Exception $e
	 */
	public static function logException($e)
	{
		if ($e instanceof DatabaseException)
		{ // It is database exception
			Buxus::log('SQL critical error occured. Code: [' . @mysql_errno($GLOBALS['default_db']) . ']. Message: [' . @mysql_error($GLOBALS['default_db']) . ']. Query performed: [' . $e->getMessage() . '].');
		}
		else
		{ // It is not database exception
			Buxus::log('Exception "' . get_class($e) . '" with message "' . $e->getMessage() . '" in ' . $e->getFile() . ':' . $e->getLine());
		}
	}

	/**
	 * Vrati cast kodu zo suboru
	 *
	 * @param int $lines
	 * @param int $errorLine
	 * @return string
	 */
	private static function getSourceCode($lines, $errorLine)
	{
		$begin_line = ($errorLine - self::$source_lines >= 0) ? ($errorLine - self::$source_lines) : 0;
		$end_line = ($errorLine + self::$source_lines <= count($lines)) ? ($errorLine + self::$source_lines) : count($lines);

		$source = '';
		for ($i = $begin_line; $i < $end_line; ++$i)
		{
			if ($i === $errorLine - 1)
			{ // Riadok chyby
				$line = htmlspecialchars(sprintf('%04d: %s', $i + 1, str_replace("\t", '    ', $lines[$i])));
				$source .= '<div class="error">' . $line . '</div>';
			}
			else
			{ // Ostatne riadky
				$source .= htmlspecialchars(sprintf('%04d: %s', $i + 1, str_replace("\t", '    ', $lines[$i])));
			}
		}

		return $source;
	}

	/**
	 * Upravenie pola pre vypis
	 *
	 * @param unknown_type $array
	 * @return unknown
	 */
	private static function prepareArray($array)
	{
		$return_html = '';
		if (is_array($array) && ! empty($array))
		{
			foreach ($array as $key => $val)
			{
				if (is_array($val))
				{ // The value is array
					$val = print_r($val, true);
				}
				$return_html .= $key . ':<span class="array">' . $val . '</span>' . "\n";
			}
		}
		return $return_html;
	}

	/**
	 * Generovanie HTML opisujuceho exception
	 *
	 * @param Exception $e
	 * @return string
	 */
	private static function renderException($e)
	{
		$fileName = $e->getFile();
		$errorLine = $e->getLine();

		$request_data = $_REQUEST;
		ksort($request_data);

		$server_data = isset($_SERVER) ? $_SERVER : array();
		ksort($server_data);

		$return_html = '<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en" lang="en">
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
		<title>' . get_class($e) . '</title>
		<style type="text/css">
			/*<![CDATA[*/
			body { font-family: "Verdana"; font-weight: normal; color: black; background-color: white; }
			h1 { font-family: "Verdana"; font-weight: normal; font-size: 18pt; color: #2B528A }
			h2 { font-family: "Verdana"; font-weight: normal; font-size: 13pt; color: #2B528A; }
			h3 { font-family: "Verdana"; font-weight: bold; font-size: 10pt; }
			p { font-family: "Verdana"; font-weight: normal; color: black; font-size: 9pt; margin-top: -5px; }
			code, pre { font-family: "Lucida Console"; font-size: 10pt; }
			td, .version { color: gray; font-size: 8pt; border-top: 1px solid #aaaaaa; }
			.source {font-family: "Lucida Console"; font-weight: normal; background-color: #E2EAF4; }
			.error { background-color: #BDCFE5; }
			.array { font-style: italic; color: #445D2A; }
			.mysql_statement { font-weight:bold; color: #9B8FEA; }
			#server_toggle { cursor: pointer; }
			/*]]>*/
		</style>
		<script type="text/javascript" language="JavaScript">
			function toggle(){
				var server = document.getElementById("server");
				if (server) {
					if (server.style.display == "none") {
						server.style.display = "block";
					}
					else {
						server.style.display = "none";
					}
				}
			}
		</script>
	</head>
	<body onload="toggle()">
		<h1>' . get_class($e) . '</h1>';

		if ($e instanceof DatabaseException)
		{ // It is database exception
			$return_html .= '
		<pre style="color:maroon">Buxus encountered unrecoverable error while trying to perform an SQL query.</pre>
		<h3>Query performed</h3>
		<div class="source">
			<code>
				<pre>' . htmlspecialchars($e->getMessage()) . '</pre>
			</code>
		</div>
		<h3>Error details</h3>
		<div class="source">
			<code>
				<pre>
Error code: ' . $e->getSqlCode() . '
Error message: ' . $e->getSqlMessage()  . '
				</pre>
			</code>
		</div>';
            if (!empty($fileName) && file_exists($fileName)) {
                $return_html .= '
		<h3>Source File</h3>
		<p>' . htmlspecialchars($fileName) . ' (' . $errorLine . ')' . '</p>
		<div class="source">
			<code>
				<pre>' . self::getSourceCode(@file($fileName), $errorLine) . '</pre>
			</code>
		</div>';
            }
        } else { // It is not database exception
			$return_html .= '
		<h3>Description</h3>
		<pre style="color:maroon">' . htmlspecialchars($e->getMessage()) . '</pre>';

			if (!empty($fileName) && file_exists($fileName)) {
                $return_html .= '
		<h3>Source File</h3>
		<p>' . htmlspecialchars($fileName) . ' (' . $errorLine . ')' . '</p>
		<div class="source">
			<code>
				<pre>' . self::getSourceCode(@file($fileName), $errorLine) . '</pre>
			</code>
		</div>';
            }
		}

		$url = Buxus\Util\Url::getProtocol() . (isset($_SERVER['HTTP_HOST']) ? $_SERVER['HTTP_HOST'] : '') . (isset($_SERVER['REQUEST_URI']) ? $_SERVER['REQUEST_URI'] : '');

		$return_html .= '
		<h3>Stack Trace</h3>
		<div class="source">
			<code>
				<pre>' . htmlspecialchars($e->getTraceAsString()) . '</pre>
			</code>
		</div>
		<h3>Script details</h3>
		<div class="source">
			<code>
				<pre>
URL: <a href="' . $url . '" target="_blank">' . $url . '</a>
Referer: ' . (isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : '') . ' (WARNING! Can not be trusted!)
Browser: ' . (isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : '') . ' (WARNING! Can not be trusted!)
Method: ' . (isset($_SERVER['REQUEST_METHOD']) ? $_SERVER['HTTP_USER_AGENT'] : '') . '
				</pre>
			</code>
		</div>
		<h3>Buxus details</h3>
		<div class="source">
			<code>
				<pre>
Buxus version: ' . \Buxus::version() . '</pre>
			</code>
		</div>
		<h3>Server</h3>
		<div class="source" onclick="toggle()" id="server_toggle">
			<b>+</b>
			...
		</div>
		<div class="source" id="server" style="display:block">
			<h3>$_REQUEST</h3>
			<code>
				<pre>' . self::prepareArray($request_data) . '</pre>
			</code>
			<h3>$_SERVER</h3>
			<code>
				<pre>' . self::prepareArray($server_data) . '</pre>
			</code>
		</div>
		<div class="version">
			' . @strftime('%Y-%m-%d %H:%M', time()) . ' ' . (isset($_SERVER['SERVER_SOFTWARE']) ? $_SERVER['SERVER_SOFTWARE'] : '') . '
		</div>
	</body>
</html>';

		return $return_html;
	}
}
