<?php

	/**
	 * Static class implementing one-method-call interface to Buxus captcha to be used
	 * within templates/pages to quickly generate and validate captcha control. All
	 * methods first initialize object of BuxusCaptcha class with provided paramethers
	 * (defaults are the same) and then generates control or validates captcha test.
	 *
	 * @static
	 * @package captcha_controls
	 *
	 * <AUTHOR> <<EMAIL>>
	 * @copyright Copyright © 2006, ui42, spol. s r.o., http://www.ui42.sk
	 */
	class BuxusCaptchaControl
	{
		/**
		 * Static one-method-call interface for generation of Buxus captcha control.
		 *
		 * @param string $control_name name of the captcha validation input field will be passed to XTemplate as CONTROL_NAME tag - default is the value of CAPTCHA_DEFAULT_CONTROL_NAME set in captcha configuration.
		 * @param string $xtemplate filename of the XTemplate to be used to generate the control HTML code; has to be located within the xtemplate directory specified by CAPTCHA_XTPL_DIRECTORY set in captcha configuration - default is the value of CAPTCHA_XTPL_DEFAULT_TEMPLATE set in the captcha configuration.
		 * @param int $width width of captcha image in px will be passed to XTemplate as CONTROL_IMAGE_WIDTH tag (including the 'px' string) - default is the value of CAPTCHA_WIDTH set in captcha configuration.
		 * @param int $height height of captcha image in px will be passed to XTemplate as CONTROL_IMAGE_HEIGHT tag (including the 'px' string) - default is the value of CAPTCHA_HEIGHT set in captcha configuration.
		 * @return HTML_string HTMl stanpshot containing captcha image and input field for validation as specified by XTemplate.
		 *
		 * @access public
		 * @static
		 */
		function show($control_name = CAPTCHA_DEFAULT_CONTROL_NAME, $xtemplate = CAPTCHA_XTPL_DEFAULT_TEMPLATE, $width = CAPTCHA_WIDTH, $height = CAPTCHA_HEIGHT)
		{
			$buxus_captcha = BuxusCaptchaControl::createBuxusCaptcha($control_name, $xtemplate, $width, $height);
			return $buxus_captcha->getControl();
		}

		/**
		 * Wrapper for the {@link show()} method translates empty paramethers for their default
		 * values when calling from generate_page.php when translating special tags <buxus-captcha>
		 *
		 * @param string $control_name name of the captcha validation input field will be passed to XTemplate as CONTROL_NAME tag.
		 * @param string $xtemplate filename of the XTemplate to be used to generate the control HTML code; has to be located within the xtemplate directory specified by CAPTCHA_XTPL_DIRECTORY set in captcha configuration.
		 * @param int $width width of captcha image in px will be passed to XTemplate as CONTROL_IMAGE_WIDTH tag (including the 'px' string).
		 * @param int $height height of captcha image in px will be passed to XTemplate as CONTROL_IMAGE_HEIGHT tag (including the 'px' string).
		 * @return HTML_string HTMl stanpshot containing captcha image and input field for validation as specified by XTemplate.
		 *
		 * @access public
		 * @static
		 */
		function showFromPage($control_name, $xtemplate, $width, $height)
		{
			if (empty($control_name))
			{
				$control_name = CAPTCHA_DEFAULT_CONTROL_NAME;
			}

			if (empty($xtemplate))
			{
				$xtemplate = CAPTCHA_XTPL_DEFAULT_TEMPLATE;
			}

			if (empty($width))
			{
				$width = CAPTCHA_WIDTH;
			}

			if (empty($height))
			{
				$height = CAPTCHA_HEIGHT;
			}

			return BuxusCaptchaControl::show($control_name, $xtemplate, $width, $height);
		}

		/**
		 * Static one-method-call interface for Buxus captcha validation.
		 *
		 * @param string $control_name name of the captcha validation input field that contains captcha validation string - default is the value of CAPTCHA_DEFAULT_CONTROL_NAME set in captcha configuration.
		 * @return bool true if captcha test passed, false if captcha test failed.
		 */
		function validate($control_name = CAPTCHA_DEFAULT_CONTROL_NAME)
		{
			$result = false;
			$buxus_captcha_names = BuxusCaptchaControl::getAllCaptchaNames();
			$buxus_captcha_names[] = $control_name;

			if (is_array($buxus_captcha_names) && count($buxus_captcha_names) > 0)
			{
				foreach ($buxus_captcha_names as $buxus_captcha_name)
				{
					$buxus_captcha = new BuxusCaptcha($buxus_captcha_name);
					$result = $result || $buxus_captcha->validate();
				}
			}

			return $result;
		}

		/**
		 * Factory method which initialises BuxusCaptcha object and register it's control name
		 *
		 * @param string $control_name name of the captcha validation input field will be passed to XTemplate as CONTROL_NAME tag.
		 * @param string $xtemplate filename of the XTemplate to be used to generate the control HTML code; has to be located within the xtemplate directory specified by CAPTCHA_XTPL_DIRECTORY set in captcha configuration.
		 * @param int $width width of captcha image in px will be passed to XTemplate as CONTROL_IMAGE_WIDTH tag (including the 'px' string).
		 * @param int $height height of captcha image in px will be passed to XTemplate as CONTROL_IMAGE_HEIGHT tag (including the 'px' string).
		 * @return object captcha object.
		 */
		function createBuxusCaptcha($control_name, $xtemplate, $width, $height)
		{
			$control_name = BuxusCaptchaControl::registerCaptchaControlName($control_name);

			$buxus_captcha = new BuxusCaptcha($control_name, $xtemplate, $width, $height);

			return $buxus_captcha;
		}

		/**
		 * Gets captcha control names stack.
		 *
		 * @return array captcha control names stack.
		 */
		function getAllCaptchaNames()
		{
			return is_array(Session::get('buxus.' . CAPTCHA_SESSION_CONTROL_NAME)) ?Session::get('buxus.' . CAPTCHA_SESSION_CONTROL_NAME) : array();
		}

		/**
		 * Checks desired control name and puts valid control name to the captcha control names stack.
		 * If given control name is already used this method will modify it to avoid conflicts.
		 *
		 * @param string $captcha_control_name name of the desired captcha validation input field.
		 * @return string valid control name.
		 */
		function registerCaptchaControlName($captcha_control_name)
		{
			if (!isset($GLOBALS['WAS_FIRST_CAPTCHA']))
			{
                Session::put('buxus.' . CAPTCHA_SESSION_CONTROL_NAME, array());
                Session::put('buxus.' . CAPTCHA_SESSION_ID, array());
				$GLOBALS['WAS_FIRST_CAPTCHA'] = true;
			}

			while (in_array($captcha_control_name, Session::get('buxus.' . CAPTCHA_SESSION_CONTROL_NAME)))
			{
				$captcha_control_name .= '_' . count(Session::get('buxus.' . CAPTCHA_SESSION_CONTROL_NAME));
			}

            $tmp = Session::get('buxus.' . CAPTCHA_SESSION_CONTROL_NAME);
            $tmp[] = $captcha_control_name;
            Session::put('buxus.' . CAPTCHA_SESSION_CONTROL_NAME, $tmp);

			return $captcha_control_name;
		}
	}

	/**
	 * Buxus CAPTCHA control based on Eliot Eward's PhpCaptcha class. Uses XTemplate to
	 * show the form control.
	 *
	 * @package captcha_controls
	 *
	 * <AUTHOR> Majek <<EMAIL>>
	 * @copyright Copyright © 2006, ui42, spol. s r.o., http://www.ui42.sk
	 */
	class BuxusCaptcha extends PhpCaptcha
	{
		/**
		 * Complete path to the xtemplate to be used for captcha control HTML code generation
		 *
		 * @var string
		 * @access private
		 */
		var $xtemplate;
		/**
		 * Name of the captcha validation input field
		 *
		 * @var string
		 * @access private
		 */
		var $control_name;

		/**
		 * Constructor initializes the captcha object. Note: for validation only $control_name is important.
		 *
		 * @param string $control_name name of the captcha validation input field will be passed to XTemplate as CONTROL_NAME tag - default is the value of CAPTCHA_DEFAULT_CONTROL_NAME set in captcha configuration.
		 * @param string $xtemplate filename of the XTemplate to be used to generate the control HTML code; has to be located within the xtemplate directory specified by CAPTCHA_XTPL_DIRECTORY set in captcha configuration - default is the value of CAPTCHA_XTPL_DEFAULT_TEMPLATE set in the captcha configuration.
		 * @param int $width width of captcha image in px will be passed to XTemplate as CONTROL_IMAGE_WIDTH tag (including the 'px' string) - default is the value of CAPTCHA_WIDTH set in captcha configuration.
		 * @param int $height height of captcha image in px will be passed to XTemplate as CONTROL_IMAGE_HEIGHT tag (including the 'px' string) - default is the value of CAPTCHA_HEIGHT set in captcha configuration.
		 * @return BuxusCaptcha object ready for control generation or validation.
		 * @access public
		 */
        public function __construct($control_name = CAPTCHA_DEFAULT_CONTROL_NAME, $xtemplate = CAPTCHA_XTPL_DEFAULT_TEMPLATE, $width = CAPTCHA_WIDTH, $height = CAPTCHA_HEIGHT)
		{
			$fonts = glob(CAPTCHA_FONT_DIRECTORY . '*.ttf');

			if (!is_array($fonts) || count($fonts) == 0)
			{
				blog ('Captcha: Error! No font selected in [' . __FILE__ . '] at [' . __LINE__ . ']');
				return;
			}

			$this->setXTemplate($xtemplate);
			$this->control_name = $control_name;

			parent::__construct($fonts, $width, $height);
		}

		/**
		 * Validates and sets the path to the template file. If provided filename does not exist,
		 * attempt to set default template will be made, if that fails to, false is set meaning
		 * no template is set. Note that no warning is logged at this point, since we do not need
		 * valid template to be set for validation, the warning is logged when generation attempt
		 * on invalid template is made by calling the {@link getControl()} method.
		 *
		 * @param string $xtemplate filename of the XTemplate to be used to generate the control HTML code; has to be located within the xtemplate directory specified by CAPTCHA_XTPL_DIRECTORY set in captcha configuration - default is the value of CAPTCHA_XTPL_DEFAULT_TEMPLATE set in the captcha configuration.
		 * @access private
		 */
		function setXTemplate($xtemplate)
		{
			if (is_file(CAPTCHA_XTPL_DIRECTORY . $xtemplate))
			{
				$this->xtemplate = CAPTCHA_XTPL_DIRECTORY . $xtemplate;
			}
			elseif (is_file(CAPTCHA_XTPL_DIRECTORY . CAPTCHA_XTPL_DEFAULT_TEMPLATE))
			{
				blog ('Captcha: Warning! Non-existing xtemplate file specified [' . CAPTCHA_XTPL_DIRECTORY . $xtemplate . ']. Using default xtemplate file. In [' . __FILE__ . '] at [' . __LINE__ . ']');
				$this->xtemplate = CAPTCHA_XTPL_DIRECTORY . CAPTCHA_XTPL_DEFAULT_TEMPLATE;
			}
			else
			{
				$this->xtemplate = false;
			}
		}

		/**
		 * Generates the HTML code snapshot based on set template. If no valid template is set,
		 * warning is logged and false is returned.
		 *
		 * @return HTML_string HTML stanpshot containing captcha image and input field for validation as specified by XTemplate.
		 * @access public
		 */
		function getControl()
		{
			if ($this->xtemplate === false)
			{
				blog ('Captcha: Warning! No template selected in [' . __FILE__ . '] at [' . __LINE__ . ']');

				return false;
			}

			$xtpl = new XTemplate(basename($this->xtemplate), dirname($this->xtemplate));

			$captcha_image = CAPTCHA_IMAGE_URL;
			$captcha_image .= '?captcha_width=' . $this->iWidth;
			$captcha_image .= '&amp;captcha_height=' . $this->iHeight;
			$captcha_image .= '&amp;initial_refresh=' . md5(uniqid(rand(), true));
			$xtpl->assign('CONTROL_IMAGE_SRC', $captcha_image);
			$xtpl->assign('CONTROL_IMAGE_WIDTH', $this->iWidth . 'px');
			$xtpl->assign('CONTROL_IMAGE_HEIGHT', $this->iHeight . 'px');

			/**
			 * Display the title text of the image in correct language
			 *
			 * @since Task 1422
			 * <AUTHOR> Majek <<EMAIL>>
			 */
			$image_titles = array(
				'sk' => 'Prepíšte text z obrázku do poľa. Ak nedokážete text rozoznať, kliknite na obrázok.',
				'en' => 'Enter the code you see on this image. If you cannot read the code, click on the image.'
			);
			if (Session::has('buxus.captcha_resubmit_form_language') && isset($image_titles[Session::get('buxus.captcha_resubmit_form_language')]))
			{
				$xtpl->assign('CONTROL_IMAGE_TITLE', $image_titles[Session::get('buxus.captcha_resubmit_form_language')]);
			}
			else
			{
				$xtpl->assign('CONTROL_IMAGE_TITLE', $image_titles['sk']);
			}

			$xtpl->assign('CONTROL_NAME', $this->control_name);
			$xtpl->assign('CONTROL_SIZE', CAPTCHA_NUM_CHARS);

			$xtpl->parse('main');
			return $xtpl->text('main');
		}

		/**
		 * Returns result of the captcha test based on validation string provided by user within
		 * the $this->control_name input field.
		 *
		 * @return bool true if captcha test passed, false if captcha test failed.
		 * @access public
		 */
		function validate($sUserCode, $bCaseInsensitive = true)
		{
			if (isset($_REQUEST[$this->control_name]))
			{
				return parent::Validate($_REQUEST[$this->control_name], CAPTCHA_CASE_INSENSITIVE);
			}
			else
			{
				return false;
			}
		}
	}

	/**
	 * Buxus CAPTCHA image generator based on Eliot Eward's PhpCaptcha class. Used to
	 * generate CAPTCHA image itself.
	 *
	 * @package captcha_controls
	 *
	 * <AUTHOR> Majek <<EMAIL>>
	 * @copyright Copyright © 2006, ui42, spol. s r.o., http://www.ui42.sk
	 */
	class BuxusCaptchaImage extends PhpCaptcha
	{
		/**
		 * Constructor loads all the available fonts located within the CAPTCHA_FONT_DIRECTORY. All
		 * you need to do to generate a captcha image is call the {@link create()} method.
		 *
		 * @param int $width width of captcha image in px will be passed to XTemplate as CONTROL_IMAGE_WIDTH tag (including the 'px' string) - default is the value of CAPTCHA_WIDTH set in captcha configuration.
		 * @param int $height height of captcha image in px will be passed to XTemplate as CONTROL_IMAGE_HEIGHT tag (including the 'px' string) - default is the value of CAPTCHA_HEIGHT set in captcha configuration.
		 * @return BuxusCaptchaImage
		 * @access public
		 */
        public function __construct($width = CAPTCHA_WIDTH, $height = CAPTCHA_HEIGHT)
		{
			$fonts = glob(CAPTCHA_FONT_DIRECTORY . '*.ttf');

			if (!is_array($fonts) || count($fonts) == 0)
			{
				blog ('Captcha: Error! No font selected in [' . __FILE__ . '] at [' . __LINE__ . ']');
				return;
			}

			parent::__construct($fonts, $width, $height);
		}
	}

   /**
   	* PhpCaptcha - A visual and audio CAPTCHA generation library
	*
    * Software License Agreement (BSD License)
	*
	*
    * Redistribution and use in source and binary forms, with or without
    * modification, are permitted provided that the following conditions are met:
	*
    *    * Redistributions of source code must retain the above copyright
    *      notice, this list of conditions and the following disclaimer.
    *    * Redistributions in binary form must reproduce the above copyright
    *      notice, this list of conditions and the following disclaimer in the
    *      documentation and/or other materials provided with the distribution.
    *    * Neither the name of Edward Eliot nor the names of its contributors
    *      may be used to endorse or promote products derived from this software
    *      without specific prior written permission of Edward Eliot.
	*
    * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDER AND CONTRIBUTORS "AS IS" AND ANY
    * EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
    * WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
    * DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR ANY
    * DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
    * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
    * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
    * ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
    * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
    * SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
	*
	* <AUTHOR> Eliot
    * @since Last Updated:  18th April 2006
    * @copyright Copyright (C) 2005-2006, Edward Eliot. All rights reserved.
    * @see http://www.ejeliot.com/pages/2
    *
    * @since 2006-11-02
    * <AUTHOR> Majek <<EMAIL>>
    * @copyright Copyright © 2006, ui42, spol. s r.o., http://www.ui42.sk
    * @package captcha_controls
    * - md5() used to hash the captcha code before string in session.
    * - background lines are drawed shorter to improve defense.
    * - AudioCaptcha class removed since we do not intent to use it.
    */
   class PhpCaptcha {
      var $oImage;
      var $aFonts;
      var $iWidth;
      var $iHeight;
      var $iNumChars;
      var $iNumLines;
      var $iSpacing;
      var $bCharShadow;
      var $sOwnerText;
      var $aCharSet;
      var $bCaseInsensitive;
      var $vBackgroundImages;
      var $iMinFontSize;
      var $iMaxFontSize;
      var $bUseColour;
      var $sFileType;
      var $sCode = '';

       public function __construct(
         $aFonts, // array of TrueType fonts to use - specify full path
         $iWidth = CAPTCHA_WIDTH, // width of image
         $iHeight = CAPTCHA_HEIGHT // height of image
      ) {
         // get parameters
         $this->aFonts = $aFonts;
         $this->SetNumChars(CAPTCHA_NUM_CHARS);
         $this->SetNumLines(CAPTCHA_NUM_LINES);
         $this->DisplayShadow(CAPTCHA_CHAR_SHADOW);
         $this->SetOwnerText(CAPTCHA_OWNER_TEXT);
         $this->SetCharSet(CAPTCHA_CHAR_SET);
         $this->CaseInsensitive(CAPTCHA_CASE_INSENSITIVE);
         $this->SetBackgroundImages(CAPTCHA_BACKGROUND_IMAGES);
         $this->SetMinFontSize(CAPTCHA_MIN_FONT_SIZE);
         $this->SetMaxFontSize(CAPTCHA_MAX_FONT_SIZE);
         $this->UseColour(CAPTCHA_USE_COLOUR);
         $this->SetFileType(CAPTCHA_FILE_TYPE);
         $this->SetWidth($iWidth);
         $this->SetHeight($iHeight);
      }

      function CalculateSpacing() {
         $this->iSpacing = (int)($this->iWidth / $this->iNumChars);
      }

      function SetWidth($iWidth) {
         $this->iWidth = $iWidth;
         if ($this->iWidth > 500) $this->iWidth = 500; // to prevent perfomance impact
         $this->CalculateSpacing();
      }

      function SetHeight($iHeight) {
         $this->iHeight = $iHeight;
         if ($this->iHeight > 200) $this->iHeight = 200; // to prevent performance impact
      }

      function SetNumChars($iNumChars) {
         $this->iNumChars = $iNumChars;
         $this->CalculateSpacing();
      }

      function SetNumLines($iNumLines) {
         $this->iNumLines = $iNumLines;
      }

      function DisplayShadow($bCharShadow) {
         $this->bCharShadow = $bCharShadow;
      }

      function SetOwnerText($sOwnerText) {
         $this->sOwnerText = $sOwnerText;
      }

      function SetCharSet($vCharSet) {
         // check for input type
         if (is_array($vCharSet)) {
            $this->aCharSet = $vCharSet;
         } else {
            if ($vCharSet != '') {
               // mb_split items on commas
               $aCharSet = explode(',', $vCharSet);

               // initialise array
               $this->aCharSet = array();

               // loop through items
               foreach ($aCharSet as $sCurrentItem) {
                  // a range should have 3 characters, otherwise is normal character
                  if (mb_strlen($sCurrentItem) == 3) {
                     // mb_split on range character
                     $aRange = explode('-', $sCurrentItem);

                     // check for valid range
                     if (count($aRange) == 2 && $aRange[0] < $aRange[1]) {
                        // create array of characters from range
                        $aRange = range($aRange[0], $aRange[1]);

                        // add to charset array
                        $this->aCharSet = array_merge($this->aCharSet, $aRange);
                     }
                  } else {
                     $this->aCharSet[] = $sCurrentItem;
                  }
               }
            }
         }
      }

      function CaseInsensitive($bCaseInsensitive) {
         $this->bCaseInsensitive = $bCaseInsensitive;
      }

      function SetBackgroundImages($vBackgroundImages) {
         $this->vBackgroundImages = $vBackgroundImages;
      }

      function SetMinFontSize($iMinFontSize) {
         $this->iMinFontSize = $iMinFontSize;
      }

      function SetMaxFontSize($iMaxFontSize) {
         $this->iMaxFontSize = $iMaxFontSize;
      }

      function UseColour($bUseColour) {
         $this->bUseColour = $bUseColour;
      }

      function SetFileType($sFileType) {
         // check for valid file type
         if (in_array($sFileType, array('gif', 'png', 'jpeg'))) {
            $this->sFileType = $sFileType;
         } else {
            $this->sFileType = 'jpeg';
         }
      }

      function DrawLines() {
         for ($i = 0; $i < $this->iNumLines; $i++) {
            // allocate colour
            if ($this->bUseColour) {
               $iLineColour = imagecolorallocate($this->oImage, rand(100, 250), rand(100, 250), rand(100, 250));
            } else {
               $iRandColour = rand(180, 250);
               $iLineColour = imagecolorallocate($this->oImage, $iRandColour, $iRandColour, $iRandColour);
            }

            // draw line
            $top_x = rand(0, $this->iWidth);
            $top_y = rand(0, $this->iHeight);

            $low_x = $top_x + rand(-15, 15);
            $low_y = $top_y + rand(-10, 10);
            imageline($this->oImage, $top_x, $top_y, $low_x, $low_y, $iLineColour);
         }
      }

      function DrawOwnerText() {
         // allocate owner text colour
         $iBlack = imagecolorallocate($this->oImage, 0, 0, 0);
         // get height of selected font
         $iOwnerTextHeight = imagefontheight(2);
         // calculate overall height
         $iLineHeight = $this->iHeight - $iOwnerTextHeight - 4;

         // draw line above text to separate from CAPTCHA
         imageline($this->oImage, 0, $iLineHeight, $this->iWidth, $iLineHeight, $iBlack);

         // write owner text
         imagestring($this->oImage, 2, 3, $this->iHeight - $iOwnerTextHeight - 3, $this->sOwnerText, $iBlack);

         // reduce available height for drawing CAPTCHA
         $this->iHeight = $this->iHeight - $iOwnerTextHeight - 5;
      }

      function GenerateCode() {
         // reset code
         $this->sCode = '';

         // loop through and generate the code letter by letter
         for ($i = 0; $i < $this->iNumChars; $i++) {
            if (count($this->aCharSet) > 0) {
               // select random character and add to code string
               $this->sCode .= $this->aCharSet[array_rand($this->aCharSet)];
            } else {
               // select random character and add to code string
               $this->sCode .= chr(rand(65, 90));
            }
         }

         $code = $this->sCode;

         // save code in session variable
         if ($this->bCaseInsensitive)
         {
         	$code = mb_strtoupper($code);
         }

         $code = md5($code);
         Session::put('buxus.' . CAPTCHA_SESSION_ID . '.' . $code, md5($code));
      }

      function DrawCharacters() {
         // loop through and write out selected number of characters
         $str_length = mb_strlen($this->sCode);
         for ($i = 0; $i < $str_length; $i++) {
         	$letter = mb_substr($this->sCode, $i, 1);

            // select random font
            $sCurrentFont = $this->aFonts[array_rand($this->aFonts)];

            // select random colour
            if ($this->bUseColour) {
               $iTextColour = imagecolorallocate($this->oImage, rand(0, 100), rand(0, 100), rand(0, 100));

               if ($this->bCharShadow) {
                  // shadow colour
                  $iShadowColour = imagecolorallocate($this->oImage, rand(0, 100), rand(0, 100), rand(0, 100));
               }
            } else {
               $iRandColour = rand(0, 100);
               $iTextColour = imagecolorallocate($this->oImage, $iRandColour, $iRandColour, $iRandColour);

               if ($this->bCharShadow) {
                  // shadow colour
                  $iRandColour = rand(240, 250);
                  $iShadowColour = imagecolorallocate($this->oImage, $iRandColour, $iRandColour, $iRandColour);
               }
            }

            // select random font size
            $iFontSize = rand($this->iMinFontSize, $this->iMaxFontSize);

            // select random angle
            $iAngle = rand(-30, 30);

            // get dimensions of character in selected font and text size
            $aCharDetails = imageftbbox($iFontSize, $iAngle, $sCurrentFont, $letter, array());

            // calculate character starting coordinates
            $iX = $this->iSpacing / 4 + $i * $this->iSpacing;
            $iCharHeight = $aCharDetails[2] - $aCharDetails[5];
            $iY = $this->iHeight / 2 + $iCharHeight / 4;

            // write text to image
            imagefttext($this->oImage, $iFontSize, $iAngle, $iX, $iY, $iTextColour, $sCurrentFont, $letter, array());

            if ($this->bCharShadow) {
               $iOffsetAngle = rand(-30, 30);

               $iRandOffsetX = rand(-5, 5);
               $iRandOffsetY = rand(-5, 5);

               imagefttext($this->oImage, $iFontSize, $iOffsetAngle, $iX + $iRandOffsetX, $iY + $iRandOffsetY, $iShadowColour, $sCurrentFont, $letter, array());
            }
         }
      }

      function WriteFile($sFilename) {
         if ($sFilename == '') {
            // tell browser that data is jpeg
            header("Content-type: image/$this->sFileType");
            // force expiration
			header("Cache-Control: no-cache, must-revalidate"); // HTTP/1.1
			header("Expires: Mon, 26 Jul 1997 05:00:00 GMT"); // Date in the past
         }

         switch ($this->sFileType) {
            case 'gif':
               $sFilename != '' ? imagegif($this->oImage, $sFilename) : imagegif($this->oImage);
               break;
            case 'png':
               $sFilename != '' ? imagepng($this->oImage, $sFilename) : imagepng($this->oImage);
               break;
            default:
               $sFilename != '' ? imagejpeg($this->oImage, $sFilename) : imagejpeg($this->oImage);
         }
      }

      function Create($sFilename = '') {
         // check for required gd functions
         if (!function_exists('imagecreate') || !function_exists("image$this->sFileType") || ($this->vBackgroundImages != '' && !function_exists('imagecreatetruecolor'))) {
            return false;
         }

         // get background image if specified and copy to CAPTCHA
         if (is_array($this->vBackgroundImages) || $this->vBackgroundImages != '') {
            // create new image
            $this->oImage = imagecreatetruecolor($this->iWidth, $this->iHeight);

            // create background image
            if (is_array($this->vBackgroundImages)) {
               $iRandImage = array_rand($this->vBackgroundImages);
               $oBackgroundImage = imagecreatefromjpeg($this->vBackgroundImages[$iRandImage]);
            } else {
               $oBackgroundImage = imagecreatefromjpeg($this->vBackgroundImages);
            }

            // copy background image
            imagecopy($this->oImage, $oBackgroundImage, 0, 0, 0, 0, $this->iWidth, $this->iHeight);

            // free memory used to create background image
            imagedestroy($oBackgroundImage);
         } else {
            // create new image
            $this->oImage = imagecreate($this->iWidth, $this->iHeight);
         }

         // allocate white background colour
         imagecolorallocate($this->oImage, 255, 255, 255);

         // check for owner text
         if ($this->sOwnerText != '') {
            $this->DrawOwnerText();
         }

         // check for background image before drawing lines
         if (!is_array($this->vBackgroundImages) && $this->vBackgroundImages == '') {
            $this->DrawLines();
         }

         $this->GenerateCode();
         $this->DrawCharacters();

         // write out image to file or browser
         $this->WriteFile($sFilename);

         // free memory used in creating image
         imagedestroy($this->oImage);

         return true;
      }

      // call this method statically
      function Validate($sUserCode, $bCaseInsensitive = true) {
         if ($bCaseInsensitive) {
            $sUserCode = mb_strtoupper($sUserCode);
         }

         $sUserCode = md5($sUserCode);

         if (is_array(Session::get('buxus.' . CAPTCHA_SESSION_ID)) && Session::has('buxus.' . CAPTCHA_SESSION_ID . '.' . $sUserCode)) {
            // clear to prevent re-use
             Session::forget('buxus.' . CAPTCHA_SESSION_ID . '.' . $sUserCode);

            return true;
         }

         return false;
      }
   }

