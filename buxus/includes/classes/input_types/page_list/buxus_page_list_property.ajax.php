<?php

define('C_NO_REDIRECT_TO_LOGIN', true);
require_once 'includes/functions.php';
require_once 'includes/validate.php';
require_once 'includes/uif_functions.php';
require_once 'includes/validate.php';

/**
 * Finish ajax script with error
 *
 * @param string $message error message
 */
function ajaxPageListError($message)
{
    return Response::json((array('message' => $message)));
}

/**
 * Finish ajax script with error - please refresh
 *
 */
function ajaxPageListErrorRefresh()
{
    return Response::json(array('message' => __bx('legacy-base::buxus_page_list_property.PL_error_please_refresh')));
}


////Buxus::log(print_r($_REQUEST,true));////

if (empty(\Buxus::userId())) {
    //user is not logged in
    ajaxPageListErrorRefresh();
}

//input filters and validation

$request = new Zend_Controller_Request_Http();

//DBOF page object id
$OID = (int)$request->getParam('oid');

//page-property id
$PID = (int)$request->getParam('pid');

//link id in page list
$link_key = $request->getParam('link_key');

// url or text
$column = strval($request->getParam('column'));

//page id to add or edit
$page_id = $request->getParam('page_id');

//move up, down, delete, save, add ...
$action = strval($request->getParam('action'));

$cell_value = strval($request->getParam('cell_value'));

//page types filter
$selected_page_types = strval($request->getParam('page_types'));
$change_filter = $request->getParam('change_filter');


if (($action == 'save') && ($link_key == 'new')) {
    //create new link

    $action = 'create';
} elseif ($action == 'suggestion_save') {

    //suggestion click

    if ($link_key == 'new') {
        $action = 'suggestion_add';
    } else {
        $action = 'suggestion_edit';
        $link_key = intval($link_key);
    }
} else {

    //edit, move, delete link

    $link_key = intval($link_key);
}

$id_url_value = null;
$text_value = null;
switch ($column) {
    case BuxusPageListProperty::column_type_id_url :
        $id_url_value = $cell_value;
        break;
    case BuxusPageListProperty::column_type_text :
        $text_value = $cell_value;
        break;
}


if ($PID == 0) {
    Buxus::log('PID == 0');
    //ajaxPageListError('undefined PID');
    ajaxPageListErrorRefresh();
}

if ($OID == 0) {
    Buxus::log('OID == 0');
    //ajaxPageListError('undefined OID');
    ajaxPageListErrorRefresh();
}


DBOF::setUpObjectFactory();

$page = DBOF::getObject($OID);
/* @var $page Page */

if (empty($page)) {
    Buxus::log('page object getter has failed');
    //ajaxPageListError('page object getter has failed');
    ajaxPageListErrorRefresh();
}

$pageListProperty = $page->propertyList->PIDlist[$PID]->input_object;
//nastavenie hodnoty z draftu, ale iba ak to nie je ziadna akcia, teda je to prve nahranie editovania stranky
if ($action == '') {
    //$pageListProperty->readData();
}

if (empty($pageListProperty) || (get_class($pageListProperty) != 'BuxusPageListProperty')) {
    Buxus::log('property object getter has failed');
    //ajaxPageListError('property object getter has failed');
    ajaxPageListErrorRefresh();
}


$pageListEdit = new BuxusPageListPropertyEdit($page, $pageListProperty, $PID);


//$GLOBALS['C_log_queries'] = true;////

$html = '';
$filter_html = '';
if ($action == 'suggest') {
    //page list suggestions
    session_write_close();

    if (!empty($change_filter)) {
        $pageListEdit->setUserSelectedPageTypeIdsByStr($selected_page_types);
        $pageListEdit->generateSuggestionFilterHTML();
    }

    $isSuggestion = false;
    if (strlen($id_url_value)) {
        $isSuggestion = $pageListEdit->setSuggestionById($id_url_value);
    } else {
        if (strlen($text_value)) {
            $isSuggestion = $pageListEdit->setSuggestionByText($text_value);
        }
    }

    if ($isSuggestion) {
        $suggestion_data_ac = $pageListEdit->suggestion_data_ac;
        $filter_html = $pageListEdit->suggestion_filter_html;
        $data = [
            'result' => 'ok',
            'pid' => $PID,
            'isSuggestion' => $isSuggestion,
            'suggestion_data_ac' => $suggestion_data_ac,
            'filter_html' => $filter_html,
        ];
    } else {
        if (empty($change_filter)) {
            $data = [
                'result' => 'ok',
                'pid' => $PID,
                'isSuggestion' => $isSuggestion,
            ];
        } else {
            $data = [
                'result' => 'ok',
                'pid' => $PID,
                'isSuggestion' => $isSuggestion,
                'filter_html' => $pageListEdit->suggestion_filter_html,
            ];
        }

    }

} else {
    //page list edit

    switch ($action) {
        case 'save':
            $pageListEdit->editLink($link_key, $id_url_value, $text_value);
            break;
        case 'save-column':
            $pageListEdit->editCustomColumn($link_key, $column, $cell_value);
            break;
        case 'bind_link':
            $pageListEdit->editLink($link_key, null, '');
            break;
        case 'create':
            $pageListEdit->createLink($id_url_value, $text_value);
            break;
        case 'move_up':
            $pageListEdit->moveUpLink($link_key);
            break;
        case 'move_down':
            $pageListEdit->moveDownLink($link_key);
            break;
        case 'delete':
            $pageListEdit->deleteLink($link_key);
            break;
        case 'suggestion_add':
            $pageListEdit->createLinkByPageID($page_id);
            break;
        case 'suggestion_edit':
            $pageListEdit->editLinkByPageID($page_id, $link_key);
            break;
        case 'delete_all':
            $pageListEdit->deleteAll();
            break;
        case 'show_filter':
            $pageListEdit->generateSuggestionFilterHTML();
            break;
        case 'toggle_inherit':
            $pageListEdit->toggleInherited();
            break;
        default:
            break;
    }

    $html = $pageListEdit->show();

    DBOF::saveObjectFactory();

    switch ($action) {
        case 'show_filter':
            $data = [
                'result' => 'ok',
                'pid' => $PID,
                'filter_html' => $pageListEdit->suggestion_filter_html,
            ];
            break;
        default:
            $data = [
                'result' => 'ok',
                'pid' => $PID,
                'html' => $html,
                'count' => $pageListEdit->getPageListCount(),
                'last_key' => $pageListEdit->getLastLinkKey(),
            ];
            break;
    }
}

return Response::json($data);
