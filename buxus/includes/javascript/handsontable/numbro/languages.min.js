!function(t){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=t();else if("function"==typeof define&&define.amd)define([],t);else{var e;((e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).numbro||(e.numbro={})).allLanguages=t()}}(function(){return function t(e,n,o){function r(a,u){if(!n[a]){if(!e[a]){var f="function"==typeof require&&require;if(!u&&f)return f(a,!0);if(i)return i(a,!0);var s=new Error("Cannot find module '"+a+"'");throw s.code="MODULE_NOT_FOUND",s}var l=n[a]={exports:{}};e[a][0].call(l.exports,function(t){var n=e[a][1][t];return r(n||t)},l,l.exports,t,e,n,o)}return n[a].exports}for(var i="function"==typeof require&&require,a=0;a<o.length;a++)r(o[a]);return r}({1:[function(t,e,n){(function(o){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};!function(t){if("object"==(void 0===n?"undefined":r(n))&&void 0!==e)e.exports=t();else{var i;((i="undefined"!=typeof window?window:void 0!==o?o:"undefined"!=typeof self?self:this).numbro||(i.numbro={})).bg=t()}}(function(){return function e(n,o,r){function i(u,f){if(!o[u]){if(!n[u]){var s="function"==typeof t&&t;if(!f&&s)return s(u,!0);if(a)return a(u,!0);var l=new Error("Cannot find module '"+u+"'");throw l.code="MODULE_NOT_FOUND",l}var d=o[u]={exports:{}};n[u][0].call(d.exports,function(t){return i(n[u][1][t]||t)},d,d.exports,e,n,o,r)}return o[u].exports}for(var a="function"==typeof t&&t,u=0;u<r.length;u++)i(r[u]);return i}({1:[function(t,e,n){e.exports={languageTag:"bg",delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"И",million:"А",billion:"M",trillion:"T"},ordinal:function(){return"."},currency:{symbol:"лв.",code:"BGN"}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],2:[function(t,e,n){(function(o){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};!function(t){if("object"==(void 0===n?"undefined":r(n))&&void 0!==e)e.exports=t();else{var i;((i="undefined"!=typeof window?window:void 0!==o?o:"undefined"!=typeof self?self:this).numbro||(i.numbro={})).csCZ=t()}}(function(){return function e(n,o,r){function i(u,f){if(!o[u]){if(!n[u]){var s="function"==typeof t&&t;if(!f&&s)return s(u,!0);if(a)return a(u,!0);var l=new Error("Cannot find module '"+u+"'");throw l.code="MODULE_NOT_FOUND",l}var d=o[u]={exports:{}};n[u][0].call(d.exports,function(t){return i(n[u][1][t]||t)},d,d.exports,e,n,o,r)}return o[u].exports}for(var a="function"==typeof t&&t,u=0;u<r.length;u++)i(r[u]);return i}({1:[function(t,e,n){e.exports={languageTag:"cs-CZ",delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"tis.",million:"mil.",billion:"mld.",trillion:"bil."},ordinal:function(){return"."},spaceSeparated:!0,currency:{symbol:"Kč",position:"postfix",code:"CZK"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{output:"currency",mantissa:2,spaceSeparated:!0,thousandSeparated:!0},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",spaceSeparated:!0,thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],3:[function(t,e,n){(function(o){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};!function(t){if("object"==(void 0===n?"undefined":r(n))&&void 0!==e)e.exports=t();else{var i;((i="undefined"!=typeof window?window:void 0!==o?o:"undefined"!=typeof self?self:this).numbro||(i.numbro={})).daDK=t()}}(function(){return function e(n,o,r){function i(u,f){if(!o[u]){if(!n[u]){var s="function"==typeof t&&t;if(!f&&s)return s(u,!0);if(a)return a(u,!0);var l=new Error("Cannot find module '"+u+"'");throw l.code="MODULE_NOT_FOUND",l}var d=o[u]={exports:{}};n[u][0].call(d.exports,function(t){return i(n[u][1][t]||t)},d,d.exports,e,n,o,r)}return o[u].exports}for(var a="function"==typeof t&&t,u=0;u<r.length;u++)i(r[u]);return i}({1:[function(t,e,n){e.exports={languageTag:"da-DK",delimiters:{thousands:".",decimal:","},abbreviations:{thousand:"t",million:"mio",billion:"mia",trillion:"b"},ordinal:function(){return"."},currency:{symbol:"kr",position:"postfix",code:"DKK"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{output:"currency",mantissa:2,spaceSeparated:!0,thousandSeparated:!0},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",spaceSeparated:!0,thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],4:[function(t,e,n){(function(o){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};!function(t){if("object"==(void 0===n?"undefined":r(n))&&void 0!==e)e.exports=t();else{var i;((i="undefined"!=typeof window?window:void 0!==o?o:"undefined"!=typeof self?self:this).numbro||(i.numbro={})).deAT=t()}}(function(){return function e(n,o,r){function i(u,f){if(!o[u]){if(!n[u]){var s="function"==typeof t&&t;if(!f&&s)return s(u,!0);if(a)return a(u,!0);var l=new Error("Cannot find module '"+u+"'");throw l.code="MODULE_NOT_FOUND",l}var d=o[u]={exports:{}};n[u][0].call(d.exports,function(t){return i(n[u][1][t]||t)},d,d.exports,e,n,o,r)}return o[u].exports}for(var a="function"==typeof t&&t,u=0;u<r.length;u++)i(r[u]);return i}({1:[function(t,e,n){e.exports={languageTag:"de-AT",delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"k",million:"m",billion:"b",trillion:"t"},ordinal:function(){return"."},currency:{symbol:"€",code:"EUR"}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],5:[function(t,e,n){(function(o){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};!function(t){if("object"==(void 0===n?"undefined":r(n))&&void 0!==e)e.exports=t();else{var i;((i="undefined"!=typeof window?window:void 0!==o?o:"undefined"!=typeof self?self:this).numbro||(i.numbro={})).deCH=t()}}(function(){return function e(n,o,r){function i(u,f){if(!o[u]){if(!n[u]){var s="function"==typeof t&&t;if(!f&&s)return s(u,!0);if(a)return a(u,!0);var l=new Error("Cannot find module '"+u+"'");throw l.code="MODULE_NOT_FOUND",l}var d=o[u]={exports:{}};n[u][0].call(d.exports,function(t){return i(n[u][1][t]||t)},d,d.exports,e,n,o,r)}return o[u].exports}for(var a="function"==typeof t&&t,u=0;u<r.length;u++)i(r[u]);return i}({1:[function(t,e,n){e.exports={languageTag:"de-CH",delimiters:{thousands:"'",decimal:"."},abbreviations:{thousand:"k",million:"m",billion:"b",trillion:"t"},ordinal:function(){return"."},currency:{symbol:"CHF",position:"postfix",code:"CHF"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{output:"currency",mantissa:2,spaceSeparated:!0,thousandSeparated:!0},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",spaceSeparated:!0,thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],6:[function(t,e,n){(function(o){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};!function(t){if("object"==(void 0===n?"undefined":r(n))&&void 0!==e)e.exports=t();else{var i;((i="undefined"!=typeof window?window:void 0!==o?o:"undefined"!=typeof self?self:this).numbro||(i.numbro={})).deDE=t()}}(function(){return function e(n,o,r){function i(u,f){if(!o[u]){if(!n[u]){var s="function"==typeof t&&t;if(!f&&s)return s(u,!0);if(a)return a(u,!0);var l=new Error("Cannot find module '"+u+"'");throw l.code="MODULE_NOT_FOUND",l}var d=o[u]={exports:{}};n[u][0].call(d.exports,function(t){return i(n[u][1][t]||t)},d,d.exports,e,n,o,r)}return o[u].exports}for(var a="function"==typeof t&&t,u=0;u<r.length;u++)i(r[u]);return i}({1:[function(t,e,n){e.exports={languageTag:"de-DE",delimiters:{thousands:".",decimal:","},abbreviations:{thousand:"k",million:"m",billion:"b",trillion:"t"},ordinal:function(){return"."},spaceSeparated:!0,currency:{symbol:"€",position:"postfix",code:"EUR"},currencyFormat:{totalLength:4,thousandSeparated:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{output:"currency",mantissa:2,spaceSeparated:!0,thousandSeparated:!0},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",spaceSeparated:!0,thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],7:[function(t,e,n){(function(o){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};!function(t){if("object"==(void 0===n?"undefined":r(n))&&void 0!==e)e.exports=t();else{var i;((i="undefined"!=typeof window?window:void 0!==o?o:"undefined"!=typeof self?self:this).numbro||(i.numbro={})).deLI=t()}}(function(){return function e(n,o,r){function i(u,f){if(!o[u]){if(!n[u]){var s="function"==typeof t&&t;if(!f&&s)return s(u,!0);if(a)return a(u,!0);var l=new Error("Cannot find module '"+u+"'");throw l.code="MODULE_NOT_FOUND",l}var d=o[u]={exports:{}};n[u][0].call(d.exports,function(t){return i(n[u][1][t]||t)},d,d.exports,e,n,o,r)}return o[u].exports}for(var a="function"==typeof t&&t,u=0;u<r.length;u++)i(r[u]);return i}({1:[function(t,e,n){e.exports={languageTag:"de-LI",delimiters:{thousands:"'",decimal:"."},abbreviations:{thousand:"k",million:"m",billion:"b",trillion:"t"},ordinal:function(){return"."},currency:{symbol:"CHF",position:"postfix",code:"CHF"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{output:"currency",mantissa:2,spaceSeparated:!0,thousandSeparated:!0},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",spaceSeparated:!0,thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],8:[function(t,e,n){(function(o){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};!function(t){if("object"==(void 0===n?"undefined":r(n))&&void 0!==e)e.exports=t();else{var i;((i="undefined"!=typeof window?window:void 0!==o?o:"undefined"!=typeof self?self:this).numbro||(i.numbro={})).el=t()}}(function(){return function e(n,o,r){function i(u,f){if(!o[u]){if(!n[u]){var s="function"==typeof t&&t;if(!f&&s)return s(u,!0);if(a)return a(u,!0);var l=new Error("Cannot find module '"+u+"'");throw l.code="MODULE_NOT_FOUND",l}var d=o[u]={exports:{}};n[u][0].call(d.exports,function(t){return i(n[u][1][t]||t)},d,d.exports,e,n,o,r)}return o[u].exports}for(var a="function"==typeof t&&t,u=0;u<r.length;u++)i(r[u]);return i}({1:[function(t,e,n){e.exports={languageTag:"el",delimiters:{thousands:".",decimal:","},abbreviations:{thousand:"χ",million:"ε",billion:"δ",trillion:"τ"},ordinal:function(){return"."},currency:{symbol:"€",code:"EUR"}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],9:[function(t,e,n){(function(o){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};!function(t){if("object"==(void 0===n?"undefined":r(n))&&void 0!==e)e.exports=t();else{var i;((i="undefined"!=typeof window?window:void 0!==o?o:"undefined"!=typeof self?self:this).numbro||(i.numbro={})).enAU=t()}}(function(){return function e(n,o,r){function i(u,f){if(!o[u]){if(!n[u]){var s="function"==typeof t&&t;if(!f&&s)return s(u,!0);if(a)return a(u,!0);var l=new Error("Cannot find module '"+u+"'");throw l.code="MODULE_NOT_FOUND",l}var d=o[u]={exports:{}};n[u][0].call(d.exports,function(t){return i(n[u][1][t]||t)},d,d.exports,e,n,o,r)}return o[u].exports}for(var a="function"==typeof t&&t,u=0;u<r.length;u++)i(r[u]);return i}({1:[function(t,e,n){e.exports={languageTag:"en-AU",delimiters:{thousands:",",decimal:"."},abbreviations:{thousand:"k",million:"m",billion:"b",trillion:"t"},ordinal:function(t){var e=t%10;return 1==~~(t%100/10)?"th":1===e?"st":2===e?"nd":3===e?"rd":"th"},currency:{symbol:"$",position:"prefix",code:"AUD"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{thousandSeparated:!0,mantissa:2},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],10:[function(t,e,n){(function(o){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};!function(t){if("object"==(void 0===n?"undefined":r(n))&&void 0!==e)e.exports=t();else{var i;((i="undefined"!=typeof window?window:void 0!==o?o:"undefined"!=typeof self?self:this).numbro||(i.numbro={})).enGB=t()}}(function(){return function e(n,o,r){function i(u,f){if(!o[u]){if(!n[u]){var s="function"==typeof t&&t;if(!f&&s)return s(u,!0);if(a)return a(u,!0);var l=new Error("Cannot find module '"+u+"'");throw l.code="MODULE_NOT_FOUND",l}var d=o[u]={exports:{}};n[u][0].call(d.exports,function(t){return i(n[u][1][t]||t)},d,d.exports,e,n,o,r)}return o[u].exports}for(var a="function"==typeof t&&t,u=0;u<r.length;u++)i(r[u]);return i}({1:[function(t,e,n){e.exports={languageTag:"en-GB",delimiters:{thousands:",",decimal:"."},abbreviations:{thousand:"k",million:"m",billion:"b",trillion:"t"},ordinal:function(t){var e=t%10;return 1==~~(t%100/10)?"th":1===e?"st":2===e?"nd":3===e?"rd":"th"},currency:{symbol:"£",position:"prefix",code:"GBP"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{output:"currency",thousandSeparated:!0,spaceSeparated:!0,mantissa:2},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",thousandSeparated:!0,spaceSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],11:[function(t,e,n){(function(o){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};!function(t){if("object"==(void 0===n?"undefined":r(n))&&void 0!==e)e.exports=t();else{var i;((i="undefined"!=typeof window?window:void 0!==o?o:"undefined"!=typeof self?self:this).numbro||(i.numbro={})).enIE=t()}}(function(){return function e(n,o,r){function i(u,f){if(!o[u]){if(!n[u]){var s="function"==typeof t&&t;if(!f&&s)return s(u,!0);if(a)return a(u,!0);var l=new Error("Cannot find module '"+u+"'");throw l.code="MODULE_NOT_FOUND",l}var d=o[u]={exports:{}};n[u][0].call(d.exports,function(t){return i(n[u][1][t]||t)},d,d.exports,e,n,o,r)}return o[u].exports}for(var a="function"==typeof t&&t,u=0;u<r.length;u++)i(r[u]);return i}({1:[function(t,e,n){e.exports={languageTag:"en-IE",delimiters:{thousands:",",decimal:"."},abbreviations:{thousand:"k",million:"m",billion:"b",trillion:"t"},ordinal:function(t){var e=t%10;return 1==~~(t%100/10)?"th":1===e?"st":2===e?"nd":3===e?"rd":"th"},currency:{symbol:"€",code:"EUR"}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],12:[function(t,e,n){(function(o){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};!function(t){if("object"==(void 0===n?"undefined":r(n))&&void 0!==e)e.exports=t();else{var i;((i="undefined"!=typeof window?window:void 0!==o?o:"undefined"!=typeof self?self:this).numbro||(i.numbro={})).enNZ=t()}}(function(){return function e(n,o,r){function i(u,f){if(!o[u]){if(!n[u]){var s="function"==typeof t&&t;if(!f&&s)return s(u,!0);if(a)return a(u,!0);var l=new Error("Cannot find module '"+u+"'");throw l.code="MODULE_NOT_FOUND",l}var d=o[u]={exports:{}};n[u][0].call(d.exports,function(t){return i(n[u][1][t]||t)},d,d.exports,e,n,o,r)}return o[u].exports}for(var a="function"==typeof t&&t,u=0;u<r.length;u++)i(r[u]);return i}({1:[function(t,e,n){e.exports={languageTag:"en-NZ",delimiters:{thousands:",",decimal:"."},abbreviations:{thousand:"k",million:"m",billion:"b",trillion:"t"},ordinal:function(t){var e=t%10;return 1==~~(t%100/10)?"th":1===e?"st":2===e?"nd":3===e?"rd":"th"},currency:{symbol:"$",position:"prefix",code:"NZD"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{thousandSeparated:!0,mantissa:2},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],13:[function(t,e,n){(function(o){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};!function(t){if("object"==(void 0===n?"undefined":r(n))&&void 0!==e)e.exports=t();else{var i;((i="undefined"!=typeof window?window:void 0!==o?o:"undefined"!=typeof self?self:this).numbro||(i.numbro={})).enZA=t()}}(function(){return function e(n,o,r){function i(u,f){if(!o[u]){if(!n[u]){var s="function"==typeof t&&t;if(!f&&s)return s(u,!0);if(a)return a(u,!0);var l=new Error("Cannot find module '"+u+"'");throw l.code="MODULE_NOT_FOUND",l}var d=o[u]={exports:{}};n[u][0].call(d.exports,function(t){return i(n[u][1][t]||t)},d,d.exports,e,n,o,r)}return o[u].exports}for(var a="function"==typeof t&&t,u=0;u<r.length;u++)i(r[u]);return i}({1:[function(t,e,n){e.exports={languageTag:"en-ZA",delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"k",million:"m",billion:"b",trillion:"t"},ordinal:function(t){var e=t%10;return 1==~~(t%100/10)?"th":1===e?"st":2===e?"nd":3===e?"rd":"th"},currency:{symbol:"R",position:"prefix",code:"ZAR"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{thousandSeparated:!0,mantissa:2},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],14:[function(t,e,n){(function(o){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};!function(t){if("object"==(void 0===n?"undefined":r(n))&&void 0!==e)e.exports=t();else{var i;((i="undefined"!=typeof window?window:void 0!==o?o:"undefined"!=typeof self?self:this).numbro||(i.numbro={})).esAR=t()}}(function(){return function e(n,o,r){function i(u,f){if(!o[u]){if(!n[u]){var s="function"==typeof t&&t;if(!f&&s)return s(u,!0);if(a)return a(u,!0);var l=new Error("Cannot find module '"+u+"'");throw l.code="MODULE_NOT_FOUND",l}var d=o[u]={exports:{}};n[u][0].call(d.exports,function(t){return i(n[u][1][t]||t)},d,d.exports,e,n,o,r)}return o[u].exports}for(var a="function"==typeof t&&t,u=0;u<r.length;u++)i(r[u]);return i}({1:[function(t,e,n){e.exports={languageTag:"es-AR",delimiters:{thousands:".",decimal:","},abbreviations:{thousand:"k",million:"mm",billion:"b",trillion:"t"},ordinal:function(t){var e=t%10;return 1===e||3===e?"er":2===e?"do":7===e||0===e?"mo":8===e?"vo":9===e?"no":"to"},currency:{symbol:"$",position:"postfix",code:"ARS"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{output:"currency",mantissa:2,spaceSeparated:!0,thousandSeparated:!0},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",spaceSeparated:!0,thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],15:[function(t,e,n){(function(o){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};!function(t){if("object"==(void 0===n?"undefined":r(n))&&void 0!==e)e.exports=t();else{var i;((i="undefined"!=typeof window?window:void 0!==o?o:"undefined"!=typeof self?self:this).numbro||(i.numbro={})).esCL=t()}}(function(){return function e(n,o,r){function i(u,f){if(!o[u]){if(!n[u]){var s="function"==typeof t&&t;if(!f&&s)return s(u,!0);if(a)return a(u,!0);var l=new Error("Cannot find module '"+u+"'");throw l.code="MODULE_NOT_FOUND",l}var d=o[u]={exports:{}};n[u][0].call(d.exports,function(t){return i(n[u][1][t]||t)},d,d.exports,e,n,o,r)}return o[u].exports}for(var a="function"==typeof t&&t,u=0;u<r.length;u++)i(r[u]);return i}({1:[function(t,e,n){e.exports={languageTag:"es-CL",delimiters:{thousands:".",decimal:","},abbreviations:{thousand:"k",million:"mm",billion:"b",trillion:"t"},ordinal:function(t){var e=t%10;return 1===e||3===e?"er":2===e?"do":7===e||0===e?"mo":8===e?"vo":9===e?"no":"to"},currency:{symbol:"$",position:"prefix",code:"CLP"},currencyFormat:{output:"currency",thousandSeparated:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{output:"currency",mantissa:2,spaceSeparated:!0,thousandSeparated:!0},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",spaceSeparated:!0,thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],16:[function(t,e,n){(function(o){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};!function(t){if("object"==(void 0===n?"undefined":r(n))&&void 0!==e)e.exports=t();else{var i;((i="undefined"!=typeof window?window:void 0!==o?o:"undefined"!=typeof self?self:this).numbro||(i.numbro={})).esCO=t()}}(function(){return function e(n,o,r){function i(u,f){if(!o[u]){if(!n[u]){var s="function"==typeof t&&t;if(!f&&s)return s(u,!0);if(a)return a(u,!0);var l=new Error("Cannot find module '"+u+"'");throw l.code="MODULE_NOT_FOUND",l}var d=o[u]={exports:{}};n[u][0].call(d.exports,function(t){return i(n[u][1][t]||t)},d,d.exports,e,n,o,r)}return o[u].exports}for(var a="function"==typeof t&&t,u=0;u<r.length;u++)i(r[u]);return i}({1:[function(t,e,n){e.exports={languageTag:"es-CO",delimiters:{thousands:".",decimal:","},abbreviations:{thousand:"k",million:"mm",billion:"b",trillion:"t"},ordinal:function(t){var e=t%10;return 1===e||3===e?"er":2===e?"do":7===e||0===e?"mo":8===e?"vo":9===e?"no":"to"},currency:{symbol:"€",position:"postfix",code:"EUR"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{output:"currency",mantissa:2,spaceSeparated:!0,thousandSeparated:!0},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",spaceSeparated:!0,thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],17:[function(t,e,n){(function(o){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};!function(t){if("object"==(void 0===n?"undefined":r(n))&&void 0!==e)e.exports=t();else{var i;((i="undefined"!=typeof window?window:void 0!==o?o:"undefined"!=typeof self?self:this).numbro||(i.numbro={})).esCR=t()}}(function(){return function e(n,o,r){function i(u,f){if(!o[u]){if(!n[u]){var s="function"==typeof t&&t;if(!f&&s)return s(u,!0);if(a)return a(u,!0);var l=new Error("Cannot find module '"+u+"'");throw l.code="MODULE_NOT_FOUND",l}var d=o[u]={exports:{}};n[u][0].call(d.exports,function(t){return i(n[u][1][t]||t)},d,d.exports,e,n,o,r)}return o[u].exports}for(var a="function"==typeof t&&t,u=0;u<r.length;u++)i(r[u]);return i}({1:[function(t,e,n){e.exports={languageTag:"es-CR",delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"k",million:"mm",billion:"b",trillion:"t"},ordinal:function(t){var e=t%10;return 1===e||3===e?"er":2===e?"do":7===e||0===e?"mo":8===e?"vo":9===e?"no":"to"},currency:{symbol:"₡",position:"postfix",code:"CRC"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{output:"currency",mantissa:2,spaceSeparated:!0,thousandSeparated:!0},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",spaceSeparated:!0,thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],18:[function(t,e,n){(function(o){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};!function(t){if("object"==(void 0===n?"undefined":r(n))&&void 0!==e)e.exports=t();else{var i;((i="undefined"!=typeof window?window:void 0!==o?o:"undefined"!=typeof self?self:this).numbro||(i.numbro={})).esES=t()}}(function(){return function e(n,o,r){function i(u,f){if(!o[u]){if(!n[u]){var s="function"==typeof t&&t;if(!f&&s)return s(u,!0);if(a)return a(u,!0);var l=new Error("Cannot find module '"+u+"'");throw l.code="MODULE_NOT_FOUND",l}var d=o[u]={exports:{}};n[u][0].call(d.exports,function(t){return i(n[u][1][t]||t)},d,d.exports,e,n,o,r)}return o[u].exports}for(var a="function"==typeof t&&t,u=0;u<r.length;u++)i(r[u]);return i}({1:[function(t,e,n){e.exports={languageTag:"es-ES",delimiters:{thousands:".",decimal:","},abbreviations:{thousand:"k",million:"mm",billion:"b",trillion:"t"},ordinal:function(t){var e=t%10;return 1===e||3===e?"er":2===e?"do":7===e||0===e?"mo":8===e?"vo":9===e?"no":"to"},currency:{symbol:"€",position:"postfix",code:"EUR"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{output:"currency",mantissa:2,spaceSeparated:!0,thousandSeparated:!0},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",spaceSeparated:!0,thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],19:[function(t,e,n){(function(o){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};!function(t){if("object"==(void 0===n?"undefined":r(n))&&void 0!==e)e.exports=t();else{var i;((i="undefined"!=typeof window?window:void 0!==o?o:"undefined"!=typeof self?self:this).numbro||(i.numbro={})).esMX=t()}}(function(){return function e(n,o,r){function i(u,f){if(!o[u]){if(!n[u]){var s="function"==typeof t&&t;if(!f&&s)return s(u,!0);if(a)return a(u,!0);var l=new Error("Cannot find module '"+u+"'");throw l.code="MODULE_NOT_FOUND",l}var d=o[u]={exports:{}};n[u][0].call(d.exports,function(t){return i(n[u][1][t]||t)},d,d.exports,e,n,o,r)}return o[u].exports}for(var a="function"==typeof t&&t,u=0;u<r.length;u++)i(r[u]);return i}({1:[function(t,e,n){e.exports={languageTag:"es-MX",delimiters:{thousands:",",decimal:"."},abbreviations:{thousand:"k",million:"mm",billion:"b",trillion:"t"},ordinal:function(t){var e=t%10;return 1===e||3===e?"er":2===e?"do":7===e||0===e?"mo":8===e?"vo":9===e?"no":"to"},currency:{symbol:"$",position:"postfix",code:"MXN"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{output:"currency",mantissa:2,spaceSeparated:!0,thousandSeparated:!0},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",spaceSeparated:!0,thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],20:[function(t,e,n){(function(o){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};!function(t){if("object"==(void 0===n?"undefined":r(n))&&void 0!==e)e.exports=t();else{var i;((i="undefined"!=typeof window?window:void 0!==o?o:"undefined"!=typeof self?self:this).numbro||(i.numbro={})).esNI=t()}}(function(){return function e(n,o,r){function i(u,f){if(!o[u]){if(!n[u]){var s="function"==typeof t&&t;if(!f&&s)return s(u,!0);if(a)return a(u,!0);var l=new Error("Cannot find module '"+u+"'");throw l.code="MODULE_NOT_FOUND",l}var d=o[u]={exports:{}};n[u][0].call(d.exports,function(t){return i(n[u][1][t]||t)},d,d.exports,e,n,o,r)}return o[u].exports}for(var a="function"==typeof t&&t,u=0;u<r.length;u++)i(r[u]);return i}({1:[function(t,e,n){e.exports={languageTag:"es-NI",delimiters:{thousands:",",decimal:"."},abbreviations:{thousand:"k",million:"mm",billion:"b",trillion:"t"},ordinal:function(t){var e=t%10;return 1===e||3===e?"er":2===e?"do":7===e||0===e?"mo":8===e?"vo":9===e?"no":"to"},currency:{symbol:"C$",position:"prefix",code:"NIO"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{output:"currency",mantissa:2,spaceSeparated:!0,thousandSeparated:!0},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",spaceSeparated:!0,thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],21:[function(t,e,n){(function(o){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};!function(t){if("object"==(void 0===n?"undefined":r(n))&&void 0!==e)e.exports=t();else{var i;((i="undefined"!=typeof window?window:void 0!==o?o:"undefined"!=typeof self?self:this).numbro||(i.numbro={})).esPE=t()}}(function(){return function e(n,o,r){function i(u,f){if(!o[u]){if(!n[u]){var s="function"==typeof t&&t;if(!f&&s)return s(u,!0);if(a)return a(u,!0);var l=new Error("Cannot find module '"+u+"'");throw l.code="MODULE_NOT_FOUND",l}var d=o[u]={exports:{}};n[u][0].call(d.exports,function(t){return i(n[u][1][t]||t)},d,d.exports,e,n,o,r)}return o[u].exports}for(var a="function"==typeof t&&t,u=0;u<r.length;u++)i(r[u]);return i}({1:[function(t,e,n){e.exports={languageTag:"es-PE",delimiters:{thousands:",",decimal:"."},abbreviations:{thousand:"k",million:"mm",billion:"b",trillion:"t"},ordinal:function(t){var e=t%10;return 1===e||3===e?"er":2===e?"do":7===e||0===e?"mo":8===e?"vo":9===e?"no":"to"},currency:{symbol:"S/.",position:"prefix",code:"PEN"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{output:"currency",mantissa:2,spaceSeparated:!0,thousandSeparated:!0},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",spaceSeparated:!0,thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],22:[function(t,e,n){(function(o){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};!function(t){if("object"==(void 0===n?"undefined":r(n))&&void 0!==e)e.exports=t();else{var i;((i="undefined"!=typeof window?window:void 0!==o?o:"undefined"!=typeof self?self:this).numbro||(i.numbro={})).esPR=t()}}(function(){return function e(n,o,r){function i(u,f){if(!o[u]){if(!n[u]){var s="function"==typeof t&&t;if(!f&&s)return s(u,!0);if(a)return a(u,!0);var l=new Error("Cannot find module '"+u+"'");throw l.code="MODULE_NOT_FOUND",l}var d=o[u]={exports:{}};n[u][0].call(d.exports,function(t){return i(n[u][1][t]||t)},d,d.exports,e,n,o,r)}return o[u].exports}for(var a="function"==typeof t&&t,u=0;u<r.length;u++)i(r[u]);return i}({1:[function(t,e,n){e.exports={languageTag:"es-PR",delimiters:{thousands:",",decimal:"."},abbreviations:{thousand:"k",million:"mm",billion:"b",trillion:"t"},ordinal:function(t){var e=t%10;return 1===e||3===e?"er":2===e?"do":7===e||0===e?"mo":8===e?"vo":9===e?"no":"to"},currency:{symbol:"$",position:"prefix",code:"USD"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{output:"currency",mantissa:2,spaceSeparated:!0,thousandSeparated:!0},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",spaceSeparated:!0,thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],23:[function(t,e,n){(function(o){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};!function(t){if("object"==(void 0===n?"undefined":r(n))&&void 0!==e)e.exports=t();else{var i;((i="undefined"!=typeof window?window:void 0!==o?o:"undefined"!=typeof self?self:this).numbro||(i.numbro={})).esSV=t()}}(function(){return function e(n,o,r){function i(u,f){if(!o[u]){if(!n[u]){var s="function"==typeof t&&t;if(!f&&s)return s(u,!0);if(a)return a(u,!0);var l=new Error("Cannot find module '"+u+"'");throw l.code="MODULE_NOT_FOUND",l}var d=o[u]={exports:{}};n[u][0].call(d.exports,function(t){return i(n[u][1][t]||t)},d,d.exports,e,n,o,r)}return o[u].exports}for(var a="function"==typeof t&&t,u=0;u<r.length;u++)i(r[u]);return i}({1:[function(t,e,n){e.exports={languageTag:"es-SV",delimiters:{thousands:",",decimal:"."},abbreviations:{thousand:"k",million:"mm",billion:"b",trillion:"t"},ordinal:function(t){var e=t%10;return 1===e||3===e?"er":2===e?"do":7===e||0===e?"mo":8===e?"vo":9===e?"no":"to"},currency:{symbol:"$",position:"prefix",code:"SVC"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{output:"currency",mantissa:2,spaceSeparated:!0,thousandSeparated:!0},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",spaceSeparated:!0,thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],24:[function(t,e,n){(function(o){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};!function(t){if("object"==(void 0===n?"undefined":r(n))&&void 0!==e)e.exports=t();else{var i;((i="undefined"!=typeof window?window:void 0!==o?o:"undefined"!=typeof self?self:this).numbro||(i.numbro={})).etEE=t()}}(function(){return function e(n,o,r){function i(u,f){if(!o[u]){if(!n[u]){var s="function"==typeof t&&t;if(!f&&s)return s(u,!0);if(a)return a(u,!0);var l=new Error("Cannot find module '"+u+"'");throw l.code="MODULE_NOT_FOUND",l}var d=o[u]={exports:{}};n[u][0].call(d.exports,function(t){return i(n[u][1][t]||t)},d,d.exports,e,n,o,r)}return o[u].exports}for(var a="function"==typeof t&&t,u=0;u<r.length;u++)i(r[u]);return i}({1:[function(t,e,n){e.exports={languageTag:"et-EE",delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"tuh",million:"mln",billion:"mld",trillion:"trl"},ordinal:function(){return"."},currency:{symbol:"€",position:"postfix",code:"EUR"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{output:"currency",mantissa:2,spaceSeparated:!0,thousandSeparated:!0},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",spaceSeparated:!0,thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],25:[function(t,e,n){(function(o){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};!function(t){if("object"==(void 0===n?"undefined":r(n))&&void 0!==e)e.exports=t();else{var i;((i="undefined"!=typeof window?window:void 0!==o?o:"undefined"!=typeof self?self:this).numbro||(i.numbro={})).faIR=t()}}(function(){return function e(n,o,r){function i(u,f){if(!o[u]){if(!n[u]){var s="function"==typeof t&&t;if(!f&&s)return s(u,!0);if(a)return a(u,!0);var l=new Error("Cannot find module '"+u+"'");throw l.code="MODULE_NOT_FOUND",l}var d=o[u]={exports:{}};n[u][0].call(d.exports,function(t){return i(n[u][1][t]||t)},d,d.exports,e,n,o,r)}return o[u].exports}for(var a="function"==typeof t&&t,u=0;u<r.length;u++)i(r[u]);return i}({1:[function(t,e,n){e.exports={languageTag:"fa-IR",delimiters:{thousands:"،",decimal:"."},abbreviations:{thousand:"هزار",million:"میلیون",billion:"میلیارد",trillion:"تریلیون"},ordinal:function(){return"ام"},currency:{symbol:"﷼",code:"IRR"}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],26:[function(t,e,n){(function(o){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};!function(t){if("object"==(void 0===n?"undefined":r(n))&&void 0!==e)e.exports=t();else{var i;((i="undefined"!=typeof window?window:void 0!==o?o:"undefined"!=typeof self?self:this).numbro||(i.numbro={})).fiFI=t()}}(function(){return function e(n,o,r){function i(u,f){if(!o[u]){if(!n[u]){var s="function"==typeof t&&t;if(!f&&s)return s(u,!0);if(a)return a(u,!0);var l=new Error("Cannot find module '"+u+"'");throw l.code="MODULE_NOT_FOUND",l}var d=o[u]={exports:{}};n[u][0].call(d.exports,function(t){return i(n[u][1][t]||t)},d,d.exports,e,n,o,r)}return o[u].exports}for(var a="function"==typeof t&&t,u=0;u<r.length;u++)i(r[u]);return i}({1:[function(t,e,n){e.exports={languageTag:"fi-FI",delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"k",million:"M",billion:"G",trillion:"T"},ordinal:function(){return"."},currency:{symbol:"€",position:"postfix",code:"EUR"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{output:"currency",mantissa:2,spaceSeparated:!0,thousandSeparated:!0},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",spaceSeparated:!0,thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],27:[function(t,e,n){(function(o){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};!function(t){if("object"==(void 0===n?"undefined":r(n))&&void 0!==e)e.exports=t();else{var i;((i="undefined"!=typeof window?window:void 0!==o?o:"undefined"!=typeof self?self:this).numbro||(i.numbro={})).filPH=t()}}(function(){return function e(n,o,r){function i(u,f){if(!o[u]){if(!n[u]){var s="function"==typeof t&&t;if(!f&&s)return s(u,!0);if(a)return a(u,!0);var l=new Error("Cannot find module '"+u+"'");throw l.code="MODULE_NOT_FOUND",l}var d=o[u]={exports:{}};n[u][0].call(d.exports,function(t){return i(n[u][1][t]||t)},d,d.exports,e,n,o,r)}return o[u].exports}for(var a="function"==typeof t&&t,u=0;u<r.length;u++)i(r[u]);return i}({1:[function(t,e,n){e.exports={languageTag:"fil-PH",delimiters:{thousands:",",decimal:"."},abbreviations:{thousand:"k",million:"m",billion:"b",trillion:"t"},ordinal:function(t){var e=t%10;return 1==~~(t%100/10)?"th":1===e?"st":2===e?"nd":3===e?"rd":"th"},currency:{symbol:"₱",code:"PHP"}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],28:[function(t,e,n){(function(o){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};!function(t){if("object"==(void 0===n?"undefined":r(n))&&void 0!==e)e.exports=t();else{var i;((i="undefined"!=typeof window?window:void 0!==o?o:"undefined"!=typeof self?self:this).numbro||(i.numbro={})).frCA=t()}}(function(){return function e(n,o,r){function i(u,f){if(!o[u]){if(!n[u]){var s="function"==typeof t&&t;if(!f&&s)return s(u,!0);if(a)return a(u,!0);var l=new Error("Cannot find module '"+u+"'");throw l.code="MODULE_NOT_FOUND",l}var d=o[u]={exports:{}};n[u][0].call(d.exports,function(t){return i(n[u][1][t]||t)},d,d.exports,e,n,o,r)}return o[u].exports}for(var a="function"==typeof t&&t,u=0;u<r.length;u++)i(r[u]);return i}({1:[function(t,e,n){e.exports={languageTag:"fr-CA",delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"k",million:"M",billion:"G",trillion:"T"},ordinal:function(t){return 1===t?"er":"ème"},spaceSeparated:!0,currency:{symbol:"$",position:"postfix",code:"USD"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{thousandSeparated:!0,mantissa:2},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],29:[function(t,e,n){(function(o){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};!function(t){if("object"==(void 0===n?"undefined":r(n))&&void 0!==e)e.exports=t();else{var i;((i="undefined"!=typeof window?window:void 0!==o?o:"undefined"!=typeof self?self:this).numbro||(i.numbro={})).frCH=t()}}(function(){return function e(n,o,r){function i(u,f){if(!o[u]){if(!n[u]){var s="function"==typeof t&&t;if(!f&&s)return s(u,!0);if(a)return a(u,!0);var l=new Error("Cannot find module '"+u+"'");throw l.code="MODULE_NOT_FOUND",l}var d=o[u]={exports:{}};n[u][0].call(d.exports,function(t){return i(n[u][1][t]||t)},d,d.exports,e,n,o,r)}return o[u].exports}for(var a="function"==typeof t&&t,u=0;u<r.length;u++)i(r[u]);return i}({1:[function(t,e,n){e.exports={languageTag:"fr-CH",delimiters:{thousands:" ",decimal:"."},abbreviations:{thousand:"k",million:"m",billion:"b",trillion:"t"},ordinal:function(t){return 1===t?"er":"ème"},currency:{symbol:"CHF",position:"postfix",code:"CHF"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{output:"currency",mantissa:2,spaceSeparated:!0,thousandSeparated:!0},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",spaceSeparated:!0,thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],30:[function(t,e,n){(function(o){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};!function(t){if("object"==(void 0===n?"undefined":r(n))&&void 0!==e)e.exports=t();else{var i;((i="undefined"!=typeof window?window:void 0!==o?o:"undefined"!=typeof self?self:this).numbro||(i.numbro={})).frFR=t()}}(function(){return function e(n,o,r){function i(u,f){if(!o[u]){if(!n[u]){var s="function"==typeof t&&t;if(!f&&s)return s(u,!0);if(a)return a(u,!0);var l=new Error("Cannot find module '"+u+"'");throw l.code="MODULE_NOT_FOUND",l}var d=o[u]={exports:{}};n[u][0].call(d.exports,function(t){return i(n[u][1][t]||t)},d,d.exports,e,n,o,r)}return o[u].exports}for(var a="function"==typeof t&&t,u=0;u<r.length;u++)i(r[u]);return i}({1:[function(t,e,n){e.exports={languageTag:"fr-FR",delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"k",million:"m",billion:"b",trillion:"t"},ordinal:function(t){return 1===t?"er":"ème"},currency:{symbol:"€",position:"postfix",code:"EUR"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{output:"currency",mantissa:2,spaceSeparated:!0,thousandSeparated:!0},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",spaceSeparated:!0,thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],31:[function(t,e,n){(function(o){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};!function(t){if("object"==(void 0===n?"undefined":r(n))&&void 0!==e)e.exports=t();else{var i;((i="undefined"!=typeof window?window:void 0!==o?o:"undefined"!=typeof self?self:this).numbro||(i.numbro={})).heIL=t()}}(function(){return function e(n,o,r){function i(u,f){if(!o[u]){if(!n[u]){var s="function"==typeof t&&t;if(!f&&s)return s(u,!0);if(a)return a(u,!0);var l=new Error("Cannot find module '"+u+"'");throw l.code="MODULE_NOT_FOUND",l}var d=o[u]={exports:{}};n[u][0].call(d.exports,function(t){return i(n[u][1][t]||t)},d,d.exports,e,n,o,r)}return o[u].exports}for(var a="function"==typeof t&&t,u=0;u<r.length;u++)i(r[u]);return i}({1:[function(t,e,n){e.exports={languageTag:"he-IL",delimiters:{thousands:",",decimal:"."},abbreviations:{thousand:"אלף",million:"מליון",billion:"בליון",trillion:"טריליון"},currency:{symbol:"₪",position:"prefix",code:"ILS"},ordinal:function(){return""},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{output:"currency",mantissa:2,spaceSeparated:!0,thousandSeparated:!0},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",spaceSeparated:!0,thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],32:[function(t,e,n){(function(o){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};!function(t){if("object"==(void 0===n?"undefined":r(n))&&void 0!==e)e.exports=t();else{var i;((i="undefined"!=typeof window?window:void 0!==o?o:"undefined"!=typeof self?self:this).numbro||(i.numbro={})).huHU=t()}}(function(){return function e(n,o,r){function i(u,f){if(!o[u]){if(!n[u]){var s="function"==typeof t&&t;if(!f&&s)return s(u,!0);if(a)return a(u,!0);var l=new Error("Cannot find module '"+u+"'");throw l.code="MODULE_NOT_FOUND",l}var d=o[u]={exports:{}};n[u][0].call(d.exports,function(t){return i(n[u][1][t]||t)},d,d.exports,e,n,o,r)}return o[u].exports}for(var a="function"==typeof t&&t,u=0;u<r.length;u++)i(r[u]);return i}({1:[function(t,e,n){e.exports={languageTag:"hu-HU",delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"E",million:"M",billion:"Mrd",trillion:"T"},ordinal:function(){return"."},currency:{symbol:"Ft",position:"postfix",code:"HUF"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{output:"currency",mantissa:2,spaceSeparated:!0,thousandSeparated:!0},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",spaceSeparated:!0,thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],33:[function(t,e,n){(function(o){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};!function(t){if("object"==(void 0===n?"undefined":r(n))&&void 0!==e)e.exports=t();else{var i;((i="undefined"!=typeof window?window:void 0!==o?o:"undefined"!=typeof self?self:this).numbro||(i.numbro={})).id=t()}}(function(){return function e(n,o,r){function i(u,f){if(!o[u]){if(!n[u]){var s="function"==typeof t&&t;if(!f&&s)return s(u,!0);if(a)return a(u,!0);var l=new Error("Cannot find module '"+u+"'");throw l.code="MODULE_NOT_FOUND",l}var d=o[u]={exports:{}};n[u][0].call(d.exports,function(t){return i(n[u][1][t]||t)},d,d.exports,e,n,o,r)}return o[u].exports}for(var a="function"==typeof t&&t,u=0;u<r.length;u++)i(r[u]);return i}({1:[function(t,e,n){e.exports={languageTag:"id",delimiters:{thousands:",",decimal:"."},abbreviations:{thousand:"r",million:"j",billion:"m",trillion:"t"},ordinal:function(){return"."},currency:{symbol:"Rp",code:"IDR"}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],34:[function(t,e,n){(function(o){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};!function(t){if("object"==(void 0===n?"undefined":r(n))&&void 0!==e)e.exports=t();else{var i;((i="undefined"!=typeof window?window:void 0!==o?o:"undefined"!=typeof self?self:this).numbro||(i.numbro={})).itCH=t()}}(function(){return function e(n,o,r){function i(u,f){if(!o[u]){if(!n[u]){var s="function"==typeof t&&t;if(!f&&s)return s(u,!0);if(a)return a(u,!0);var l=new Error("Cannot find module '"+u+"'");throw l.code="MODULE_NOT_FOUND",l}var d=o[u]={exports:{}};n[u][0].call(d.exports,function(t){return i(n[u][1][t]||t)},d,d.exports,e,n,o,r)}return o[u].exports}for(var a="function"==typeof t&&t,u=0;u<r.length;u++)i(r[u]);return i}({1:[function(t,e,n){e.exports={languageTag:"it-CH",delimiters:{thousands:"'",decimal:"."},abbreviations:{thousand:"mila",million:"mil",billion:"b",trillion:"t"},ordinal:function(){return"°"},currency:{symbol:"CHF",code:"CHF"}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],35:[function(t,e,n){(function(o){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};!function(t){if("object"==(void 0===n?"undefined":r(n))&&void 0!==e)e.exports=t();else{var i;((i="undefined"!=typeof window?window:void 0!==o?o:"undefined"!=typeof self?self:this).numbro||(i.numbro={})).itIT=t()}}(function(){return function e(n,o,r){function i(u,f){if(!o[u]){if(!n[u]){var s="function"==typeof t&&t;if(!f&&s)return s(u,!0);if(a)return a(u,!0);var l=new Error("Cannot find module '"+u+"'");throw l.code="MODULE_NOT_FOUND",l}var d=o[u]={exports:{}};n[u][0].call(d.exports,function(t){return i(n[u][1][t]||t)},d,d.exports,e,n,o,r)}return o[u].exports}for(var a="function"==typeof t&&t,u=0;u<r.length;u++)i(r[u]);return i}({1:[function(t,e,n){e.exports={languageTag:"it-IT",delimiters:{thousands:".",decimal:","},abbreviations:{thousand:"mila",million:"mil",billion:"b",trillion:"t"},ordinal:function(){return"º"},currency:{symbol:"€",position:"postfix",code:"EUR"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{output:"currency",mantissa:2,spaceSeparated:!0,thousandSeparated:!0},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",spaceSeparated:!0,thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],36:[function(t,e,n){(function(o){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};!function(t){if("object"==(void 0===n?"undefined":r(n))&&void 0!==e)e.exports=t();else{var i;((i="undefined"!=typeof window?window:void 0!==o?o:"undefined"!=typeof self?self:this).numbro||(i.numbro={})).jaJP=t()}}(function(){return function e(n,o,r){function i(u,f){if(!o[u]){if(!n[u]){var s="function"==typeof t&&t;if(!f&&s)return s(u,!0);if(a)return a(u,!0);var l=new Error("Cannot find module '"+u+"'");throw l.code="MODULE_NOT_FOUND",l}var d=o[u]={exports:{}};n[u][0].call(d.exports,function(t){return i(n[u][1][t]||t)},d,d.exports,e,n,o,r)}return o[u].exports}for(var a="function"==typeof t&&t,u=0;u<r.length;u++)i(r[u]);return i}({1:[function(t,e,n){e.exports={languageTag:"ja-JP",delimiters:{thousands:",",decimal:"."},abbreviations:{thousand:"千",million:"百万",billion:"十億",trillion:"兆"},ordinal:function(){return"."},currency:{symbol:"¥",position:"prefix",code:"JPY"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{thousandSeparated:!0,mantissa:2},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],37:[function(t,e,n){(function(o){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};!function(t){if("object"==(void 0===n?"undefined":r(n))&&void 0!==e)e.exports=t();else{var i;((i="undefined"!=typeof window?window:void 0!==o?o:"undefined"!=typeof self?self:this).numbro||(i.numbro={})).koKR=t()}}(function(){return function e(n,o,r){function i(u,f){if(!o[u]){if(!n[u]){var s="function"==typeof t&&t;if(!f&&s)return s(u,!0);if(a)return a(u,!0);var l=new Error("Cannot find module '"+u+"'");throw l.code="MODULE_NOT_FOUND",l}var d=o[u]={exports:{}};n[u][0].call(d.exports,function(t){return i(n[u][1][t]||t)},d,d.exports,e,n,o,r)}return o[u].exports}for(var a="function"==typeof t&&t,u=0;u<r.length;u++)i(r[u]);return i}({1:[function(t,e,n){e.exports={languageTag:"ko-KR",delimiters:{thousands:",",decimal:"."},abbreviations:{thousand:"천",million:"백만",billion:"십억",trillion:"일조"},ordinal:function(){return"."},currency:{symbol:"₩",code:"KPW"}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],38:[function(t,e,n){(function(o){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};!function(t){if("object"==(void 0===n?"undefined":r(n))&&void 0!==e)e.exports=t();else{var i;((i="undefined"!=typeof window?window:void 0!==o?o:"undefined"!=typeof self?self:this).numbro||(i.numbro={})).lvLV=t()}}(function(){return function e(n,o,r){function i(u,f){if(!o[u]){if(!n[u]){var s="function"==typeof t&&t;if(!f&&s)return s(u,!0);if(a)return a(u,!0);var l=new Error("Cannot find module '"+u+"'");throw l.code="MODULE_NOT_FOUND",l}var d=o[u]={exports:{}};n[u][0].call(d.exports,function(t){return i(n[u][1][t]||t)},d,d.exports,e,n,o,r)}return o[u].exports}for(var a="function"==typeof t&&t,u=0;u<r.length;u++)i(r[u]);return i}({1:[function(t,e,n){e.exports={languageTag:"lv-LV",delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"tūkst.",million:"milj.",billion:"mljrd.",trillion:"trilj."},ordinal:function(){return"."},currency:{symbol:"€",position:"postfix",code:"EUR"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{output:"currency",mantissa:2,spaceSeparated:!0,thousandSeparated:!0},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",spaceSeparated:!0,thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],39:[function(t,e,n){(function(o){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};!function(t){if("object"==(void 0===n?"undefined":r(n))&&void 0!==e)e.exports=t();else{var i;((i="undefined"!=typeof window?window:void 0!==o?o:"undefined"!=typeof self?self:this).numbro||(i.numbro={})).nbNO=t()}}(function(){return function e(n,o,r){function i(u,f){if(!o[u]){if(!n[u]){var s="function"==typeof t&&t;if(!f&&s)return s(u,!0);if(a)return a(u,!0);var l=new Error("Cannot find module '"+u+"'");throw l.code="MODULE_NOT_FOUND",l}var d=o[u]={exports:{}};n[u][0].call(d.exports,function(t){return i(n[u][1][t]||t)},d,d.exports,e,n,o,r)}return o[u].exports}for(var a="function"==typeof t&&t,u=0;u<r.length;u++)i(r[u]);return i}({1:[function(t,e,n){e.exports={languageTag:"nb-NO",delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"t",million:"M",billion:"md",trillion:"b"},ordinal:function(){return""},currency:{symbol:"kr",position:"postfix",code:"NOK"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{output:"currency",mantissa:2,spaceSeparated:!0,thousandSeparated:!0},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",spaceSeparated:!0,thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],40:[function(t,e,n){(function(o){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};!function(t){if("object"==(void 0===n?"undefined":r(n))&&void 0!==e)e.exports=t();else{var i;((i="undefined"!=typeof window?window:void 0!==o?o:"undefined"!=typeof self?self:this).numbro||(i.numbro={})).nb=t()}}(function(){return function e(n,o,r){function i(u,f){if(!o[u]){if(!n[u]){var s="function"==typeof t&&t;if(!f&&s)return s(u,!0);if(a)return a(u,!0);var l=new Error("Cannot find module '"+u+"'");throw l.code="MODULE_NOT_FOUND",l}var d=o[u]={exports:{}};n[u][0].call(d.exports,function(t){return i(n[u][1][t]||t)},d,d.exports,e,n,o,r)}return o[u].exports}for(var a="function"==typeof t&&t,u=0;u<r.length;u++)i(r[u]);return i}({1:[function(t,e,n){e.exports={languageTag:"nb",delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"t",million:"mil",billion:"mia",trillion:"b"},ordinal:function(){return"."},currency:{symbol:"kr",code:"NOK"}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],41:[function(t,e,n){(function(o){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};!function(t){if("object"==(void 0===n?"undefined":r(n))&&void 0!==e)e.exports=t();else{var i;((i="undefined"!=typeof window?window:void 0!==o?o:"undefined"!=typeof self?self:this).numbro||(i.numbro={})).nlBE=t()}}(function(){return function e(n,o,r){function i(u,f){if(!o[u]){if(!n[u]){var s="function"==typeof t&&t;if(!f&&s)return s(u,!0);if(a)return a(u,!0);var l=new Error("Cannot find module '"+u+"'");throw l.code="MODULE_NOT_FOUND",l}var d=o[u]={exports:{}};n[u][0].call(d.exports,function(t){return i(n[u][1][t]||t)},d,d.exports,e,n,o,r)}return o[u].exports}for(var a="function"==typeof t&&t,u=0;u<r.length;u++)i(r[u]);return i}({1:[function(t,e,n){e.exports={languageTag:"nl-BE",delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"k",million:"mln",billion:"mld",trillion:"bln"},ordinal:function(t){var e=t%100;return 0!==t&&e<=1||8===e||e>=20?"ste":"de"},currency:{symbol:"€",position:"postfix",code:"EUR"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{output:"currency",mantissa:2,spaceSeparated:!0,thousandSeparated:!0},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",spaceSeparated:!0,thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],42:[function(t,e,n){(function(o){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};!function(t){if("object"==(void 0===n?"undefined":r(n))&&void 0!==e)e.exports=t();else{var i;((i="undefined"!=typeof window?window:void 0!==o?o:"undefined"!=typeof self?self:this).numbro||(i.numbro={})).nlNL=t()}}(function(){return function e(n,o,r){function i(u,f){if(!o[u]){if(!n[u]){var s="function"==typeof t&&t;if(!f&&s)return s(u,!0);if(a)return a(u,!0);var l=new Error("Cannot find module '"+u+"'");throw l.code="MODULE_NOT_FOUND",l}var d=o[u]={exports:{}};n[u][0].call(d.exports,function(t){return i(n[u][1][t]||t)},d,d.exports,e,n,o,r)}return o[u].exports}for(var a="function"==typeof t&&t,u=0;u<r.length;u++)i(r[u]);return i}({1:[function(t,e,n){e.exports={languageTag:"nl-NL",delimiters:{thousands:".",decimal:","},abbreviations:{thousand:"k",million:"mln",billion:"mrd",trillion:"bln"},ordinal:function(t){var e=t%100;return 0!==t&&e<=1||8===e||e>=20?"ste":"de"},currency:{symbol:"€",position:"prefix",code:"EUR"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{output:"currency",mantissa:2,spaceSeparated:!0,thousandSeparated:!0},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",spaceSeparated:!0,thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],43:[function(t,e,n){(function(o){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};!function(t){if("object"==(void 0===n?"undefined":r(n))&&void 0!==e)e.exports=t();else{var i;((i="undefined"!=typeof window?window:void 0!==o?o:"undefined"!=typeof self?self:this).numbro||(i.numbro={})).nn=t()}}(function(){return function e(n,o,r){function i(u,f){if(!o[u]){if(!n[u]){var s="function"==typeof t&&t;if(!f&&s)return s(u,!0);if(a)return a(u,!0);var l=new Error("Cannot find module '"+u+"'");throw l.code="MODULE_NOT_FOUND",l}var d=o[u]={exports:{}};n[u][0].call(d.exports,function(t){return i(n[u][1][t]||t)},d,d.exports,e,n,o,r)}return o[u].exports}for(var a="function"==typeof t&&t,u=0;u<r.length;u++)i(r[u]);return i}({1:[function(t,e,n){e.exports={languageTag:"nn",delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"t",million:"mil",billion:"mia",trillion:"b"},ordinal:function(){return"."},currency:{symbol:"kr",code:"NOK"}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],44:[function(t,e,n){(function(o){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};!function(t){if("object"==(void 0===n?"undefined":r(n))&&void 0!==e)e.exports=t();else{var i;((i="undefined"!=typeof window?window:void 0!==o?o:"undefined"!=typeof self?self:this).numbro||(i.numbro={})).plPL=t()}}(function(){return function e(n,o,r){function i(u,f){if(!o[u]){if(!n[u]){var s="function"==typeof t&&t;if(!f&&s)return s(u,!0);if(a)return a(u,!0);var l=new Error("Cannot find module '"+u+"'");throw l.code="MODULE_NOT_FOUND",l}var d=o[u]={exports:{}};n[u][0].call(d.exports,function(t){return i(n[u][1][t]||t)},d,d.exports,e,n,o,r)}return o[u].exports}for(var a="function"==typeof t&&t,u=0;u<r.length;u++)i(r[u]);return i}({1:[function(t,e,n){e.exports={languageTag:"pl-PL",delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"tys.",million:"mln",billion:"mld",trillion:"bln"},ordinal:function(){return"."},currency:{symbol:" zł",position:"postfix",code:"PLN"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{output:"currency",mantissa:2,spaceSeparated:!0,thousandSeparated:!0},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",spaceSeparated:!0,thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],45:[function(t,e,n){(function(o){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};!function(t){if("object"==(void 0===n?"undefined":r(n))&&void 0!==e)e.exports=t();else{var i;((i="undefined"!=typeof window?window:void 0!==o?o:"undefined"!=typeof self?self:this).numbro||(i.numbro={})).ptBR=t()}}(function(){return function e(n,o,r){function i(u,f){if(!o[u]){if(!n[u]){var s="function"==typeof t&&t;if(!f&&s)return s(u,!0);if(a)return a(u,!0);var l=new Error("Cannot find module '"+u+"'");throw l.code="MODULE_NOT_FOUND",l}var d=o[u]={exports:{}};n[u][0].call(d.exports,function(t){return i(n[u][1][t]||t)},d,d.exports,e,n,o,r)}return o[u].exports}for(var a="function"==typeof t&&t,u=0;u<r.length;u++)i(r[u]);return i}({1:[function(t,e,n){e.exports={languageTag:"pt-BR",delimiters:{thousands:".",decimal:","},abbreviations:{thousand:"mil",million:"milhões",billion:"b",trillion:"t"},ordinal:function(){return"º"},currency:{symbol:"R$",position:"prefix",code:"BRL"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{output:"currency",mantissa:2,spaceSeparated:!0,thousandSeparated:!0},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",spaceSeparated:!0,thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],46:[function(t,e,n){(function(o){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};!function(t){if("object"==(void 0===n?"undefined":r(n))&&void 0!==e)e.exports=t();else{var i;((i="undefined"!=typeof window?window:void 0!==o?o:"undefined"!=typeof self?self:this).numbro||(i.numbro={})).ptPT=t()}}(function(){return function e(n,o,r){function i(u,f){if(!o[u]){if(!n[u]){var s="function"==typeof t&&t;if(!f&&s)return s(u,!0);if(a)return a(u,!0);var l=new Error("Cannot find module '"+u+"'");throw l.code="MODULE_NOT_FOUND",l}var d=o[u]={exports:{}};n[u][0].call(d.exports,function(t){return i(n[u][1][t]||t)},d,d.exports,e,n,o,r)}return o[u].exports}for(var a="function"==typeof t&&t,u=0;u<r.length;u++)i(r[u]);return i}({1:[function(t,e,n){e.exports={languageTag:"pt-PT",delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"k",million:"m",billion:"b",trillion:"t"},ordinal:function(){return"º"},currency:{symbol:"€",position:"postfix",code:"EUR"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{output:"currency",mantissa:2,spaceSeparated:!0,thousandSeparated:!0},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",spaceSeparated:!0,thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],47:[function(t,e,n){(function(o){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};!function(t){if("object"==(void 0===n?"undefined":r(n))&&void 0!==e)e.exports=t();else{var i;((i="undefined"!=typeof window?window:void 0!==o?o:"undefined"!=typeof self?self:this).numbro||(i.numbro={})).roRO=t()}}(function(){return function e(n,o,r){function i(u,f){if(!o[u]){if(!n[u]){var s="function"==typeof t&&t;if(!f&&s)return s(u,!0);if(a)return a(u,!0);var l=new Error("Cannot find module '"+u+"'");throw l.code="MODULE_NOT_FOUND",l}var d=o[u]={exports:{}};n[u][0].call(d.exports,function(t){return i(n[u][1][t]||t)},d,d.exports,e,n,o,r)}return o[u].exports}for(var a="function"==typeof t&&t,u=0;u<r.length;u++)i(r[u]);return i}({1:[function(t,e,n){e.exports={languageTag:"ro-RO",delimiters:{thousands:".",decimal:","},abbreviations:{thousand:"mii",million:"mil",billion:"mld",trillion:"bln"},ordinal:function(){return"."},currency:{symbol:" lei",position:"postfix",code:"RON"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{output:"currency",mantissa:2,spaceSeparated:!0,thousandSeparated:!0},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",spaceSeparated:!0,thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],48:[function(t,e,n){(function(o){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};!function(t){if("object"==(void 0===n?"undefined":r(n))&&void 0!==e)e.exports=t();else{var i;((i="undefined"!=typeof window?window:void 0!==o?o:"undefined"!=typeof self?self:this).numbro||(i.numbro={})).ro=t()}}(function(){return function e(n,o,r){function i(u,f){if(!o[u]){if(!n[u]){var s="function"==typeof t&&t;if(!f&&s)return s(u,!0);if(a)return a(u,!0);var l=new Error("Cannot find module '"+u+"'");throw l.code="MODULE_NOT_FOUND",l}var d=o[u]={exports:{}};n[u][0].call(d.exports,function(t){return i(n[u][1][t]||t)},d,d.exports,e,n,o,r)}return o[u].exports}for(var a="function"==typeof t&&t,u=0;u<r.length;u++)i(r[u]);return i}({1:[function(t,e,n){e.exports={languageTag:"ro-RO",delimiters:{thousands:".",decimal:","},abbreviations:{thousand:"mii",million:"mil",billion:"mld",trillion:"bln"},ordinal:function(){return"."},currency:{symbol:" lei",position:"postfix",code:"RON"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{output:"currency",mantissa:2,spaceSeparated:!0,thousandSeparated:!0},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",spaceSeparated:!0,thousandSeparated:!0,mantissa:0}}}},{}],2:[function(t,e,n){e.exports=t("./ro-RO")},{"./ro-RO":1}]},{},[2])(2)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],49:[function(t,e,n){(function(o){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};!function(t){if("object"==(void 0===n?"undefined":r(n))&&void 0!==e)e.exports=t();else{var i;((i="undefined"!=typeof window?window:void 0!==o?o:"undefined"!=typeof self?self:this).numbro||(i.numbro={})).ruRU=t()}}(function(){return function e(n,o,r){function i(u,f){if(!o[u]){if(!n[u]){var s="function"==typeof t&&t;if(!f&&s)return s(u,!0);if(a)return a(u,!0);var l=new Error("Cannot find module '"+u+"'");throw l.code="MODULE_NOT_FOUND",l}var d=o[u]={exports:{}};n[u][0].call(d.exports,function(t){return i(n[u][1][t]||t)},d,d.exports,e,n,o,r)}return o[u].exports}for(var a="function"==typeof t&&t,u=0;u<r.length;u++)i(r[u]);return i}({1:[function(t,e,n){e.exports={languageTag:"ru-RU",delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"тыс.",million:"млн",billion:"b",trillion:"t"},ordinal:function(){return"."},currency:{symbol:"руб.",position:"postfix",code:"RUB"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{output:"currency",mantissa:2,spaceSeparated:!0,thousandSeparated:!0},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",spaceSeparated:!0,thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],50:[function(t,e,n){(function(o){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};!function(t){if("object"==(void 0===n?"undefined":r(n))&&void 0!==e)e.exports=t();else{var i;((i="undefined"!=typeof window?window:void 0!==o?o:"undefined"!=typeof self?self:this).numbro||(i.numbro={})).ruUA=t()}}(function(){return function e(n,o,r){function i(u,f){if(!o[u]){if(!n[u]){var s="function"==typeof t&&t;if(!f&&s)return s(u,!0);if(a)return a(u,!0);var l=new Error("Cannot find module '"+u+"'");throw l.code="MODULE_NOT_FOUND",l}var d=o[u]={exports:{}};n[u][0].call(d.exports,function(t){return i(n[u][1][t]||t)},d,d.exports,e,n,o,r)}return o[u].exports}for(var a="function"==typeof t&&t,u=0;u<r.length;u++)i(r[u]);return i}({1:[function(t,e,n){e.exports={languageTag:"ru-UA",delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"тыс.",million:"млн",billion:"b",trillion:"t"},ordinal:function(){return"."},currency:{symbol:"₴",position:"postfix",code:"UAH"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{output:"currency",mantissa:2,spaceSeparated:!0,thousandSeparated:!0},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",spaceSeparated:!0,thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],51:[function(t,e,n){(function(o){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};!function(t){if("object"==(void 0===n?"undefined":r(n))&&void 0!==e)e.exports=t();else{var i;((i="undefined"!=typeof window?window:void 0!==o?o:"undefined"!=typeof self?self:this).numbro||(i.numbro={})).skSK=t()}}(function(){return function e(n,o,r){function i(u,f){if(!o[u]){if(!n[u]){var s="function"==typeof t&&t;if(!f&&s)return s(u,!0);if(a)return a(u,!0);var l=new Error("Cannot find module '"+u+"'");throw l.code="MODULE_NOT_FOUND",l}var d=o[u]={exports:{}};n[u][0].call(d.exports,function(t){return i(n[u][1][t]||t)},d,d.exports,e,n,o,r)}return o[u].exports}for(var a="function"==typeof t&&t,u=0;u<r.length;u++)i(r[u]);return i}({1:[function(t,e,n){e.exports={languageTag:"sk-SK",delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"tis.",million:"mil.",billion:"mld.",trillion:"bil."},ordinal:function(){return"."},spaceSeparated:!0,currency:{symbol:"€",position:"postfix",code:"EUR"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{output:"currency",mantissa:2,spaceSeparated:!0,thousandSeparated:!0},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",spaceSeparated:!0,thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],52:[function(t,e,n){(function(o){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};!function(t){if("object"==(void 0===n?"undefined":r(n))&&void 0!==e)e.exports=t();else{var i;((i="undefined"!=typeof window?window:void 0!==o?o:"undefined"!=typeof self?self:this).numbro||(i.numbro={})).sl=t()}}(function(){return function e(n,o,r){function i(u,f){if(!o[u]){if(!n[u]){var s="function"==typeof t&&t;if(!f&&s)return s(u,!0);if(a)return a(u,!0);var l=new Error("Cannot find module '"+u+"'");throw l.code="MODULE_NOT_FOUND",l}var d=o[u]={exports:{}};n[u][0].call(d.exports,function(t){return i(n[u][1][t]||t)},d,d.exports,e,n,o,r)}return o[u].exports}for(var a="function"==typeof t&&t,u=0;u<r.length;u++)i(r[u]);return i}({1:[function(t,e,n){e.exports={languageTag:"sl",delimiters:{thousands:".",decimal:","},abbreviations:{thousand:"tis.",million:"mil.",billion:"b",trillion:"t"},ordinal:function(){return"."},currency:{symbol:"€",code:"EUR"}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],53:[function(t,e,n){(function(o){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};!function(t){if("object"==(void 0===n?"undefined":r(n))&&void 0!==e)e.exports=t();else{var i;((i="undefined"!=typeof window?window:void 0!==o?o:"undefined"!=typeof self?self:this).numbro||(i.numbro={})).srCyrlRS=t()}}(function(){return function e(n,o,r){function i(u,f){if(!o[u]){if(!n[u]){var s="function"==typeof t&&t;if(!f&&s)return s(u,!0);if(a)return a(u,!0);var l=new Error("Cannot find module '"+u+"'");throw l.code="MODULE_NOT_FOUND",l}var d=o[u]={exports:{}};n[u][0].call(d.exports,function(t){return i(n[u][1][t]||t)},d,d.exports,e,n,o,r)}return o[u].exports}for(var a="function"==typeof t&&t,u=0;u<r.length;u++)i(r[u]);return i}({1:[function(t,e,n){e.exports={languageTag:"sr-Cyrl-RS",delimiters:{thousands:".",decimal:","},abbreviations:{thousand:"тыс.",million:"млн",billion:"b",trillion:"t"},ordinal:function(){return"."},currency:{symbol:"RSD",code:"RSD"}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],54:[function(t,e,n){(function(o){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};!function(t){if("object"==(void 0===n?"undefined":r(n))&&void 0!==e)e.exports=t();else{var i;((i="undefined"!=typeof window?window:void 0!==o?o:"undefined"!=typeof self?self:this).numbro||(i.numbro={})).svSE=t()}}(function(){return function e(n,o,r){function i(u,f){if(!o[u]){if(!n[u]){var s="function"==typeof t&&t;if(!f&&s)return s(u,!0);if(a)return a(u,!0);var l=new Error("Cannot find module '"+u+"'");throw l.code="MODULE_NOT_FOUND",l}var d=o[u]={exports:{}};n[u][0].call(d.exports,function(t){return i(n[u][1][t]||t)},d,d.exports,e,n,o,r)}return o[u].exports}for(var a="function"==typeof t&&t,u=0;u<r.length;u++)i(r[u]);return i}({1:[function(t,e,n){e.exports={languageTag:"sv-SE",delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"t",million:"M",billion:"md",trillion:"tmd"},ordinal:function(){return""},currency:{symbol:"kr",position:"postfix",code:"SEK"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{output:"currency",mantissa:2,spaceSeparated:!0,thousandSeparated:!0},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",spaceSeparated:!0,thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],55:[function(t,e,n){(function(o){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};!function(t){if("object"==(void 0===n?"undefined":r(n))&&void 0!==e)e.exports=t();else{var i;((i="undefined"!=typeof window?window:void 0!==o?o:"undefined"!=typeof self?self:this).numbro||(i.numbro={})).thTH=t()}}(function(){return function e(n,o,r){function i(u,f){if(!o[u]){if(!n[u]){var s="function"==typeof t&&t;if(!f&&s)return s(u,!0);if(a)return a(u,!0);var l=new Error("Cannot find module '"+u+"'");throw l.code="MODULE_NOT_FOUND",l}var d=o[u]={exports:{}};n[u][0].call(d.exports,function(t){return i(n[u][1][t]||t)},d,d.exports,e,n,o,r)}return o[u].exports}for(var a="function"==typeof t&&t,u=0;u<r.length;u++)i(r[u]);return i}({1:[function(t,e,n){e.exports={languageTag:"th-TH",delimiters:{thousands:",",decimal:"."},abbreviations:{thousand:"พัน",million:"ล้าน",billion:"พันล้าน",trillion:"ล้านล้าน"},ordinal:function(){return"."},currency:{symbol:"฿",position:"postfix",code:"THB"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{output:"currency",mantissa:2,spaceSeparated:!0,thousandSeparated:!0},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",spaceSeparated:!0,thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],56:[function(t,e,n){(function(o){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};!function(t){if("object"==(void 0===n?"undefined":r(n))&&void 0!==e)e.exports=t();else{var i;((i="undefined"!=typeof window?window:void 0!==o?o:"undefined"!=typeof self?self:this).numbro||(i.numbro={})).trTR=t()}}(function(){return function e(n,o,r){function i(u,f){if(!o[u]){if(!n[u]){var s="function"==typeof t&&t;if(!f&&s)return s(u,!0);if(a)return a(u,!0);var l=new Error("Cannot find module '"+u+"'");throw l.code="MODULE_NOT_FOUND",l}var d=o[u]={exports:{}};n[u][0].call(d.exports,function(t){return i(n[u][1][t]||t)},d,d.exports,e,n,o,r)}return o[u].exports}for(var a="function"==typeof t&&t,u=0;u<r.length;u++)i(r[u]);return i}({1:[function(t,e,n){var o={1:"'inci",5:"'inci",8:"'inci",70:"'inci",80:"'inci",2:"'nci",7:"'nci",20:"'nci",50:"'nci",3:"'üncü",4:"'üncü",100:"'üncü",6:"'ncı",9:"'uncu",10:"'uncu",30:"'uncu",60:"'ıncı",90:"'ıncı"};e.exports={languageTag:"tr-TR",delimiters:{thousands:".",decimal:","},abbreviations:{thousand:"bin",million:"milyon",billion:"milyar",trillion:"trilyon"},ordinal:function(t){if(0===t)return"'ıncı";var e=t%10,n=t%100-e,r=t>=100?100:null;return o[e]||o[n]||o[r]},currency:{symbol:"₺",position:"postfix",code:"TRY"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{output:"currency",mantissa:2,spaceSeparated:!0,thousandSeparated:!0},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",spaceSeparated:!0,thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],57:[function(t,e,n){(function(o){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};!function(t){if("object"==(void 0===n?"undefined":r(n))&&void 0!==e)e.exports=t();else{var i;((i="undefined"!=typeof window?window:void 0!==o?o:"undefined"!=typeof self?self:this).numbro||(i.numbro={})).ukUA=t()}}(function(){return function e(n,o,r){function i(u,f){if(!o[u]){if(!n[u]){var s="function"==typeof t&&t;if(!f&&s)return s(u,!0);if(a)return a(u,!0);var l=new Error("Cannot find module '"+u+"'");throw l.code="MODULE_NOT_FOUND",l}var d=o[u]={exports:{}};n[u][0].call(d.exports,function(t){return i(n[u][1][t]||t)},d,d.exports,e,n,o,r)}return o[u].exports}for(var a="function"==typeof t&&t,u=0;u<r.length;u++)i(r[u]);return i}({1:[function(t,e,n){e.exports={languageTag:"uk-UA",delimiters:{thousands:" ",decimal:","},abbreviations:{thousand:"тис.",million:"млн",billion:"млрд",trillion:"блн"},ordinal:function(){return""},currency:{symbol:"₴",position:"postfix",code:"UAH"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{output:"currency",mantissa:2,spaceSeparated:!0,thousandSeparated:!0},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",spaceSeparated:!0,thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],58:[function(t,e,n){(function(o){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};!function(t){if("object"==(void 0===n?"undefined":r(n))&&void 0!==e)e.exports=t();else{var i;((i="undefined"!=typeof window?window:void 0!==o?o:"undefined"!=typeof self?self:this).numbro||(i.numbro={})).zhCN=t()}}(function(){return function e(n,o,r){function i(u,f){if(!o[u]){if(!n[u]){var s="function"==typeof t&&t;if(!f&&s)return s(u,!0);if(a)return a(u,!0);var l=new Error("Cannot find module '"+u+"'");throw l.code="MODULE_NOT_FOUND",l}var d=o[u]={exports:{}};n[u][0].call(d.exports,function(t){return i(n[u][1][t]||t)},d,d.exports,e,n,o,r)}return o[u].exports}for(var a="function"==typeof t&&t,u=0;u<r.length;u++)i(r[u]);return i}({1:[function(t,e,n){e.exports={languageTag:"zh-CN",delimiters:{thousands:",",decimal:"."},abbreviations:{thousand:"千",million:"百万",billion:"十亿",trillion:"兆"},ordinal:function(){return"."},currency:{symbol:"¥",position:"prefix",code:"CNY"},currencyFormat:{thousandSeparated:!0,totalLength:4,spaceSeparated:!0,average:!0},formats:{fourDigits:{totalLength:4,spaceSeparated:!0,average:!0},fullWithTwoDecimals:{thousandSeparated:!0,mantissa:2},fullWithTwoDecimalsNoCurrency:{mantissa:2,thousandSeparated:!0},fullWithNoDecimals:{output:"currency",thousandSeparated:!0,mantissa:0}}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],59:[function(t,e,n){(function(o){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};!function(t){if("object"==(void 0===n?"undefined":r(n))&&void 0!==e)e.exports=t();else{var i;((i="undefined"!=typeof window?window:void 0!==o?o:"undefined"!=typeof self?self:this).numbro||(i.numbro={})).zhMO=t()}}(function(){return function e(n,o,r){function i(u,f){if(!o[u]){if(!n[u]){var s="function"==typeof t&&t;if(!f&&s)return s(u,!0);if(a)return a(u,!0);var l=new Error("Cannot find module '"+u+"'");throw l.code="MODULE_NOT_FOUND",l}var d=o[u]={exports:{}};n[u][0].call(d.exports,function(t){return i(n[u][1][t]||t)},d,d.exports,e,n,o,r)}return o[u].exports}for(var a="function"==typeof t&&t,u=0;u<r.length;u++)i(r[u]);return i}({1:[function(t,e,n){e.exports={languageTag:"zh-MO",delimiters:{thousands:",",decimal:"."},abbreviations:{thousand:"千",million:"百萬",billion:"十億",trillion:"兆"},ordinal:function(){return"."},currency:{symbol:"MOP",code:"MOP"}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],60:[function(t,e,n){(function(o){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};!function(t){if("object"==(void 0===n?"undefined":r(n))&&void 0!==e)e.exports=t();else{var i;((i="undefined"!=typeof window?window:void 0!==o?o:"undefined"!=typeof self?self:this).numbro||(i.numbro={})).zhSG=t()}}(function(){return function e(n,o,r){function i(u,f){if(!o[u]){if(!n[u]){var s="function"==typeof t&&t;if(!f&&s)return s(u,!0);if(a)return a(u,!0);var l=new Error("Cannot find module '"+u+"'");throw l.code="MODULE_NOT_FOUND",l}var d=o[u]={exports:{}};n[u][0].call(d.exports,function(t){return i(n[u][1][t]||t)},d,d.exports,e,n,o,r)}return o[u].exports}for(var a="function"==typeof t&&t,u=0;u<r.length;u++)i(r[u]);return i}({1:[function(t,e,n){e.exports={languageTag:"zh-SG",delimiters:{thousands:",",decimal:"."},abbreviations:{thousand:"千",million:"百万",billion:"十亿",trillion:"兆"},ordinal:function(){return"."},currency:{symbol:"$",code:"SGD"}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],61:[function(t,e,n){(function(o){"use strict";var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};!function(t){if("object"==(void 0===n?"undefined":r(n))&&void 0!==e)e.exports=t();else{var i;((i="undefined"!=typeof window?window:void 0!==o?o:"undefined"!=typeof self?self:this).numbro||(i.numbro={})).zhTW=t()}}(function(){return function e(n,o,r){function i(u,f){if(!o[u]){if(!n[u]){var s="function"==typeof t&&t;if(!f&&s)return s(u,!0);if(a)return a(u,!0);var l=new Error("Cannot find module '"+u+"'");throw l.code="MODULE_NOT_FOUND",l}var d=o[u]={exports:{}};n[u][0].call(d.exports,function(t){return i(n[u][1][t]||t)},d,d.exports,e,n,o,r)}return o[u].exports}for(var a="function"==typeof t&&t,u=0;u<r.length;u++)i(r[u]);return i}({1:[function(t,e,n){e.exports={languageTag:"zh-TW",delimiters:{thousands:",",decimal:"."},abbreviations:{thousand:"千",million:"百萬",billion:"十億",trillion:"兆"},ordinal:function(){return"第"},currency:{symbol:"NT$",code:"TWD"}}},{}]},{},[1])(1)})}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],62:[function(t,e,n){"use strict";n.bg=t("./dist/languages/bg.min.js"),n["cs-CZ"]=t("./dist/languages/cs-CZ.min.js"),n["da-DK"]=t("./dist/languages/da-DK.min.js"),n["de-AT"]=t("./dist/languages/de-AT.min.js"),n["de-CH"]=t("./dist/languages/de-CH.min.js"),n["de-DE"]=t("./dist/languages/de-DE.min.js"),n["de-LI"]=t("./dist/languages/de-LI.min.js"),n.el=t("./dist/languages/el.min.js"),n["en-AU"]=t("./dist/languages/en-AU.min.js"),n["en-GB"]=t("./dist/languages/en-GB.min.js"),n["en-IE"]=t("./dist/languages/en-IE.min.js"),n["en-NZ"]=t("./dist/languages/en-NZ.min.js"),n["en-ZA"]=t("./dist/languages/en-ZA.min.js"),n["es-AR"]=t("./dist/languages/es-AR.min.js"),n["es-CL"]=t("./dist/languages/es-CL.min.js"),n["es-CO"]=t("./dist/languages/es-CO.min.js"),n["es-CR"]=t("./dist/languages/es-CR.min.js"),n["es-ES"]=t("./dist/languages/es-ES.min.js"),n["es-MX"]=t("./dist/languages/es-MX.min.js"),n["es-NI"]=t("./dist/languages/es-NI.min.js"),n["es-PE"]=t("./dist/languages/es-PE.min.js"),n["es-PR"]=t("./dist/languages/es-PR.min.js"),n["es-SV"]=t("./dist/languages/es-SV.min.js"),n["et-EE"]=t("./dist/languages/et-EE.min.js"),n["fa-IR"]=t("./dist/languages/fa-IR.min.js"),n["fi-FI"]=t("./dist/languages/fi-FI.min.js"),n["fil-PH"]=t("./dist/languages/fil-PH.min.js"),n["fr-CA"]=t("./dist/languages/fr-CA.min.js"),n["fr-CH"]=t("./dist/languages/fr-CH.min.js"),n["fr-FR"]=t("./dist/languages/fr-FR.min.js"),n["he-IL"]=t("./dist/languages/he-IL.min.js"),n["hu-HU"]=t("./dist/languages/hu-HU.min.js"),n.id=t("./dist/languages/id.min.js"),n["it-CH"]=t("./dist/languages/it-CH.min.js"),n["it-IT"]=t("./dist/languages/it-IT.min.js"),n["ja-JP"]=t("./dist/languages/ja-JP.min.js"),n["ko-KR"]=t("./dist/languages/ko-KR.min.js"),n["lv-LV"]=t("./dist/languages/lv-LV.min.js"),n["nb-NO"]=t("./dist/languages/nb-NO.min.js"),n.nb=t("./dist/languages/nb.min.js"),n["nl-BE"]=t("./dist/languages/nl-BE.min.js"),n["nl-NL"]=t("./dist/languages/nl-NL.min.js"),n.nn=t("./dist/languages/nn.min.js"),n["pl-PL"]=t("./dist/languages/pl-PL.min.js"),n["pt-BR"]=t("./dist/languages/pt-BR.min.js"),n["pt-PT"]=t("./dist/languages/pt-PT.min.js"),n["ro-RO"]=t("./dist/languages/ro-RO.min.js"),n.ro=t("./dist/languages/ro.min.js"),n["ru-RU"]=t("./dist/languages/ru-RU.min.js"),n["ru-UA"]=t("./dist/languages/ru-UA.min.js"),n["sk-SK"]=t("./dist/languages/sk-SK.min.js"),n.sl=t("./dist/languages/sl.min.js"),n["sr-Cyrl-RS"]=t("./dist/languages/sr-Cyrl-RS.min.js"),n["sv-SE"]=t("./dist/languages/sv-SE.min.js"),n["th-TH"]=t("./dist/languages/th-TH.min.js"),n["tr-TR"]=t("./dist/languages/tr-TR.min.js"),n["uk-UA"]=t("./dist/languages/uk-UA.min.js"),n["zh-CN"]=t("./dist/languages/zh-CN.min.js"),n["zh-MO"]=t("./dist/languages/zh-MO.min.js"),n["zh-SG"]=t("./dist/languages/zh-SG.min.js"),n["zh-TW"]=t("./dist/languages/zh-TW.min.js")},{"./dist/languages/bg.min.js":1,"./dist/languages/cs-CZ.min.js":2,"./dist/languages/da-DK.min.js":3,"./dist/languages/de-AT.min.js":4,"./dist/languages/de-CH.min.js":5,"./dist/languages/de-DE.min.js":6,"./dist/languages/de-LI.min.js":7,"./dist/languages/el.min.js":8,"./dist/languages/en-AU.min.js":9,"./dist/languages/en-GB.min.js":10,"./dist/languages/en-IE.min.js":11,"./dist/languages/en-NZ.min.js":12,"./dist/languages/en-ZA.min.js":13,"./dist/languages/es-AR.min.js":14,"./dist/languages/es-CL.min.js":15,"./dist/languages/es-CO.min.js":16,"./dist/languages/es-CR.min.js":17,"./dist/languages/es-ES.min.js":18,"./dist/languages/es-MX.min.js":19,"./dist/languages/es-NI.min.js":20,"./dist/languages/es-PE.min.js":21,"./dist/languages/es-PR.min.js":22,"./dist/languages/es-SV.min.js":23,"./dist/languages/et-EE.min.js":24,"./dist/languages/fa-IR.min.js":25,"./dist/languages/fi-FI.min.js":26,"./dist/languages/fil-PH.min.js":27,"./dist/languages/fr-CA.min.js":28,"./dist/languages/fr-CH.min.js":29,"./dist/languages/fr-FR.min.js":30,"./dist/languages/he-IL.min.js":31,"./dist/languages/hu-HU.min.js":32,"./dist/languages/id.min.js":33,"./dist/languages/it-CH.min.js":34,"./dist/languages/it-IT.min.js":35,"./dist/languages/ja-JP.min.js":36,"./dist/languages/ko-KR.min.js":37,"./dist/languages/lv-LV.min.js":38,"./dist/languages/nb-NO.min.js":39,"./dist/languages/nb.min.js":40,"./dist/languages/nl-BE.min.js":41,"./dist/languages/nl-NL.min.js":42,"./dist/languages/nn.min.js":43,"./dist/languages/pl-PL.min.js":44,"./dist/languages/pt-BR.min.js":45,"./dist/languages/pt-PT.min.js":46,"./dist/languages/ro-RO.min.js":47,"./dist/languages/ro.min.js":48,"./dist/languages/ru-RU.min.js":49,"./dist/languages/ru-UA.min.js":50,"./dist/languages/sk-SK.min.js":51,"./dist/languages/sl.min.js":52,"./dist/languages/sr-Cyrl-RS.min.js":53,"./dist/languages/sv-SE.min.js":54,"./dist/languages/th-TH.min.js":55,"./dist/languages/tr-TR.min.js":56,"./dist/languages/uk-UA.min.js":57,"./dist/languages/zh-CN.min.js":58,"./dist/languages/zh-MO.min.js":59,"./dist/languages/zh-SG.min.js":60,"./dist/languages/zh-TW.min.js":61}]},{},[62])(62)});
//# sourceMappingURL=languages.min.js.map
