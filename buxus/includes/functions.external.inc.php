<?php
/**
 * Special include file containing functions needed to work with import classes
 * in Buxus templates. See task #745 for details.
 *
 * @since #827
 * Existence of each function is checked using function_exists() to ensure that
 * the page would not die on a fatal error on some old webs which may have defined
 * this functions by their own, which was the only way how to use ImportReplacePage
 * in Buxus < 5.5.0. However, a notice is logged into the log saying that you sould
 * check this, because the self-defined functions may be out-of-date. It is generally
 * advised to replace all the self-defined copies of the functions by including this
 * file (functions.external.inc.php).
 *
 * <AUTHOR> <PERSON>
 */

  //################################
  //### CheckAccessRightsForPage ###
  //################################
  /**
   * Funk<PERSON> acquires the rights of user specified by $user_id for the page specified by $page_id
   *
   * @deprecated as of version 5.5, use getUserRightsForPage()
   *
   * @param int $user_id - id uzivatela, ktoreho prava zistujeme
   * @param int $page_id - stranka, ktorej prava zistujeme
   * @param TrueFalseChar $read_right (referencia) - user ma alebo nema pravo na citanie pre danu stranku
   * @param TrueFalseChar $write_right (referencia) - user ma alebo nema pravo na zapis pre danu stranku
   * @param TrueFalseChar $inherited_read (referencia) - userovo pravo na citanie bolo zdedene (T) alebo bolo nastavene priamo (F)
   * @param TrueFalseChar $inherited_write (referencia) - userovo pravo na zapis bolo zdedene (T) alebo bolo nastavene priamo (F)
   * @param bool $cache_admin_rights - v priupade, ze sa kontroluje konkretny uzivatel,odporuca sa nastavit na true. Potom v pripade, ze prave prihlaseny uzivatel ma admin_rights, vrati vsetky prava true!
   */
  function CheckAccessRightsForPage($user_id, $page_id, &$read_right, &$write_right, &$inherited_read, &$inherited_write, $cache_admin_rights = true)
  {
      if (config('buxus_core.ignore_page_user_rights')) {
          $read_right = C_True_Char;
          $write_right = C_True_Char;
          return;
      }

    $read_right = "";
    $write_right = "";
    $inherited = "";
    $found = C_False_Char;
    $inherited_read = C_False_Char;
    $inherited_write = C_False_Char;

    if (isset($_GLOBALS['accessRightsCacheUserPage'][$user_id][$page_id])) {

    }

    while (($page_id > -1) && ($found != C_True_Char))
    {
      $selected_row = BuxusDB::get()->fetchRow(
        "SELECT tblAccessRightsPageUser.read_right,
                tblAccessRightsPageUser.write_right,
                tblAccessRightsPageUser.generate_right
         FROM tblAccessRightsPageUser
         WHERE page_id = :page_id
         AND user_id = :user_id",
        array(':page_id' => $page_id, ':user_id' => $user_id));


	   //ak je to admin, tak ma pravo na vsetko...
       if ($cache_admin_rights && \BuxusUserManager::hasUserRight('admin_right')) {
       		$read_right = C_True_Char;
       		$write_right = C_True_Char;
       		return;
       };

      if (!($selected_row))
      {
//        echo "IF page_id = $page_id, user = $user_id, read = $read_right, write = $write_right, generate = $generate_right <br>";
        $page_id = GetParentPageID($page_id);
        if ($read_right == "") $inherited_read = C_True_Char;
        if ($write_right == "") $inherited_write = C_True_Char;
      }
      else
      {
        if ($read_right == "") $read_right = $selected_row["read_right"];
        if ($write_right == "") $write_right = $selected_row["write_right"];
//        echo "ELSE page_id = $page_id, user = $user_id, read = $read_right, write = $write_right, generate = $generate_right <br>";
        if (($read_right == "") || ($write_right == ""))
        {
          if ($read_right == "") $inherited_read = C_True_Char;
          if ($write_right == "") $inherited_write = C_True_Char;
          $page_id = GetParentPage($page_id);
        }
        else
          $found = C_True_Char;
      }

    }

    if ($page_id <= -1)
    {
      if ($read_right == "") $read_right = C_False_Char;
      if ($write_right == "") $write_right = C_False_Char;
      if ($inherited == "") $inherited = C_False_Char;
    }
  }

function GParseXMLIntoArray($string, $decode_html_entity = true)
{
	$newlines 		= array("\n", "\r");
	$replacements 	= array("%%0A%%", "%%0D%%");
	$string = str_replace($newlines, $replacements, $string);

	$offset = 0;

	$data = Array();

	while(1)
	{
		$pos1 = mb_strpos($string, "<", $offset);

		if ($pos1 === false)
		{
			break;
		}

		$pos2 = mb_strpos($string, ">", $pos1 + 1);

		if ($pos2 === false)
		{
			break;
		}

		$elmName = mb_substr($string, $pos1 + 1, ($pos2 - $pos1) - 1);

		$pos3 = mb_strpos($string, "</", $pos2 + 1);

		if ($pos3 === false)
		{
			break;
		}

		$pos4 = mb_strpos($string, ">", $pos3 + 2);

		if ($pos4 === false)
		{
			break;
		}

		$elmValue = mb_substr($string, $pos2 + 1, $pos3 - $pos2 - 1);
		$elmName  = str_replace($replacements, '', $elmName);
		$elmValue = str_replace($replacements, $newlines, $elmValue);

		if ($decode_html_entity)
		{
			$data[$elmName] = html_entity_decode($elmValue, ENT_COMPAT, GetSystemOption('C_default_charset'));
		}
		else
		{
			$data[$elmName] = $elmValue;
		}

		$offset = $pos4 + 1;
	}

	return $data;
}

function GetTypeDescByTypeID($type_id)
{
		$sql_qry = "SELECT
    					page_type_description
    				FROM 
    					tblPageTypes
    				WHERE
    					page_type_id = :page_type_id";

    	return BuxusDB::get()->fetchOne($sql_qry, array(':page_type_id' => $type_id));
}
  //#############################
  //###    GetPageTypeName    ###
  //#############################
  function GetPageTypeName($page_type_id)
  {

    if ($page_type_id != "")
    {
      $page_type_descriptor = BuxusDB::get()->fetchOne(
        "SELECT page_type_name
         FROM tblPageTypes
         WHERE page_type_id = :page_type_id",
        array(':page_type_id' => $page_type_id));

      if (empty($page_type_descriptor))
      {
        $result = -1;
      }
      else
      {
        $result = $page_type_descriptor;
      }
    }
    else
      $result = "";

    return ($result);
  }

	/**
	 * Removes slovak special characters from a string, replacing them with
	 * ASCII chars of the same case.
	 *
	 * @param string $str
	 * @return string the replaced string
	 */
	function RemoveDiakritic($string)
	{
		$GLOBALS['LOG']->log('The function RemoveDiakritic() is not supported anymore. Use TF_RemoveDiacritic().');

		$string = Buxus::removeDiacritic($string);

		return $string;
	}

  //#############################
  //###    GetUserLongName    ###
  //#############################
  function GetUserLongName($user_id)
  {
      try {
          $name = \Buxus\User\Facades\BuxusUserManager::getUserLongName($user_id);
      } catch (Exception $e) {
          return $user_id;
      }

      return $name;
  }

  //#############################
  //###    GetPageTypeReal    ###
  //#############################
  function GetPageTypeReal($page_id) {
      if ($page_id != "") {
            $page_type_id = BuxusDB::get()->fetchOne(
                "SELECT page_type_id
                 FROM tblPages
                 WHERE page_id = :page_id", array(
                    ':page_id' => $page_id,
            ));

            if ($page_type_id === false || is_null($page_type_id)) {
                $result = -1;
            } else {
                $result = $page_type_id;
              }
      } else {
          $result = '';
      }
      return ($result);
  }

/**
 * Function taken from {@link CBuxusInputType} class to get list of all options for
 * a select, checkbox group and possibly also another Buxus properties.
 * Need to be placed here so it can be used in template functions.
 *
 * @see CBuxusInputType::getOptions()
 * @param string $options SQL query or array definition (ID => NAME)
 * @param bool $getOptionsFromSql tells whether there is an SQl query in $options param (true) or array definition (false)
 * @return array of all options in associative form ID => NAME
 */
function getOptions($options, $getOptionsFromSql = false)
{
	//sql command in the <options></options> tag
	if ($getOptionsFromSql === true)
	{
		$sql = cleanStringForEval($options);

		$data = BuxusDB::get()->fetchAll($sql);
		$options = Array();

		foreach($data as $row)
		{
			$options[$row["ID"]] = $row["NAME"];
		}
	}
	else	//options are nominated in the tag
	{
		$phpCode = cleanStringForEval($options);

		if (!preg_match("/^\(.*\)$/", $phpCode))
		{
			$phpCode = "(" . $phpCode . ")";
		}

		eval("\$options = Array".$phpCode.";");
	}

	return $options;
}

/**
 * Function taken from {@link CBuxusInputType} class to clear a option string of
 * a select, checkbox group and possibly also another Buxus properties before it is parsed.
 * Need to be placed here so it can be used in template functions.
 *
 * @see CBuxusInputType::cleanStringForEval()
 * @param string $string of unparsed options
 * @return string with replaced HTML special chars back to normal characters "<" and  ">"
 */
function cleanStringForEval($string)
{
	$string = str_replace("&lt;", "<", $string);
	$string = str_replace("&gt;", ">", $string);

	return $string;
}

function GetUserName($user_id) {
    if ($user_id != '') {
        $username = BuxusDB::get()->fetchRow("SELECT user_name FROM tblUsers WHERE user_id = :user_id", array(':user_id' => $user_id));
        if (empty($username)) {
            $result = -1;
        } else {
            $result = $username;
        }
    } else {
      $result = '';
    }
    return $result;
}
