<?php
//this file contains the definition od the class Navigator
//and some supporting routines

class Navigator
{
    var $expanded_pages;
    var $closed_pages;
    var $closed_types;
    var $collapsed;

    var $image_path;
    var $show_checkboxes;

    var $limit_used;
    var $limit_value;
    var $limits;

    var $show_mode;
    var $action_mode;

    var $pos_num;
    var $search;
    var $search_phrase;


    protected $tinymce_target_element;

    protected $group_by_type = true;

    //structure:
    //limits["page_id"]["type_id"]["start"]
    //limits["page_id"]["type_id"]["end"]


    //if this one is set, we put anchor on type
    var $put_anchor_on_type_id = null;
    //if only this one, we put anchor on page
    var $put_anchor_on_page_id = null;

    var $sort;

    var $user_id;

    /**
     * Page types which will show in navigator
     *
     * @var array
     */
    protected $selected_page_types = array();

    /**
     * if true user rights from tblAccessRightsPageUser are ignored in navigator
     *
     * @var bool
     */
    protected $ignore_user_rights = false;

    function GetSubmitFormStart($form_action)
    {
        echo "<form name='navigator_submit_form' method='post' action='" . $form_action . "'>";

    }

    /**
     * Returns true, if the navigator is in insert mode,
     * i.e. it is called from the DHTMl editor or
     * common.
     *
     * @return bool true or false
     */
    function isInsertMode()
    {
        return in_array($this->show_mode, Array(NAV_SM_INSERT_LINK_IMAGE, NAV_SM_INSERT_LINK_MULTI, NAV_SM_INSERT_LINK_SINGLE));
    }

    function GetSubmitFormEnd()
    {
        echo "</form>";
    }

    function GetPageTitle()
    {
        switch ($this->show_mode) {
            case NAV_SM_PAGES :
                return __bx('legacy-base::navigator.Page_Tree');
            case NAV_SM_TEMPLATES :
                return __bx('legacy-base::navigator.Template_Tree');
            case NAV_SM_SYSTEM_OPTIONS :
                return __bx('legacy-base::navigator.System_Option_Tree');
            case NAV_SM_ITEMS :
                return __bx('legacy-base::navigator.Item_Tree');
            case NAV_SM_ALL :
                return __bx('legacy-base::navigator.Complete_Tree');
        }
    }

    function GetMenuType()
    {
        if ($this->action_mode == NAV_AM_ADMIN) {
            return C_MenuType_Admin;
        }

        switch ($this->show_mode) {
            case NAV_SM_INSERT_LINK_MULTI :
            case NAV_SM_PAGES :
                return C_MenuType_Pages;
            case NAV_SM_TEMPLATES :
                return C_MenuType_Templates;
            case NAV_SM_SYSTEM_OPTIONS :
                return C_MenuType_Admin;
            case NAV_SM_ITEMS :
                return C_MenuType_Item;
            case NAV_SM_ALL :
                return C_MenuType_None;
        }
    }

    function GetPageCaptionCore()
    {
        switch ($this->show_mode) {
            case NAV_SM_PAGES :
                return __bx('legacy-base::navigator.Page_Tree_Caption');
            case NAV_SM_TEMPLATES :
                return __bx('legacy-base::navigator.Template_Tree_Caption');
            case NAV_SM_SYSTEM_OPTIONS :
                return __bx('legacy-base::navigator.System_Option_Tree_Caption');
            case NAV_SM_ITEMS :
                return __bx('legacy-base::navigator.Items_Tree_Caption');
            case NAV_SM_ALL :
                return __bx('legacy-base::navigator.Complete_Tree_Caption');
        }
    }

    function GetObjectName()
    {
        switch ($this->show_mode) {
            case NAV_SM_PAGES :
                return __bx('legacy-base::navigator.Nav_Page');
            case NAV_SM_TEMPLATES :
                return __bx('legacy-base::navigator.Nav_Template');
            case NAV_SM_SYSTEM_OPTIONS :
                return __bx('legacy-base::navigator.Nav_System_Option');
            case NAV_SM_ITEMS :
                return __bx('legacy-base::navigator.Nav_Item');
            case NAV_SM_ALL :
                return __bx('legacy-base::navigator.Nav_Page');
        }
    }

    function GetPageCaption()
    {
        return __bx('legacy-base::navigator.PageCaption');
    }

    function GetPageInfo($page_id)
    {
        $sql_qry = "
				SELECT
					page_id,
					page_name
				FROM
					tblPages
				WHERE
					page_id = :page_id";

        return BuxusDB::get()->fetchRow($sql_qry, array(':page_id' => $page_id));
    }

    function DrawPlus()
    {
        return '<img alt="' . __bx('legacy-base::navigator.Collapse_Tree') . '" src="/buxus/dynamic-assets/buxus/core/images/plus-seda.png" />';
    }

    function DrawMinus()
    {
        return '<img alt="' . __bx('legacy-base::navigator.Collapse_Tree') . '" src="/buxus/dynamic-assets/buxus/core/images/minus-seda.png" />';
    }

    function ExpandOneLevel()
    {
        //this function expands the trees one level more
        //on all levels, so to say:))


        $copy = $this->expanded_pages;

        $ids = BuxusDB::get()->quoteInto('?', $copy);

        $roots = $this->GetAccessibleRoots($this->user_id);

        reset($roots);

        while ($ep = current($roots)) {
            $this->ExpandPage($ep);
            next($roots);
        }

        if (count($copy) > 0) {
            while ($ep = current($copy)) {
                $sql = "SELECT
							page_id
						FROM
							tblPages
						WHERE
							parent_page_id = :parent_page_id
						AND
							page_id NOT IN (" . $ids . ")";

                $data = BuxusDB::get()->fetchCol($sql, array(':parent_page_id' => $ep));

                reset($data);

                while ($pid = current($data)) {
                    $this->ExpandPage($pid);

                    next($data);
                }

                next($copy);
            }
        }

    }

    function DrawExpansionControls()
    {
        $html = "";
        $html .= '<div class="nav_tools">';

        /*if (in_array($this->show_mode, array(0, 1, 2, 8)))
        {
            switch ($this->show_mode)
            {
                case 0 :
                    $search_link = config('buxus_core.base_url') . "system/search/search.php";
                    break;

                case 1 :
                    $search_link = config('buxus_core.base_url') . "system/page_search.php?pageSubtree=" . C_maintemplate_id;
                    break;

                case 2 :
                    $search_link = config('buxus_core.base_url') . "system/page_search.php?pageSubtree=" . C_mainitem_id;
                    break;

                case 8 :
                    $search_link = config('buxus_core.base_url') . "system/page_search.php";
                    break;
            }
            $html .= '<img src="' . config('buxus_core.base_url') . 'system/images/search.gif" /> ';
            $html .= "<a href='" . $search_link . "'>" . __bx('legacy-base::navigator.Search') . "</a>&nbsp;&nbsp;&nbsp;&nbsp;";
        }*/

        $html .= "<img src='/buxus/dynamic-assets/buxus/core/images/plus.png' /> ";
        $html .= "<a href='" . $this->TNL("expand_one_level=1") . "'>" . __bx('legacy-base::navigator.Expand') . "</a>&nbsp;&nbsp;";
        $html .= "<img src='/buxus/dynamic-assets/buxus/core/images/minus.png' /> ";
        $html .= "<a href='" . $this->TNL("collapse_all=1") . "'>" . __bx('legacy-base::navigator.Collapse_all') . "</a>";

        $html .= '&nbsp;&nbsp;' . __bx('legacy-base::navigator.zoskupit_podla_') . '&nbsp;';

        if ($this->group_by_type) {
            $html .= __bx('legacy-base::navigator.typov');
            $html .= '&nbsp;/&nbsp;';
            $html .= '<a href="' . $this->TNL("group_by_type=0") . '">' . __bx('legacy-base::navigator.hierarchie') . '</a>';
        } else {
            $html .= '<a href="' . $this->TNL("group_by_type=1") . '">' . __bx('legacy-base::navigator.typov') . '</a>';
            $html .= '&nbsp;/&nbsp;';
            $html .= __bx('legacy-base::navigator.hierarchie');
        }


        $html .= '</div>';


        $html .= '
            <style>.jump-target::before {content:"";display:block;height:62px;margin:-62px 0 0;}</style>
        ';

        return $html;
    }

    function DrawConfiguration($form_action = "", $navigatorWidth = '100%')
    {
        $html = "";

        $nodes = $this->GetAccessibleRoots($this->user_id);

        if ($this->group_by_type) {
            $used_type_ids = array();

            foreach ($nodes as $node) {
                $this->expanded_pages = array_unique($this->expanded_pages);

                $info = $this->CreateTypeInfo($node);

                if (empty($info)) {
                    continue;
                }

                $node_html = '';

                //parent_page_id of this node is returned
                //as "root_page_id", since this is a root
                //of this block
                if (in_array($info ["page_type_id"] . $info ["root_page_id"], $used_type_ids)) {
                    continue;
                } else {
                    array_push($used_type_ids, $info ["page_type_id"] . $info ["root_page_id"]);

                    $node_html .= "<table cellspacing='0' cellpadding='0' class='nav-listBorder' width='" . $navigatorWidth . "' border='0'>";

                    $in_table = true;
                }

                $node_html .= "";

                List ($sub_html, $count) = $this->DrawTypeTree($info);

                $node_html .= $sub_html;

                $node_html .= "</table><br>";

                $in_table = false;

                if (!empty($sub_html)) {
                    $html .= $node_html;
                }
            }
        } else {
            foreach ($nodes as $node) {
                $this->expanded_pages = array_unique($this->expanded_pages);

                $info = $this->CreateTypeInfo($node);

                if (empty($info)) {
                    continue;
                }

                $html .= "<table cellspacing='0' cellpadding='0' class='nav-listBorder' width='" . $navigatorWidth . "' border='0'>";

                $html .= "";

                List ($sub_html, $count) = $this->DrawHierarchyEntry($info);//$this->DrawPageTree_GroupByHierarchy($info);

                $html .= $sub_html;

                $html .= "</table><br>";
            }
        }

        return $html;
    }

    function DrawSearchConfiguration($search_phrase)
    {
        $encoding = GetSystemOption("C_default_charset");

        if (mb_strtoupper($encoding) == "UTF-8") {

        }

        $this->search_phrase = $search_phrase;
        $this->search = true;

        $type = $this->CreateSearchType();

        $html = "<table cellspacing='0' cellpadding='0' class='nav-listBorder' style='border-right:1px black solid;' width='100%' border='0'>";

        List ($sub_html, $count) = $this->DrawTypeTree($type);

        $html .= $sub_html;

        $html .= "</table>";

        return $html;
    }

    /**
     * Since version 5.5 this function is no longer used because the search box was moved to the top menu.
     *
     * @deprecated
     * @return string
     */
    function GetSearchForm()
    {
        $html = "<form action='" . $this->self_link . "' method='get'>";
        $html .= $this->TNLForm();
        $html .= "<span class='blue'>" . __bx('legacy-base::navigator.Search_Phrase') . ":</span>&nbsp;" . HTML_TextField("search_phrase", $this->search_phrase,
                C_HTML_TextFieldSize, "", "nav_search_field");
        $html .= "&nbsp;" . HTML_SubmitButton("submit", __bx('legacy-base::navigator.Search')) . "<br>";
        $html .= HTML_HiddenField("search_mode", "T");

        $html .= '<a href="search/search.php">' . __bx('legacy-base::navigator.AdvancedSearch') . '</a><br />';

        return $html;
    }

    function Search($search_phrase, $page_classes)
    {
        //include only pages enlisted in page_classes
        if (count($page_classes) == 1) {
            $page_class_string = " AND page_class_id = " . BuxusDB::get()->quote($page_classes[0]) . " ";
        } else {
            if (count($page_classes) > 0) {
                $page_class_string = " AND page_class_id IN (" . BuxusDB::get()->quoteInto('?', $page_classes) . ") ";
            } else {
                //include all pages
                $page_class_string = "";
            }
        }

        if (empty($this->selected_page_types)) { // The selected page types are not defined
            $page_type_condition = "";
            $fulltext_page_type_condition = "tblPages.page_type_id IS NOT NULL";
        } else { // The selected page types are defined
            $page_type_condition = "AND page_type_id IN (" . BuxusDB::get()->quoteInto('?', $this->selected_page_types) . ")";
            $fulltext_page_type_condition = "tblPages.page_type_id IN (" . BuxusDB::get()->quoteInto('?', $this->selected_page_types) . ")";
        }

        $fulltextSearch = Buxus_Search_Manager::getInstance()->getSearch(Buxus_Search_Manager::SEARCH_FULLTEXT_BUXUS_PAGES);
        /**
         * @var Buxus_Search_Result $searchFulltextResult
         */
        $searchFulltextResult = $fulltextSearch->search($this->search_phrase);
        $pageIds = [];
        foreach ($searchFulltextResult as $res) {
            $pageIds[] = $res->entity_id;
        }

        if (count($pageIds)) {
            $pages = \BuxusDB::get()->fetchAll("SELECT DISTINCT
                    tblPages.page_id AS page_id,
                    tblPages.creation_date AS creation_date,
                    tblPages.last_updated AS last_updated,
                    tblPages.sort_date_time AS sort_date_time,
                    tblPages.page_name AS page_name,
                    tblPages.page_state_id AS page_state_id
                FROM
                    tblPages
                WHERE
                    tblPages.page_id IN (" . implode(',', $pageIds) . ")" .
                (!empty($fulltext_page_type_condition) ? ' AND ' . $fulltext_page_type_condition : '')

            );
        } else {
            $pages = null;
        }

        if (!empty($pages)) { // Limit result for root pages
            $rootNodeOverride = $this->getRootNodeOverride();
            if (!empty($rootNodeOverride)) { // The root page is not inserted


                /**
                 * Parents, which could be or not shown (parent_page_id => bool)
                 */
                $root_parants = array();
                foreach ($rootNodeOverride as $root_parent_id) {
                    $root_parants [$root_parent_id] = true;
                }

                $new_page_list = array();
                foreach ($pages as $page_info) {
                    $page_id = $page_info ['page_id'];
                    $parent_page_id = $page_info ['parent_page_id'];
                    if (array_key_exists($page_id, $root_parants)) { // The page has been search before
                        $add_page = $root_parants [$page_id];
                    } else {
                        if (array_key_exists($parent_page_id, $root_parants)) { // The parent has been search before
                            $add_page = $root_parants [$parent_page_id];
                        } else { // The parent has not serach yet
                            $parents_stack = array($parent_page_id);
                            $add_page = false;
                            do {
                                $parent_page_id = GetParentPageID($parent_page_id);
                                if ($parent_page_id == -1) { // It is the homepage
                                    $add_page = false;
                                    break;
                                } else {
                                    if (array_key_exists($parent_page_id, $root_parants)) { // It has been known parent yet
                                        $add_page = $root_parants [$parent_page_id];
                                        break;
                                    } else { // It has not been known parent yet
                                        $parents_stack [] = $parent_page_id;
                                    }
                                }
                            } while (!array_key_exists($parent_page_id, $root_parants));

                            // Set flag to parents
                            foreach ($parents_stack as $parent_page_id) {
                                $root_parants [$parent_page_id] = $add_page;
                            }
                        }
                    }
                    if ($add_page) { // The page should be added
                        $new_page_list [] = $page_info;
                    }
                }
                $pages = $new_page_list;
            }
        }

        return $pages;
    }

    /**
     * Get search table head HTML first part
     *
     * @return string html
     */
    protected function getSearchTableHeadHtmlBegin()
    {
        $html = "<table cellspacing='0' cellpadding='0' style='border:1px black solid;' width='100%'>";

        $html .= "<tr><td class='nav-tn-exp' colspan='2'>" . "&nbsp;<a class='nav-tn' href='" . $this->TNL("back_to_tree=T") . "'>" . __bx('legacy-base::navigator.Back_To_Tree') . "</a>";

        return $html;
    }

    /**
     * Get search table foot HTML and paging
     *
     * @param string $search_phrase
     * @param int $pages_count all results count
     * @param int $start shown results start index
     * @param int $end shown results end index
     */
    protected function getSearchTableFootHtml($search_phrase, $pages_count, $start, $end)
    {
        $page_id = "SEARCH";
        $type_id = "SEARCH";

        $search_phrase_link = ((empty($search_phrase)) ? '' : '&amp;search_phrase=' . $search_phrase);

        $html = '';

        if ($this->limit_used && ($pages_count > $end || $start > 0)) {
            $html .= "<tr><td colspan='3'>";
            $html .= "<table cellspacin='0' cellpadding='0' style='border-top:1px black solid;' width='100%'>";
            $html .= "<tr><td width='100%'>";

            if ($start > 0) {
                $html .= "<a class='nav-pn-A' href='" . $this->TNL("search_mode=T&amp;move_up_on_page=$page_id&amp;move_up_type=$type_id&amp;move_start=1" . $search_phrase_link . "&amp;s=" . mb_substr(md5(time()),
                            0, 4) . "#pos") . "'>" . __bx('legacy-base::navigator.Move_Top') . "</a>";
                $html .= "&nbsp;&nbsp;<a class='nav-pn-A' href='" . $this->TNL("search_mode=T&amp;move_up_on_page=$page_id&amp;move_up_type=$type_id" . $search_phrase_link . "&amp;s=" . mb_substr(md5(time()),
                            0, 4) . "#pos") . "'>" . __bx('legacy-base::navigator.Move_Up') . "</a>";
            }

            if ($pages_count > $end) {
                $html .= "&nbsp;&nbsp;<a class='nav-pn-A' href='" . $this->TNL("search_mode=T&amp;move_down_on_page=$page_id&amp;move_down_type=$type_id" . $search_phrase_link . "&amp;s=" . mb_substr(md5(time()),
                            0, 4) . "#pos") . "'>" . __bx('legacy-base::navigator.Move_Down') . "</a>";
                $html .= "&nbsp;<a class='nav-pn-A' href='" . $this->TNL("search_mode=T&amp;move_down_on_page=$page_id&amp;move_down_type=$type_id&amp;move_end=1" . $search_phrase_link . "&amp;s=" . mb_substr(md5(time()),
                            0, 4) . "#pos") . "'>" . __bx('legacy-base::navigator.Move_Bottom') . "</a>";
            }

            //$html .= "&nbsp;&nbsp;<a class='nav-pn' href='navigator.php?move_down_on_page=$page_id&amp;move_down_type=$type_id&amp;move_end=1&amp;s=".mb_substr(md5(time()),0,4)."#pos'>".__bx('legacy-base::navigator.Move_Bottom')."</a>";
            $html .= "</td></tr></table>";
            $html .= "</td></tr>";
        }

        $html .= "</table>";

        return $html;
    }

    /**
     * Get search table head HTML end part
     *
     * @return string html
     */
    protected function getSearchTableHeadHtmlEnd()
    {
        $html = "</td>";

        if ($this->search_phrase) {
            $html .= "<td class='nav-tn-exp'>" . __bx('legacy-base::navigator.PageType') . "</td>";
        }

        $html .= "</tr>";

        return $html;
    }

    /**
     * Apply limits on result
     *
     * @param array $pages
     * @return array
     */
    protected function applyLimits($pages)
    {
        if ($this->limit_used) {
            $this->limits ["SEARCH"] ["SEARCH"] ["count"] = count($pages);

            if (!is_array($this->limits ["SEARCH"] ["SEARCH"]) || empty($this->limits ["SEARCH"] ["SEARCH"] ["start"])) {
                $this->limits ["SEARCH"] ["SEARCH"] ["start"] = 0;
                $this->limits ["SEARCH"] ["SEARCH"] ["end"] = $this->limit_value;
            }

            $pages = $this->cutPages($pages, $this->limits ["SEARCH"] ["SEARCH"] ["start"], $this->limits ["SEARCH"] ["SEARCH"] ["end"]);
        }

        return $pages;
    }

    public function DrawSearchTable($search_phrase, $user_id, $pageListProperty = null)
    {
        if (!empty($search_phrase)) {
            if ($this->limit_used && $this->search_phrase != $search_phrase) {
                $this->limits ["SEARCH"] ["SEARCH"] ["start"] = 0;
                $this->limits ["SEARCH"] ["SEARCH"] ["end"] = $this->limit_value;
            }

            $this->search_phrase = $search_phrase;

        }

        $html = $this->getSearchTableHeadHtmlBegin();

        $html .= "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;" . __bx('legacy-base::navigator.Sort_By') . ":&nbsp;&nbsp;";

        if (!isset($this->sort ["SEARCH"] ["SEARCH"] ["field"])) {
            $this->sort ["SEARCH"] ["SEARCH"] ["field"] = "sort_date_time";
            $this->sort ["SEARCH"] ["SEARCH"] ["dir"] = "DESC";
        }

        if ($this->sort ["SEARCH"] ["SEARCH"] ["field"] == "page_name") {
            $change_sort_field = "sort_date_time";
        } else {
            $change_sort_field = "page_name";
        }

        if ($change_sort_field == "page_name") {
            $page_name_sort_link = "<a class='nav-tn' style='text-decoration:underline' href='" . $this->TNL("change_search_sort=" . $change_sort_field . "&amp;search_mode=T") . "'>" . __bx('legacy-base::navigator.Alphabetically') . "</a>";
        } else {
            $page_name_sort_link = __bx('legacy-base::navigator.Alphabetically');
        }

        if ($change_sort_field == "sort_date_time") {
            $date_sort_link = "<a class='nav-tn' style='text-decoration:underline' href='" . $this->TNL("change_search_sort=" . $change_sort_field . "&amp;search_mode=T") . "'>" . __bx('legacy-base::navigator.By_Date') . "</a>";
        } else {
            $date_sort_link = __bx('legacy-base::navigator.By_Date');
        }

        $html .= $page_name_sort_link . "&nbsp;&nbsp;";

        $html .= $date_sort_link;

        $html .= $this->getSearchTableHeadHtmlEnd();

        $pages = $this->Search($search_phrase, $this->GetSearchClasses());

        $pages = $this->addAccessRightsSearch($pages);

        $pages = $this->addPageTypeSearch($pages);

        $pages_count = count($pages);

        $index = 0;

        $pages = $this->applyLimits($pages);

        while ($page = array_shift($pages)) {
            $page ['is_search'] = true;
            $html .= $this->GetPageLink($page);
        }

        $html .= $this->getSearchTableFootHtml($search_phrase, $pages_count, $this->limits ["SEARCH"] ["SEARCH"] ["start"],
            $this->limits ["SEARCH"] ["SEARCH"] ["end"]);

        return $html;
    }

    function GetSearchClasses()
    {
        switch ($this->show_mode) {
            case NAV_SM_INSERT_LINK_SINGLE :
            case NAV_SM_INSERT_LINK_IMAGE :
            case NAV_SM_INSERT_LINK_MULTI :
            case NAV_SM_PAGES :
                $retval = Array(C_pc_Web_page);
                break;
            case NAV_SM_TEMPLATES :
                $retval = Array(C_pc_Template);
                break;
            case NAV_SM_SYSTEM_OPTIONS :
                $retval = Array(C_pc_System_Option);
                break;
            case NAV_SM_ITEMS :
                $retval = Array(C_pc_Item, C_pc_ParentItem);
                break;
            case NAV_SM_ALL :
                $retval = Array(C_pc_Web_page, C_pc_Template, C_pc_System_Option, C_pc_ParentItem, C_pc_Item);
                break;
        }

        return $retval;
    }

    function CreateTypeInfo($page_id)
    {
        $parent_page_id = GetParentPageID($page_id);

        $type = BuxusDB::get()->fetchRow("SELECT
											P.page_id,
											P.page_name,
											P.page_state_id,
											PT.page_type_id,
											PT.page_type_name,
											parent_page_id AS root_page_id
										  FROM
										  	tblPages P
										  INNER JOIN
										  	tblPageTypes PT
										  ON
										  	P.page_type_id = PT.page_type_id
										  WHERE
										    page_id = :page_id", array(':page_id' => $page_id));

        return $type;
    }

    function CreateSearchType()
    {
        $type ["page_type_id"] = -1;
        $type ["page_type_name"] = __bx('legacy-base::navigator.Back_To_Tree');
        $type ["root_page_id"] = "NULL";

        return $type;
    }

    function overrideRoots($rootNodeOverride)
    {
        if (!is_array($rootNodeOverride)) {
            $rootNodeOverride = array($rootNodeOverride);
        }

        $sql = "SELECT
						DISTINCT page_type_id,
						page_id
					FROM
						tblPages
					WHERE
						page_type_id IS NOT NULL
					AND
						parent_page_id IN (" . BuxusDB::get()->quoteInto('?', $rootNodeOverride) . ")";

        $roots = BuxusDB::get()->fetchAll($sql);

        $rootIDs = Array();

        reset($roots);

        while ($rootRow = array_shift($roots)) {
            array_push($rootIDs, $rootRow ["page_id"]);
        }

        return $rootIDs;
    }

    /**
     * set ignore_user_rights
     *
     * @param bool $ignore_user_rights
     */
    public function setIgnoreUserRights($ignore_user_rights = true)
    {
        $this->ignore_user_rights = $ignore_user_rights;
    }

    function GetAccessibleRoots($user_id)
    {
        global $rootNodeOverride;

        $rootNodeOverride = $this->getRootNodeOverride();

        $type_sql = '';

        //if admin, return root page #1
        if (\Buxus\User\Facades\BuxusUserManager::getCurrentUser()->hasAdminRights()) {
            switch ($this->show_mode) {
                case NAV_SM_INSERT_LINK_MULTI :
                    if (!empty($rootNodeOverride)) {
                        return $rootNodeOverride;

                        //return $this->overrideRoots($rootNodeOverride);
                    }
                case NAV_SM_INSERT_LINK_SINGLE :
                case NAV_SM_INSERT_LINK_IMAGE :
                case NAV_SM_PAGES :
                    return Array(C_rootpage_id);
                case NAV_SM_TEMPLATES :
                    return Array(C_maintemplate_id);
                case NAV_SM_SYSTEM_OPTIONS :
                    return Array();
                case NAV_SM_ITEMS :
                    return Array(C_mainitem_id);
                case NAV_SM_ALL :
                    return array(C_rootpage_id/*, C_maintemplate_id, C_mainitem_id*/);
            }
        } elseif ($this->isInsertMode()) {

            switch ($this->show_mode) {
                case NAV_SM_INSERT_LINK_MULTI :
                    if (!empty($rootNodeOverride)) {
                        return $rootNodeOverride;
                    }
                case NAV_SM_INSERT_LINK_SINGLE :
                    $type_sql = " AND P.page_class_id = " . BuxusDB::get()->quote(C_pc_Web_page);
                    break;
                case NAV_SM_PAGES :
                    $type_sql = " AND P.page_class_id = " . BuxusDB::get()->quote(C_pc_Web_page);
                    break;
                case NAV_SM_TEMPLATES :
                    $type_sql = " AND P.page_class_id = " . BuxusDB::get()->quote(C_pc_Template);
                    break;
                case NAV_SM_SYSTEM_OPTIONS :
                    $type_sql = " AND P.page_class_id = " . BuxusDB::get()->quote(C_pc_System_Option);
                    break;
                case NAV_SM_ITEMS :
                    $type_sql = " AND (P.page_class_id = " . BuxusDB::get()->quote(C_pc_Item) . " OR P.page_class_id = " . BuxusDB::get()->quote(C_pc_ParentItem) . ")";
                    break;
            }
        } else {
            switch ($this->show_mode) {
                case NAV_SM_INSERT_LINK_MULTI :
                    if (!empty($rootNodeOverride)) {
                        return $rootNodeOverride;
                    }
                case NAV_SM_PAGES :
                    $type_sql = " AND P.page_class_id = " . C_pc_Web_page;
                    break;
                case NAV_SM_TEMPLATES :
                    $type_sql = " AND P.page_class_id = " . C_pc_Template;
                    break;
                case NAV_SM_SYSTEM_OPTIONS :
                    $type_sql = " AND P.page_class_id = " . C_pc_System_Option;
                    break;
                case NAV_SM_ITEMS :
                    $type_sql = " AND (P.page_class_id = " . C_pc_Item . " OR P.page_class_id = " . C_pc_ParentItem . ")";
                    break;
            }
        }

        //else find'em

        if ($this->ignore_user_rights) {
            $roots = BuxusDB::get()->fetchCol("
					SELECT
						P.page_id
					FROM
						tblPages P
					WHERE
					    P.parent_page_id IS NULL
					    AND
						P.page_type_id IS NOT NULL
					$type_sql
					");
        } else {
            $roots = BuxusDB::get()->fetchCol("
					SELECT
						P.page_id
					FROM
						tblAccessRightsPageUser ARPU
					INNER JOIN
						tblPages P
					ON
						ARPU.page_id = P.page_id
					WHERE
						P.page_type_id IS NOT NULL
					AND
						ARPU.user_id = :user_id
					AND
						ARPU.read_right = 'T'
					$type_sql
					", array(':user_id' => $user_id));
        }

        usort($roots, "Navigator::cmp_types");

        $used_roots = array();

        reset($roots);

        while ($root = current($roots)) {
            $page_id = $root;

            while (1) {
                //get the parent
                $page_id = GetParentPageID($page_id);

                //if it is what we begun with
                if ($page_id == $root) {
                    //detect cycle
                    die("DATABASE ERROR - CYCLIC PARENT PAGE ID");
                }

                //if TOP
                if ($page_id == -1 || !CanReadPage($page_id, $user_id)) {
                    //the root is not in subtree, or we have no access
                    //so we have to preserve it
                    array_push($used_roots, $root);

                    //test the next
                    next($roots);
                    break;
                }

                //if it is in the roots field, we have reached higher accessible node,
                //so no need to use this root
                if (in_array($page_id, $roots)) {
                    next($roots);
                    break;
                }
            }
        }

        return $used_roots;
    }

    public function __construct($user_id)
    {
        $this->ignore_user_rights = config('buxus_core.ignore_page_user_rights', false) || \Buxus\User\Facades\BuxusUserManager::getCurrentUser()->hasAdminRights();

        $this->show_mode = NAV_SM_ALL;
        $this->action_mode = NAV_AM_EDIT;

        $this->user_id = $user_id;

        $this->limit_value = GetSystemOption("C_page_details_select_limit");

        $this->limit_used = $this->limit_value != 0;

        $this->image_path = config('buxus_core.base_url') . "system/images/";

        //this will be saved in session, since

        $this->expanded_pages = Array();
        $this->closed_types = Array();
        $this->closed_pages = Array();

        $sql = "SELECT
					P.page_id
				FROM
					tblPageTypes PT
				INNER JOIN
					tblPages P
				ON
					PT.page_type_id = P.page_type_id
				WHERE
					PT.auto_expand = 'T'";

        $this->expanded_pages = BuxusDB::get()->fetchCol($sql);

        $this->pos_num = mb_substr(md5(rand()), 0, 10);

        $this->tinymce_target_element = (isset($_REQUEST['target']) ? $_REQUEST['target'] : 'href');
        $this->group_by_type = (isset($_REQUEST['group_by_type']) ? ($_REQUEST['group_by_type'] == 1) : true);
    }

    protected function setLimitForPagedPageId($pageId)
    {
        if (!$this->limit_used) {
            return;
        }

        $page = \PageFactory::get($pageId);

        if (!$page instanceof \Buxus\Page\PageInterface) {
            return;
        }

        // get the position for the paged value
        $pageTypeId = $page->getPageTypeId();
        $parentPageId = $page->getParentPageId();

        if (empty($parentPageId)) {
            $operator = 'IS';
            $parentPageId = 'NULL';
        } else {
            $operator = '=';
        }

        if (!isset($this->sort[$parentPageId][$pageTypeId])) {
            $this->sort[$parentPageId][$pageTypeId] = $this->getPageTypeSort($pageTypeId);
        }

        $sortField = $this->sort[$parentPageId][$pageTypeId]['field'] ?? 'page.sort_date_time';
        $sortDir = $this->sort[$parentPageId][$pageTypeId]['dir'] ?? 'desc';

        if (!$this->ignore_user_rights) {
            $sql = 'SELECT pos
                FROM
                    (SELECT
						(@pos := @pos+1) pos, page.page_id
					FROM
						tblPages page
						LEFT JOIN
							tblAccessRightsPageUser ARPU
						ON
							page.page_id = ARPU.page_id
							AND
							ARPU.user_id = :user_id
					    , (SELECT @pos := 0) p
					WHERE
						page.parent_page_id ' . $operator . ' ' . ($parentPageId === 'NULL' ? $parentPageId : BuxusDB::get()->quote($parentPageId)) . '
						AND
						page.page_type_id = :page_type_id
						AND
						page.page_type_id IS NOT NULL
					ORDER BY
						' . $sortField . ' ' . $sortDir . ',page.page_id) `sorted`
						WHERE `page_id` = :page_id';
            $queryData = [
                'page_id' => $pageId,
                'page_type_id' => $pageTypeId,
                'user_id' => $this->user_id,
            ];

        } else {
            $sql = 'SELECT pos
                FROM
                    (SELECT
						(@pos := @pos+1) pos, page.page_id
					FROM
						tblPages page
					    , (SELECT @pos := 0) p
					WHERE
						page.parent_page_id ' . $operator . ' ' . ($parentPageId === 'NULL' ? $parentPageId : BuxusDB::get()->quote($parentPageId)) . '
						AND
						page.page_type_id = :page_type_id
						AND
						page.page_type_id IS NOT NULL
					ORDER BY
						' . $sortField . ' ' . $sortDir . ', page.page_id) `sorted`
						WHERE `page_id` = :page_id';
            $queryData = [
                'page_id' => $pageId,
                'page_type_id' => $pageTypeId,
            ];
        }

        $value = \DB::select($sql, $queryData);

        $position = (isset($value[0]) ? (int)$value[0]->pos : 0);
        if ($position > 0) {
            $position--;
        }
        $start = intdiv($position, $this->limit_value) * $this->limit_value;

        $this->limits[$parentPageId][$pageTypeId]['start'] = $start;
        $this->limits[$parentPageId][$pageTypeId]['end'] = $start + $this->limit_value;
    }

    function openPathForPageID($ID)
    {
        while ($ID != -1) {
            $this->setLimitForPagedPageId($ID);
            $ID = GetParentPageID($ID);

            if ($ID == '') {
                $ID = -1;
            }

            if ($ID != -1) {
                array_push($this->expanded_pages, $ID);
            }
        }

        $closed = [];
        if (!is_array($this->closed_pages)) {
            $this->closed_pages = [];
        }
        foreach ($this->closed_pages as $closed_page_id) {
            if (!in_array($closed_page_id, $this->expanded_pages)) {
                $closed[] = $closed_page_id;
            }
        }
        $this->closed_pages = $closed;
    }

    function GetPageClass($page_id)
    {
        $sql = "SELECT
						page_class_id
					FROM
						tblPages
					WHERE
						page_id = :page_id";

        $class_id = BuxusDB::get()->fetchOne($sql, array(':page_id' => $page_id));

        return $class_id;
    }

    function SetShowMode()
    {
        if ($this->openedPageID != null) {
            $this->show_mode = NAV_SM_ALL;

            return;
        }
    }

    function getPageChildrenCount($page_id)
    {
        $admin_right = \Buxus\User\UserUtils::getGlobalUserRight($this->user_id, "admin_right");


        if ($this->ignore_user_rights || $admin_right == 'T') {
            $sql = 'SELECT
						P.page_id
					FROM
						tblPages P
					WHERE
						P.parent_page_id = :parent_page_id
					AND
						page_type_id IS NOT NULL';

            return BuxusDB::get()->fetchAll($sql, [':parent_page_id' => $page_id]);
        }

        //read the pages along with their rights
        $sql = "SELECT
						P.page_id,
						ARPU.read_right
					FROM
						tblPages P
						LEFT JOIN
						tblAccessRightsPageUser ARPU
						ON P.page_id = ARPU.page_id
						AND ARPU.user_id = :user_id
					WHERE
						P.parent_page_id = :parent_page_id
					AND
						page_type_id IS NOT NULL";

        $pages = BuxusDB::get()->fetchAll($sql, array(':user_id' => $this->user_id, ':parent_page_id' => $page_id));

        //the rights of the parent page
        $page_rights = getUserRightsForPage($this->user_id, $page_id);

        $newPages = Array();

        foreach ($pages as $page) {
            if ($admin_right == "T" || (($page_rights['read_right'] == C_True_Char && empty($page["read_right"])) || $page["read_right"] == C_True_Char)) {
                array_push($newPages, $page);
            }
        }

        return $newPages;
    }

    function expandPage($page_id)
    {
        if (count($this->getPageChildrenCount($page_id)) > 0) {
            array_push($this->expanded_pages, $page_id);
            array_unique($this->expanded_pages);

            $new_array = Array();

            reset($this->closed_pages);

            while ($closed_page = current($this->closed_pages)) {
                if ($closed_page != $page_id) {
                    array_push($new_array, $closed_page);
                }

                next($this->closed_pages);
            }

            $this->closed_pages = $new_array;
        }
    }

    function UpdateState()
    {
        global $expand_one_level, $collapse_all;
        if (isset($_REQUEST['expand_one_level'])) {
            $expand_one_level = $_REQUEST['expand_one_level'];
        }
        if (isset($_REQUEST['collapse_all'])) {
            $collapse_all = $_REQUEST['collapse_all'];
        }

        if ($expand_one_level == 1) {
            $this->ExpandOneLevel();
        }

        if ($collapse_all == 1) {
            $this->CollapseAll("");
        }

        global $open_page_id, $open_subpages;
        if (isset($_REQUEST['open_page_id'])) {
            $open_page_id = $_REQUEST['open_page_id'];
        }
        if (isset($_REQUEST['open_subpages'])) {
            $open_subpages = $_REQUEST['open_subpages'];
        }

        $this->collapsed = false;

        if (is_numeric($open_page_id)) {
            $this->openPathForPageID($open_page_id);
            $this->openedPageID = $open_page_id;

            if ($open_subpages === 'T') {
                $this->expandPage($open_page_id);
            }
        } else {
            $this->openedPageID = null;
        }

        global $show_mode, $action_mode;
        if (isset($_REQUEST['show_mode'])) {
            $show_mode = $_REQUEST['show_mode'];
        }
        if (isset($_REQUEST['action_mode'])) {
            $action_mode = $_REQUEST['action_mode'];
        }

        if (isset($show_mode)) {
            $this->show_mode = $show_mode;

            if (isset($action_mode)) {
                $this->action_mode = $action_mode;
            } else {
                if ($this->show_mode != NAV_SM_ALL && $this->show_mode != NAV_SM_SYSTEM_OPTIONS) {
                    $this->action_mode = NAV_AM_ADMIN;
                } else {
                    $this->action_mode = NAV_AM_EDIT;
                }
            }
        } else {
            if (empty($this->show_mode) || !empty($this->openedPageID)) {
                $this->SetShowMode();

                if (empty($this->action_mode) || !empty($this->openedPageID)) {
                    $this->action_mode = NAV_AM_EDIT;
                }
            }
        }

        global $search_phrase, $self_link;
        if (isset($_REQUEST['search_phrase'])) {
            $search_phrase = $_REQUEST['search_phrase'];
        }
        if (isset($_REQUEST['self_link'])) {
            $self_link = $_REQUEST['self_link'];
        }

        $this->search = false;

        $this->self_link = "/buxus/system/navigator.php";

        if ($this->show_mode == NAV_SM_INSERT_LINK_MULTI) {
            if (!empty($search_phrase)) {
                $this->search = true;
                $this->search_phrase = $search_phrase;
            }

            $this->show_checkboxes = true;

            $this->self_link = "insert_buxus_link.php";

        } else {
            if ($this->show_mode == NAV_SM_INSERT_LINK_SINGLE || $this->show_mode == NAV_SM_INSERT_LINK_IMAGE) {
                $this->self_link = "insert_buxus_link.php";

                if (!empty($search_phrase)) {
                    $this->search = true;
                    $this->search_phrase = $search_phrase;
                }

                $this->show_checkboxes = false;

                Session::forget('buxus.a_id');
                Session::forget('buxus.a_class');
                Session::forget('buxus.a_target');
            } elseif ($this->show_mode == NAV_SM_GET_PAGE_ID_FORM) {
                $this->self_link = "pageSelector.php";

                if (!empty($search_phrase)) {
                    $this->search = true;
                    $this->search_phrase = $search_phrase;
                }

                $this->show_checkboxes = false;

                Session::forget('buxus.a_id');
                Session::forget('buxus.a_class');
                Session::forget('buxus.a_target');
            } else {
                $this->show_checkboxes = false;
                $this->self_link = "/buxus/system/navigator.php";

                if (!empty($search_phrase)) {
                    $this->search = true;
                    $this->search_phrase = $search_phrase;
                }

                Session::forget('buxus.a_id');
                Session::forget('buxus.a_class');
                Session::forget('buxus.a_target');
            }
        }

        if (!empty($self_link)) {
            $this->self_link = $self_link;
        }

        $this->image_path = config('buxus_core.base_url') . "system/images/";

        $this->pos_num = mb_substr(md5(rand()), 0, 10);

        global $change_sort_on_page, $change_sort_on_type, $sort_field;
        if (isset($_REQUEST['change_sort_on_page'])) {
            $change_sort_on_page = $_REQUEST['change_sort_on_page'];
        }
        if (isset($_REQUEST['change_sort_on_type'])) {
            $change_sort_on_type = $_REQUEST['change_sort_on_type'];
        }
        if (isset($_REQUEST['sort_field'])) {
            $sort_field = $_REQUEST['sort_field'];
        }

        if (!empty($change_sort_on_page)) {
            $this->sort [$change_sort_on_page] [$change_sort_on_type] ["field"] = $sort_field;

            if ($sort_field == "page_name") {
                $dir = "ASC";
            } else {
                $dir = "DESC";
            }

            $this->sort [$change_sort_on_page] [$change_sort_on_type] ["dir"] = $dir;

            $this->openedPageID = null;
            $this->put_anchor_on_page_id = $change_sort_on_page;
        }

        //print_r($this->sort);


        global $close_type, $close_from_page;
        if (isset($_REQUEST['close_type'])) {
            $close_type = $_REQUEST['close_type'];
        }
        if (isset($_REQUEST['close_from_page'])) {
            $close_from_page = $_REQUEST['close_from_page'];
        }

        $this->close_type = $close_type;
        $this->close_from_page = $close_from_page;

        if (is_numeric($close_type)) {
            if (!isset($this->closed_types [$close_from_page]) || !is_array($this->closed_types[$close_from_page])) {
                $this->closed_types[$close_from_page] = array($close_type);
            } else {
                array_push($this->closed_types[$close_from_page], $close_type);
            }

            $this->put_anchor_on_type_id = $close_type;
            $this->put_anchor_on_page_id = $close_from_page;
        }

        global $expand_type, $expand_from_page;
        if (isset($_REQUEST['expand_type'])) {
            $expand_type = $_REQUEST['expand_type'];
        }
        if (isset($_REQUEST['expand_from_page'])) {
            $expand_from_page = $_REQUEST['expand_from_page'];
        }

        $this->expand_type = $expand_type;
        $this->expand_from_page = $expand_from_page;

        if (is_numeric($expand_type)) {
            $this->ExpandType($expand_type, $expand_from_page);

            $this->put_anchor_on_type_id = $expand_type;
            $this->put_anchor_on_page_id = $expand_from_page;
        }

        global $expand_page;
        if (isset($_REQUEST['expand_page'])) {
            $expand_page = $_REQUEST['expand_page'];
        }

        $this->expand_page = $expand_page;

        if (!empty($expand_page)) {
            $this->expandPage($expand_page);
            $this->put_anchor_on_page_id = $expand_page;
        }

        global $close_page;
        if (isset($_REQUEST['close_page'])) {
            $close_page = $_REQUEST['close_page'];
        }

        $this->close_page = $close_page;

        if (!empty($close_page)) {
            $this->ClosePage($close_page);

            $this->put_anchor_on_page_id = $close_page;
        }

        $expanded_pages = $this->expanded_pages;
        $closed_types = $this->closed_types;

        global $move_down_on_page, $move_down_type, $move_end;
        if (isset($_REQUEST['move_down_on_page'])) {
            $move_down_on_page = $_REQUEST['move_down_on_page'];
        }
        if (isset($_REQUEST['move_down_type'])) {
            $move_down_type = $_REQUEST['move_down_type'];
        }
        if (isset($_REQUEST['move_end'])) {
            $move_end = $_REQUEST['move_end'];
        }

        if (!empty($move_down_on_page)) {
            if ($move_end == 1) {
                $this->limits [$move_down_on_page] [$move_down_type] ["start"] = $this->limits [$move_down_on_page] [$move_down_type] ["count"] - $this->limit_value;
                $this->limits [$move_down_on_page] [$move_down_type] ["end"] = $this->limits [$move_down_on_page] [$move_down_type] ["count"];
            } else {
                $this->limits [$move_down_on_page] [$move_down_type] ["start"] += $this->limit_value;
                $this->limits [$move_down_on_page] [$move_down_type] ["end"] += $this->limit_value;

                if ($this->limits [$move_down_on_page] [$move_down_type] ["end"] > $this->limits [$move_down_on_page] [$move_down_type] ["count"]) {
                    $this->limits [$move_down_on_page] [$move_down_type] ["start"] = $this->limits [$move_down_on_page] [$move_down_type] ["count"] - $this->limit_value;
                    $this->limits [$move_down_on_page] [$move_down_type] ["end"] = $this->limits [$move_down_on_page] [$move_down_type] ["count"];
                }
            }

            $this->put_anchor_on_page_id = $move_down_on_page;
            $this->put_anchor_on_type_id = $move_down_type;
        }

        global $move_up_on_page, $move_up_type, $move_start;
        if (isset($_REQUEST['move_up_on_page'])) {
            $move_up_on_page = $_REQUEST['move_up_on_page'];
        }
        if (isset($_REQUEST['move_up_type'])) {
            $move_up_type = $_REQUEST['move_up_type'];
        }
        if (isset($_REQUEST['move_start'])) {
            $move_start = $_REQUEST['move_start'];
        }

        if (!empty($move_up_on_page)) {
            if ($move_start == 1) {
                $this->limits [$move_up_on_page] [$move_up_type] ["start"] = 0;
                $this->limits [$move_up_on_page] [$move_up_type] ["end"] = $this->limit_value;
            } else {
                $this->limits [$move_up_on_page] [$move_up_type] ["start"] -= $this->limit_value;
                $this->limits [$move_up_on_page] [$move_up_type] ["end"] -= $this->limit_value;

                if ($this->limits [$move_up_on_page] [$move_up_type] ["start"] < 0) {
                    $this->limits [$move_up_on_page] [$move_up_type] ["start"] = 0;
                    $this->limits [$move_up_on_page] [$move_up_type] ["end"] = $this->limit_value;
                }
            }

            $this->put_anchor_on_page_id = $move_up_on_page;
            $this->put_anchor_on_type_id = $move_up_type;
        }

        $this->expanded_pages = array_unique($this->expanded_pages);

        if (is_numeric($this->openedPageID)) {
            $this->put_anchor_on_page_id = null;
        }

        global $change_search_sort;
        if (isset($_REQUEST['change_search_sort'])) {
            $change_search_sort = $_REQUEST['change_search_sort'];
        }

        if (!empty($change_search_sort)) {
            $this->sort ["SEARCH"] ["SEARCH"] ["field"] = $change_search_sort;

            if ($change_search_sort == "page_name") {
                $dir = "ASC";
            } else {
                $dir = "DESC";
            }

            $this->sort ["SEARCH"] ["SEARCH"] ["dir"] = $dir;
        }

        if (isset($_REQUEST['target'])) {
            $this->tinymce_target_element = $_REQUEST['target'];
        }

        if (in_array($this->show_mode, array(
            NAV_SM_GET_PAGE_ID_FORM,
            NAV_SM_INSERT_LINK_IMAGE,
            NAV_SM_INSERT_LINK_MULTI,
            NAV_SM_INSERT_LINK_SINGLE,
        ))) {
            $this->group_by_type = true;
        } else {
            if (isset($_REQUEST['group_by_type'])) {
                $this->group_by_type = ($_REQUEST['group_by_type'] == 1);
            }
        }
    }

    function CollapseAll($style)
    {
        if ($style == 'temp') {
            $this->collapsed = true;
        } else {
            $this->expanded_pages = Array();
            $this->closed_types = Array();
        }
    }

    function ExpandType($expand_type, $expand_from_page)
    {
        //if (!is_array($this->closed_types[


        $new_array = Array();

        reset($this->closed_types [$expand_from_page]);

        while ($closed_type = current($this->closed_types [$expand_from_page])) {
            if ($closed_type != $expand_type) {
                array_push($new_array, $closed_type);
            }

            next($this->closed_types [$expand_from_page]);
        }

        $this->closed_types [$expand_from_page] = $new_array;
    }

    function ClosePage($close_page)
    {
        $new_array = Array();

        array_push($this->closed_pages, $close_page);
        array_unique($this->closed_pages);

        reset($this->expanded_pages);

        while ($expanded_page = current($this->expanded_pages)) {
            if ($expanded_page != $close_page) {
                array_push($new_array, $expanded_page);
            }

            next($this->expanded_pages);
        }

        $this->expanded_pages = $new_array;
    }

    function PageExpanded($page_id)
    {
        if ($this->collapsed) {
            return false;
        }

        // If expanded but has no children (this happens after deleting all children), close the page.
        if (in_array($page_id, $this->expanded_pages) && count($this->getPageChildrenCount($page_id)) == 0) {
            $this->ClosePage($page_id);
        }

        // Is in expanded page array AND not in closed page array
        return in_array($page_id, $this->expanded_pages) && !in_array($page_id, $this->closed_pages);
    }

    function GetActionLink($page_id)
    {
        switch ($this->show_mode) {
            case NAV_SM_INSERT_LINK_SINGLE :
                return "insert_link.php?from_navigator=T&amp;page_id=" . $page_id . '&amp;target=' . urlencode($this->tinymce_target_element);
            case NAV_SM_INSERT_LINK_IMAGE :
                return "link_image.php?from_navigator=T&amp;page_id=" . $page_id;
            case NAV_SM_GET_PAGE_ID_FORM :
                return 'javascript:updateNodeId(' . $page_id . ', "' . htmlspecialchars(htmlspecialchars(GetPageName($page_id), ENT_QUOTES)) . '")';
            default :
                {
                    switch ($this->action_mode) {
                        case NAV_AM_ADMIN :
                            return "../admin/admin_page_templates.php?page_id=" . $page_id;
                        case NAV_AM_EDIT :
                        default :
                            return $this->getPageDetailsScriptName('?page_id=' . $page_id, '/buxus/system/');
                    }
                }
        }
    }

    function TypeExpanded($page_id, $type_id)
    {
        //testing
        //return true;


        if ($type_id == -1) {
            return true;
        }

        if (empty($page_id)) {
            $page_id = "NULL";
        }

        if (isset($this->closed_types[$page_id]) && is_array($this->closed_types[$page_id])) {
            return !in_array($type_id, $this->closed_types [$page_id]);
        } else {
            return true;
        }
    }

    /**
     * get checkbox HTML
     *
     * @param array $page_info associative items: page_id, page_name, type_id, page_state_id
     * @return string
     */
    protected function getChecboxHtml($page_info)
    {
        $checkbox = "&nbsp;<input onclick='CheckboxClicked(this);' type='checkbox' style='background-color:white; height:12px; width:12px;' name='insert_ids[]' value='" . $page_info ["page_id"] . "'>&nbsp;";

        return $checkbox;
    }

    protected $navigatorManager = null;

    /**
     * @return \Buxus\Navigator\NavigatorManager
     */
    protected function getNavigatorManager()
    {
        if (is_null($this->navigatorManager)) {
            $this->navigatorManager = app('buxus:navigator:manager');
        }
        return $this->navigatorManager;
    }

    function GetPageLink($page_info)
    {
        $root_id = $page_info ["page_id"];
        $page_name = $page_info ["page_name"];
        //$type_id = $page_info ["type_id"];
        //$state = $page_info ["page_state_id"] == C_active_page_state_id ? "A" : "P";
        $is_search = ((array_key_exists('is_search', $page_info)) ? $page_info ['is_search'] : false);

        $workflow = $GLOBALS ['workflow'];
        $state_abbr = $workflow->GetStateAbbr($page_info ["page_state_id"]);
        $state_class_postfix = $workflow->GetStateClassPostfix($page_info ["page_state_id"]);

        //mark the opened page by different style
        if ((!$is_search) && ($this->PageExpanded($root_id)) && ($this->group_by_type)) {
            $class = "nav-pn-exp-" . $state_class_postfix;
        } else {
            $class = "nav-pn-" . $state_class_postfix;
        }

        //create the anchor code
        if ($this->put_anchor_on_page_id == $root_id || $this->openedPageID == $root_id) {
            $anchor = "<a name=\"pos\" class=\"jump-target\"></a>";
        } else {
            $anchor = "";
        }

        if ($this->show_checkboxes) {
            $checkbox = $this->getChecboxHtml($page_info);
        } else {
            $checkbox = '';
        }

        //create 1st part
        $navigator_link = "<tr>
								<td class='$class' colspan='2'>" . $anchor . $checkbox;

        //if not multi, href points to navigator
        if ($this->show_mode != NAV_SM_INSERT_LINK_MULTI) {
            if (/*!empty($anchor) || */!empty($checkbox)) {
                $navigator_link .= '&nbsp;';
            }
            $navigator_link .= "<a class='$class' href='" . $this->GetActionLink($root_id) . "'>";
        }

        $link_text = htmlspecialchars($page_name) . "&nbsp;(" . $state_abbr . ":" . $root_id . ")";

        $link_text = $this->getNavigatorManager()->decoratePageLink(
            $link_text,
            $root_id,
            $page_name,
            $page_info['page_state_id'],
            $page_info['type_id']
        );

        $navigator_link .= $link_text;

        if ($this->show_mode != NAV_SM_INSERT_LINK_MULTI && $this->show_mode != NAV_SM_GET_PAGE_ID_FORM) {
            $navigator_link .= "</a>";

            if (!$this->group_by_type) {
                $navigator_link .= '&nbsp;<span style="color:#aaaaaa">(' . $page_info ['page_type_name'] . ')</span>';
            }

            if ($this->PageExpanded($root_id) && !$this->search) {
                $navigator_link .= "&nbsp;&nbsp;&nbsp;&nbsp;<a class='$class sort-link' href='pageSorter.php?parent_page_id=" . $root_id . "'>" . __bx('legacy-base::navigator.Reorder_Children') . "</a>";
            }
        }

        $navigator_link .= "</td>";

        if ($this->search_phrase) {
            $navigator_link .= '<td>' . $page_info ['page_type_name'] . '</td>';
        }

        $navigator_link .= "</tr>";
        return $navigator_link;
    }

    /**
     * get page type condition for navigator tree
     *
     * @return string
     */
    protected function getPageTypeCondition()
    {
        if (empty($this->selected_page_types)) { // The selected page types are not defined
            $page_type_condition = "";
        } else { // The selected page types are defined
            $page_type_condition = "AND page.page_type_id IN (" . BuxusDB::get()->quoteInto('?', $this->selected_page_types) . ")";
        }

        return $page_type_condition;
    }

    function DrawPageTree_GroupByType($page_info)
    {
        $root_id = $page_info ["page_id"];

        $page_type_condition = $this->getPageTypeCondition();

        $expanded = $this->PageExpanded($root_id);

        $l_count = BuxusDB::get()->fetchOne("
					SELECT
						COUNT(DISTINCT page.page_type_id)
					FROM
						tblPages page

						INNER JOIN
							tblPageTypes PT
						ON
							PT.page_type_id = page.page_type_id

					WHERE
						page.page_type_id IS NOT NULL
						" . $page_type_condition . "
					AND
						parent_page_id = :parent_page_id
					", array(':parent_page_id' => $root_id));

        $html = "";

        $pre_html = $html;

        if ($expanded) {
            $sql_qry = "
				SELECT DISTINCT
					page.page_type_id,
					PT.page_type_name,
					PT.auto_expand,
					parent_page_id AS root_page_id,
					PT.page_type_order IS NULL as page_type_order_by_null,
					PT.page_type_order
				FROM
					tblPages page
				INNER JOIN
					tblPageTypes PT
				ON
					page.page_type_id = PT.page_type_id
				WHERE
					page.page_type_id IS NOT NULL
					AND
					parent_page_id = :parent_page_id
					" . $page_type_condition . "
                ORDER BY page_type_order_by_null, PT.page_type_order, PT.page_type_name";

            $page_types = BuxusDB::get()->fetchAll($sql_qry, array(':parent_page_id' => $root_id));

            $l_count = count($page_types);

            reset($page_types);

            $index = 0;

            while ($page_type = current($page_types)) {
                List ($sub_html, $count) = $this->DrawTypeTree($page_type);

                if (empty($sub_html)) {
                    next($page_types);
                    continue;
                }

                if ($expanded) {
                    $width = 5;
                } else {
                    $width = 5;
                }

                $index++;

                $bg = '';

                if ($index < count($page_types)) {
                    $html .= "<tr><td " . $bg . "class='nav-tt-l' width='$width' valign='middle' align='left'>";
                } else {
                    $html .= "<tr><td " . $bg . "class='nav-tt-l' width='$width' valign='middle' align='left'>";
                }

                $html .= "<img src='" . $this->image_path . "px.gif' height='1' width='$width'></td><td width='100%'>";

                $html .= "<table class='nav-listBorder' width='100%' cellpadding='0' cellspacing='0'>";

                $html .= $sub_html;

                $html .= "</table>";

                $html .= "</td></tr>";

                next($page_types);

                //if we're not at the end yet


                if (current($page_types)) {
                    $html .= "<tr><td " . $bg . "colspan='2' height='5'>&nbsp;</td></tr>";
                } else {
                    $html .= "<tr><td " . $bg . "colspan='2' height='4'></td></tr>";
                    $html .= "<tr><td colspan='2' height='5'>
								</td></tr>";
                }

            }
        } // end of if ($expanded)


        $navigator_link = $this->GetPageLink($page_info);

        $html = $pre_html . $navigator_link . $html;

        return Array($html, $l_count);
    }

    function DrawPageTree_GroupByHierarchy($page_info)
    {
        $root_id = $page_info ["page_id"];
//		$page_type_condition = $this->getPageTypeCondition();
        $expanded = $this->PageExpanded($root_id);

        $html = "";

        $pre_html = $html;
        $pages = BuxusDB::get()->fetchAll("
					SELECT
						page.page_id,
						page.page_name,
						page.page_state_id,
						page.last_updated,
						ARPU.read_right,
						pt.page_type_name
					FROM
						tblPages page
					JOIN
						tblPageTypes pt
						ON
							(pt.page_type_id = page.page_type_id)
						LEFT JOIN
							tblAccessRightsPageUser ARPU
						ON
							page.page_id = ARPU.page_id
							AND
							ARPU.user_id = :user_id
					WHERE
						page.parent_page_id = :parent_page_id
						AND
						page.page_type_id IS NOT NULL
					ORDER BY
					    page.sort_date_time DESC", array(
            ':user_id' => $this->user_id,
            ':parent_page_id' => $root_id,
        ));

        $l_count = count($pages);

        if ($expanded) {
            foreach ($pages as $page) {
                list($sub_html, $sub_count) = $this->DrawHierarchyEntry($page);
                $html .= $sub_html;
            }
        }

        $navigator_link = $this->GetPageLink($page_info);

        $html = $pre_html . $navigator_link . $html;

        return Array($html, $l_count);
    }


    function DrawPageTree($page_info)
    {
        if ($this->group_by_type) {
            $result = $this->DrawPageTree_GroupByType($page_info);
        } else {
            $result = $this->DrawPageTree_GroupByHierarchy($page_info);
        }
        return $result;
    }


    function addAccessRights($rows, $parentPageId)
    {
        $page_rights = getUserRightsForPage($this->user_id, $parentPageId);

        $rights = Array();

        if (\Buxus\User\UserUtils::getGlobalUserRight($this->user_id, "admin_right") == C_True_Char || $this->isInsertMode()) {
            return $rows;
        }

        $new_pages = Array();

        reset($rows);

        $done = false;

        while ($row = current($rows)) {
            if ((empty($row ["read_right"]) && $page_rights['read_right'] == C_True_Char) || $row ["read_right"] == "T") {
                array_push($new_pages, $row);
            }

            next($rows);
        }

        return $new_pages;
    }

    function addAccessRightsSearch($rows)
    {
        $rights = Array();

        if (\Buxus\User\UserUtils::getGlobalUserRight($this->user_id, "admin_right") == C_True_Char || $this->isInsertMode()) {
            return $rows;
        }

        $newPages = Array();

        foreach ($rows as $row) {
            //explicit denial
            if ($row ["read_right"] == "F") {
                continue;
            } elseif ($row ["read_right"] == "T") {
                //explicit grant
                array_push($newPages, $row);
            }
            if (empty($row ["read_right"])) {
                //inherited access
                $page_rights = getUserRightsForPage($this->user_id, $row ["parent_page_id"]);

                //if granted on inherit
                if ($page_rights['read_right'] == C_True_Char) {
                    array_push($newPages, $row);
                }
            }
        }

        return $newPages;
    }

    function addPageTypeSearch($rows)
    {
        $newPages = Array();

        foreach ($rows as $row) {
            $sql = "SELECT
							page_type_name
						FROM
							tblPages P
						LEFT JOIN
							tblPageTypes PT
						ON PT.page_type_id = P.page_type_id
					WHERE
						P.page_id = :page_id";
            $row ['page_type_name'] = BuxusDB::get()->fetchOne($sql, array(':page_id' => $row ['page_id']));
            $newPages [] = $row;
        }

        return $newPages;
    }

    function cutPages($pages, $start, $end)
    {
        $new_pages = Array();

        $count = count($pages);

        if ($count < $end - $start + 1) {
            return $pages;
        }

        for ($i = $start < 0 ? 0 : $start; $i < $end && $i < $count; $i++) {
            array_push($new_pages, $pages [$i]);
        }

        return $new_pages;
    }

    function CanInsertType($type_id)
    {
        if ($this->show_mode == NAV_SM_INSERT_LINK_MULTI) {
            return false;
        }

        return \Buxus\PageType\PageTypeInsertRightsManager::getInstance()->userAllowedToInsert(
            \Buxus\PageType\PageTypesManager::getInstance()->getPageTypeById($type_id),
            \Buxus\User\Facades\BuxusUserManager::getCurrentUser()
        );
    }

    function getPageTypeSort($type_id)
    {
        if (isset($type_id)) {
            $tag = \Buxus\PageType\PageTypesManager::getInstance()->getPageTypeById($type_id)->getPageSortTypeTag();
        } else {
            $tag = ''; //default
        }

        switch ($tag) {
            case 'page_name' :
                return Array("field" => "page_name", "dir" => "ASC");
                break;
            case 'sort_date_time' : /* POZOR: TOTO JE DEFAULT */
            default :
                return Array("field" => "sort_date_time", "dir" => "DESC");
        }
    }

    function getAdditionalParameters()
    {
        $params = Array();

        global $OID, $PID;

        if (!empty($OID)) {
            array_push($params, "OID=" . $OID);
        }

        if (!empty($PID)) {
            array_push($params, "PID=" . $PID);
        }

        return $params;
    }

    function TNLForm()
    {
        $params = $this->getAdditionalParameters();

        reset($params);

        $html = "";

        while ($parm = current($params)) {
            List ($tag, $value) = Explode("=", $parm);

            $html .= "<input type='hidden' name='" . $tag . "' value='" . $value . "'>";

            next($params);
        }

        return $html;
    }

    //centralizovane linkovanie vnutri navigatora
    //parameter je iba parameter skriptu navigatora samotneho
    function TNL($parm)
    {
        $additionalParameters = Implode("&amp;", $this->getAdditionalParameters());

        if (!empty($parm) && !empty($additionalParameters)) {
            if (preg_match("/^(.*)(#.*)$/", $parm, $m)) {
                $parm = $m [1] . "&amp;" . $additionalParameters . $m [2];
            } else {
                $parm .= "&amp;" . $additionalParameters;
            }

        } elseif (empty($parm)) {
            $parm = $additionalParameters;
        }

        if (!empty($parm)) {
            $parm = "?" . $parm;
        }

        return $this->self_link . $parm;
    }

    protected function loadPagesForPageAndType($pageId, $pageTypeId, $offset = null, $count = null)
    {
        $originalPageId = $pageId;

        if (empty($pageId)) {
            $operator = 'IS';
            $pageId = 'NULL';
        } else {
            $operator = '=';
        }

        $sql_qry = "
					SELECT
						page.page_id,
						page.page_name,
						page.page_state_id,
						page.last_updated
					FROM
						tblPages page
					WHERE
						page.parent_page_id $operator " . ($pageId == 'NULL' ? $pageId : BuxusDB::get()->quote($pageId)) . "
						AND
						page.page_type_id = :page_type_id
						AND
						page.page_type_id IS NOT NULL
					ORDER BY
					    page.page_type_id,
						" . $this->sort[$pageId][$pageTypeId]['field'] . ' ' . $this->sort[$pageId][$pageTypeId]['dir'] . ', page.page_id';

        if ($offset !== null) {
            $sql_qry .= ' LIMIT ' . $offset;
            if ($count !== null) {
                $sql_qry .= ',' . $count;
            }
        }

        return BuxusDB::get()->fetchAll($sql_qry, [':page_type_id' => $pageTypeId]);
    }

    function DrawTypeTree($type_info)
    {
        $pages = array();
        $page_id = $type_info ["root_page_id"];
        $original_page_id = $page_id;
        $type_id = $type_info ["page_type_id"];
        $type_name = $type_info ["page_type_name"];

        $expanded = $this->TypeExpanded($page_id, $type_id);

        $class = "nav-tn-exp";

        if ($this->put_anchor_on_type_id == $type_id && $this->put_anchor_on_page_id == $page_id) {
            $anchor = "<a name=\"pos\" class=\"jump-target\" />";
        } else {
            $anchor = "";
        }
        if (empty($page_id)) {
            $operator = "IS";
            $page_id = "NULL";
        } else {
            $operator = "=";
        }

        //set sort, and show sort
        if (!isset($this->sort [$page_id] [$type_id] ["field"])) {
            $this->sort [$page_id] [$type_id] = $this->getPageTypeSort($type_id);
        }

        if ($this->sort [$page_id] [$type_id] ["field"] == "page_name") {
            $change_sort_field = "sort_date_time";
        } else {
            $change_sort_field = "page_name";
        }

        if ($expanded) {
            if ($change_sort_field == "page_name") {
                $page_name_sort_link = "<a class='nav-tn' href='" . $this->TNL("change_sort_on_page=" . $page_id . "&amp;change_sort_on_type=" . $type_id . "&amp;sort_field=" . $change_sort_field . "#pos") . "'>" . __bx('legacy-base::navigator.Alphabetically') . "</a>";
            } else {
                $page_name_sort_link = "<span class='nav-selected-sorting'>" . __bx('legacy-base::navigator.Alphabetically') . '</span>';
            }

            if ($change_sort_field == "sort_date_time") {
                $date_sort_link = "<a class='nav-tn' href='" . $this->TNL("change_sort_on_page=" . $page_id . "&amp;change_sort_on_type=" . $type_id . "&amp;sort_field=" . $change_sort_field . "#pos") . "'>" . __bx('legacy-base::navigator.By_Date') . "</a>";
            } else {
                $date_sort_link = "<span class='nav-selected-sorting'>" . __bx('legacy-base::navigator.By_Date') . '</span>';
            }
        } else {
            $page_name_sort_link = "";
            $date_sort_link = "";
        }

        if ($this->show_mode != NAV_SM_GET_PAGE_ID_FORM && $this->CanInsertType($type_id) && is_numeric($page_id)) {
            $rights = getUserRightsForPage($this->user_id, $page_id);
            if ($rights ['write_right'] == 'T') {
                $desc = GetTypeDescByTypeID($type_id);
                $tName = GetPageTypeName($type_id);
                $insert_page_link = "<a class='nav-tn' href='" . $this->getPageDetailsScriptName('?parent_page_id=' . $page_id . '&amp;show_type=insert&amp;page_type_id=' . $type_id) . "'>" . __bx('legacy-base::navigator.InsertNew') . "</a>";
                $insert_page_action_link = "<a  class='insert-link' href='" . $this->getPageDetailsScriptName('?parent_page_id=' . $page_id . '&amp;show_type=insert&amp;page_type_id=' . $type_id) . "'>" . __bx('legacy-base::navigator.InsertNew') . " " . $type_name . "</a>";
                $insert_page_footer_link = '';
            }
        }
        $html = "<tr><td class='$class' colspan='2'><div class='col-lg-6'>" . $anchor . "<a class='nav-tn' href='" . $this->TNL(($expanded ? "close_type=" . $type_id . "&amp;close_from_page=" . $page_id : "expand_type=" . $type_id . "&amp;expand_from_page=" . $page_id)) . "'>" . $type_name . "</a>";
        $html .= "</div>";

        if ($expanded) {
            $html .= "<div class='col-lg-6 text-right right-nav'>";
            $html .= "X_INSERT_LINK | X_SORT_LINK | ";

            $html .= __bx('legacy-base::navigator.Sort_By') . ": ";

            $html .= $page_name_sort_link . " | ";

            $html .= $date_sort_link;
        }
        $html .= "</td></tr>";

        if ($expanded) {

            //if we should be showing LAST N, then select last N - reverse order, man :)

            $pages_count = null;
            $reverse = false;

            if ($this->search) {
                    $sql_qry = "
							SELECT
		 						page.page_id,
		 						page.page_name,
		 						page.sort_date_time,
		 						COUNT(page_child.page_id) AS subpage_count
		 					FROM
		 						tblPages page

			 					LEFT JOIN
			 						tblPages page_child
			 					ON
			 						page_child.parent_page_id = page.page_id
		 					WHERE
			 					page_child.page_type_id IS NOT NULL
			 					AND
			 					page.page_name LIKE :page_name
			 					AND
			 					page.page_type_id IS NOT NULL
			 					AND
			 					(
			 						page.page_type_id <> :page_type_id1
			 						OR
			 						page.page_type_id <> :page_type_id2
			 						OR
			 						page.page_type_id <> :page_type_id3
		 						)
		 					GROUP BY
		 						page.page_id
		 					ORDER BY
		 						page." . $this->sort [$page_id] [$type_id] ["field"] . " " . $this->sort [$page_id] [$type_id] ["dir"];

                    $pages = BuxusDB::get()->fetchAll($sql_qry, array(
                        ':page_name' => '%' . $this->search_phrase . '%',
                        ':page_type_id1' => C_pc_Template,
                        ':page_type_id2' => C_pc_System_Option,
                        ':page_type_id3' => C_pc_Item
                    ));
            } else {
                if (!$this->ignore_user_rights) {
                    $sql_qry = "
					SELECT
						page.page_id,
						page.page_name,
						page.page_state_id,
						page.last_updated,
						ARPU.read_right
					FROM
						tblPages page

						LEFT JOIN
							tblAccessRightsPageUser ARPU
						ON
							page.page_id = ARPU.page_id
							AND
							ARPU.user_id = :user_id
					WHERE
						page.parent_page_id $operator " . ($page_id == 'NULL' ? $page_id : BuxusDB::get()->quote($page_id)) . "
						AND
						page.page_type_id = :page_type_id
						AND
						page.page_type_id IS NOT NULL
					ORDER BY
						" . $this->sort [$page_id] [$type_id] ["field"] . " " . $this->sort [$page_id] [$type_id] ["dir"] . ', page.page_id';

                    $pages = BuxusDB::get()->fetchAll($sql_qry, array(':user_id' => $this->user_id, ':page_type_id' => $type_id));
                } else {
                    $pages = null;

                    $sql_qry = "
					SELECT
						COUNT(*)
					FROM
						tblPages page
					WHERE
						page.parent_page_id $operator " . ($page_id == 'NULL' ? $page_id : BuxusDB::get()->quote($page_id)) . "
						AND
						page.page_type_id = :page_type_id
						AND
						page.page_type_id IS NOT NULL";

                    $pages_count = BuxusDB::get()->fetchOne($sql_qry, [':page_type_id' => $type_id]);
                }

            }

            if (!$this->ignore_user_rights && $pages !== null) { //now we only have those pages, which we need
                $pages = $this->addAccessRights($pages, $page_id);
            }

            if (isset($GLOBALS ['rootNodeOverride']) && $pages !== null) {
                $rootNodeOverride = $this->getRootNodeOverride();

                $filtered_pages = array();
                foreach ($pages as $page) {
                    if (in_array($page ['page_id'], $rootNodeOverride)) {
                        $filtered_pages [] = $page;
                    }
                }

                if (count($filtered_pages) > 0) {
                    $pages = $filtered_pages;
                }
            }

            if ($pages_count === null) {
                $pages_count = count($pages);
            }

            $this->limits [$page_id] [$type_id] ['count'] = $pages_count;

            if ($this->limit_used) {
                //if the pages are open for the first time,
                //we need to set their limit window start at 0
                //and limit window end at 0 + limit
                if (!isset($this->limits [$page_id] [$type_id] ["start"])) {
                    $this->limits [$page_id] [$type_id] ["start"] = 0;
                    $this->limits [$page_id] [$type_id] ["end"] = $this->limit_value;
                } else {
                    //here nothing, since the values are saved in the session
                }
            }

            if ($this->limit_used) {
                $start = $this->limits [$page_id] [$type_id] ['start'];
                $end = $this->limits [$page_id] [$type_id] ['end'];

                //create the section
                if ($pages !== null) {
                    $pages = $this->cutPages($pages, $start, $end);
                } else {
                    $start = $start < 0 ? 0 : $start;
                    $limit = $end - $start + 1;
                    if ($limit < 0) {
                        $limit = 0;
                    }

                    $pages = $this->loadPagesForPageAndType($original_page_id, $type_id, $start, $limit);
                }
            } elseif ($pages === null) {
                $pages = $this->loadPagesForPageAndType($original_page_id, $type_id);
            }

            reset($pages);

            $page_html = '';

            while ($page = current($pages)) {
                $page ["type_id"] = $type_id;

                //we will watch the output as we go through the tree
                //and when limits are used
                List ($sub_html, $count) = $this->DrawPageTree($page);

                $page_html .= "<tr><td class='nav-tt-l' align='left' valign='top'>";

                if ($count > 0 && !$this->search) {

                    $link = "<a style='text-decoration:none;' href='" . $this->TNL(($this->PageExpanded($page ["page_id"]) ? "close_page=" : "expand_page=") . $page ["page_id"] . "#pos");

                    $link .= "'>";

                    $link .= ($this->PageExpanded($page ["page_id"]) ? $this->DrawMinus() : $this->DrawPlus()) . "</a>";

                    $page_html .= $link;

                    //$page_html .= "<img src='".$this->image_path."spacer.gif' class='nav-t-gif-w7'>";


                } else {

                    $page_html .= '<img src="' . $this->image_path . 'spacer.gif"' . ($this->group_by_type ? '' : ' style="width:35px;height:1px;border:0"') . '>';
                }

                $page_html .= "</td><td width='100%' class='nav-tt-r'>";

                $page_html .= "<table width='100%' cellpadding='0' cellspacing='0' border='0' class='" . (($count > 0 && !$this->search) ? 'multi' : 'single') . "'>";

                $page_html .= $sub_html;

                $page_html .= "</table>";

                $page_html .= "</td></tr>";

                next($pages);

            } //end of looping through pages


            if ($pages_count >= 1 && ($this->show_mode != NAV_SM_INSERT_LINK_MULTI && $this->show_mode != NAV_SM_GET_PAGE_ID_FORM)) {
                $pageSorterLink = "<a class='nav-tn sort-link' href='pageSorter.php?parent_page_id=" . $page_id . "&amp;type_id=" . $type_id . "'>" . __bx('legacy-base::navigator.Sort') . "</a>";

                $html = str_replace("X_SORT_LINK", $pageSorterLink, $html);
                $html = str_replace("X_INSERT_LINK", $insert_page_action_link, $html);
            } else {
                $html = str_replace("X_SORT_LINK |", '', $html);
                $html = str_replace("X_INSERT_LINK |", '', $html);
            }

            $html .= $page_html;

            if (empty($page_html)) {
                return array('', 0);
            }

            // footer
            $html .= "<tr><td colspan='2'><div class='hr'></div><div class='nav-footer'>";
            if (isset($insert_page_footer_link)) {
                $html .= $insert_page_footer_link;
            }
            $html .= "</div></td></tr>";
            // end footer


            if ($this->limit_used && ($pages_count > $end || $start > 0)) {
                $html .= "<tr><td colspan='2'>";
                $html .= "<table cellspacing='0' cellpadding='0' style='border-top:1px black solid;' width='100%'>";
                $html .= "<tr valign='middle'><td valign='middle' width='100%'>";

                if ($start > 0) {
                    $html .= "&nbsp;&nbsp;<a class='nav-pn-A' href='" . $this->TNL("move_up_on_page=$page_id&amp;move_up_type=$type_id&amp;move_start=1&amp;s=" . mb_substr(md5(time()),
                                0, 4) . "#pos") . "'>" . __bx('legacy-base::navigator.Move_Top') . "</a>";
                    $html .= "&nbsp;&nbsp;<a class='nav-pn-A' href='" . $this->TNL("move_up_on_page=$page_id&amp;move_up_type=$type_id&amp;s=" . mb_substr(md5(time()),
                                0, 4) . "#pos") . "'>" . __bx('legacy-base::navigator.Move_Up') . "</a>";
                }

                if ($pages_count > $end) {
                    $html .= "&nbsp;&nbsp;<a class='nav-pn-A' href='" . $this->TNL("move_down_on_page=$page_id&amp;move_down_type=$type_id&amp;s=" . mb_substr(md5(time()),
                                0, 4) . "#pos") . "'>" . __bx('legacy-base::navigator.Move_Down') . "</a>";
                    $html .= "&nbsp;<a class='nav-pn-A' href='" . $this->TNL("move_down_on_page=$page_id&amp;move_down_type=$type_id&amp;move_end=1&amp;s=" . mb_substr(md5(time()),
                                0, 4) . "#pos") . "'>" . __bx('legacy-base::navigator.Move_Bottom') . "</a>";
                }

                $html .= "&nbsp;&nbsp;<span class='blue'>" . ($start + 1) . " - " . ($end) . " / " . ($pages_count) . "</span>";

                //$html .= "&nbsp;&nbsp;<a class='nav-pn' href='navigator.php?move_down_on_page=$page_id&amp;move_down_type=$type_id&amp;move_end=1&amp;s=".mb_substr(md5(time()),0,4)."#pos'>".__bx('legacy-base::navigator.Move_Bottom')."</a>";
                $html .= "</td></tr></table>";
                $html .= "</td></tr>";
            }

            if ($this->limit_used) {
                //save the value of the limits into the session var, since we don't know whether we end or not


                global $nav_limits;

                $nav_limits = $this->limits;
            }

            if ($reverse) {
                $this->limits [$page_id] [$type_id] ["end"] = "end";
                $this->sort [$page_id] [$type_id] ["dir"] = SwitchDir($this->sort [$page_id] [$type_id] ["dir"]);
            }

        } //end of if ($expanded)
        else {
            $html = str_replace("X_SORT_LINK", "", $html);
        }

        return array($html, count($pages));
    } //end of DrawTypeTree

    function DrawHierarchyEntry($type_info)
    {
        $pages = array();
        $page_id = $type_info ["page_id"];
        //$type_id = $type_info ["page_type_id"];
        //$type_name = $type_info ["page_type_name"];

        //$class = "nav-tn-exp";

        /*if ($this->put_anchor_on_page_id == $page_id) {
            $anchor = "<a name=\"pos\" style=\"position: relative; top: -43px; visibility: hidden;height:0px;width:0;display:block\" />";
        } else {
            $anchor = "";
        }*/

        $page_html = "";

        list($sub_html, $count) = $this->DrawPageTree($type_info);

        $page_html .= "<tr><td class='nav-tt-l' align='left' valign='top'>";

        if ($count > 0 && !$this->search) {
            $link = "<a style='text-decoration:none;' href='" . $this->TNL(($this->PageExpanded($page_id) ? "close_page=" : "expand_page=") . $page_id . "#pos");
            $link .= "'>";
            $link .= ($this->PageExpanded($page_id) ? $this->DrawMinus() : $this->DrawPlus()) . "</a>";
            $page_html .= $link;
        } else {
//            $page_html .= "<img src='" . $this->image_path . "spacer.gif' class='nav-t-gif-w35'>";
            $page_html .= '<img src="' . $this->image_path . 'spacer.gif"' . ($this->group_by_type ? '' : ' style="width:35px;height:1px;border:0"') . '>';
        }
        $page_html .= "</td><td width='100%' class='nav-tt-r'>";
        $page_html .= "<table width='100%' cellpadding='0' cellspacing='0' border='0'>";
        $page_html .= $sub_html;
        $page_html .= "</table>";
        $page_html .= "</td></tr>";

        return array($page_html, count($pages));
    }

    /**
     * Set page types which wil show in navigator
     *
     * @param array $selected_page_types
     */
    public function setSelectedPageTyps($selected_page_types)
    {
        $this->selected_page_types = $selected_page_types;
    }

    /**
     * Reset all set settings of navigator
     *
     */
    public function reset()
    {
        $this->selected_page_types = array();
    }

    /**
     * Return page details script name
     *
     * @param string $parameters
     * @param string $prefix
     * @return string
     */
    protected function getPageDetailsScriptName($parameters = '', $prefix = '')
    {
        return $prefix . 'page_details.php' . $parameters;
    }

    /**
     * universal get rootNodeOverride from global/GLOBALS
     *
     * @return array
     */
    protected function getRootNodeOverride()
    {
        $result = array();

        if (!empty($GLOBALS ['rootNodeOverride'])) {
            $result = $GLOBALS ['rootNodeOverride'];
        } else {
            global $rootNodeOverride;

            if (!empty($rootNodeOverride)) {
                $result = $rootNodeOverride;
            }
        }

        if (!is_array($result)) {
            $root_node_override_str = strval($result);
            $root_node_override_str = str_replace(' ', '', $root_node_override_str);
            $root_node_override_str = str_replace(';', ',', $root_node_override_str);

            if (strlen($root_node_override_str) > 0) {
                $result = explode(',', $root_node_override_str);
            } else {
                $result = array();
            }
        }

        return $result;
    }

    public function SwitchDir($dir)
    {
        if (mb_strtoupper($dir) == "ASC") {
            return "DESC";
        } else {
            return "ASC";
        }
    }

    public static $page_type_weights = array(
        C_pc_System_Option => 0,
        C_pc_Web_page => 4,
        C_pc_Template => 3,
        C_pc_Item => 2,
        C_pc_ParentItem => 2,
        C_pc_OtherType => 1
    );

    public static function cmp_types($type_a, $type_b)
    {
        $w_a = self::$page_type_weights [GetPageType($type_a)];
        $w_b = self::$page_type_weights [GetPageType($type_b)];

        if ($w_a < $w_b) {
            return 1;
        }

        if ($w_a > $w_b) {
            return -1;
        }

        return 0;
    }

    public function __sleep()
    {
        return array_diff(array_keys(get_object_vars($this)), ['navigatorManager']);
    }

} //end of class Navigator
