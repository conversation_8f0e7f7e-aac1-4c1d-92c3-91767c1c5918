<?php 
	require_once 'includes/functions.php';
	require_once 'includes/uif_functions.php';
	require_once 'includes/validate.php';

	if (!\BuxusUserManager::hasUserRight('admin_right')) {
		ResultMessage::warnMessage(__bx('legacy-base::pages_table.AccessDeniedAndRedirectedToMain'));
		header('Location: ' . config('buxus_core.base_url') . 'system/main.php');
		exit();
	}
	
    DisplayHtmlHeader(__bx('legacy-base::pages_table.AdminPageTitle'), C_MenuType_Admin);
    
    echo HTML_PageTitle(__bx('legacy-base::pages_table.PageTitle'), __bx('legacy-base::pages_table.PageAnotation'));
    
    $order_column = (isset($_GET['order_column']) ? (int)$_GET['order_column'] : 2);

	$pages_descriptor = BuxusDB::get()->fetchAll("SELECT 
			tblPages.page_id, 
			tblPages.page_name, 
			tblUsers.user_name, 
			tblPages.creation_date, 
			tblPageTypes.page_type_name, 
			tblPageStates.page_state_id AS page_state_id, 			 
			tblPages.last_updated,
			tblPages.sort_date_time, 
			tblPages.page_class_id,
			tblPageStates.page_state_name
		FROM 
			tblPages
		INNER JOIN
			tblUsers
		INNER JOIN
			tblPageTypes
		INNER JOIN
			tblPageStates
		WHERE 
			tblPages.page_type_id = tblPageTypes.page_type_id		
		AND 
			tblPages.page_state_id = tblPageStates.page_state_id
		AND 
			tblPages.author_id = tblUsers.user_id
		ORDER BY :order_column",
		array(':order_column' => $order_column));
	
	$admin_right = \Buxus\User\UserUtils::getGlobalUserRight(\Buxus::userId(), 'admin_right');

	echo HTML_DataTableBegin(__bx('legacy-base::pages_table.TableHeader'), 9);
	echo HTML_RowBegin();
		echo HTML_HeaderCell(HTML_Link('pages_table.php?order_column=1', __bx('legacy-base::pages_table.Page_ID')));
		echo HTML_HeaderCell(HTML_Link('pages_table.php?order_column=2', __bx('legacy-base::pages_table.Page_Name')));
		echo HTML_HeaderCell(HTML_Link('pages_table.php?order_column=3', __bx('legacy-base::pages_table.Page_Author')));
		echo HTML_HeaderCell(HTML_Link('pages_table.php?order_column=4', __bx('legacy-base::pages_table.Page_Date_Created')));
		echo HTML_HeaderCell(HTML_Link('pages_table.php?order_column=5', __bx('legacy-base::pages_table.Page_Type')));
		echo HTML_HeaderCell(HTML_Link('pages_table.php?order_column=6', __bx('legacy-base::pages_table.Page_State')));
		echo HTML_HeaderCell(HTML_Link('pages_table.php?order_column=7', __bx('legacy-base::pages_table.Page_Date_Last_Updated')));
		echo HTML_HeaderCell(HTML_Link('pages_table.php?order_column=8', __bx('legacy-base::pages_table.Page_Sort_Time')));
		echo HTML_HeaderCell(HTML_Link('pages_table.php?order_column=9', __bx('legacy-base::pages_table.Page_Class_ID')));
	echo HTML_RowEnd();

	$i = 0;
	foreach($pages_descriptor as $selected_row) {
        $page_rights = getUserRightsForPage(\Buxus::userId(), $selected_row["page_id"]);

        if ($admin_right == C_True_Char || ($page_rights['read_right'] == C_True_Char) || ($page_rights['write_right'] == C_True_Char))
		{
			if ($selected_row["page_state_id"] == 2)
				$class = "passive";
			else
				$class= "active";
			
			echo HTML_RowBegin();
				echo HTML_ValueCell($selected_row["page_id"]);
				echo HTML_ValueCell(HTML_Link("admin_page_rights.php?page_id=".$selected_row["page_id"], $selected_row["page_name"],$class));
				echo HTML_ValueCell($selected_row["user_name"]);
				echo HTML_ValueCell($selected_row["creation_date"]);
				echo HTML_ValueCell($selected_row["page_type_name"]);
				echo HTML_ValueCell($selected_row["page_state_name"]);
				echo HTML_ValueCell($selected_row["last_updated"]);
				echo HTML_ValueCell($selected_row["sort_date_time"]);
				echo HTML_ValueCell(GetPageClassName($selected_row["page_class_id"]));
			echo HTML_RowEnd();
		}
	
		$i++;
	}

	echo HTML_DataTableEnd();

	DisplayHtmlFooter();