<?php
	//this file saves the newly posted image to SOME directory and shows preview (optional)
  require_once 'includes/functions.php';
  require_once 'includes/local_property_functions.php';
  require_once 'includes/validate.php';

	$server_name = GetServerName();

	//read the directory strings from DB
	define(C_IMAGE_DIR, config('buxus_core.upload_tmp_path')."images/");
	
	define(C_IMAGE_URL, $server_name . config('buxus_core.upload_tmp_url')."images/");
	
	if (empty($serverImageName))
	{
		if (!empty($subdir) && !file_exists(C_IMAGE_DIR.$subdir))
		{
			$GLOBALS["FS"]->makedir(C_IMAGE_DIR.$subdir);
		}
			
		//PHP feature
		$image = $_FILES["file_name"];
		
		if (empty($image["tmp_name"]))
		{
			$image["tmp_name"] = "none";
		}
	
		$new_name = ReplaceSpecialChars($image["name"]);
		
		if (!empty($subdir))
		{
			$dest = C_IMAGE_DIR.$subdir."/".$new_name;
			$dest_url = C_IMAGE_URL.$subdir."/".$new_name;
		}
		else
		{
			$dest = C_IMAGE_DIR.$new_name;
			$dest_url = C_IMAGE_URL.$new_name;
		}
	
		//if something was selected to be uploaded
		if ($image["tmp_name"] != "none") 
		{
			$GLOBALS["FS"]->copy($image["tmp_name"], $dest);    
		}
	}
	else
	{
		$dest_url = $serverImageName;
	}
?>
<html>
<head>
</head>
<body>
<script type='text/javascript'>

//shorcut
c = window.opener.Composition.document;

//create URL
<?php
	if ($image['tmp_name'] == 'none')
	{
		$image_url = 'none';
	}
	else
	{
		$image_url = $dest_url;
	}
	
?>

new_image_src  = "<?= $image_url ?>";

<?php
	// if editing
	if (!empty($image_id)) 
	{
		echo "img = EditImage();\n";
	}
	else	//if inserting
	{
		//$size_reset = true;
		
		echo "img = InsertNewImage();";
	}
	
	$alt = stripslashes($alt);	
	
	$alt = preg_replace("/\"/", "&quot;", $alt);
?>	
	//set props
	
	
		
	SetImageProps(img, 
					<?php echo "'$width', '$height', \"".$alt."\" , '$border', '$align'"; ?>, 
					<?php echo "'$hspace', '$vspace'"; ?>,
					<?php echo $size_reset ? "true" : "false"; ?>);


function EditImage()
{
	//find image
	img = window.opener.Composition.document.all["<?php echo $image_id; ?>"];

	// if changed URL (new image data)
	if (new_image_src != img.src && new_image_src != "none") 
	{
		//update URL
	    img.src = new_image_src;
	} 

	return img;
} // end func

function InsertNewImage()
{
	//insert the image
	var res = c.execCommand("InsertImage", false, new_image_src); 

	//now go find it
	last = -1;

	//through all images
	for(i=0;i<c.images.length;i++)
	{
		img = c.images(i);

		//search for the URL
		if (img.src == new_image_src) 
		{
			last = i;
		}
	}

	//if found
	if (last != -1) 
	{
		//get image ibject
		img = c.images(last);

		//assign an id to it
		img.id = "img_" + last;
	}

	return img;
} // end func

//code speaks for itself
//actually, it doesn't
//trick:
//	we create whole new image and copy the src only
//  the object initializes itself with default values
//	of the image
//  and only if parameters allow, we copy also the other preset attributes
//problem:
//	when loading the image from a remote server on slow line
//  the image object is not initialised with the size attributes
//	and the image retians at size 0x0
//	which actually IS a problem, a big one, actually

function SetImageProps(img, width, height, alt, border, align, hspace, vspace, reset_size)
{
	var new_img = new Image();
	
	new_img.src = img.src;
	
	if (reset_size != true)
	{		
		if (width != '') 
		{
		    new_img.width = width;
		    new_img.style.width = width;
		}
		else
		{
			new_img.width = img.width;
			new_img.style.width = img.width;
		}
	
		if (height != '') 
		{
		    new_img.height = height;
		    new_img.style.height = height;
		}
		else
		{
			new_img.height = img.height;
			new_img.style.height= img.height;
		}
	}

	if (alt != '' || img.alt != '') 
	{
	    img.alt = alt;
	}
	
	
	if (align != '' || img.align != '') 
	{
		if (align == 'none')
		{
			align = '';
		}
	    img.align = align;
	}	
	
	if (border != '' || img.border != '') 
	{
	    img.border = border;
	}
	
	if (hspace != '' || img.hspace != '')
	{
		img.hspace = hspace;
	}
	
	if (vspace != '' || img.vspace != '')
	{
		img.vspace = vspace;
	}
		
	//window.opener.Composition.document.all[img.id] = new_img;
	
	img.width = new_img.width;
	img.style.width = new_img.style.width;
	
	img.height = new_img.height;
	img.style.height = new_img.style.height;
	
} // end func


//close small image-insert window
window.close();

</script>
</body>
</html>
