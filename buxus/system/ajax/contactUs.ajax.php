<?php
  	require_once 'includes/functions.php';
  	require_once 'includes/validate.php';
  	
  	$xw = new BuxusXMLWriter(GetSystemOption('C_default_charset'));
  	
  	if (!isset($_POST['message']))
  	{
  		$result = Array("result" => Array("success" => 0));  		
  	}
  	else 
  	{
		$user_name = \BuxusUserManager::getUserLongName(\Buxus::userId());
		/**
		 * Get user email:
		 */
		$sql = "
			SELECT
				`e_mail`
			FROM
				`tblUsers`
			WHERE 
				`user_id` = :user_id";
		
		$from = mb_strtolower(BuxusDB::get()->fetchOne($sql, array(':user_id' => \Buxus::userId())));
		if (empty($from))
		{
			$from = \BuxusUserManager::getCurrentUser()->getUsername() . '@' . $_SERVER['SERVER_NAME'];
		}

		$registration_code = GetSystemOption('C_BUXUS_registration_code');		
		$subject = 'Odkaz od pouzivatela Buxusu';
		$body  = "Pouzivatel: $user_name\n";
		$body .= "Buxus registracia: $registration_code\n";
		$body .= "Odkaz zaslaný z URL: " . $_POST['from_url'] . "\n";
		$body .= "Odkaz:\n" . $_POST['message'];
		
        /**
         * @var \Buxus\Email\Contract\BaseEmail $email
         */
        $email = app()->make(\Buxus\Email\Contract\BaseEmail::class);
        $email->setBodyText($body);
        $email->setSubject($subject);
        $email->setSender($from, $user_name);
        $email->send(GetSystemOption('C_contact_us_address'));

  		$result = Array("result" => Array("success" => 1));
  	}
  	
  	$xml = $xw->writeXML($result);
  	
  	$xw->setHeader(strlen($xml));
  	echo $xml;