@blue_font_color: #0a3065;
@portlet_head : url("images/headerbg.png") repeat-x scroll 0 -20px transparent;

@light_blue_bg: #E2EAF4;

html {
  overflow-y: scroll;
}

body {
  padding: 0px;
  margin: 0px;
  background: url("images/menu_bckg.gif") repeat-y scroll 0 0 #FFFFFF;
  font-family: Verdana, Arial, Helvetica, sans-serif;
  font-size: 11px;
  height: 100%;

}

BODY.no_bg {
  background: #FFFFFF;
}

FORM {
  padding: 0px;
  margin: 0px;
  font: 11px Verdana, Arial, Helvetica, sans-serif;
}

SPAN {
  padding: 0px;
  margin: 0px;
}

SPAN.warning {
  color: #FF0000;
  font-size: 11px;
}

IFRAME.seamless {
  border: 0px;
}

/*
SPAN.nav-insert-title
{
	color:#FFFFFF; font-size:11px;text-decoration: none; font-weight: bold;
}
*/

SPAN.blue {
  font: 11px <PERSON>erd<PERSON>, Arial, Helvetica, sans-serif;
  color: @blue_font_color;
  vertical-align: middle;
}

SPAN.colon {
  background-color: @light_blue_bg;
}

.strong {
  font-weight: bold;
}

.center {
  text-align: center !important;
}

.left {
  text-align: left;
  padding-left: 3px;
}

.right {
  text-align: right;
}

.top {
  vertical-align: top;
  padding-top: 5px;
}

.error {
  font: 11px Verdana, Arial, Helvetica, sans-serif;
  color: #AA0000;
}

H1 {
  margin: 11px 0px 5px 0px;
  border: 0px;
  font: 12px Verdana, Arial, Helvetica, sans-serif;
  color: @blue_font_color;
  font-weight: bold;
}

H2 {
  margin: 5px 0px 5px 0px;
  border: 0px;
  padding: 0px;
  font: 12px Verdana, Arial, Helvetica, sans-serif;
  color: @blue_font_color;
  font-weight: bold;
}

H6 {
  font: 11px Verdana, Arial, Helvetica, sans-serif;
  color: @blue_font_color;
  margin: 10px 0px 0px 0px;
  border: 0px;
  font-weight: normal;
}

P {
  font: 11px Verdana, Arial, Helvetica, sans-serif;
  font-weight: normal;
}

TD {
  padding: 0px;
  margin: 0px;
  font-family: Verdana, Arial, Helvetica, sans-serif;
  font-size: 11px;
}

TD.toolbar {
  background-color: #CCCCCC;
  font-size: 11px;
}

TD.mainhr {
  background: url(images/title_delimiter.gif);
  border: none;
  height: 3px;
  margin: 0px 0px 0px 0px;
}

TABLE.sorterTable {
  padding: 0px;
  margin: 0px;
}

TABLE.sorterTable TH {
  font-size: 11px;
}

TABLE.sorterTable TD {
  font-size: 11px;
  text-align: center;
}

TR.sorterTR-H {
  background-color: @light_blue_bg;
}

TR.sorterTR {
  background-color: #FFFFFF;
}

TABLE.bodytable, TABLE.datatable {
  width: 100%;
  margin: 11px 0px 5px 0px;
  border-collapse: collapse;
  border: 1px solid #2B528A;
}

TABLE.noborder {
  width: 100%;
  margin: 11px 0px 5px 0px;
  border-collapse: collapse;
  border: 0px none;
}

TABLE.inline {
  width: 100%;
  margin: 0px 0px 0px 0px;
  border-collapse: collapse;
  border: 0px none;
}

TABLE.outer-border {
  width: 100%;
  margin: 11px 0px 5px 0px;
  border-collapse: collapse;
  border: 1px solid #2B528A;
}

TABLE.nomargin {
  border: 1px solid #2B528A;
  border-collapse: collapse;
  margin: 6px 6px 6px 6px;
}

TABLE.nomargin TD {
  border: 1px solid #2B528A;
  font-size: 11px;
  color: @blue_font_color;
}

TABLE.nbtable {
  width: 100%;
}

TABLE.nbtable TD {
  padding-right: 20;
  padding-top: 5;
  padding-bottom: 5;
  font: 11px Verdana, Arial, Helvetica, sans-serif;
  color: #000000;
}

A.bigger {
  font-size: 12px;
  font-weight: bold;
  color: @blue_font_color;
}

A.bigger:hover {
  font-size: 12px;
  font-weight: bold;
  color: #F87012;
}

A.bigger:visited {
  font-size: 12px;
  font-weight: bold;
  color: @blue_font_color;
}

A.bigger:link {
  font-size: 12px;
  font-weight: bold;
  color: @blue_font_color;
}

TABLE.bodytable-small {
  margin: 11px 0px 5px 0px;
  border-collapse: collapse;
  border: 1px solid #2B528A;
}

TABLE.bodytable TH, TABLE.bodytable-small TH {
  background: @portlet_head;
  background-position: 0 -10px;
  text-align: left;
  padding-left: 10px;
  color: #FFFFFF;
  font: 11px Verdana, Arial, Helvetica, sans-serif;
  font-weight: bold;
  height: 16px;
  vertical-align: middle;
  input {
    margin: 0;
    vertical-align: middle;
  }
}

TABLE.datatable TH {
  background: @portlet_head;
  background-position: 0 -10px;
  text-align: left;
  padding-left: 3px;
  color: #FFFFFF;
  font: 11px Verdana, Arial, Helvetica, sans-serif;
  font-weight: bold;
  height: 16px;
}

TABLE.bodytable TD, TABLE.bodytable-small TD {
  border: 1px solid #2B528A;
  padding-left: 10px;
  font: 11px Verdana, Arial, Helvetica, sans-serif;
  font-weight: normal;
  color: @blue_font_color;
}

TABLE.listtable TD {
  padding: 4px 4px 4px 10px;
}

TABLE.datatable TD {
  border: 1px solid #2B528A;
  font: 11px Verdana, Arial, Helvetica, sans-serif;
  font-weight: normal;
  color: @blue_font_color;
  height: 22px;
}

TABLE.bodytable TD.header, TABLE.bodytable-small TD.header {
  border: 1px solid #2B528A;
  padding-left: 10px;
  padding-top: 2px;
  font: 11px Verdana, Arial, Helvetica, sans-serif;
  font-weight: bold;
  color: #2B528A;
  background: @light_blue_bg;
  height: 22px;
  text-align: left;
  vertical-align: top;
}

TABLE.datatable TD.header {
  border: 1px solid #2B528A;
  font: 11px Verdana, Arial, Helvetica, sans-serif;
  font-weight: bold;
  color: @blue_font_color;
  background: @light_blue_bg;
  height: 22px;
}

TABLE.noborder TD, TABLE.outer-border TD, TABLE.inline TD {
  border: 0px none;
  padding-left: 10px;
  font: 11px Verdana, Arial, Helvetica, sans-serif;
  font-weight: normal;
  color: @blue_font_color;
  height: 22px;
}

TD.noborder {
  border: 0px none;
}

TD.header-noborder {
  border: 1px solid #2B528A;
  padding-left: 10px;
  font: 11px Verdana, Arial, Helvetica, sans-serif;
  font-weight: bold;
  color: @blue_font_color;
  background: @light_blue_bg;
  height: 22px;
}

TD.header-noborder-wh {
  border: 1px solid #2B528A;
  padding-left: 10px;
  font: 11px Verdana, Arial, Helvetica, sans-serif;
  font-weight: bold;
  color: @blue_font_color;
  background: #FFFFFF;
  height: 22px;
}

INPUT.button,
BUTTON.button {
  /*height : 20px;*/
  margin: 1px 5px 1px 0px;
  padding: 0 10px;
  font: 12px Verdana, Arial, Helvetica, sans-serif;
  background: @portlet_head;
  background-position: 0 -20px;
  line-height: 22px;
  height: 22px;
  display: inline-block;
  color: white;
  border: 0;
/*  -webkit-box-shadow: 0px 0px 5px 0px rgba(0,0,0,0.50);
  -moz-box-shadow: 0px 0px 5px 0px rgba(0,0,0,0.50);
  box-shadow: 0px 0px 5px 0px rgba(0,0,0,0.50);*/
}

INPUT.button:disabled,
BUTTON.button:disabled {

  color: #aaaaaa;

}

INPUT.radio {
  color: @blue_font_color;
  margin: 0px 0px 0px 0px;
  font: 11px Verdana, Arial, Helvetica, sans-serif;
}

INPUT.file {
  border: 1px solid #2B528A;
  color: @blue_font_color;
  height: 18px;
  margin: 0px 0px 0px 0px;
  font: 11px Verdana, Arial, Helvetica, sans-serif;
  background: #FFFFFF;
}

INPUT.textfield {
  border: 1px solid #2B528A;
  color: @blue_font_color;
  height: 15px;
  line-height: 15px;
  /*margin: 0px 0px 0px 0px;*/
  margin: 2px 0 1px 0;
  font: 11px Verdana, Arial, Helvetica, sans-serif;
  font-weight: normal;
  background: #FFFFFF;
  padding: 0 0 1px 0;
}

textarea {
  background-color: #FFFFFF;
  color: @blue_font_color;
  font: 12px "Courier New", Courier, monospace;
  border: 1px solid #2B528A;
}

select {
  border: 1px solid @light_blue_bg;
  color: @blue_font_color;
  font: 11px Verdana, Arial, Helvetica, sans-serif;
  font-weight: normal;
  background: #FFFFFF;
}

.yellow-menu {
  color: #F87012;
  font-family: "Helvetica CE", Verdana, Arial, sans-serif;
  font-size: 11px;
  font-weight: bold;
  background: #FFE8BA;
  border: solid;
  border-top: 0px;
  border-left: 0px;
  border-right: 0px;
  border-bottom: 1px solid white;
  padding: 3px 0px 3px 15px;
}

.yellow-menu-last {
  color: #F87012;
  font-family: "Helvetica CE", Verdana, Arial, sans-serif;
  font-size: 11px;
  font-weight: bold;
  background: #FFE8BA;
  border: solid;
  border-top: 0px;
  border-left: 0px;
  border-right: 0px;
  border-bottom: 1px solid #F87012;
  padding: 3px 0px 3px 15px;
}

.yellow-menu-notactive {
  color: #AAAAAA;
  font-family: "Helvetica CE", Verdana, Arial, sans-serif;
  font-size: 11px;
  font-weight: bold;
  background: #FFE8BA;
  border: solid;
  border-top: 0px;
  border-left: 0px;
  border-right: 0px;
  border-bottom: 1px solid white;
  padding: 3px 0px 3px 15px;
}

span.yellow-menu-notactive {
  color: #AAAAAA;
  font-family: "Helvetica CE", Verdana, Arial, sans-serif;
  font-size: 11px;
  font-weight: bold;
  background: #FFE8BA;
  border: none;
  border-top: 0px;
  border-left: 0px;
  border-right: 0px;
  border-bottom-width: 0px;
  padding: 0px 0px 0px 0px;
}

.yellow-menu-notactive-last {
  color: #AAAAAA;
  font-family: "Helvetica CE", Verdana, Arial, sans-serif;
  font-size: 11px;
  font-weight: bold;
  background: #FFE8BA;
  border: solid;
  border-top: 0px;
  border-left: 0px;
  border-right: 0px;
  border-bottom: 1px solid #F87012;
  padding: 3px 0px 3px 15px;
}

.yellow-menu-section {
  color: #FFFFFF;
  font-family: "Helvetica CE", Verdana, Arial, sans-serif;
  font-size: 11px;
  font-weight: bold;
  background: #FF9C00;
  border: solid;
  border-top: 0px;
  border-left: 0px;
  border-right: 0px;
  border-bottom: 1px solid white;
  padding: 0px 0px 0px 0px;
}

.blue-menu {
  color: #2B528A;
  font-family: "Helvetica CE", Verdana, Arial, sans-serif;
  font-size: 11px;
  font-weight: bold;
  background: @light_blue_bg;
  border: solid;
  border-top: 0px;
  border-left: 0px;
  border-right: 0px;
  border-bottom: 1px solid white;
  padding: 3px 0px 3px 15px;

}

.blue-menu-active {
  color: #FFFFFF;
  font-family: "Helvetica CE", Verdana, Arial, sans-serif;
  font-size: 11px;
  font-weight: bold;
  background: #FF9C00;
  border: solid;
  border-top: 0px;
  border-left: 0px;
  border-right: 0px;
  border-bottom: 1px solid white;
  padding: 0px 0px 0px 0px;
}

/*
.blue-menu-active {
	color : #AAAAAA;
	font-family : "Helvetica CE", Verdana, Arial, sans-serif;
	font-size : 11px;
	font-weight : bold;
	background: #FFE8BA;
	border: solid;
	border-top: 0px;
	border-left: 0px;
	border-right: 0px;
	border-bottom-width: 1px;
	padding: 0 0 0 0;
}
*/
/*
.blue-menu-active {
	color : #2B528A;
	font-family : "Helvetica CE", Verdana, Arial, sans-serif;
	font-size : 11px;
	font-weight : bold;
	background: @light_blue_bg;
	border: solid;
	border-top: 0px;
	border-left: 0px;
	border-right: 0px;
	border-bottom-width: 1px;
	padding: 0 0 0 0;
}
*/

.blue-menu-section {
  color: #2B528A;
  font-family: "Helvetica CE", Verdana, Arial, sans-serif;
  font-size: 11px;
  font-weight: bold;
  background: #BED0E6 url(images/menu_bckg.gif);
  border: solid;
  border-top: 0px;
  border-left: 0px;
  border-right: 0px;
  border-bottom: 1px solid white;
  padding: 15px 0px 3px 15px;
}

.link {
  font: 11px "Helvetica CE", Verdana, Arial, sans-serif;
  color: @blue_font_color;
  text-decoration: underline;
  cursor: pointer;
}

A:link {
  font: 11px "Helvetica CE", Verdana, Arial, sans-serif;
  color: @blue_font_color;
  text-decoration: underline;
}

A:visited {
  font: 11px "Helvetica CE", Verdana, Arial, sans-serif;
  color: @blue_font_color;
  text-decoration: underline;
}

A:active {
  font: 11px "Helvetica CE", Verdana, Arial, sans-serif;
  color: @blue_font_color;
  text-decoration: underline;
}

A:hover {
  font: 11px "Helvetica CE", Verdana, Arial, sans-serif;
  color: #F87012;
  text-decoration: underline;
}

A.passive:link {
  color: #888888;
  text-decoration: underline;
}

A.passive:visited {
  color: #888888;
  text-decoration: underline;
}

A.passive:active {
  color: #888888;
  text-decoration: underline;
}

A.passive:hover {
  color: #F87012;
  text-decoration: underline;
}

.passive {
  color: #888888;
}

TH A:link {
  color: #FFFFFF;
  text-decoration: underline;
}

TH A:visited {
  color: #FFFFFF;
  text-decoration: underline;
}

TH A:active {
  color: #FFFFFF;
  text-decoration: underline;
}

TH A:hover {
  color: #F87012;
  text-decoration: underline;
}

#mainmenutable A:link {
  color: #2B528A;
  text-decoration: none;
  font-weight: bold;
}

#mainmenutable A:visited {
  color: #2B528A;
  text-decoration: none;
  font-weight: bold;
}

#mainmenutable A:active {
  color: #2B528A;
  text-decoration: none;
  font-weight: bold;
}

#mainmenutable A:hover {
  color: #F87012;
  text-decoration: none;
  font-weight: bold;
}

#mainmenutable .blue-menu-active A:link {
  color: #FFFFFF;
  text-decoration: none;
  font-weight: bold;
}

#mainmenutable .blue-menu-active A:visited {
  color: #FFFFFF;
  text-decoration: none;
  font-weight: bold;
}

#mainmenutable .blue-menu-active A:active {
  color: #FFFFFF;
  text-decoration: none;
  font-weight: bold;
}

#mainmenutable .blue-menu-active A:hover {
  color: #FFFFFF;
  text-decoration: none;
  font-weight: bold;
}

/*
#mainmenutable .blue-menu-active A:link    { color: #F87012; text-decoration: none; font-weight: bold;} 
#mainmenutable .blue-menu-active A:visited { color: #F87012; text-decoration: none; font-weight: bold;}
#mainmenutable .blue-menu-active A:active  { color: #F87012; text-decoration: none; font-weight: bold;}
#mainmenutable .blue-menu-active A:hover  { color: #2B528A; text-decoration: none; font-weight: bold;}
*/

#contextmenutable A:link {
  color: #F87012;
  text-decoration: none;
  font-weight: bold;
}

#contextmenutable A:visited {
  color: #F87012;
  text-decoration: none;
  font-weight: bold;
}

#contextmenutable A:active {
  color: #F87012;
  text-decoration: none;
  font-weight: bold;
}

#contextmenutable A:hover {
  color: #2B528A;
  text-decoration: none;
  font-weight: bold;
}

#topmenu A:link {
  color: #FFFFFF;
  text-decoration: none;
}

#topmenu A:visited {
  color: #FFFFFF;
  text-decoration: none;
}

#topmenu A:active {
  color: #FFFFFF;
  text-decoration: none;
}

#topmenu A:hover {
  color: #F87012;
  text-decoration: none;
}

#idtable A:link {
  color: #2B528A;
  text-decoration: none;
  font-weight: bold;
}

#idtable A:visited {
  color: #2B528A;
  text-decoration: none;
  font-weight: bold;
}

#idtable A:active {
  color: #2B528A;
  text-decoration: none;
  font-weight: bold;
}

#idtable A:hover {
  color: #F87012;
  text-decoration: none;
  font-weight: bold;
}

#buxusfootertable A:link {
  color: #2B528A;
  text-decoration: none;
}

#buxusfootertable A:visited {
  color: #2B528A;
  text-decoration: none;
}

#buxusfootertable A:active {
  color: #2B528A;
  text-decoration: none;
}

#buxusfootertable A:hover {
  color: #F87012;
  text-decoration: none;
}

#buxusfootertable A.strong:link {
  color: #2B528A;
  text-decoration: none;
  font-weight: bold;
}

#buxusfootertable A.strong:visited {
  color: #2B528A;
  text-decoration: none;
  font-weight: bold;
}

#buxusfootertable A.strong:active {
  color: #2B528A;
  text-decoration: none;
  font-weight: bold;
}

#buxusfootertable A.strong:hover {
  color: #F87012;
  text-decoration: none;
  font-weight: bold;
}

/* navigator */

TD.nav-pn-exp-A {
  color: @blue_font_color;
  background-color: @light_blue_bg;
  font-size: 11px;
  font-weight: bold;
  vertical-align: middle;
  height: 18px;
}

TD.nav-pn-exp-P {
  color: @blue_font_color;
  background-color: @light_blue_bg;
  font-size: 11px;
  font-weight: bold;
  vertical-align: middle;
  height: 18px;
}

TD.nav-pn-A {
  font-size: 11px;
  vertical-align: middle;
  height: 18px;
}

TD.nav-pn-P {
  font-size: 11px;
  vertical-align: middle;
  height: 18px;
}

TD.nav-tt-l {
  width: 5px;
  padding-top: 2px;
  padding-left: 3px;
}

TD.nav-tt-r {
}

A.nav-pn-A {
  font-size: 11px;
  vertical-align: middle;
  text-decoration: none;
}

A.nav-pn-A:Visited {
  font-size: 11px;
  vertical-align: middle;
  text-decoration: none;
}

A.nav-pn-A:Link {
  font-size: 11px;
  vertical-align: middle;
  text-decoration: none;
}

A.nav-pn-A:Hover {
  font-size: 11px;
  vertical-align: middle;
}

A.nav-pn-P {
  color: #888888;
  font-size: 11px;
  vertical-align: middle;
  text-decoration: none;
}

A.nav-pn-P:Visited {
  color: #888888;
  font-size: 11px;
  vertical-align: middle;
  text-decoration: none;
}

A.nav-pn-P:Link {
  color: #888888;
  font-size: 11px;
  vertical-align: middle;
  text-decoration: none;
}

A.nav-pn-P:Hover {
  font-size: 11px;
  vertical-align: middle;
}

A.nav-pn-exp-A {
  font-size: 11px;
  font-weight: normal;
  vertical-align: middle;
  text-decoration: none;
}

A.nav-pn-exp-A:Visited {
  font-size: 11px;
  font-weight: normal;
  vertical-align: middle;
  text-decoration: none;
}

A.nav-pn-exp-A:Link {
  font-size: 11px;
  font-weight: normal;
  vertical-align: middle;
  text-decoration: none;
}

A.nav-pn-exp-A:Hover {
  font-size: 11px;
  font-weight: normal;
  vertical-align: middle;
}

A.nav-pn-exp-P {
  color: #888888;
  font-size: 11px;
  font-weight: normal;
  vertical-align: middle;
  text-decoration: none;
}

A.nav-pn-exp-P:Visited {
  color: #888888;
  font-size: 11px;
  font-weight: normal;
  vertical-align: middle;
  text-decoration: none;
}

A.nav-pn-exp-P:Link {
  color: #888888;
  font-size: 11px;
  font-weight: normal;
  vertical-align: middle;
  text-decoration: none;
}

A.nav-pn-exp-P:Hover {
  font-size: 11px;
  font-weight: normal;
  vertical-align: middle;
}

A.nav-tn {
  color: #FFFFFF;
  text-decoration: none;
  font-weight: bold;
}

A.nav-tn:Link {
  color: #FFFFFF;
  text-decoration: none;
  font-weight: bold;
}

A.nav-tn:Visited {
  color: #FFFFFF;
  text-decoration: none;
  font-weight: bold;
}

A.nav-tn:Hover {
  color: #FFFFFF;
  text-decoration: none;
  font-weight: bold;
}

.nav-type-title {
  padding: 0px 0px 0px 9px;
  background: url(images/portlet_head_divider_small.gif) no-repeat right;
  height: 21px;
}

TD.nav-tn-exp {
  font-size: 11px;
  height: 21px;
  background: @portlet_head;
  color: #FFFFFF;
  vertical-align: middle;
}

TD.nav-tn-exp h2 {
  float: left;
  margin: 0px 0px 0px 0px;
  padding: 3px 0px 0px 9px;
  height: 18px;
  vertical-align: bottom;
  text-align: left;
  font: bold 12px Verdana, Arial, Helvetica, sans-serif;
  color: #FFFFFF;
}

TD.nav-tn-exp h2 a, TD.nav-tn-exp h2 a:link, TD.nav-tn-exp h2 a:visited, TD.nav-tn-exp h2 a:hover {
  font: bold 12px Verdana, Arial, Helvetica, sans-serif;
}

TD.nav-tn-exp .nav-controls {
  background: url(images/portlet_head_divider.gif) no-repeat top left;
  float: right;
  height: 17px;
  padding: 4px 9px 0px 9px;
  vertical-align: middle;
  font: normal 10px Verdana, Arial, Helvetica, sans-serif;
}

TD.nav-tn-exp .nav-controls .nav-selected-sorting {
  font: bold 10px Verdana, Arial, Helvetica, sans-serif;
}

TD.nav-tn-exp .nav-controls a.nav-tn {
  font: normal 10px Verdana, Arial, Helvetica, sans-serif;
  text-decoration: underline;
  color: #FFFFFF;
}

TD.nav-tn-exp .nav-controls a.nav-tn:visited {
  color: #FFFFFF;
}

TD.nav-tn-exp .nav-controls a.nav-tn:link {
  color: #FFFFFF;
}

TD.nav-tn-exp .nav-controls a.nav-tn:hover {
  color: #F87012;
}

DIV.nav-footer {
  padding: 0px 4px 4px 4px;
  font-size: 11px;
  vertical-align: middle;
  text-decoration: none;
}

TD.nav-tn {
  font-size: 11px;
  height: 16px;
  background-color: #29528C;
  color: #FFFFFF;
  vertical-align: middle;
}

IMG.nav-icon {
  border: 0px;
  width: 31px;
  height: 13px;
  vertical-align: middle;
}

IMG.nav-t-gif-w7 {
  border: 0px;
  width: 7px;
  height: 1px;
}

IMG.nav-t-gif-w35 {
  border: 0px;
  width: 35px;
  height: 1px;
}

TABLE.nav-listBorder {
  border: 1px solid #C0C1C3;
  padding: 1px 1px 1px 1px;
}

/* podla ID */
#bodytitle {
  width: 100%;
  border-collapse: collapse;
}

#maintable {
  width: 100%;
  top: 0;
  border-collapse: collapse;
  position: fixed;
  z-index: 100000;
}

#header {
  height: 46px;
  background: url(images/headerbg.png) repeat-x transparent;
}

#headerinfo {
  width: 100%;
  height: 42px;
  margin-bottom: 4px;
  border-collapse: collapse;
}

.buxus-top-link {
  width: 130px;
  height: 42px;
  margin: 0px 12px 0px 0px;
  background: url(images/delimiter.png) no-repeat right;
  /*border-right: 1px solid #094A6F;*/
}

.top-link {
  margin: 0;
  height: 42px;
  line-height: 42px;
}

.top-link a {
  line-height: 42px;
}

.head-delimiter {
  background: url(images/delimiter.png) no-repeat left;
  width: 2px;
  margin: 0 10px 0px 10px;
  /*border-left: 1px solid #094A6F;*/
  height: 42px;
}

#buxus-search {
  height: 42px;
  line-height: 42px;
}

#logo {
  /*	width: 740px; */
  background: url(images/logo.png) no-repeat 15px 12px;
  height: 42px;
}

#layouttable {
  width: 100%;
  /*	height : 100%;*/
  padding: 0px;
  margin: 0px;
  margin-top: 43px;
  border-collapse: collapse;
}

#leftmenutable {
  width: 100%;
  height: 100%;
  padding: 0px;
  margin: 0px;
  border-collapse: collapse;
  background: #BED0E6 url(images/menu_bckg.gif);
}

#idtable {
  width: 190px;
  padding: 0px;
  margin: 0px;
  border-collapse: collapse;
  font: 11px "Helvetica CE", Verdana, Arial, sans-serif;
  font-weight: bold;
  color: #2B528A;
}

#idfield {
  width: 45px;
  font-family: "Helvetica CE", Verdana, Arial, sans-serif;
  font-size: 11px;
}

#mainbodytd {
  padding: 5px 5px 10px 15px;
  font-family: "Helvetica CE", Verdana, Arial, sans-serif;
  font-size: 11px;
}

TABLE.nospacing {
  padding: 0px;
  margin: 0px;
  border-collapse: collapse;
}

#buxusversion {
  text-align: right;
  width: 180px;
  padding: 0px;
  font-family: "Helvetica CE", Verdana, Arial, sans-serif;
  font-size: 9px;
  color: #FFFFFF;
}

#topmenu {
  width: 100%;
  text-align: left;
  padding: 0px 20px 0px 0px;
  font-family: "Helvetica CE", Verdana, Arial, sans-serif;
  font-size: 11px;
  color: #FFFFFF;
}

#contextmenutable {
  width: 100%;
  background: #FFFFFF;
  border-collapse: collapse;
  border: #FFFFFF;
}

#mainmenutable {
  width: 100%;
  background: #FFFFFF;
  border-collapse: collapse;
}

#buxusfootertable {
  width: 100%;
  border-collapse: collapse;
}

#buxusfooter {
  font-family: "Helvetica CE", Verdana, Arial, sans-serif;
  font-size: 11px;
  color: #2B528A;
  padding-top: 30px;
}

.float-right {
  float: right;
}

.float-left {
  float: left;
}

div.bookmark-header {
  padding-top: 2px;
  padding-bottom: 2px;
}

a.link-button {
  font-weight: bold;
  border: 1px solid #2B528A;
  padding: 2px;
  text-decoration: none !important;
  background: @light_blue_bg;
  color: #2B528A !important;
}

a.link-button:visited {
  font-weight: bold;
  border: 1px solid #2B528A;
  padding: 2px;
  text-decoration: none;
  background: @light_blue_bg;
  color: #2B528A;
}

.link-button-disabled {
  font-weight: bold;
  border: 1px solid #2B528A;
  padding: 2px;
  text-decoration: none;
  background: @light_blue_bg;
  color: #FF9C00;
}

a.link-button:hover {
  font-weight: bold;
  border: 1px solid #2B528A;
  padding: 2px;
  text-decoration: none;
  background: #FF9C00;
  color: #2B528A;
}

div.bookmark-list {
  margin: 0px 10px 0px 5px;
  /*border: 1px solid blue;*/
}

div.bookmark-list div {
  padding: 0px 0px 0px 7px;
  margin: 0px 0px 5px 0px;
  background: url(images/list_bullet.gif) no-repeat left top;
  /*border: 1px solid red;*/
}

.bold {
  font-weight: bold !important;
}

/* PORTLET WINDOW STYLES */
.portlet-layout {
  width: 100% !important;
  margin: 0px 0px 0px 0px;
  padding: 0px 0px 0px 0px;
}

.portlet-layout .portlet-layout-column {
  float: left;
  width: 49% !important;
  margin: 0px 0px 0px 0px;
  padding: 0px 0px 0px 0px;
  vertical-align: top;
}

.portlet {
  border: 1px solid #C0C1C3;
  margin: 10px 20px 5px 0 ;
  padding: 1px 1px 1px 1px;
  background: #FAFAFA;
  border-radius: 5px;
  -webkit-box-shadow: 1px 1px 8px 0px rgba(0, 0, 0, 0.30);
  -moz-box-shadow: 1px 1px 8px 0px rgba(0, 0, 0, 0.30);
  box-shadow: 1px 1px 8px 0px rgba(0, 0, 0, 0.30);

  .portlet-head {
    margin: 0px 0px 0px 0px;
    padding: 0px 0px 0px 0px;
    background: @portlet_head;
    border-radius: 5px 5px 0 0;
    height: 21px;
  }
}

.portlet .portlet-head-orange {
  background: url(images/portlet_head_background_orange.gif);
}

.portlet .portlet-head h2 {
  float: left;
  height: 21px;
  margin: 0px 0px 0px 0px;
  padding: 3px 0px 0px 9px;
  vertical-align: bottom;
  text-align: left;
  font: bold 12px Verdana, Arial, Helvetica, sans-serif;
  color: #FFFFFF;
}

.portlet .portlet-head a.portlet-edit-link {
  background: url(images/portlet_head_divider.gif) no-repeat top left;
  float: right;
  text-align: right;
  height: 21px;
  margin: 0px 0px 0px 0px;
  padding: 4px 9px 0px 9px;
  font: normal 10px Verdana, Arial, Helvetica, sans-serif;
  color: #FFFFFF;
}

.portlet .portlet-head a.portlet-edit-link-orange {
  background: url(images/portlet_head_divider_orange.gif) no-repeat top left;
}

.portlet .portlet-head a.portlet-edit-link:visited {
  color: #FFFFFF;
}

.portlet .portlet-head a.portlet-edit-link:link {
  color: #FFFFFF;
}

.portlet .portlet-head a.portlet-edit-link:hover {
  color: #F87012;
}

.portlet .portlet-head .portlet-minimize-button {
  background: url(images/portlet_head_divider.gif) no-repeat top left;
  width: 21px;
  float: right;
}

.portlet .portlet-head .portlet-minimize-button-orange {
  background: url(images/portlet_head_divider_orange.gif) no-repeat top left;
}

.portlet .portlet-head .portlet-close-button {
  background: url(images/portlet_head_divider.gif) no-repeat top left;
  width: 21px;
  float: right;
}

.portlet .portlet-head .portlet-close-button-orange {
  background: url(images/portlet_head_divider_orange.gif) no-repeat top left;
}

.portlet .portlet-body {
  font: normal 11px Verdana, Arial, Helvetica, sans-serif;
  color: @blue_font_color;
  padding: 5px 10px 10px;
  /* border: 1px solid green; */
}

.portlet .portlet-body h3 {
  font: bold 11px Verdana, Arial, Helvetica, sans-serif;
  color: @blue_font_color;
  display: block;
  margin: 0px 5px 5px 5px;
}

.portlet .portlet-body div.hr {
  height: 1px;
  line-height: 1px;
  border-bottom: 1px solid #DDDDDD;
  margin: 10px 0px 5px 0px;
  /*border: 1px solid green;*/
}

/* END PORTLET WINDOW STYLES */

.float-cleaner {
  clear: both;
  height: 1px;
  font-size: 1px;
  line-height: 1px;
  border: none;
  margin: 0px;
  padding: 0px;
  background: transparent;
}

/* HELP STYLES */

div.hr {
  height: 1px;
  line-height: 1px;
  border-bottom: 1px solid #DDDDDD;
  margin: 10px 0px 5px 0px;
}

div.help_index_block {
  margin: 10px;
}

div.help_index_block div.help_title {
  color: @blue_font_color;
}

div.help_index_block div.help_title a {
  font-size: 12px;
  font-weight: bold;
}

div.help_index_block div.help_annotation {
  margin-top: 3px;
  color: @blue_font_color;
}

div#requirements_wrapper {
  background: red;
  padding-top: 40px;
}

div#requirements_encaps {
  margin-left: auto;
  margin-right: auto;
  width: 800px;
}

div#requirements_failure {
  margin: 10px;
  padding: 5px;
  padding-left: 10px;
  float: left;
  width: 600px;
  background-color: red;
  color: white;
  text-align: left;
}

div#requirements_failure h2 {
  color: white;
}

div#requirements_failure p {
  color: white;
}

div#requirements_failure p a {
  color: white;
  font-weight: bold;
  text-decoration: underline;
}

div#requirements_failure p a:hover {
  color: @blue_font_color;
}

div#requirements_failure p a.external {
  color: white;
  font-weight: bold;
  text-decoration: underline;
  background: url(images/popup.gif) no-repeat right top;
  padding-right: 14px;
}

div#requirements_failure p a.external:hover {
  color: @blue_font_color;
}

/* END HELp STYLES */

div.nobookmark {
  font-style: italic;
}

div.tip-of-the-day {
  font-style: italic;
  text-align: justify;
  background: url(images/lightbulb.gif) no-repeat left top;
  padding-left: 35px;
  margin-top: 10px;
}

/* Styles for Buxus JS prompt alternative for Javascript window.prompt() function */
div#overlay_layer {
  background: #BDCFE5;
  position: absolute;
  top: 0px;
  left: 0px;
  width: 100%;
  height: 100%;
  z-index: 99;
  -moz-opacity: 0.75;
  opacity: 0.75;
  filter: alpha(opacity=75);
  display: none;
}

div#bx_prompt_placeholder {
  display: none;
}

div #bx_prompt_label {
  text-align: left;
  padding: 0px 12px 10px 6px;
}

input#bx_prompt_text {
  width: 280px;
}

img.filter-picker {
  vertical-align: middle;
  margin-bottom: 5px;
  cursor: pointer;
}

div.callendar {
  display: block;
  visibility: visible;
  z-index: 100;
  position: absolute;
  width: 200;
  top: 50px;
  left: 200px;
  background-color: #CCCCCC;
  border: 2px #ddddff solid;
  font-size: 12px;
  -moz-opacity: 1;
}

div.callendar td {
  font-size: 12px;
  vertical-align: middle;
}

/* ============== tabs ===================== */

ul.tabs {
  margin: 0px;
  padding: 10px 6px 5px 0px;
  list-style: none;
}

ul.tabs li {
  display: inline;
  padding: 4px 6px 4px 6px;
  background: @portlet_head;
  cursor: pointer;
  cursor: hand;
  color: white;
  margin-right: 1px;
  -moz-user-select: none;

}

ul.tabs li.active {
  padding-top: 6px;
  padding-bottom: 6px;
  background-color: white;
  border-color: yellow;
  color: #2B528A;
  border: 1px solid #C0C1C3;
  border-bottom: 0px;
  background-image: none;
}

div.tabs-content {
  border: 1px solid #C0C1C3;
  padding: 6px;
}

div#shop table {
  width: 97%;
  margin-top: 0px;
  border-bottom-width: 0px;
  border-top-width: 0px;
}

div#shop table td, div#shop table td.header {
  border-top-width: 0px;
  border-right-width: 0px;
  padding: 2px;
  vertical-align: middle;
  font-size: 11px;
}

div#shop table th {
  padding: 4px;
  font-size: 11px;
}

div#shop table.items-table {
  border-color: silver;
  border-top: 1px solid silver;
  border-left: 1px solid silver;
}

table.items-table td {
  border-color: silver;
  vertical-align: top;
}

table.items-table td.header {
  border-color: silver;

}

/* ============ order details =============== */

tr.new-item-edit td {
  background-color: #BED0E5;
  padding-top: 3px;
  padding-bottom: 3px;
}

tr.new-item td {
  background-color: #F4F7FB;
}

tr.new-item-edit td a.edit-link {
  display: none;
}

tr.new-item td a.edit-link {
  display: inline;
}

tr.new-item-edit td a.close-link {
  display: inline;
}

tr.new-item td a.close-link {
  display: none;
}

tr.topborder td a, tr.topborder td a:hover, tr.topborder td a:visited {
  color: black;
}

div#shop tr.topborder td {
  background-color: #BFD1E6;
  color: black;
  border: 1px solid #2B528A;
  border-bottom-width: 0px;
  border-bottom: 1px solid #2B528A;
  border-left-width: 1px;
  border-width: 0px;
  border-top: 2px solid #2B528A;
}

tr.topborder td input {
  color: black;
}

div#shop tr.bottomborder td {
  border-top-width: 0px;
  background-color: #BFD1E6;
  padding: 0px;
  border-left-width: 0px;
}

div.item-form {
  border: 2px solid #2B528A;
  border-top-width: 0px;
  padding: 6px;
  border-top: 1px solid #2B528A;
}

div.item-form select {
  color: black;
  width: 100%;
}

div.item-form table {
  border-collapse: collapse;

}

div.item-form th, div.item-form table td {
  padding: 3px;
  color: black;
}

div.item-form th {
  background-color: #BFD1E6;
  padding-right: 6px;
  width: 120px;
  text-align: right;
}

div.item-form td {
  border-width: 0px;
}

div#shop table td.first-cell {
  width: 70px;
  padding-right: 6px;
  vertical-align: middle;
  border-left-width: 0px;
  padding-left: 6px;
  text-align: right;
}

div#shop div.add-cell {
  padding: 6px;
  background-image: url('../../../system/images/new.gif');
  background-repeat: no-repeat;
  background-position: 3px 5px;
  padding-left: 21px;
}

div#shop table td.actions-cell {
  width: 60px;
  background-repeat: no-repeat;
  background-position: 3px center;
  padding-left: 5px;
  padding-right: 5px;
}

div#shop table td.actions-cell .delete {
  background-repeat: no-repeat;
  background-position: 3px center;
  padding-left: 20px;
}

div#shop table tr.new-item-edit td.first-cell {
  background-color: #2B528A;
  color: white;

  background-image: url('../../../system/images/close2.gif');
  background-repeat: no-repeat;
  background-position: 4px center;
}

div#shop table tr.new-item td.first-cell {
  background-image: url('../../../system/images/edit2.gif');
  background-repeat: no-repeat;
  background-position: 4px center;
}

div#shop table tr.new-item-edit td.first-cell a {
  color: white;
}

div#shop table tr.new-item-edit td.actions-cell {
  border-right: 2px solid #2B528A;
  background-image: none;
}

div#shop table tr.new-item-edit td.actions-cell a {
  visibility: hidden;
}

div#shop tr.topborder td.item-column {
  background-color: #BFD1E6;
  color: black;
  border-bottom-width: 0px;
}

#save_new_item {
  display: none;
}

ul.tabs li#changed {
  background-color: #FF9C00;
  display: none;
  background-image: none;
}

div#shop table td.id-cell {
  text-align: right;
  width: 20px;
  padding-right: 6px;
  border-left-width: 0px;
  vertical-align: middle;
}

div#shop table td.item-name-cell {
  vertical-align: middle;
}

#wait {
  display: none;
}

img.price-type-switcher {
  border-width: 0px;
  cursor: pointer;
  cursor: hand;
  float: left;
  margin-left: 2px;
}

input.price-type {
  float: left;
  width: 70px;
  _margin-top: 1px;
}

/* END OF Styles for Buxus JS prompt alternative for Javascript window.prompt() function */

#value_type_form_holder {
  margin-bottom: 6px;
}

#value_type_form_loading {
  margin-bottom: 6px;
  margin-top: 6px;
  display: none;
}

div.info {
  border: 2px solid green;
  padding: 1.2em;
  color: green;
  background-color: #E9FEE3;
  margin-top: 12px;
}

div.error {
  border: 2px solid #b02500;
  padding: 1.2em;
  color: #b02500;
  margin-top: 12px;
  background-color: #fdd7cd;
}

table.options {
  border: 1px solid #ededed;
  border-collapse: collapse;
  margin-top: 6px;
  margin-bottom: 6px;
}

table.options th, table.options td {
  border: 1px solid #ededed;
  background-color: white;
  color: black;
}

table.options td input {
  border: 1px solid gray;
}

table.options th, table.options td {
  border: 1px solid #ededed;
  background-color: white;
  color: black;
  padding: 3px;
}

TABLE.bodytable td.invalid_property,
TABLE.bodytable td.invalid_property .colon {
  background-color: #fdd7cd;
  color: #b02500;
}

/* Photo gallery */
.photo-gallery-edit-list-thumbnail {
  padding: 5px;
}

.pagelist_entertext {
  color: grey;
  font-style: italic;
}

div.pagelist_table {
  float: left;
  margin: 0px;
  padding: 0px;
  overflow: hidden;
}

div.pagelist_table table {
  table-layout: fixed;
  border-collapse: collapse;
}

div.pagelist_table table.bodytable td.pagelist_td {
  overflow: hidden;
  text-overflow: ellipsis;
}

.page_list_input input {
  width: 99%;
}

table.pagelist_suggestion {

  border: 1px solid #dbdbdb;
  padding: 0px;

  border-collapse: collapse;
  width: 420px;
}

table.pagelist_suggestion td {
  border: 0px;
  cursor: pointer;
}

div.pagelist_filter {
  float: left;
  padding: 15px 0px 0px 15px;
  width: 100px;
}

table.pagelist_filter td {
  border: 0px;
}

table.pagelist_filter_checkbox td {
  border: 0px;
}

img.pagelist_delete_all {
  vertical-align: middle;
  cursor: pointer;
}

div.pagelist_delete_all {
  width: 500px;
  text-align: right;
  cursor: pointer;
}

span.pagelist_delete_all_text {
  font-weight: bold;
  padding-left: 5px;
  cursor: pointer;
}

table.bodytable td.pagelist_warnings {
  border: 1px solid #FFFFFF;
  background-color: #FFFFFF;
}

span.page_list_input ul.ui-autocomplete {

  width: 410px;
}

/* autocomplete - selected suggestion */
span.page_list_input .ui-state-hover, span.page_list_input .ui-widget-content .ui-state-hover, span.page_list_input .ui-state-focus, span.page_list_input .ui-widget-content .ui-state-focus {
  border: 1px solid #ffffff;
  background-color: #ff9c00;
  font-weight: bold;
  /*color: #2b528a;*/
  color: #ffffff;
}

div.page_list_items_value_cell {
  cursor: text;
}

div.page_list_items_actions .move_down, div.page_list_items_actions .move_up, div.page_list_items_actions .delete, div.page_list_items_actions .bind_link {
  cursor: pointer;
}

span.pagelist_suggestion_text {
  font: 11px Verdana, Arial, Helvetica, sans-serif;
}

span.pagelist_suggestion_id {
  font: 11px Verdana, Arial, Helvetica, sans-serif;
  width: 100px;
  float: left;
}

.page_list_read_only_value {
  cursor: default;
  font-weight: bold;
}

.bind_mode {
  color: #808080;
}

.page_list_wait_for_server {
  float: left;
  padding-top: 10px;
  padding-left: 5px;
}

a.link_link {
  border: 0px solid red;
  margin: 0px;
  padding: 0px;
}

a.link_link img {
  border: 0px solid red;
  margin: 0px;
  padding: 0px;
}

tr.layoutspacer td {
  background: #ffffff;
}

ul.errors {
  font: 11px Verdana, Arial, Helvetica, sans-serif;
  color: #AA0000;
  list-style: none;
  padding: 0;
}

ul.errors li {
  padding: 0;
}

/* suggest */
#buxus-search {
  position: relative;
  height: 22px;
  -moz-border-radius: 10px;
  -webkit-border-radius: 10px;
  border-radius: 10px;
  padding: 0 5px 2px 5px;
  box-shadow: 0 0 5px 0 #666666 inset;
  margin-top: 8px;
  background: #ffffff;

  vertical-align: top;
}

#buxus-search .search_suggest {
  width: 460px;
  background: #ffffff;
  border: 1px solid #a1a1a1;
  padding: 0;
  position: absolute;
  top: 22px;
  left: 8px;
  z-index: 600;
  font-size: 10px;
  line-height: 14px;
}

#buxus-search .search_suggest .section {
  margin: 0 0 5px 0;
}

#buxus-search .search_suggest .title {
  font-size: 12px;
  color: #aaaaaa;
  padding: 0 0 4px 0;
  margin: 5px 5px 1px 5px;
  font-style: italic;
}

#buxus-search .search_suggest .suggest-item {
  padding: 1px 5px 1px 15px;
}

#buxus-search .search_suggest .suggest-item a {
  display: block;
  color: #000000;
}

#buxus-search .search_suggest .suggest-item:hover {
  background-color: /*#2B528A*/ #888888;
  color: #ffffff;
}

#buxus-search .search_suggest .suggest-active {
  background-color: #2B528A;
  color: #ffffff;
}

#buxus-search .search_suggest .suggest-active a,
#buxus-search .search_suggest .suggest-item:hover a {
  color: #ffffff;
  text-decoration: none;
}

#mainSearchPageName {
  border: 0;
  background: #ffffff;
  margin: 4px 0 0 5px;
  height: 14px;
  vertical-align: top;
}

#pageSearchSubmit {
  border: 0;
  background: url(images/search.png) no-repeat 2px 4px transparent;
  cursor: pointer;
  width: 20px;
  height: 22px;
  box-shadow: none;
}

#pageSearchSubmit.wait {
  background: url(images/search_wait.gif) no-repeat 2px 4px transparent;
}

#buxus-search label {
  color: #A1A1A0;
  cursor: text;
  left: 10px;
  position: absolute;
  top: 5px;
}

.ui-tabs {
  border: 0;
}

.ui-tabs .ui-tabs-panel {
  padding: 0;
}

.ui-tabs .ui-tabs-nav {
  background: #ffffff;
  border: 0;
  border-bottom: 1px solid #aaaaaa;

  -moz-border-radius-bottomright: 0;
  -webkit-border-bottom-right-radius: 0;
  -khtml-border-bottom-right-radius: 0;
  border-bottom-right-radius: 0;

  -moz-border-radius-bottomleft: 0;
  -webkit-border-bottom-left-radius: 0;
  -khtml-border-bottom-left-radius: 0;
  border-bottom-left-radius: 0;
}

.paginationControl span,
.paginationControl a {
  font-size: 10px;
}

.nav_tools {
  float: right;
  margin: 5px 0;
}



table#login-table.bodytable-small {

  border: none;
  border-collapse: collapse;
  tr {
    th {
      border:none;
      height: 30px;
      line-height: 30px;
      background-position: 0 0;
      border-radius: 5px 5px 0 0;
    }
  }

  td.header, td {
    border: 0;
    padding: 4px 4px 0 4px;
    input {
      height: 20px;
      padding: 2px 5px;
    }


    .button {
      padding: 0 10px;
    }

  }

  margin: 20px 0px;

}

.left_menu_block {
/*  -webkit-box-shadow: 3px 0px 10px 0px rgba(0, 0, 0, 0.75);
  -moz-box-shadow: 3px 0px 10px 0px rgba(0, 0, 0, 0.75);
  box-shadow: 3px 0px 10px 0px rgba(0, 0, 0, 0.75);*/
}

.chzn-select
{
  width: 100%;
}

#properties-list {
  tr.item:hover {
    background: #cccccc;
  }
}