<?php
/**
 * One time template convertor - converts existing templates to file based templates.
 * Walks through all the templates, checks whether the template is a file based one.
 *
 * The template is a file based one, if ihe value of the template is
 *
 * UsePropertyFromFile('file-name');
 *
 * If the template is not file based, it's file is created, the template is
 * saved into the file and the database value is overwritten with UsePropertyFromFile().
 *
 * <AUTHOR>
 * @package Templates
 */
  require_once 'includes/functions.php';
	require_once 'includes/uif_functions.php';
	require_once 'includes/local_property_functions.php';

    require_once 'includes/validate.php';

    class TemplateConvertor
    {
    	var $status;


    	/**
    	 * Log.
    	 *
    	 * @var FileLog
    	 */
    	var $log;

    	/**
    	 * @var FileSystem
    	 */
    	var $fs;

    	/**
    	 * If set to true, the converting function will convert a file, if possible.
    	 * If set to false, it will not do any changes, just show log.
    	 *
    	 * @var boolean
    	 */
    	var $do_convert = false;

    	/**
    	 * If set to true, analysis will be run. No conversion unless do_convert is true.
    	 *
    	 * @var boolean
    	 */
    	var $do_switch = false;

    	var $syntax_checker;

    	var $count = 0;

    	var $mode;

    	var $transition_subdir;

        public function __construct()
    	{
    		// Skontroluje, ci existuje adresar pre log
    		if (!is_dir(config('buxus_core.doc_upload_path') . 'log/'))
    		{// Adresar logu nie je vytvoreny
    			mkdir(config('buxus_core.doc_upload_path') . 'log/', 0777);
    		}

    		$log_file_name = config('buxus_core.doc_upload_path') . 'log/tmpl_conv_' . Date('Ymd-His.txt');
    		$this->log = new FileLog($log_file_name);
    		chmod($log_file_name, 0777);

    		$this->syntax_checker = GetSystemOption('C_syntax_checker');
    		$this->fs = $GLOBALS['FS'];

    		$this->status['issues'] = 0;

    		switch (GetSystemOption('C_including_properties_from_files'))
    		{
    			case 0:
    				$this->mode = 'OLD_DB_BASED';
    			break;

    			case 1:
    				$this->mode = 'NEW_FILE_BASED';
    			break;

    			case 2:
    				$this->mode = 'TRANSITION';
    			break;
    		}

    		$this->transition_subdir = C_PROPERTY_TRANSITION_DIR;
    	}

    	function getTemplates()
    	{
    		$sql = "SELECT
    					page_id
    				FROM
    					tblPages
    				WHERE
    					page_class_id = :page_class_id";

    		$pages = BuxusDB::get()->fetchCol($sql, array(':page_class_id' => C_pc_Template));

    		return $pages;
    	}

    	function getStatus()
    	{
    		$pages = $this->getTemplates();

    		$this->status['count'] = count($pages);

    		$file_based = 0;

    		foreach($pages as $page_id)
    		{
    			if ($this->fileBased($page_id))
    			{
    				$file_based++;
    			}
    		}

    		$this->status['file_based'] = $file_based;
    	}

    	function getTextAreaProperties($page_id)
    	{
    		$sql = "SELECT
    					PPV.property_value,
    					P.property_name,
    					P.property_tag,
    					P.property_id
    				FROM
    					tblPagePropertyValues PPV
    					INNER JOIN
    					tblProperties P
    					ON
    					PPV.property_id = P.property_id
    				WHERE
    					PPV.page_id = :page_id
    					AND
    					P.show_type_tag = :show_type_tag";

    		$values = BuxusDB::get()->fetchAll($sql, array(':page_id' => $page_id, ':show_type_tag' => C_BuxusInputTextArea));

    		return $values;
    	}

    	function propertyFileBased($value)
    	{
    		$value = preg_replace('/\\s+/', '', $value);
    		$value = str_replace('//<?php', '', $value);
    		$value = str_replace('//<?', '', $value);
    		$value = str_replace('//?>', '', $value);
    		$value = trim($value);

			return preg_match("/^usepropertyfromfile\(.*\);?$/i", $value);
    	}

    	/**
    	 * Returns true, if the whole template is file based,
    	 * false if at least one property is not.
    	 *
    	 * @param int $page_id
    	 */
    	function fileBased($page_id)
    	{
    		$values = $this->getTextAreaProperties($page_id);

    		foreach($values as $value_row)
    		{
    			$value = $value_row['property_value'];

    			if (!$this->propertyFileBased($value))
    			{
    				return false;
    			}
    		}

    		return true;
    	}

    	function switchToFileMode()
    	{
    		SetSystemOption('C_including_properties_from_files', 1);

    	    $pages = $this->getTemplates();

    		foreach($pages as $page_id)
    		{
    			$this->copyTransitionFiles($page_id);
    		}
    	}

    	function createTransitionFiles()
    	{
    		if (empty($this->syntax_checker) || !file_exists($this->syntax_checker))
    		{
    			$this->log->log('Syntax checker ['. $this->syntax_checker . '] not found. Aborting includability checks. Check SO C_syntax_checker.');
    			return;
    		}

    		$pages = $this->getTemplates();

    		foreach($pages as $page_id)
    		{
    			$this->createTransitionFilesForPage($page_id);
    		}
    	}

    	function savePropertyValue($value, $page_id, $property_id)
    	{
    		$sql = "REPLACE INTO
    					tblPagePropertyValues
    				SET
    					property_value = :property_value,
    					page_id = :page_id,
    					property_id = :property_id";

    		BuxusDB::get()->query($sql, array(':property_value' => $value, ':page_id' => $page_id, ':property_id' => $property_id));
    	}

    	function checkAndFixDirectory($dirname, $chmod = false)
    	{
    	    if (!$this->fs->dirWritable($dirname))
    		{
    			$created = $this->fs->makeDir($dirname);

    			if (!$created)
    			{
	    			$this->log->log('Directory "' . $dirname . '" cannot be created.');
	    			return false;
    			}
    		}

    		if ($chmod)
    		{
				$writable = $this->fs->chmod($dirname, 0777);

	    		if (!$writable)
	    		{
			    	$this->log->log('Cannot set write permissions for directory "' . $dirname . '"');
			    	return false;
	    		}
    		}

    		return true;
    	}

    	function checkAndFixTransitionDir($property_dir, $page_name, $property_name)
    	{
    		$page_name = CBuxusTextAreaDual::fixPathString($page_name);
    		$property_filename = CBuxusTextAreaDual::getPropertyFilenamePart($property_name);

    		$transition_subdir = $property_dir . $this->transition_subdir;
			$page_name_subdir = $transition_subdir . '/' . $page_name;

    		$directory_ok = $this->checkAndFixDirectory($transition_subdir, $this->fs->getId() == 'direct');

    		if ($directory_ok)
			{
    			 $directory_ok = $this->checkAndFixDirectory($page_name_subdir, $this->fs->getId() == 'direct');

    			 if ($directory_ok)
    			 {
					if ($this->fs->is_readable($page_name_subdir . '/' . $property_filename))
		    		{
		    			$deleted = $this->fs->delete($page_name_subdir . '/' . $property_filename);

		    			if (!$deleted)
		    			{
			    			$this->log->log('File "' . $page_name_subdir . '/' . $property_filename . '" cannot be deleted.');

			    			return false;
		    			}
		    		}
				}
				else
				{
					return false;
				}
			}
			else
			{
				return false;
			}

            return true;
    	}

    	function copyTransitionFiles($page_id)
    	{
   			$property_dir = GetSystemOption('C_properties_dir');

    		$sql = "SELECT
    					*
    				FROM
    					tblPages
    				WHERE
    					page_id = :page_id";

    		$info = BuxusDB::get()->fetchRow($sql, array(':page_id' => $page_id));

    		$this->log->log('Copying transition files for template '.$info['page_name'].', ID:' . $page_id . ', parent ID: '. $info['parent_page_id']);

    		$properties = $this->getTextAreaProperties($page_id);

    		$page_name = $info['page_name'];

    		foreach($properties as $property)
    		{
    			//$this->checkAndFixTransitionDir($property_dir, $page_name, $property['property_tag']);

    			$log_row = 'Property: ' . $property['property_name'] . ', ID: ' . $property['property_id'].': ';

    		   	$transition_filename = CBuxusTextAreaDual::getPropertyTransitionFilename($page_name, $property['property_tag']);
    		   	$filename = CBuxusTextAreaDual::getPropertyFilename($page_name, $property['property_tag']);

   				if (!$transition_filename)
   				{
   					$log_row .= 'ERROR: filename cannot be created';
   					$this->log->log($log_row);
   					continue;
   				}

    			// NOT FILE BASED - create new file and change property value in DB
    			if (!$this->propertyFileBased($property['property_value']))
    			{
    				$log_row .= 'not file based - creating new file';

    				$this->checkAndFixDirectory(dirname($property_dir . $filename), $this->fs->getId() == 'direct');

    				$copied = $this->fs->copy($property_dir . $transition_filename, $property_dir . $filename);

					if (!$copied)
					{
						$log_row .= ', ERROR: unable to copy file from "' . $transition_filename . '" to "' . $filename . '".';
						$this->log->log($log_row);
						continue;
					}
					else
					{
						$log_row .= ', file "' . $transition_filename . '" was successfuly copied to "' . $filename . '".';
					}

   					$new_value = 'UsePropertyFromFile(\''.$filename.'\');';

   					$this->savePropertyValue($new_value, $page_id, $property['property_id']);

   					$log_row .= ', content of db property was updated to "UsePropertyFromFile(\''.$filename.'\');"';
    			}
    			// FILE BASED - save content of the file used by db property into transition file
    			else
    			{
    			    preg_match('/usepropertyfromfile\([\'\"]([^\)]+)[\'\"]\)/i', $property['property_value'], $m);

	    			$filename = $m[1];

	    			$log_row .= 'file based';

	    			if (empty($filename))
					{
						$log_row .= ', cannot find file name.';
						$this->log->log($log_row);
						continue;
					}
					else
					{
						$log_row .= ', file name: '.$filename;

						if (!file_exists($property_dir . $filename))
						{
							$log_row .= ', **  FILE NOT FOUND  **.';
							$this->status['issues']++;

							$this->log->log($log_row);
							continue;
						}
					}

					$current_file = $property_dir . $filename;

					$transition_filename = CBuxusTextAreaDual::getPropertyTransitionFilename($page_name, $property['property_tag']);

					if (!$transition_filename)
					{
						$log_row .= ', unable to prepare transition file.';
						$this->log->log($log_row);
						continue;
					}
					else
					{
						$copied = $this->fs->copy($property_dir . $transition_filename, $current_file);

						if (!$copied)
						{
							$log_row .= ', unable to copy file from "' . $property_dir . $transition_filename . '" to "' . $current_file . '".';
							$this->log->log($log_row);
							continue;
						}
						else
						{
							$log_row .= ', file "' . $transition_filename . '" was successfuly copied to "' . $filename . '".';
						}
					}
    			}

    			if ($this->fs->getId() == 'direct')
    			{
	    			$writable = $this->fs->chmod($property_dir . $filename, 0777);

			    	if (!$writable)
			    	{
					   	$this->log->log('Cannot set write permissions for file "' . $filename . '"');
			    	}
    			}

    			$this->log->log($log_row);
    		}

    		$this->log->log('************************************************************************');
    	}

    	function createTransitionFilesForPage($page_id)
    	{
    		$sql = "SELECT
    					*
    				FROM
    					tblPages
    				WHERE
    					page_id = :page_id";

    		$info = BuxusDB::get()->fetchRow($sql, array(':page_id' => $page_id));

    		$this->log->log('Checking template '.$info['page_name'].', ID:' . $page_id . ', parent ID: '. $info['parent_page_id']);

    		$properties = $this->getTextAreaProperties($page_id);

    		$page_name = $info['page_name'];

    		$property_dir = GetSystemOption('C_properties_dir');

    		foreach($properties as $property)
    		{
    			$this->checkAndFixTransitionDir($property_dir, $page_name, $property['property_tag']);

    			$log_row = 'Property: ' . $property['property_name'] . ', ID: ' . $property['property_id'].': ';

    			// NOT FILE BASED - save content of db property into transition file
    			if (!$this->propertyFileBased($property['property_value']))
    			{
    				$log_row .= 'not file based';

   					$filename = CBuxusTextAreaDual::getPropertyTransitionFilename($page_name, $property['property_tag']);

   					if (!$filename)
   					{
   						$log_row .= ', CANNOT CREATE TRANSITION FILE';
   						$this->log->log($log_row);
   						continue;
   					}

   					$fd = $this->fs->openfile($property_dir . $filename, 'w');

   					if (!$fd)
   					{
   						$log_row .= ', cannot create file, did not convert.';
   						$this->log->log($log_row);
   						continue;
   					}

   					$this->fs->fwrite($fd, $property['property_value']);

   					$this->fs->closefile($fd);

   					$log_row .= ', saved to file.';

   					//$new_value = 'UsePropertyFromFile(\''.$upf_filename.'\');';

   					//$this->savePropertyValue($new_value, $page_id, $property['property_id']);

   					//$property['property_value'] = $new_value;
    			}
    			// FILE BASED - save content of the file used by db property into transition file
    			else
    			{
    			    preg_match('/usepropertyfromfile\([\'\"]([^\)]+)[\'\"]\)/i', $property['property_value'], $m);

	    			$filename = $m[1];

	    			$log_row .= 'file based';

	    			if (empty($filename))
					{
						$log_row .= ', cannot find file name.';
						$this->log->log($log_row);
						continue;
					}
					else
					{
						$log_row .= ', file name: '.$filename;

						if (!file_exists($property_dir . $filename))
						{
							$log_row .= ', **  FILE NOT FOUND  **.';
							$this->status['issues']++;

							$this->log->log($log_row);
							continue;
						}
					}

					$current_file = $property_dir . $filename;

					$filename = CBuxusTextAreaDual::getPropertyTransitionFilename($page_name, $property['property_tag']);

					if (!$filename)
					{
						$log_row .= ', unable to prepare transition file.';
						$this->log->log($log_row);
						continue;
					}
					else
					{
						$copied = $this->fs->copy($current_file, $property_dir . $filename);

						if (!$copied)
						{
							$log_row .= ', unable to copy file to "' . $property_dir . $filename . '".';
							$this->log->log($log_row);
							continue;
						}
					}
    			}

				$includable = $this->checkIncludability($property_dir . $filename);

				switch($includable)
				{
					case 1:
						$log_row .= ', error occured';
						$this->status['issues']++;
						break;
					case 2:
						$log_row .= ', convertable, did not convert.';
						break;
					case 3:
						$log_row .= ', ** CONVERTED **';
						break;
					case 4:
						$log_row .= ', not convertable, *** SYNTAX ERRORS ***, needs manual attention';
						$this->status['issues']++;
						break;
					case 5:
						$log_row .= ', valid PHP file, no need to convert';
						break;

				}

				if ($this->fs->getId() == 'direct')
				{
	    			$writable = $this->fs->chmod($property_dir . $filename, 0777);

			    	if (!$writable)
			    	{
					   	$this->log->log('Cannot set write permissions for file "' . $filename . '"');
			    	}
				}

    			$this->log->log($log_row);
    		}

    		$this->log->log('************************************************************************');
    	}

    	/**
    	 * Returns true, if the file is syntactically OK and can be included.
    	 *
    	 * The algorithm:
    	 * 1. check the file for syntax - get result R1
    	 * 2. check the file for syntax - get result R2
    	 *
    	 * If R1 = true and R2 = false -> the file is a valid PHP include
    	 * if R1 = true and R2 = true -> the file is a convserion candidate
    	 * if R1 = false, then there is a problem - we will not touch the file
    	 *
    	 * @param string $file_name
    	 * @return int 1 - error, 2 - ok, not converted, 3 - ok, converted, 4 - syntax error, 5 - OK, no need to convert
    	 */
    	function checkIncludability($file_name)
    	{
    		$tmp_dir = config('buxus_core.upload_tmp_path');

    		$target_file = $tmp_dir . 'convertor_test.php';

    		$fd = $this->fs->openfile($target_file, 'w');

    		if (!$fd)
    		{
    			$this->log->log('Cannot create temporary file');
    			return 1;
    		}

    		$this->fs->fwrite($fd, "<?php\n");

    		$lines = $this->fs->file($file_name);

    		foreach($lines as $line)
    		{
    			if (
    				trim($line) == '//<?php'
    				||
    				trim($line) == '//<?'
    				||
    				trim($line) == '//?>'
    			)
    			{
    				continue;
    			}

    			$this->fs->fwrite($fd, $line);
    		}

    		$this->fs->fwrite($fd, "\n");

    		$this->fs->closefile($fd);

    		//check the original file
    		$result = $this->checkFile($file_name);

    		switch($result)
    		{
    			case 1://valid PHP
    				return 5;
    			case 2://not PHP code
    				//try the converted file
    				$result = $this->checkFile($target_file);
    				if ($result == 1)
    				{
    					if (!$this->do_convert)
						{
							return 2;
						}

						$this->fs->copy($target_file, $file_name);
						return 3;
    				}
    			case 0:
    				return 4;
    		}

			return 1;
    	}

    	/**
    	 * Checks a single file for PHP validity.
    	 * Note: a file, that does not contain the PHP opening tag <?php
    	 * is always valid.
    	 *
    	 * @param string $file_name
    	 *
    	 * @return int 1, if the file is a valid PHP file, 0 if there is a syntax error and 2, if the file is
    	 * not a PHP file (i.e. does not contain any opening PHP tag '<?' ). In case of 2, the file is not syntactically checked.
    	 *
    	 */
    	function checkFile($file_name)
    	{
    		if (!$this->isPHPFile($file_name))
    		{
    			return 2;
    		}

    		$cl = $this->syntax_checker . ' -l "'.$file_name.'"';

    		ob_start();
			system($cl);
			$output = ob_get_clean();

			$result = preg_match("/No syntax error/i", $output);

			if ($result)
			{
				return 1;
			}

			return 0;
    	}

    	/**
    	 * Checks whether the specified file contains PHP opening tag '<?'.
    	 * This tag must not be on a comment line, i.e.
    	 * //<?php
    	 *
    	 * @param string $file_name
    	 * @return unknown
    	 */
    	function isPHPFile($file_name)
    	{
    		$data = $this->fs->file($file_name);

    		if (!$data)
    		{
    			return false;
    		}

    		$result = CBuxusTextAreaSynch::isPHPCode($data);

    		return $result;
    	}

    	function draw()
    	{
    		$xtpl = new XTemplate('../includes/templates/template_convertor.xtpl.html');

    		$this->getStatus();

    		if ($this->do_convert)
    		{
    			$this->createTransitionFiles();
    		}

    		if ($this->do_switch)
    		{
    			$this->switchToFileMode();
    		}

    		$xtpl->assign('TRANSITION_DIR', GetSystemOption('C_properties_dir') . $this->transition_subdir);
    		$xtpl->assign('MODE', $this->mode);
    		$xtpl->assign('STAT', $this->status);

    		$xtpl->assign('LOG', $this->log->getLog(''));

    		if ($this->status != 'OLD_DB_BASED')
    		{
    			$xtpl->parse('main.controls');
    		}

    		$xtpl->parse('main');
    		return $xtpl->text('main');
    	}
    }

    DisplayHtmlHeader('Template convertor', C_MenuType_Admin);

	echo HTML_PageTitle('Template convertor', '');


	if (GetSystemOption('C_including_properties_from_files') == 2)
	{// Skript mafungovat
		$tc = new TemplateConvertor();

		if (isset($_REQUEST['switch']))
		{
			$tc->do_switch = true;
		}

		if (isset($_REQUEST['convert']))
		{
			$tc->do_convert = true;
		}

		echo $tc->draw();
	}
	else
	{// Skript nema fungovat
		echo 'This script works only if the value of the system option C_including_properties_from_files is set to 2.';
	}

	DisplayHtmlFooter();
