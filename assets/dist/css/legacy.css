
/*! jQuery UI - v1.10.3 - 2013-12-25
* http://jqueryui.com
* Includes: jquery.ui.core.css, jquery.ui.autocomplete.css, jquery.ui.menu.css, jquery.ui.theme.css
* To view and modify this theme, visit http://jqueryui.com/themeroller/?ffDefault=Lucida%20Grande%2CLucida%20Sans%2CArial%2Csans-serif&fwDefault=bold&fsDefault=1.1em&cornerRadius=5px&bgColorHeader=5c9ccc&bgTextureHeader=gloss_wave&bgImgOpacityHeader=55&borderColorHeader=4297d7&fcHeader=ffffff&iconColorHeader=d8e7f3&bgColorContent=fcfdfd&bgTextureContent=inset_hard&bgImgOpacityContent=100&borderColorContent=a6c9e2&fcContent=222222&iconColorContent=469bdd&bgColorDefault=dfeffc&bgTextureDefault=glass&bgImgOpacityDefault=85&borderColorDefault=c5dbec&fcDefault=2e6e9e&iconColorDefault=6da8d5&bgColorHover=d0e5f5&bgTextureHover=glass&bgImgOpacityHover=75&borderColorHover=79b7e7&fcHover=1d5987&iconColorHover=217bc0&bgColorActive=f5f8f9&bgTextureActive=inset_hard&bgImgOpacityActive=100&borderColorActive=79b7e7&fcActive=e17009&iconColorActive=f9bd01&bgColorHighlight=fbec88&bgTextureHighlight=flat&bgImgOpacityHighlight=55&borderColorHighlight=fad42e&fcHighlight=363636&iconColorHighlight=2e83ff&bgColorError=fef1ec&bgTextureError=glass&bgImgOpacityError=95&borderColorError=cd0a0a&fcError=cd0a0a&iconColorError=cd0a0a&bgColorOverlay=aaaaaa&bgTextureOverlay=flat&bgImgOpacityOverlay=0&opacityOverlay=30&bgColorShadow=aaaaaa&bgTextureShadow=flat&bgImgOpacityShadow=0&opacityShadow=30&thicknessShadow=8px&offsetTopShadow=-8px&offsetLeftShadow=-8px&cornerRadiusShadow=8px
* Copyright 2013 jQuery Foundation and other contributors; Licensed MIT */

.toolbar-autocomplete .ui-helper-hidden{display:none}.toolbar-autocomplete .ui-helper-hidden-accessible{border:0;clip:rect(0 0 0 0);height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;width:1px}.toolbar-autocomplete .ui-helper-reset{margin:0;padding:0;border:0;outline:0;line-height:1.3;text-decoration:none;font-size:100%;list-style:none}.toolbar-autocomplete .ui-helper-clearfix:before,.toolbar-autocomplete .ui-helper-clearfix:after{content:"";display:table;border-collapse:collapse}.toolbar-autocomplete .ui-helper-clearfix:after{clear:both}.toolbar-autocomplete .ui-helper-clearfix{min-height:0}.toolbar-autocomplete .ui-helper-zfix{width:100%;height:100%;top:0;left:0;position:absolute;opacity:0;filter:Alpha(Opacity=0)}.toolbar-autocomplete .ui-front{z-index:100}.toolbar-autocomplete .ui-state-disabled{cursor:default!important}.toolbar-autocomplete .ui-icon{display:block;text-indent:-99999px;overflow:hidden;background-repeat:no-repeat}.toolbar-autocomplete .ui-widget-overlay{position:fixed;top:0;left:0;width:100%;height:100%}.toolbar-autocomplete .ui-autocomplete{position:absolute;top:0;left:0;cursor:default}.toolbar-autocomplete .ui-menu{list-style:none;padding:2px;margin:0;display:block;outline:none}.toolbar-autocomplete .ui-menu .ui-menu{margin-top:-3px;position:absolute}.toolbar-autocomplete .ui-menu .ui-menu-item{margin:0;padding:0;width:100%;list-style-image:url(data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7)}.toolbar-autocomplete .ui-menu .ui-menu-divider{margin:5px -2px 5px -2px;height:0;font-size:0;line-height:0;border-width:1px 0 0 0}.toolbar-autocomplete .ui-menu .ui-menu-item a{text-decoration:none;display:block;padding:2px .4em;line-height:1.5;min-height:0;font-weight:normal}.toolbar-autocomplete .ui-menu .ui-menu-item a.ui-state-focus,.toolbar-autocomplete .ui-menu .ui-menu-item a.ui-state-active{font-weight:normal;margin:-1px}.toolbar-autocomplete .ui-menu .ui-state-disabled{font-weight:normal;margin:.4em 0 .2em;line-height:1.5}.toolbar-autocomplete .ui-menu .ui-state-disabled a{cursor:default}.toolbar-autocomplete .ui-menu-icons{position:relative}.toolbar-autocomplete .ui-menu-icons .ui-menu-item a{position:relative;padding-left:2em}.toolbar-autocomplete .ui-menu .ui-icon{position:absolute;top:.2em;left:.2em}.toolbar-autocomplete .ui-menu .ui-menu-icon{position:static;float:right}.toolbar-autocomplete .ui-widget{font-family:Lucida Grande,Lucida Sans,Arial,sans-serif;font-size:1.1em}.toolbar-autocomplete .ui-widget .ui-widget{font-size:1em}.toolbar-autocomplete .ui-widget input,.toolbar-autocomplete .ui-widget select,.toolbar-autocomplete .ui-widget textarea,.toolbar-autocomplete .ui-widget button{font-family:Lucida Grande,Lucida Sans,Arial,sans-serif;font-size:1em}.toolbar-autocomplete .ui-widget-content{border:1px solid #a6c9e2;background:#fcfdfd url(images/ui-bg_inset-hard_100_fcfdfd_1x100.png) 50% bottom repeat-x;color:#222}.toolbar-autocomplete .ui-widget-content a{color:#222}.toolbar-autocomplete .ui-widget-header{border:1px solid #4297d7;background:#5c9ccc url(images/ui-bg_gloss-wave_55_5c9ccc_500x100.png) 50% 50% repeat-x;color:#fff;font-weight:bold}.toolbar-autocomplete .ui-widget-header a{color:#fff}.toolbar-autocomplete .ui-state-default,.toolbar-autocomplete .ui-widget-content .ui-state-default,.toolbar-autocomplete .ui-widget-header .ui-state-default{border:1px solid #c5dbec;background:#dfeffc url(images/ui-bg_glass_85_dfeffc_1x400.png) 50% 50% repeat-x;font-weight:bold;color:#2e6e9e}.toolbar-autocomplete .ui-state-default a,.toolbar-autocomplete .ui-state-default a:link,.toolbar-autocomplete .ui-state-default a:visited{color:#2e6e9e;text-decoration:none}.toolbar-autocomplete .ui-state-hover,.toolbar-autocomplete .ui-widget-content .ui-state-hover,.toolbar-autocomplete .ui-widget-header .ui-state-hover,.toolbar-autocomplete .ui-state-focus,.toolbar-autocomplete .ui-widget-content .ui-state-focus,.toolbar-autocomplete .ui-widget-header .ui-state-focus{border:1px solid #79b7e7;background:#d0e5f5 url(images/ui-bg_glass_75_d0e5f5_1x400.png) 50% 50% repeat-x;font-weight:bold;color:#1d5987}.toolbar-autocomplete .ui-state-hover a,.toolbar-autocomplete .ui-state-hover a:hover,.toolbar-autocomplete .ui-state-hover a:link,.toolbar-autocomplete .ui-state-hover a:visited{color:#1d5987;text-decoration:none}.toolbar-autocomplete .ui-state-active,.toolbar-autocomplete .ui-widget-content .ui-state-active,.toolbar-autocomplete .ui-widget-header .ui-state-active{border:1px solid #79b7e7;background:#f5f8f9 url(images/ui-bg_inset-hard_100_f5f8f9_1x100.png) 50% 50% repeat-x;font-weight:bold;color:#e17009}.toolbar-autocomplete .ui-state-active a,.toolbar-autocomplete .ui-state-active a:link,.toolbar-autocomplete .ui-state-active a:visited{color:#e17009;text-decoration:none}.toolbar-autocomplete .ui-state-highlight,.toolbar-autocomplete .ui-widget-content .ui-state-highlight,.toolbar-autocomplete .ui-widget-header .ui-state-highlight{border:1px solid #fad42e;background:#fbec88 url(images/ui-bg_flat_55_fbec88_40x100.png) 50% 50% repeat-x;color:#363636}.toolbar-autocomplete .ui-state-highlight a,.toolbar-autocomplete .ui-widget-content .ui-state-highlight a,.toolbar-autocomplete .ui-widget-header .ui-state-highlight a{color:#363636}.toolbar-autocomplete .ui-state-error,.toolbar-autocomplete .ui-widget-content .ui-state-error,.toolbar-autocomplete .ui-widget-header .ui-state-error{border:1px solid #cd0a0a;background:#fef1ec url(images/ui-bg_glass_95_fef1ec_1x400.png) 50% 50% repeat-x;color:#cd0a0a}.toolbar-autocomplete .ui-state-error a,.toolbar-autocomplete .ui-widget-content .ui-state-error a,.toolbar-autocomplete .ui-widget-header .ui-state-error a{color:#cd0a0a}.toolbar-autocomplete .ui-state-error-text,.toolbar-autocomplete .ui-widget-content .ui-state-error-text,.toolbar-autocomplete .ui-widget-header .ui-state-error-text{color:#cd0a0a}.toolbar-autocomplete .ui-priority-primary,.toolbar-autocomplete .ui-widget-content .ui-priority-primary,.toolbar-autocomplete .ui-widget-header .ui-priority-primary{font-weight:bold}.toolbar-autocomplete .ui-priority-secondary,.toolbar-autocomplete .ui-widget-content .ui-priority-secondary,.toolbar-autocomplete .ui-widget-header .ui-priority-secondary{opacity:.7;filter:Alpha(Opacity=70);font-weight:normal}.toolbar-autocomplete .ui-state-disabled,.toolbar-autocomplete .ui-widget-content .ui-state-disabled,.toolbar-autocomplete .ui-widget-header .ui-state-disabled{opacity:.35;filter:Alpha(Opacity=35);background-image:none}.toolbar-autocomplete .ui-state-disabled .ui-icon{filter:Alpha(Opacity=35)}.toolbar-autocomplete .ui-icon{width:16px;height:16px}.toolbar-autocomplete .ui-icon,.toolbar-autocomplete .ui-widget-content .ui-icon{background-image:url(images/ui-icons_469bdd_256x240.png)}.toolbar-autocomplete .ui-widget-header .ui-icon{background-image:url(images/ui-icons_d8e7f3_256x240.png)}.toolbar-autocomplete .ui-state-default .ui-icon{background-image:url(images/ui-icons_6da8d5_256x240.png)}.toolbar-autocomplete .ui-state-hover .ui-icon,.toolbar-autocomplete .ui-state-focus .ui-icon{background-image:url(images/ui-icons_217bc0_256x240.png)}.toolbar-autocomplete .ui-state-active .ui-icon{background-image:url(images/ui-icons_f9bd01_256x240.png)}.toolbar-autocomplete .ui-state-highlight .ui-icon{background-image:url(images/ui-icons_2e83ff_256x240.png)}.toolbar-autocomplete .ui-state-error .ui-icon,.toolbar-autocomplete .ui-state-error-text .ui-icon{background-image:url(images/ui-icons_cd0a0a_256x240.png)}.toolbar-autocomplete .ui-icon-blank{background-position:16px 16px}.toolbar-autocomplete .ui-icon-carat-1-n{background-position:0 0}.toolbar-autocomplete .ui-icon-carat-1-ne{background-position:-16px 0}.toolbar-autocomplete .ui-icon-carat-1-e{background-position:-32px 0}.toolbar-autocomplete .ui-icon-carat-1-se{background-position:-48px 0}.toolbar-autocomplete .ui-icon-carat-1-s{background-position:-64px 0}.toolbar-autocomplete .ui-icon-carat-1-sw{background-position:-80px 0}.toolbar-autocomplete .ui-icon-carat-1-w{background-position:-96px 0}.toolbar-autocomplete .ui-icon-carat-1-nw{background-position:-112px 0}.toolbar-autocomplete .ui-icon-carat-2-n-s{background-position:-128px 0}.toolbar-autocomplete .ui-icon-carat-2-e-w{background-position:-144px 0}.toolbar-autocomplete .ui-icon-triangle-1-n{background-position:0 -16px}.toolbar-autocomplete .ui-icon-triangle-1-ne{background-position:-16px -16px}.toolbar-autocomplete .ui-icon-triangle-1-e{background-position:-32px -16px}.toolbar-autocomplete .ui-icon-triangle-1-se{background-position:-48px -16px}.toolbar-autocomplete .ui-icon-triangle-1-s{background-position:-64px -16px}.toolbar-autocomplete .ui-icon-triangle-1-sw{background-position:-80px -16px}.toolbar-autocomplete .ui-icon-triangle-1-w{background-position:-96px -16px}.toolbar-autocomplete .ui-icon-triangle-1-nw{background-position:-112px -16px}.toolbar-autocomplete .ui-icon-triangle-2-n-s{background-position:-128px -16px}.toolbar-autocomplete .ui-icon-triangle-2-e-w{background-position:-144px -16px}.toolbar-autocomplete .ui-icon-arrow-1-n{background-position:0 -32px}.toolbar-autocomplete .ui-icon-arrow-1-ne{background-position:-16px -32px}.toolbar-autocomplete .ui-icon-arrow-1-e{background-position:-32px -32px}.toolbar-autocomplete .ui-icon-arrow-1-se{background-position:-48px -32px}.toolbar-autocomplete .ui-icon-arrow-1-s{background-position:-64px -32px}.toolbar-autocomplete .ui-icon-arrow-1-sw{background-position:-80px -32px}.toolbar-autocomplete .ui-icon-arrow-1-w{background-position:-96px -32px}.toolbar-autocomplete .ui-icon-arrow-1-nw{background-position:-112px -32px}.toolbar-autocomplete .ui-icon-arrow-2-n-s{background-position:-128px -32px}.toolbar-autocomplete .ui-icon-arrow-2-ne-sw{background-position:-144px -32px}.toolbar-autocomplete .ui-icon-arrow-2-e-w{background-position:-160px -32px}.toolbar-autocomplete .ui-icon-arrow-2-se-nw{background-position:-176px -32px}.toolbar-autocomplete .ui-icon-arrowstop-1-n{background-position:-192px -32px}.toolbar-autocomplete .ui-icon-arrowstop-1-e{background-position:-208px -32px}.toolbar-autocomplete .ui-icon-arrowstop-1-s{background-position:-224px -32px}.toolbar-autocomplete .ui-icon-arrowstop-1-w{background-position:-240px -32px}.toolbar-autocomplete .ui-icon-arrowthick-1-n{background-position:0 -48px}.toolbar-autocomplete .ui-icon-arrowthick-1-ne{background-position:-16px -48px}.toolbar-autocomplete .ui-icon-arrowthick-1-e{background-position:-32px -48px}.toolbar-autocomplete .ui-icon-arrowthick-1-se{background-position:-48px -48px}.toolbar-autocomplete .ui-icon-arrowthick-1-s{background-position:-64px -48px}.toolbar-autocomplete .ui-icon-arrowthick-1-sw{background-position:-80px -48px}.toolbar-autocomplete .ui-icon-arrowthick-1-w{background-position:-96px -48px}.toolbar-autocomplete .ui-icon-arrowthick-1-nw{background-position:-112px -48px}.toolbar-autocomplete .ui-icon-arrowthick-2-n-s{background-position:-128px -48px}.toolbar-autocomplete .ui-icon-arrowthick-2-ne-sw{background-position:-144px -48px}.toolbar-autocomplete .ui-icon-arrowthick-2-e-w{background-position:-160px -48px}.toolbar-autocomplete .ui-icon-arrowthick-2-se-nw{background-position:-176px -48px}.toolbar-autocomplete .ui-icon-arrowthickstop-1-n{background-position:-192px -48px}.toolbar-autocomplete .ui-icon-arrowthickstop-1-e{background-position:-208px -48px}.toolbar-autocomplete .ui-icon-arrowthickstop-1-s{background-position:-224px -48px}.toolbar-autocomplete .ui-icon-arrowthickstop-1-w{background-position:-240px -48px}.toolbar-autocomplete .ui-icon-arrowreturnthick-1-w{background-position:0 -64px}.toolbar-autocomplete .ui-icon-arrowreturnthick-1-n{background-position:-16px -64px}.toolbar-autocomplete .ui-icon-arrowreturnthick-1-e{background-position:-32px -64px}.toolbar-autocomplete .ui-icon-arrowreturnthick-1-s{background-position:-48px -64px}.toolbar-autocomplete .ui-icon-arrowreturn-1-w{background-position:-64px -64px}.toolbar-autocomplete .ui-icon-arrowreturn-1-n{background-position:-80px -64px}.toolbar-autocomplete .ui-icon-arrowreturn-1-e{background-position:-96px -64px}.toolbar-autocomplete .ui-icon-arrowreturn-1-s{background-position:-112px -64px}.toolbar-autocomplete .ui-icon-arrowrefresh-1-w{background-position:-128px -64px}.toolbar-autocomplete .ui-icon-arrowrefresh-1-n{background-position:-144px -64px}.toolbar-autocomplete .ui-icon-arrowrefresh-1-e{background-position:-160px -64px}.toolbar-autocomplete .ui-icon-arrowrefresh-1-s{background-position:-176px -64px}.toolbar-autocomplete .ui-icon-arrow-4{background-position:0 -80px}.toolbar-autocomplete .ui-icon-arrow-4-diag{background-position:-16px -80px}.toolbar-autocomplete .ui-icon-extlink{background-position:-32px -80px}.toolbar-autocomplete .ui-icon-newwin{background-position:-48px -80px}.toolbar-autocomplete .ui-icon-refresh{background-position:-64px -80px}.toolbar-autocomplete .ui-icon-shuffle{background-position:-80px -80px}.toolbar-autocomplete .ui-icon-transfer-e-w{background-position:-96px -80px}.toolbar-autocomplete .ui-icon-transferthick-e-w{background-position:-112px -80px}.toolbar-autocomplete .ui-icon-folder-collapsed{background-position:0 -96px}.toolbar-autocomplete .ui-icon-folder-open{background-position:-16px -96px}.toolbar-autocomplete .ui-icon-document{background-position:-32px -96px}.toolbar-autocomplete .ui-icon-document-b{background-position:-48px -96px}.toolbar-autocomplete .ui-icon-note{background-position:-64px -96px}.toolbar-autocomplete .ui-icon-mail-closed{background-position:-80px -96px}.toolbar-autocomplete .ui-icon-mail-open{background-position:-96px -96px}.toolbar-autocomplete .ui-icon-suitcase{background-position:-112px -96px}.toolbar-autocomplete .ui-icon-comment{background-position:-128px -96px}.toolbar-autocomplete .ui-icon-person{background-position:-144px -96px}.toolbar-autocomplete .ui-icon-print{background-position:-160px -96px}.toolbar-autocomplete .ui-icon-trash{background-position:-176px -96px}.toolbar-autocomplete .ui-icon-locked{background-position:-192px -96px}.toolbar-autocomplete .ui-icon-unlocked{background-position:-208px -96px}.toolbar-autocomplete .ui-icon-bookmark{background-position:-224px -96px}.toolbar-autocomplete .ui-icon-tag{background-position:-240px -96px}.toolbar-autocomplete .ui-icon-home{background-position:0 -112px}.toolbar-autocomplete .ui-icon-flag{background-position:-16px -112px}.toolbar-autocomplete .ui-icon-calendar{background-position:-32px -112px}.toolbar-autocomplete .ui-icon-cart{background-position:-48px -112px}.toolbar-autocomplete .ui-icon-pencil{background-position:-64px -112px}.toolbar-autocomplete .ui-icon-clock{background-position:-80px -112px}.toolbar-autocomplete .ui-icon-disk{background-position:-96px -112px}.toolbar-autocomplete .ui-icon-calculator{background-position:-112px -112px}.toolbar-autocomplete .ui-icon-zoomin{background-position:-128px -112px}.toolbar-autocomplete .ui-icon-zoomout{background-position:-144px -112px}.toolbar-autocomplete .ui-icon-search{background-position:-160px -112px}.toolbar-autocomplete .ui-icon-wrench{background-position:-176px -112px}.toolbar-autocomplete .ui-icon-gear{background-position:-192px -112px}.toolbar-autocomplete .ui-icon-heart{background-position:-208px -112px}.toolbar-autocomplete .ui-icon-star{background-position:-224px -112px}.toolbar-autocomplete .ui-icon-link{background-position:-240px -112px}.toolbar-autocomplete .ui-icon-cancel{background-position:0 -128px}.toolbar-autocomplete .ui-icon-plus{background-position:-16px -128px}.toolbar-autocomplete .ui-icon-plusthick{background-position:-32px -128px}.toolbar-autocomplete .ui-icon-minus{background-position:-48px -128px}.toolbar-autocomplete .ui-icon-minusthick{background-position:-64px -128px}.toolbar-autocomplete .ui-icon-close{background-position:-80px -128px}.toolbar-autocomplete .ui-icon-closethick{background-position:-96px -128px}.toolbar-autocomplete .ui-icon-key{background-position:-112px -128px}.toolbar-autocomplete .ui-icon-lightbulb{background-position:-128px -128px}.toolbar-autocomplete .ui-icon-scissors{background-position:-144px -128px}.toolbar-autocomplete .ui-icon-clipboard{background-position:-160px -128px}.toolbar-autocomplete .ui-icon-copy{background-position:-176px -128px}.toolbar-autocomplete .ui-icon-contact{background-position:-192px -128px}.toolbar-autocomplete .ui-icon-image{background-position:-208px -128px}.toolbar-autocomplete .ui-icon-video{background-position:-224px -128px}.toolbar-autocomplete .ui-icon-script{background-position:-240px -128px}.toolbar-autocomplete .ui-icon-alert{background-position:0 -144px}.toolbar-autocomplete .ui-icon-info{background-position:-16px -144px}.toolbar-autocomplete .ui-icon-notice{background-position:-32px -144px}.toolbar-autocomplete .ui-icon-help{background-position:-48px -144px}.toolbar-autocomplete .ui-icon-check{background-position:-64px -144px}.toolbar-autocomplete .ui-icon-bullet{background-position:-80px -144px}.toolbar-autocomplete .ui-icon-radio-on{background-position:-96px -144px}.toolbar-autocomplete .ui-icon-radio-off{background-position:-112px -144px}.toolbar-autocomplete .ui-icon-pin-w{background-position:-128px -144px}.toolbar-autocomplete .ui-icon-pin-s{background-position:-144px -144px}.toolbar-autocomplete .ui-icon-play{background-position:0 -160px}.toolbar-autocomplete .ui-icon-pause{background-position:-16px -160px}.toolbar-autocomplete .ui-icon-seek-next{background-position:-32px -160px}.toolbar-autocomplete .ui-icon-seek-prev{background-position:-48px -160px}.toolbar-autocomplete .ui-icon-seek-end{background-position:-64px -160px}.toolbar-autocomplete .ui-icon-seek-start{background-position:-80px -160px}.toolbar-autocomplete .ui-icon-seek-first{background-position:-80px -160px}.toolbar-autocomplete .ui-icon-stop{background-position:-96px -160px}.toolbar-autocomplete .ui-icon-eject{background-position:-112px -160px}.toolbar-autocomplete .ui-icon-volume-off{background-position:-128px -160px}.toolbar-autocomplete .ui-icon-volume-on{background-position:-144px -160px}.toolbar-autocomplete .ui-icon-power{background-position:0 -176px}.toolbar-autocomplete .ui-icon-signal-diag{background-position:-16px -176px}.toolbar-autocomplete .ui-icon-signal{background-position:-32px -176px}.toolbar-autocomplete .ui-icon-battery-0{background-position:-48px -176px}.toolbar-autocomplete .ui-icon-battery-1{background-position:-64px -176px}.toolbar-autocomplete .ui-icon-battery-2{background-position:-80px -176px}.toolbar-autocomplete .ui-icon-battery-3{background-position:-96px -176px}.toolbar-autocomplete .ui-icon-circle-plus{background-position:0 -192px}.toolbar-autocomplete .ui-icon-circle-minus{background-position:-16px -192px}.toolbar-autocomplete .ui-icon-circle-close{background-position:-32px -192px}.toolbar-autocomplete .ui-icon-circle-triangle-e{background-position:-48px -192px}.toolbar-autocomplete .ui-icon-circle-triangle-s{background-position:-64px -192px}.toolbar-autocomplete .ui-icon-circle-triangle-w{background-position:-80px -192px}.toolbar-autocomplete .ui-icon-circle-triangle-n{background-position:-96px -192px}.toolbar-autocomplete .ui-icon-circle-arrow-e{background-position:-112px -192px}.toolbar-autocomplete .ui-icon-circle-arrow-s{background-position:-128px -192px}.toolbar-autocomplete .ui-icon-circle-arrow-w{background-position:-144px -192px}.toolbar-autocomplete .ui-icon-circle-arrow-n{background-position:-160px -192px}.toolbar-autocomplete .ui-icon-circle-zoomin{background-position:-176px -192px}.toolbar-autocomplete .ui-icon-circle-zoomout{background-position:-192px -192px}.toolbar-autocomplete .ui-icon-circle-check{background-position:-208px -192px}.toolbar-autocomplete .ui-icon-circlesmall-plus{background-position:0 -208px}.toolbar-autocomplete .ui-icon-circlesmall-minus{background-position:-16px -208px}.toolbar-autocomplete .ui-icon-circlesmall-close{background-position:-32px -208px}.toolbar-autocomplete .ui-icon-squaresmall-plus{background-position:-48px -208px}.toolbar-autocomplete .ui-icon-squaresmall-minus{background-position:-64px -208px}.toolbar-autocomplete .ui-icon-squaresmall-close{background-position:-80px -208px}.toolbar-autocomplete .ui-icon-grip-dotted-vertical{background-position:0 -224px}.toolbar-autocomplete .ui-icon-grip-dotted-horizontal{background-position:-16px -224px}.toolbar-autocomplete .ui-icon-grip-solid-vertical{background-position:-32px -224px}.toolbar-autocomplete .ui-icon-grip-solid-horizontal{background-position:-48px -224px}.toolbar-autocomplete .ui-icon-gripsmall-diagonal-se{background-position:-64px -224px}.toolbar-autocomplete .ui-icon-grip-diagonal-se{background-position:-80px -224px}.toolbar-autocomplete .ui-corner-all,.toolbar-autocomplete .ui-corner-top,.toolbar-autocomplete .ui-corner-left,.toolbar-autocomplete .ui-corner-tl{border-top-left-radius:5px}.toolbar-autocomplete .ui-corner-all,.toolbar-autocomplete .ui-corner-top,.toolbar-autocomplete .ui-corner-right,.toolbar-autocomplete .ui-corner-tr{border-top-right-radius:5px}.toolbar-autocomplete .ui-corner-all,.toolbar-autocomplete .ui-corner-bottom,.toolbar-autocomplete .ui-corner-left,.toolbar-autocomplete .ui-corner-bl{border-bottom-left-radius:5px}.toolbar-autocomplete .ui-corner-all,.toolbar-autocomplete .ui-corner-bottom,.toolbar-autocomplete .ui-corner-right,.toolbar-autocomplete .ui-corner-br{border-bottom-right-radius:5px}.toolbar-autocomplete .ui-widget-overlay{background:#aaa url(images/ui-bg_flat_0_aaaaaa_40x100.png) 50% 50% repeat-x;opacity:.3;filter:Alpha(Opacity=30)}.toolbar-autocomplete .ui-widget-shadow{margin:-8px 0 0 -8px;padding:8px;background:#aaa url(images/ui-bg_flat_0_aaaaaa_40x100.png) 50% 50% repeat-x;opacity:.3;filter:Alpha(Opacity=30);border-radius:8px}
/* jqModal base Styling courtesy of;
   Brice Burgess <<EMAIL>> */

/* The Window's CSS z-index value is respected (takes priority). If none is supplied,
	the Window's z-index value will be set to 3000 by default (via jqModal.js). */

.jqmWindow {
    display: none;
    position: fixed;
    top: 17%;
    left: 50%;
    margin-left: -250px;
    background-color: #EEE;
    color: #333;
    width: 0px;
    height:0px;
    line-height: normal;
}

.jqmOverlay { background-color: #000; }

/* Background iframe styling for IE6. Prevents ActiveX bleed-through (<select> form elements, etc.) */
* iframe.jqm {position:absolute;top:0;left:0;z-index:-1;
    width: expression(this.parentNode.offsetWidth+'px');
    height: expression(this.parentNode.offsetHeight+'px');
}

/* Fixed posistioning emulation for IE6
     Star selector used to hide definition from browsers other than IE6
     For valid CSS, use a conditional include instead */
* html .jqmWindow {
    position: absolute;
    top: expression((document.documentElement.scrollTop || document.body.scrollTop) + Math.round(17 * (document.documentElement.offsetHeight || document.body.clientHeight) / 100) + 'px');
}
#bt-toolbar #toolbar-menu {
    position: absolute;
    top: 43px;
    border: 1px solid #355A86;
    display: none;
    z-index: 1;
}

#bt-toolbar #toolbar-menu #mainmenutable {
    width: 100%;
    background: #FFFFFF;
    border-collapse  : collapse;
}

#bt-toolbar #toolbar-menu #mainmenutable #contextmenutable {
    width: 100%;
    background: #FFFFFF;
    border-collapse  : collapse;
    border : #FFFFFF;
}

#bt-toolbar #toolbar-menu #mainmenutable tr,
#bt-toolbar #toolbar-menu #mainmenutable td
{
    text-align: left;
    padding: 0;
}

#bt-toolbar #toolbar-menu #mainmenutable #contextmenutable td a,
#bt-toolbar #toolbar-menu #mainmenutable #contextmenutable td,
#bt-toolbar #toolbar-menu #mainmenutable .yellow-menu-section
{
    padding:0px;
    text-shadow: none;
    font-family : "Helvetica CE", Verdana, Arial, sans-serif;
    font-size : 11px;
    font-weight : bold;
    color : #F87012;
    vertical-align: middle;

}
#bt-toolbar #toolbar-menu #mainmenutable #contextmenutable td a:hover {
    color:#2b528a;
    background-color: transparent;
    background: none;
}

#bt-toolbar #toolbar-menu #mainmenutable #contextmenutable td.yellow-menu {
    background: #FFE8BA;
    border: solid;
    border-top: 0px;
    border-left: 0px;
    border-right: 0px;
    border-bottom: 1px solid white;
    padding: 3px 0px 3px 15px;
}

#bt-toolbar #toolbar-menu #mainmenutable #contextmenutable td.yellow-menu-last {
    background: #FFE8BA;
    border: solid;
    border-top: 0px;
    border-left: 0px;
    border-right: 0px;
    border-bottom: 1px solid #F87012;
    padding: 3px 0px 3px 15px;
}
#bt-toolbar #toolbar-menu #mainmenutable #contextmenutable td.yellow-menu-last a {
    color : #F87012;
}

#bt-toolbar #toolbar-menu #mainmenutable #contextmenutable td.yellow-menu-notactive {
    color : #AAAAAA;
    background: #FFE8BA;
    border: solid;
    border-top: 0px;
    border-left: 0px;
    border-right: 0px;
    border-bottom: 1px solid white;
    padding: 3px 0px 3px 15px;
}

#bt-toolbar #toolbar-menu #mainmenutable #contextmenutable span.yellow-menu-notactive {
    color : #AAAAAA;
    border: none;
    border-top: 0px;
    border-left: 0px;
    border-right: 0px;
    border-bottom-width: 0px;
    padding: 0px 0px 0px 0px;
}

#bt-toolbar #toolbar-menu #mainmenutable #contextmenutable .yellow-menu-notactive-last {
    color : #AAAAAA;
    border: solid;
    border-top: 0px;
    border-left: 0px;
    border-right: 0px;
    border-bottom: 1px solid #F87012;
    padding: 3px 0px 3px 15px;
}

#bt-toolbar #toolbar-menu #mainmenutable .yellow-menu-section {
    color : #FFFFFF;
    background: #FF9C00;
    border: solid;
    border-top: 0px;
    border-left: 0px;
    border-right: 0px;
    border-bottom: 1px solid white;
    padding: 0px 0px 0px 0px;
}

#bt-toolbar #toolbar-menu #mainmenutable td.blue-menu a,
#bt-toolbar #toolbar-menu #mainmenutable td.blue-menu,
#bt-toolbar #toolbar-menu #mainmenutable td.blue-menu-active a,
#bt-toolbar #toolbar-menu #mainmenutable td.blue-menu-active,
#bt-toolbar #toolbar-menu #mainmenutable td.blue-menu-section
{
    color : #2B528A;
    font-family : "Helvetica CE", Verdana, Arial, sans-serif;
    font-size : 11px;
    font-weight : bold;
    text-shadow: none;
    padding:0px;
    vertical-align: middle;
}

#bt-toolbar #toolbar-menu #mainmenutable td.blue-menu {
    background: #E2EAF4;
    border: solid;
    border-top: 0px;
    border-left: 0px;
    border-right: 0px;
    border-bottom: 1px solid white;
    padding: 3px 0px 3px 15px;
}
#bt-toolbar #toolbar-menu #mainmenutable td.blue-menu a:hover {
    color:#FF9C00;
    background-color: transparent;
    background: none;
}

#bt-toolbar #toolbar-menu #mainmenutable td.blue-menu-active {
    background: #FF9C00;
    border: solid;
    border-top: 0px;
    border-left: 0px;
    border-right: 0px;
    border-bottom: 1px solid white;
    padding: 0px 0px 0px 0px;
}
#bt-toolbar #toolbar-menu #mainmenutable td.blue-menu-active a {
    color : #FFFFFF;
}

#bt-toolbar #toolbar-menu #mainmenutable td.blue-menu-section {
    color : #2B528A;
    background: #BED0E6;
    border: solid;
    border-top: 0px;
    border-left: 0px;
    border-right: 0px;
    border-bottom: 1px solid white;
    padding: 15px 0px 3px 15px;
}

/*
   ColorBox Core Style:
   The following CSS is consistent between example themes and should not be altered.
*/
#colorbox {
    -moz-box-sizing: content-box !important;
    -webkit-box-sizing: content-box !important;
    box-sizing: content-box!important;
}
#colorbox, #cboxOverlay, #cboxWrapper{position:absolute; top:0; left:0; z-index:9999; overflow:hidden;}
#cboxOverlay{position:fixed; width:100%; height:100%;}
#cboxMiddleLeft, #cboxBottomLeft{clear:left;}
#cboxContent{position:relative;}
#cboxLoadedContent{overflow:auto;}
#cboxTitle{margin:0;}
#cboxLoadingOverlay, #cboxLoadingGraphic{position:absolute; top:0; left:0; width:100%; height:100%;}
#cboxPrevious, #cboxNext, #cboxClose, #cboxSlideshow{cursor:pointer;}
.cboxPhoto{float:left; margin:auto; border:0; display:block;}
.cboxIframe{width:100%; height:100%; display:block; border:0;}

/*
    User Style:
    Change the following styles to modify the appearance of ColorBox.  They are
    ordered & tabbed in a way that represents the nesting of the generated HTML.
*/
#cboxOverlay{background:#000;}
#colorbox{}
#cboxTopLeft{width:14px; height:14px; background:url(images/controls.png) no-repeat 0 0;}
#cboxTopCenter{height:14px; background:url(images/border.png) repeat-x top left;}
#cboxTopRight{width:14px; height:14px; background:url(images/controls.png) no-repeat -36px 0;}
#cboxBottomLeft{width:14px; height:43px; background:url(images/controls.png) no-repeat 0 -32px;}
#cboxBottomCenter{height:43px; background:url(images/border.png) repeat-x bottom left;}
#cboxBottomRight{width:14px; height:43px; background:url(images/controls.png) no-repeat -36px -32px;}
#cboxMiddleLeft{width:14px; background:url(images/controls.png) repeat-y -175px 0;}
#cboxMiddleRight{width:14px; background:url(images/controls.png) repeat-y -211px 0;}
#cboxContent{background:#fff; overflow:visible;}
.cboxIframe{background:#fff;}
#cboxError{padding:50px; border:1px solid #ccc;}
#cboxLoadedContent{margin-bottom:5px;}
#cboxLoadingOverlay{background:url(images/loading_background.png) no-repeat center center;}
#cboxLoadingGraphic{background:url(images/loading.gif) no-repeat center center;}
#cboxTitle{position:absolute; bottom:-25px; left:0; text-align:center; width:100%; font-weight:bold; color:#7C7C7C;top: auto !important;}
#cboxCurrent{position:absolute; bottom:-25px; left:58px; font-weight:bold; color:#7C7C7C;}

#cboxPrevious, #cboxNext, #cboxClose, #cboxSlideshow{position:absolute; bottom:-29px; background:url(images/controls.png) no-repeat 0px 0px; width:23px; height:23px; text-indent:-9999px;}
#cboxPrevious{left:0px; background-position: -51px -25px;}
#cboxPrevious:hover{background-position:-51px 0px;}
#cboxNext{left:27px; background-position:-75px -25px;}
#cboxNext:hover{background-position:-75px 0px;}
#cboxClose{right:0; background-position:-100px -25px;}
#cboxClose:hover{background-position:-100px 0px;}

.cboxSlideshow_on #cboxSlideshow{background-position:-125px 0px; right:27px;}
.cboxSlideshow_on #cboxSlideshow:hover{background-position:-150px 0px;}
.cboxSlideshow_off #cboxSlideshow{background-position:-150px -25px; right:27px;}
.cboxSlideshow_off #cboxSlideshow:hover{background-position:-125px 0px;}
#toolbar-cover/*, #toolbar-cover-fake*/ {
    top:0;
    padding:0;
    position:fixed;
    clear:both;
    width:100%;
    z-index:99999;
    text-align: left;
    font-family: Verdana !important;
}

#toolbar-body/*, #toolbar-body-fake*/ {
    color:#FFFFFF;
    margin: 0px;
    padding: 0px 5px;
    font-size: 10px;
    border: none;
    /*border-bottom: 1px solid white;*/
    height: 35px;
    background: url('images/toolbar-background.gif') repeat-x top left;
    background-position: 0px 0px;
    position: relative;
}

#toolbar-wait {
    display: none;
}

#toolbar-wait-background {
    position: absolute;
    left: 0px;
    top: 43px;
    width: 100%;
    height: 1200px;
    background-color: #000000;
    opacity:0.5;
    filter: alpha(opacity=50);
}

#toolbar-wait-icon {
    position: absolute;
    left: 0px;
    top: 42px;
    width: 100%;
    height: 150px;
    background-image: url('images/wait.gif');
    background-repeat: no-repeat;
    background-position: center 50px ;
}

#toolbar-errors {
    color: #FFFFFF;
    margin: 0 15px;
    padding: 5px 5px;
    font-weight: bold;
    font-size: 16px;
    background-color:#EF2C2C;
    border: 1px solid #27667F;
    border-bottom: 1px solid #27667F;
    border-top: 3px solid #27667F;
    display: none;
    opacity:.95;
    -moz-opacity:.95;
    filter:alpha(opacity=95);
}


#toolbar-error ul li {
    list-style: none;
}


#toolbar-small-loader, #toolbar-small-loader-fake {
    position: absolute;
    right: 5px;
    width: 33px;
    height: 33px;
    margin-top: 2px;
    margin-right: 10px;
    background-image: url('images/wait.gif');
    background-repeat: no-repeat;
    opacity:.50;
    display: none;
}

/*#toolbar-small-loader-fake {
    display: block;
}*/

.mceStatusbar {
    display: none !important;
}

.mceLayout {
    height: auto !important;
}

.ui-menu-item { font-size: 12px; font-family: Verdana}
.ui-autocomplete {text-align: left}

body .ui-pnotify {
    top: 80px !important;
    background: yellow;
}

.buxus_page_edit {
    color: #333333 !important;
}

body .ui-pnotify-title {
    font-size: 12px;
    font-weight: bold;
}

.mce-content-body {
    background: #FEFFBF;
}
#bt-toolbar #toolbar-login-window h1 {
    font-size: 	18px;
    color: #ffffff;
    line-height: 26px;
    padding: 0;
    background: none;
    text-transform: none;
    float: none;
    font-family: Helvetica CE, Verdana, Arial;
}
#bt-toolbar #toolbar-login-window {
    height: 308px;
    width: 363px;
    background: url('images/login-bg.png') no-repeat top left;
    font-size: 10px;
    color: white;
    font-family: Helvetica CE, Verdana, Arial;
    position: relative;
}

#bt-toolbar #toolbar-login-window a,#bt-toolbar #toolbar-login-window a:visited {
    color: white;
    text-decoration: none;
}

#bt-toolbar #toolbar-login-footer {
    position: absolute;
    bottom: 15px;
    left: 10px;
    text-align: left;
    font-size: 10px;
    line-height: 14px;
}

#bt-toolbar #toolbar-login-footer a {
    text-decoration: underline;
}

#bt-toolbar #toolbar-login-for-cover {
    position: absolute;
    left: 0px;
    top: 140px;
}
/*#bt-toolbar #toolbar-login-form input[type=text], #toolbar-login-form input[type=password] {*/
    /*width: 240px;*/
    /*height: 22px;*/
    /*font-size: 13px;*/
    /*border: none;*/
    /*padding: 2px;*/
    /*font-family: Helvetica CE, Verdana, Arial;*/
    /*margin: 0;*/
    /*color: #000000;*/
/*}*/

/*#bt-toolbar #toolbar-login-form label {*/
    /*width: 60px;*/
    /*text-align: right;*/
    /*font-size: 13px;*/
    /*display: block;*/
    /*margin-right: 5px;*/
    /*color: #ffffff;*/
/*}*/

#bt-toolbar #toolbar-login-form table  {
    background: none;
    border: 0;
    -webkit-border-radius: 0;
    -moz-border-radius: 0;
    border-radius: 0;
}

#bt-toolbar #toolbar-login-form table tr {
    background: none;
}

#bt-toolbar #toolbar-login-form table td {
    padding: 1px;
    background: none;
    border: 0;
}

#bt-toolbar #toolbar-login-header {
    position: absolute;
    top: 45px;
    width: 363px;
    text-align: center;
    font-size: 10px;
}

#bt-toolbar #toolbar-login-submit {
    display: inline;
    width: 100px;
    background-color: #386398;
    color: white;
    border-top: 1px solid #6e93c0;
    border-left: 1px solid #6e93c0;
    border-bottom: 1px solid #213b60;
    border-right: 1px solid #213b60;
    font-size: 13px;
    padding:2px;
    margin-top: 10px;
    /*    background:none;*/
}

#bt-toolbar #toolbar-login-wait {
    display: none;
    position: absolute;
    top: 140px;
    background: url(/buxus/system/images/wait.gif) no-repeat top left;
    padding: 10px 0px 10px 40px;
    margin-top: 20px;
    margin-left: 50px;
}

#bt-toolbar #toolbar-login-error {
    position: absolute;
    top: 115px;
    left: 15px;
    width: 333px;
    border-right: 1px solid #000;
    border-bottom: 1px solid #000;
    font-size:12px;
    display: none;
    color:#F00;
    background-color: #FFF;
    padding:3px;
}
#toolbar-cover /*, #toolbar-cover-fake*/
{
    top: 0;
    left: 0;
    padding: 0;
    position: fixed;
    clear: both;
    width: 100%;
    z-index: 99999;
    text-align: left;
    font-family: Verdana !important;
    line-height: normal;
}

/*
#bt-toolbar, #toolbar-body-fake {
    position: relative;
	background:url(images/bt_background2.png) repeat-x;
		background-color: #0E73BD;
	height: 46px;
}
*/

#bt-toolbar ul li {
    margin: 0;
}


#bt-toolbar {
    position: relative;
    background: transparent url(images/bt_background2.png) repeat-x;
    min-height: 46px;
    height: auto;
/ / height : 46 px; /* matches only IE6, IE7 */
    /*min-width: 950px;*/
    width: auto;
/ / width : 950 px; /* matches only IE6, IE7 */
}

#bt-toolbar.bt-nobg {
    background: none;
}

#bt-toolbar .bt-wrapper {
    margin: auto;
    padding: 0px 20px;
}

#bt-toolbar li a {
    color: #ffffff;
    text-decoration: none;
    display: block;
    font-family: Arial;
    font-size: 10px;
}

#bt-toolbar .toolbar-autocomplete li a {
    color: #000000;
}

#bt-toolbar ul#bt-tabs {
    /*background:url(images/bt_button.png) no-repeat right top;*/
    height: 43px;
    margin: 0px;
    padding: 0px;
    list-style: none;
    margin-right: 0px;
    float: left;
    border-right: 1px solid #094A6F;
}

#bt-toolbar ul#bt-icons {
    margin: 0px;
    padding: 0px;
    list-style: none;
}

#bt-toolbar ul#bt-tabs li {
    float: left;
    background: url(images/bt_sprite.png) no-repeat top left;
    background-position: 0 -53px;
    height: 43px;
    color: #ffffff;
    font-size: 9px;
    text-decoration: none;
    font-weight: bold;
    /*-moz-text-shadow: 1px 1px 1px #333;
    -webkit-text-shadow: 1px 1px 1px #333;
    text-shadow: 1px 1px 1px #333;*/
    text-align: center;
    position: relative;
    padding: 0;
}

#bt-toolbar ul#bt-tabs li a {
    color: #ffffff;
    text-decoration: none;
    padding: 15px 18px 15px 18px;
    display: block;
    outline: none;
    float: left;
    outline: none;
    text-shadow: 0 0 2px #000;
}

#bt-toolbar ul#bt-tabs li a:hover {
    background: url(images/bt_sprite.png) no-repeat top left;
    background-position: 0 -196px;
}

#bt-toolbar ul#bt-tabs li.bt-last:after {
    content: "";
    display: block;
    width: 1px;
    height: 42px;
    background-color: rgba(255, 255, 255, .1);
    position: absolute;
    right: -2px;
}

#bt-toolbar #bt-page_id {
    color: #FFFFFF;
    float: left;
    font-family: Arial;
    font-size: 11px;
    font-weight: bold;
    padding: 15px 18px 12px 8px;
    white-space: nowrap;
    text-shadow: 0 0 2px #000;
}

#bt-toolbar #bt-page_id #bt_page_id_value {
    font-weight: bold;
}

#bt-toolbar .bt-icons {
    margin: 0px;
    padding: 0px;
    float: left;
    list-style: none;
    padding-bottom: 1px;
}

#bt-toolbar .bt-icons li {
    float: left;
    background: none;
    padding: 0px 3px;
    margin: 0px;
    padding-bottom: 0px;
    display: none;
}

#bt-toolbar .bt-icons li a {
    font-size: 8px;
    font-weight: normal;
    padding: 0px;
    margin: 0px;
    /*height:41px !important;*/
    display: block;
    float: left;
    padding-top: 27px;
    padding-bottom: 6px;
    color: #ffffff;
    font-family: arial;
    text-decoration: none;
    width: 39px;
    text-align: center;
    outline: none;
}

#bt-toolbar #bt-icon-edituj {
    background: url(images/bt_sprite.png) no-repeat;
    background-position: 0 -103px;
    width: 39px;
}

#bt-toolbar #bt-icon-edituj:hover,
#bt-toolbar .icon-pressed #bt-icon-edituj {
    background: url(images/bt_sprite.png) no-repeat;
    background-position: 0 -148px;
    width: 39px;
}

#bt-toolbar #bt-icon-vlastnosti {
    background: url(images/bt_sprite.png) no-repeat;
    background-position: 0 -425px;
    width: 39px;
}

#bt-toolbar #bt-icon-vlastnosti:hover,
#bt-toolbar .icon-pressed #bt-icon-vlastnosti {
    background: url(images/bt_sprite.png) no-repeat;
    background-position: 0 -471px;
    width: 39px;
}

#bt-toolbar #bt-icon-publikuj {
    background: url(images/bt_sprite.png) no-repeat;
    background-position: 0 -335px;
    width: 39px;
}

#bt-toolbar #bt-icon-publikuj:hover,
#bt-toolbar .icon-pressed #bt-icon-publikuj {
    background: url(images/bt_sprite.png) no-repeat;
    background-position: 0 -381px;
    width: 39px;
}

#bt-toolbar #bt-icon-original {
    background: url(images/bt_sprite.png) no-repeat;
    background-position: 0 -245px;
    width: 39px;
}

#bt-toolbar #bt-icon-original:hover,
#bt-toolbar .icon-pressed #bt-icon-original {
    background: url(images/bt_sprite.png) no-repeat;
    background-position: 0 -291px;
    width: 39px;
}

#bt-toolbar #bt-right-container {

    float: right;
}

#bt-toolbar #bt-right {
    float: right;
    display: block;
    list-style: none;
    margin: 0px;
    padding: 0px;
}

#bt-toolbar #bt-right li {
    float: left;
    margin: 0px;
    padding: 0px;
    background: none;
}

#bt-toolbar li#bt-search {
    background: url(images/bt_sprite.png) no-repeat left top;
    background-position: 0 -714px;
    height: 26px;
    margin: 9px 10px;
}

#bt-toolbar #bt-search-text {
    border: none;
    border-width: 0px;
    float: left;
    width: 147px;
    margin-left: 3px;
    margin-top: 5px;

    height:auto;
    padding:0px;
    box-shadow:none;
    background:none;
    font-size:12px;
    font-family:"Helvetica CE", Verdana, Arial, sans-serif;
}

#bt-toolbar #bt-search-text[type="text"] {
    height:auto;
    padding:0px;
    border-radius: 0px;
    box-shadow: none;
    font-size:10px;
}

#bt-toolbar #bt-search-button {
    float: left;
    width: 26px;
    height: 26px;
    background: url(images/bt_sprite.png) no-repeat left top;
    background-position: 0 -678px;
    border: none;
    cursor: pointer;
}

.ui-autocomplete-loading {
    background: white url('images/wait-small.gif') right center no-repeat;
}

#bt-toolbar li#bt-user_menu {
    position: relative;
}

#bt-toolbar li#bt-user_menu a {
    background: url(images/bt_sprite.png) no-repeat center top;
    background-position: 0 -572px;
    display: block;
    width: 42px;
    height: 43px;
    padding: 0px;
    outline: none;
}

#bt-toolbar li.bt_button#bt-user_menu a:hover {
    background: url(images/bt_sprite.png) no-repeat center top;
    background-position: 0 -625px;
}

#bt-toolbar li.with_submenu .bt-submenu {
    position: absolute;
    top: 43px;
    width: 170px;
    list-style-type: none;
    padding: 0px;
    display: none;
    left: 0px;
    margin: 0px;
    border: 1px solid #BEBEBE;
    border-top: none !important;
    overflow: visible !important;
    z-index: 1;
}

#bt-toolbar li.with_submenu .bt-submenu-align-right {
    right: 0;
/ / right : 0;
    left: auto;
    background: #ffffff;
}

#bt-toolbar li.with_submenu .bt-submenu li,
#bt-toolbar ul#bt-tabs li.with_submenu .bt-submenu li {
    /*
        border-bottom:1px solid #BEBEBE;
        border-left:1px solid #BEBEBE;
        border-right:1px solid #BEBEBE;
        border-top:1px solid #F5F5F5;
    */

    float: left;
    background: none;
    background-color: #EDEDED;
    padding: 0px;
    margin: 0px;
    height: auto;
    width: 170px;
    position: relative;
}

#bt-toolbar li.with_submenu .bt-submenu li.menu-delimiter,
#bt-toolbar ul#bt-tabs li.with_submenu .bt-submenu li.menu-delimiter {
    height: 1px;
    line-height: 1px;
    font-size: 1px;
    background-color: #BEBEBE;
    border-bottom: 1px solid #F5F5F5;
    border-left: 1px solid white;
    border: none;
    border-right: 2px solid #BEBEBE;
}

#bt-toolbar li.with_submenu .bt-submenu li.first,
#bt-toolbar ul#bt-tabs li.with_submenu .bt-submenu li.first {
    border-top: 1px solid #BEBEBE;
}

#bt-toolbar ul#bt-tabs li.with_submenu .bt-submenu li.bt-menu-with-arrow a {
    background: url(images/menu-arrow.png) right no-repeat;
}

#bt-toolbar li#bt-user_menu .bt-submenu li a,
#bt-toolbar li.with_submenu .bt-submenu li a,
#bt-toolbar ul#bt-tabs li.with_submenu .bt-submenu li a {
    background: none;
    display: block;
    /*height:19px;*/
    color: #666666;
    font-weight: bold;
    font-size: 10px;
    padding: 5px 0px 5px 10px;
    margin: 0px;
    -moz-text-shadow: 0 1px 0 #fff;
    -webkit-text-shadow: 0 1px 0 #fff;
    text-shadow: 0 1px 0 #fff;
    text-align: left;
    float: none;
    width: auto;
    height: auto;
    position: relative;
}

#bt-toolbar .key-shortcut {
    position: absolute;
    font-size: 8px;
    top: 8px;
    right: 3px;
}

#bt-toolbar li.bt_button#bt-user_menu li#bt-logout a {
    background: url(images/bt_submenu_logout.png) right center no-repeat;
}

#bt-toolbar li.bt_button#bt-user_menu li#bt-settings a {
    background: url(images/bt_submenu_settings.png) right 4px no-repeat;
}

#bt-toolbar li.bt_button#bt-user_menu li#bt-change-pass a {
    background: url(images/bt_submenu_pass.png) right 4px no-repeat;
}

#bt-toolbar ul#bt-tabs li.with_submenu .bt-submenu .bt-submenu-level2 {
    position: absolute;
    padding: 0px;
    margin: 0px;
    /*left:171px;*/
    left: 165px;
    top: 0px;
    list-style: none;
    display: none;
    z-index: 500;
    border: 1px solid #BEBEBE;
    border-top: none;

}

#bt-toolbar ul#bt-tabs li.with_submenu .bt-submenu .bt-submenu-level2 li {

    z-index: 500;
}

#bt-toolbar ul#bt-tabs li.with_submenu .bt-submenu .bt-submenu-level2 li a {
    background: none;
    z-index: 500;
}

#bt-toolbar ul#bt-tabs li.with_submenu .bt-submenu .bt-submenu-level2 li.first {
    z-index: 500;
}

#bt-toolbar li.bt_button#bt-user_menu li#bt-logout a:hover,
#bt-toolbar li#bt-user_menu .bt-submenu li a:hover,
#bt-toolbar li.with_submenu .bt-submenu li a:hover,
#bt-toolbar ul#bt-tabs li.with_submenu .bt-submenu li a:hover {
    background-color: #FF9C00;
    color: #FFF;
    -moz-text-shadow: 1px 1px 1px #8c8c8c;
    -webkit-text-shadow: 1px 1px 1px #8c8c8c;
    text-shadow: 1px 1px 1px #8c8c8c;
}

#bt-toolbar li#bt-user_menu .bt-submenu li.menu-item-passive a,
#bt-toolbar li.with_submenu .bt-submenu li.menu-item-passive a,
#bt-toolbar ul#bt-tabs li.with_submenu .bt-submenu li.menu-item-passive a {
    color: #bfbaba;
}

#bt-toolbar li#bt-user_menu .bt-submenu li.menu-item-passive a:hover,
#bt-toolbar li.with_submenu .bt-submenu li.menu-item-passive a:hover,
#bt-toolbar ul#bt-tabs li.with_submenu .bt-submenu li.menu-item-passive a:hover {
    background-color: #EDEDED;
    color: #bfbaba;
    -moz-text-shadow: none;
    -webkit-text-shadow: none;
    text-shadow: none;
}

#bt-toolbar ul#bt-tabs li.with_submenu .bt-submenu .trigger:hover .bt-submenu-level2 {
    display: block;
}

#bt-toolbar li#bt-user {
    background: url(images/bt_user2.png) no-repeat left top;
    margin-left: 18px;
}

#buxus-toolbar #bt-toolbar-editor {
    background: url(images/bt_editor_bg.png) repeat left top;
    height: 32px;
}

#bt-toolbar #bt-toolbar-button {
    width: 28px;
    height: 32px;
}

.bt-close {
    float: right;
    margin-top: 12px;
    position: relative;
    left: 8px;
    top: 0px;
}

.bt-close img {
    border: 0px;
}

.bt-footer-left a {
    height: 30px;
    display: block;
    float: right;
    background: url(images/bt_box_footer_button_right.png) no-repeat right top;
    color: #000;
    font-size: 13px;
    line-height: 30px;
    text-decoration: none;
    margin-top: 8px;
    padding-right: 5px;
}

.bt-footer-left a span {
    background: url(images/bt_box_footer_button_left.png) no-repeat left top;
    display: block;
    padding: 0px 17px 0px 22px;
    -moz-text-shadow: 1px 1px 1px #aaa;
    -webkit-text-shadow: 1px 1px 1px #aaa;
    text-shadow: 1px 1px 1px #aaa;
}

#bt-toolbar #bt-logo a {
    padding: 0px;
    width: 98px;
    margin-left: 19px;
    height: 43px;
    float: left;
    background: url(images/bt_sprite.png) no-repeat right top;
    background-position: 0 -519px;
}

#bt-toolbar #bt-logo img {
    border: 0px;
    padding: 0px;
    margin: 0px;
}

#bt-toolbar #bt-user a {
    font-size: 10px;
    font-weight: bold;
    text-shadow: 0 0 2px #000;
    float: left;
    padding-top: 15px;
    padding-left: 20px;
    padding-right: 13px;
}

.bt-clear {
    clear: both;
}

.page-list-cover-over {
    background: #FEFFBF url(images/list-edit.png) no-repeat center center;
}

.page-list-cover-out {
    background: url(images/list-edit-small.png) no-repeat right top;
    background-color: transparent;
}

@media screen and (max-width:950px) {

    li#bt-logo {
        display: none;
    }

}

@media screen and (max-width:832px) {

    #bt-toolbar {
        position: relative;
        background: transparent url(images/bt_background3.png) repeat-x;
        height: 96px;
        min-width: 468px;
    }

    #bt-toolbar #bt-right-container {
        float: left;
    }

    #bt-toolbar li#bt-user_menu a {
        display: none;
    }

}

.defaultSkin table.mceLayout {
    border: 0 !important;
}

.defaultSkin table.mceLayout tr.mceFirst td {
    border: 0 !important;
}

.defaultSkin .mceToolbar table td {
    border: 0px none !important;
    padding: 0px !important;
}

.defaultSkin .mceToolbar img {
    margin: 0px !important;
    border: 0px none !important;
}

.defaultSkin .mceExternalToolbar {
    z-index: 500;
}

/* External */
.defaultSkin .mceExternalToolbar {
    border: 1px solid #CCC;
    display: none;
    position: absolute;
    width: 100%;
    margin-top: -1px;
    top: 0 !important;
}

.mceExternalToolbar > table {
    width: 100% !important;
}

.defaultSkin .mceExternalToolbar td.mceToolbar {
    padding-right: 30px;
}

.defaultSkin .mceExternalClose {
    position: absolute;
    top: 3px;
    right: 3px;
    width: 7px;
    height: 7px;
    background: url(/images/icons.gif) -820px 0
}

.defaultSkin table.mceToolbar {
    position: relative;
    float: left;
}

@media all and (max-width: 970px) and (-webkit-min-device-pixel-ratio:0) {
    .defaultSkin table.mceToolbar {
        float: none;
    }
}

@media all and (max-width: 1540px) and (min-width:971px) and (-webkit-min-device-pixel-ratio:0) {
    .defaultSkin table.mceToolbar{

        float: left;
    }

    .defaultSkin table.mceToolbar:last-child{

        float: none;
        clear: both;
    }

}

@media all and (min-width:1541px) and  (-webkit-min-device-pixel-ratio:0){
    .defaultSkin table.mceToolbar {
        float: left;
    }
}





#externalToolbarWrapper > div {
    position: absolute !important;
    width: 100%;
}

#externalToolbarWrapper {
    /*background-image: url('/images/toolbar.png');*/
    background-repeat: repeat-y;
    height: 32px;
    background-color: #f0f0f0;
    display: none;
    position: relative;
    top: 60px;
}
/*
Document   : jquery.pnotify.default.css
Created on : Nov 23, 2009, 3:14:10 PM
Author     : Hunter Perrin
Version    : 1.2.0
Link       : http://pinesframework.org/pnotify/
Description:
   Default styling for Pines Notify jQuery plugin.
*/
/* -- Notice */
.ui-pnotify {
    top: 25px;
    right: 25px;
    position: absolute;
    height: auto;
    /* Ensures notices are above everything */
    z-index: 9999;
}
/* Hides position: fixed from IE6 */
html > body .ui-pnotify {
    position: fixed;
}
.ui-pnotify .ui-pnotify-shadow {
    -webkit-box-shadow: 0px 2px 10px rgba(50, 50, 50, 0.5);
    -moz-box-shadow: 0px 2px 10px rgba(50, 50, 50, 0.5);
    box-shadow: 0px 2px 10px rgba(50, 50, 50, 0.5);
}
.ui-pnotify-container {
    background-position: 0 0;
    padding: .8em;
    height: 100%;
    margin: 0;
}
.ui-pnotify-sharp {
    -webkit-border-radius: 0;
    -moz-border-radius: 0;
    border-radius: 0;
}
.ui-pnotify-closer, .ui-pnotify-sticker {
    float: right;
    margin-left: .2em;
}
.ui-pnotify-title {
    display: block;
    margin-bottom: .4em;
}
.ui-pnotify-text {
    display: block;
}
.ui-pnotify-icon, .ui-pnotify-icon span {
    display: block;
    float: left;
    margin-right: .2em;
}
/* -- History Pulldown */
.ui-pnotify-history-container {
    position: absolute;
    top: 0;
    right: 18px;
    width: 70px;
    border-top: none;
    padding: 0;
    -webkit-border-top-left-radius: 0;
    -moz-border-top-left-radius: 0;
    border-top-left-radius: 0;
    -webkit-border-top-right-radius: 0;
    -moz-border-top-right-radius: 0;
    border-top-right-radius: 0;
    /* Ensures history container is above notices. */
    z-index: 10000;
}
.ui-pnotify-history-container .ui-pnotify-history-header {
    padding: 2px;
}
.ui-pnotify-history-container button {
    cursor: pointer;
    display: block;
    width: 100%;
}
.ui-pnotify-history-container .ui-pnotify-history-pulldown {
    display: block;
    margin: 0 auto;
}
/*
Document   : jquery.pnotify.default.icons.css
Created on : Nov 24, 2009, 2:58:21 PM
Author     : Hunter Perrin
Version    : 1.2.0
Link       : http://pinesframework.org/pnotify/
Description:
   Pines Icon styling for Pines Notify.
*/

.ui-pnotify .picon {
    background-color: transparent;
    background-repeat: no-repeat;
    background-position: center center;
    width: 17px;
    height: 17px;
}
.ui-pnotify-title {
    line-height: 17px;
    min-height: 17px;
}