{"version": 3, "sources": ["https://fonts.googleapis.com/css?family=Open+Sans:300,400,600,700&subset=latin,latin-ext", "style.css"], "names": [], "mappings": "AAAA,WACE,wBACA,kBACA,gBACA;AAEF,WACE,wBACA,kBACA,gBACA;AAEF,WACE,wBACA,kBACA,gBACA;AAEF,WACE,wBACA,kBACA,gBACA;ACfF,GACA,GACA,GACA,GACA,GACA,GACE;AAEF,GACE;AAEF,GACE;AAEF,GACE;AAEF,GACE;AAEF,GACE;AAEF,GACE;AAEF,GACA,GACA,GACE,eACA;AAEF,MACE;AAEF,UACE,cACA,gBACA;AAEF,uBACE;AAEF,iBACA,gCACE,WACA;AAEF,gCACE,yBACA;AAEF,aAEA,mBADA,mBAEE;AAGF,iCADA,iCAEE;AAEF,YACE;AAEF,QACE;AAEF,gBACE,6BACA;AAEF,kBACE;AAEF,qBACE;AAEF,gCACE;AAEF,uBACE,kBACA;AAEF,eACE,mBACA,kBACA,sCACA,aACA,WACA,eACA,OACA,6BACA,UACA,kBACA,iBACA,SACA;AAEF,oBACE,kBACA,cACA,iBACA,WACA,gBACA;AAEF,8BACE;AAEF,oCACE;AAEF,+CACE;AAEF,sCACE,iBACA;AAEF,0CACE;AAIF,mCAFA,qCACA,kCAEE,YACA;AAEF,qCACE;AAEF,kCACE;AAEF,mCACE;AAEF,iCACE,QACA;AAGF,iBADA,mBAEE;AAGF,sBADA,wBAEE;AAGF,uBADA,yBAEE;AAEF,yCACE;AAEF,YACE,kBACA;AAKF,eACE;AAEF,gCACE;AAEF,sCACE;AAEF,cACE;AAEF,wBACE;AAEF,mBACE,iBACA,yBACA,eACA;AAEF,oBACE,WACA,YACA,UACA,YACA;AAEF,gCACE;AAEF,iBACE;AAEF,wBACE;AAEF,kCACE,8CACA,mBACA,eACA,YACA,SACA;AAEF,mBACE,iBACA,gBACA,kBACA,UACA;AAEF,OACE;AAEF,iBACE;AAEF,2BACE;AAEF,qBACA,oBACE;AAEF,uBACE,0BACA;AAEF,sBACE;AAEF,gCACE;AAGF,6CADA,uFAEE,cACA,0BACA,gBACA;AAEF,gEACE,eACA;AAEF,qCACE;AAEF,oCACE;AAEF,+BACE,kBACA,UACA,MACA,yBACA,4BACA;AAEF,2CACE;AAEF,yCACE;AAEF,gCACE,mBACA;AAEF,sCACE,6BACA;AAEF,2CACE,gBACA;AAEF,gCACE,mBACA;AAEF,sCACE,6BACA;AAEF,2CACE,gBACA;AAEF,cACE,kBACA,eACA,gBACA,WACA,aACA;AAKF,yBACA,mBAHA,uBADA,+BAEA,qBAGE,2BACA,wBACA,sBACA;AAEF,kBAEE,wBACA;AAEF,kBACA,mBACE;AAEF,oBACE;AAGF,sCADA,4CAEE;AAEF,yBAEE,sCADA,4CAEE;;AAGJ,8BACE;AAEF,8BACE;AAEF,qCACE;AAGF,4BACA,gDAFA,kCAGE;AAEF,kCACE;AAEF,yEACE;AAEF,2CACE;AAEF,+BACE;AAEF,+BACE;AAEF,6BACE,UACA;AAEF,6BACE;AAEF,+BACE;AAGF,qCADA,uCAEE;AAEF,2CACA,qCACE;AAGF,qCADA,uCAEE,eACA,YACA,aACA;AAEF,mDACE;AAEF,6CACE;AAEF,wDACE;AAEF,8DACE;AAGF,yDADA,2DAEE,cACA;AAEF,8EACE,eACA;AAEF,4EACE,eACA;AAOF,2CADA,6CAEE,kBACA,UACA;AAGF,qDADA,uDAEE;AAGF,uDADA,yDAEE;AAIF,wCACA,4DAHA,0CACA,8DAGE;AAEF,6EACE;AAEF,gDACA,kDACE;AAGF,2CADA,6CAEE;AAEF,wCACE,aACA,mBACA,YACA,eACA;AAEF,oDACE,cACA;AAEF,WACE;AAEF,eACE,qBACA;AAEF,8BACE;AAEF,qDACE,gBACA,cACA;AAEF,4BACA,6BACE;AAEF,wBACE;AAEF,0BACE,kBACA;AAGF,gCADA,gCAEE,gBACA;AAEF,+BACE,gBACA;AAEF,iCACE;AAEF,8BACE;AAEF,2CACE,gBACA;AAEF,oCACE,SACA;AAEF,uBACE;AAEF,8BACE,mBACA,WACA;AAEF,gDACE;AAKF,oEACA,2BAFA,2CADA,mEADA,0CAKE;AAGF,mCADA,mCAEE;AAEF,8BACE;AAEF,yCACE;AAGF,qDADA,oDAEE;AAEF,eACE,yBACA,WACA,iBACA;AAGF,kEADA,sDAEE;AAEF,yBACE,+BACE,cACA;;AAGJ,iBACA,iBACE;AAEF,0BACE,iBACE;;AAGJ,yBACE,iBACE;;AAGJ,yBAIE,iBAHA,iBACE;;AAMJ,KACE;AAEF,sBACE;AAEF,SACE;AAEF,yBACE;AAEF,yBACE;AAEF,sBACE;AAEF,yBACE;AAEF,wBACE;AAMF,8BAFA,4BAFA,+BACA,+BAEA,+BAEE;AAEF,aACE,yBACA,qBACA;AAKF,oBADA,oBADA,mBADA,mBAIA,mCACE,yBACA,qBACA;AAGF,oBADA,oBAEA,mCACE;AAWF,8BATA,sBAIA,6BADA,6BADA,4BADA,4BAIA,uBAGA,8BADA,6BADA,6BAIA,gCAIA,uCADA,uCADA,sCADA,sCAIE,yBACA;AAEF,aACE,yBACA,qBACA;AAKF,oBADA,oBADA,mBADA,mBAIA,mCACE,yBACA,qBACA;AAGF,oBADA,oBAEA,mCACE;AAWF,8BATA,sBAIA,6BADA,6BADA,4BADA,4BAIA,uBAGA,8BADA,6BADA,6BAIA,gCAIA,uCADA,uCADA,sCADA,sCAIE,yBACA;AAEF,UACE,yBACA,qBACA;AAKF,iBADA,iBADA,gBADA,gBAIA,gCACE,yBACA,qBACA;AAGF,iBADA,iBAEA,gCACE;AAWF,2BATA,mBAIA,0BADA,0BADA,yBADA,yBAIA,oBAGA,2BADA,0BADA,0BAIA,6BAIA,oCADA,oCADA,mCADA,mCAIE,yBACA;AAEF,aACE,yBACA,qBACA;AAKF,oBADA,oBADA,mBADA,mBAIA,mCACE,yBACA,qBACA;AAGF,oBADA,oBAEA,mCACE;AAWF,8BATA,sBAIA,6BADA,6BADA,4BADA,4BAIA,uBAGA,8BADA,6BADA,6BAIA,gCAIA,uCADA,uCADA,sCADA,sCAIE,sBACA;AAEF,aACE,yBACA,qBACA;AAKF,oBADA,oBADA,mBADA,mBAIA,mCACE,yBACA,qBACA;AAGF,oBADA,oBAEA,mCACE;AAWF,8BATA,sBAIA,6BADA,6BADA,4BADA,4BAIA,uBAGA,8BADA,6BADA,6BAIA,gCAIA,uCADA,uCADA,sCADA,sCAIE,yBACA;AAEF,YACE,yBACA,qBACA;AAKF,mBADA,mBADA,kBADA,kBAIA,kCACE,yBACA,qBACA;AAGF,mBADA,mBAEA,kCACE;AAWF,6BATA,qBAIA,4BADA,4BADA,2BADA,2BAIA,sBAGA,6BADA,4BADA,4BAIA,+BAIA,sCADA,sCADA,qCADA,qCAIE,yBACA;AAEF,UACE;AAKF,iBADA,iBADA,gBADA,gBAIA,gCACE,cACA;AAGF,iBADA,iBAEA,gCACE;AAWF,2BATA,mBAIA,0BADA,0BADA,yBADA,yBAIA,oBAGA,2BADA,0BADA,0BAIA,6BAIA,oCADA,oCADA,mCADA,mCAIE;AAEF,WACE,cACA,gBACA;AAKF,kBADA,kBADA,iBADA,iBAIA,iCACE,cACA;AAGF,kBADA,kBAEE;AAGF,kBADA,kBAEA,iCACE;AAWF,4BATA,oBAIA,2BADA,2BADA,0BADA,0BAIA,qBAGA,4BADA,2BADA,2BAIA,8BAIA,qCADA,qCADA,oCADA,oCAIE;AAEF,cACA,oBACA,+BACA,iCACA,iCACA,iBACA,aACA,wCACA,wCACA,2BACA,OACA,SACA,UACA,cACE;AAEF,aACE,cACA,6BACA;AAEF,aACE;AAEF,eACE,WACA,YACA;AAEF,WACE,qBACA,WACA,qBACA,yBACA,kBACA,gBACA,kBACA,kBACA,eACA,kBACA,gBACA;AAEF,kBACE;AAEF,uBACE;AAEF,8BACE;AAEF,uBACE;AAEF,8BACE;AAEF,uBACE;AAEF,8BACE;AAEF,oBACE;AAEF,2BACE;AAEF,uBACE;AAEF,8BACE;AAEF,sBACE;AAEF,6BACE;AAEF,kBACE,eACA,gBACA,gBACA,WACA,cACA;AAEF,yBACE,QACA;AAGF,2BADA,2BAEE,WACA,gBACA,gBACA,kBACA;AAEF,gBACE,eACA,gBACA,gBACA;AAEF,OACE,yBACA,cACA,wBACA,eACA,gBACA,gBACA;AAEF,OACE,yBACA,cACA,wBACA,eACA,gBACA,mBACA,iBACA,kBACA;AAGF,eADA,eAEE,yBACA;AAGF,eADA,eAEE,yBACA;AAGF,eADA,eAEE,yBACA;AAGF,qBADA,qBAEE,yBACA;AAGF,cADA,cAEE,yBACA;AAGF,YADA,YAEE,yBACA;AAGF,eADA,eAEE,yBACA;AAGF,aADA,aAEE,sBACA;AAGF,eADA,aAEE,yBACA;AA8EF,wCACE,gBACA,gBACA,2BACA,sBACA,yBACA,kBACA,YACA,sBACA,SACA,gBACA,gBACA,iBACA,kBACA;AAEF,yDACE,mBACA,yBACA,kBACA,gBACA,WACA,eACA,iBACA,qBACA,yBACA;AAGF,sBAIA,4BAFA,4BADA,yBAIA,+BAFA,+BAGE,yBACA,kBACA,cACA,eACA;AAEF,iBACA,oBACE,sBACA,sBACA,cACA,WACA,oBACA,iBACA,iBACA,kBACA;AAGF,eACE;AAEF,4BACE;AAEF,8BACE;AAEF,+BACE;AAEF,6BACE;AAGF,cACE,kBACA;AAEF,kBACE,iBACA,gBACA;AAEF,qBACE,MACA;AAEF,4BACE,cACA,kBACA,SACA,WACA;AAEF,kBACE;AAEF,YACE,+BACA,4BACA;AAGF,kBACE,sBACA,sBACA,2BACA,gBACA,WACA,iBAGA;AAEF,WACE,cACA,yBACA,eACA,qBACA,aACA,kBACA,gBACA,eACA,kBACA;AAEF,iBACE,yBACA,qBACA;AAEF,oBACE,eACA,gBACA;AAGF,+BADA,8BAEE,qBACA;AAEF,0BACE;AAEF,oBACE;AAEF,gBACE,iBACA,kBACA,eACA;AAEF,oCACA,sCACE;AAGF,0BADA,UAEA,YACE,gBACA,yBAEA;AAGF,eACA,gBACE,cACA;AAEF,4BACA,6BACE;AAGF,kCACE;AAEF,0BACE;AAEF,cACE;AAEF,cACA,qBACE;AAEF,cACA,YACE;AAEF,YACE,cACA;AAEF,yBACE;AAEF,6BACA,sCACE,WACA;AAEF,oBACE,yBACA,gBACA,gBACA,kBACA,kBACA,eACA;AAEF,uBACE;AAEF,sBACE;AAEF,gCACE;AAEF,YACE,iBACA,kBACA,gBACA,kBACA;AAEF,MACE,SACA,UACA;AAEF,SACE,mBACA,mBACA;AAEF,yBACE;AAEF,0BACE;AAEF,sBACE,SACA;AAGF,iBADA,6BAEE;AAEF,wBACE,aACA;AAGF,yBAEA,+BADA,+BAEE,yBACA,qBACA,WACA;AAEF,yBACE;AAEF,sBACE,gBACA,cACA,eACA;AAEF,6BACE,iBACA,kBACA;AAEF,yCACE,iBACA,kBACA,mBACA;AAEF,wBACE;AAEF,iBACE,yBACA,yBACA,cACA,mBACA,kBACA;AAEF,gCACE,iBACA,kBAEA;AAEF,4CACE,iBACA,kBACA;AAEF,2BACE;AAEF,iBACE;AAEF,uCACA,sCACE,mBACA,cACA,qBAGA;AAEF,yBACE;AAEF,oBACE;AAGF,YACE,cACA;AAEF,oCACE,cACA,aACA;AAEF,4BACE,cACA,aACA;AAEF,oBACE,WACA;AAEF,wBACE,YACA,aACA;AAEF,YACE,kBACA,cACA,OACA,MACA,kBACA,mBACA,mCACA,WACA,gBACA,mBACA,cACA,wBACA,gBACA;AAEF,UACE,WACA;AAEF,OACE;AAEF,aACE;AAEF,0BACE;AAEF,WACE,gBACA,UACA;AAEF,cACE;AAEF,cACE,gBACA;AAGF,+BACA,yCAEA,4CADA,oCAEA,6CACE;AAEF,yCACE,YACA;AAEF,0CACE,gBACA,YACA;AAEF,qCACE,gBACA,YACA,wBACA;AAEF,oBACE;AAGF,YACE;AAEF,YACE,WACA,YACA,cACA,mBACA,kBACA,eACA;AAEF,mBACE,WACA,YACA,kBACA,mBACA,eACA;AAEF,mBACE,WACA,YACA,kBACA,mBACA,eACA;AAEF,yBACE,iBACA,oBACA,sBACA;AAEF,WACE;AAGF,sBACE;AAEF,yBACE,gBACA;AAEF,eACE,yBACA,mBACA,UACA,cACA;AAEF,oBACE,kBACA,gBACA,SACA,mBACA;AAEF,qBACE,kBACA,iBACA,SACA,mBACA,YACA;AAEF,uBACE,eACA,gBACA;AAEF,sBACE,eACA;AAGF,MACE,4BACA,oBACA,yBACA,sBACA;AAKF,qBACE,mBACA,eACA,aACA,MACA,WACA,WACA;AAMF,QACE,kBACA,kBACA,mBACA;AAEF,kBACE;AAEF,WACA,WACE,eACA;AAEF,iBACE,aACA,yBACA;AAEF,uBACE,0BACA;AAEF,oBACE;AAEF,oBACE,qBACA;AAEF,mBACA,mBACE;AAEF,WACE,6BACA,SACA,UACA;AAEF,sBACE;AAEF,yBACE,mBACA,iBACA,kBACA,kBACA,cACA,kBACA;AAGF,yCADA,8BAEE,kBACA,eACA,gBACA;AAEF,cACE,mBACA,8BACA,+BACA,kBACA,cACA,kBACA;AAEF,mBACE,YACA,qBACA,eACA;AAEF,qBACE,cACA;AAEF,YACE;AAEF,gBACE;AAEF,kBACE,eACA;AAEF,cACE;AAGF,QACE;AAEF,YACE;AAEF,SACE;AAEF,YACE;AAEF,UACE;AAEF,cACA,aACE,sBACA,sBACA,yBACA,kBACA,cACA,cACA,iBACA,2EACA,WACA;AAEF,oBACA,mBACE;AAEF,2BACE;AAEF,2BACE;AAEF,yBACE;AAEF,4BACE;AAEF,4BACE;AAEF,0BACE;AAEF,mBACE,sBACA,yBACA,kBACA,cACA,eACA,gBACA,cACA,iBACA;AAEF,yCACE;AAEF,iCACE;AAEF,cACE,0CACA;AAEF,0BACE,0CACA;AAGF,yBACE;AAEF,yBACE,oCACA,6BACA;AAEF,8BACE;AAGF,aACE,kBACA,WACA,yBACA,sBACA;AAEF,sBACE;AAEF,mBACE,cACA,gBACA,eACA,yBACA;AAEF,mBACE,cACA,WACA,kBACA,sCACA,yCACA,oCACA;AAGF,yBADA,0BAEE,cACA,WACA,UACA,YACA,UACA,iBACA,eACA,WACA,uCACA,gBACA,2BACA,8BACA;AAEF,0BACE,aACA,iBACA,yBACA;AAEF,yBACE,cACA,kBACA,sBACA,cACA;AAEF,oBACE,cACA,WACA,SACA,gBACA,yBACA,kBACA,kBACA,MACA,SACA,WACA,mCACA,sCACA,iCACA;AAEF,oEACE;AAEF,qEACE;AAGF,WACE;AAEF,kBAGA,kBAFE;AAKF,oBACE,YACA,iBACA,cACA,yBACA;AAEF,4BACE,WACA;AAEF,+BACE,kBACA,oBACA,qBACA;AAEF,kBACE,eACA,sBACA,yBAEA,yBACA;AAEF,4BAEE,yBACA,uBACA,gBACA,kBACA;AAEF,oCACE,eACA,YACA,sBACA;AAEF,kBACA,qCACA,oCACE,mBACA,sBACA,iBACA,gBACA,cACA;AAEF,mBACE;AAEF,sBACE;AAEF,mCACE,6BACA,mCACA;AAIF,gBAFA,gBAGA,mCAFA,mCAGA,kCACE,mBACA;AAEF,oBACA,uCACA,sCACE;AAEF,iBACA,oCACA,mCACE,sBACA,gBACA,gBACA;AAEF,wBACE,kBACA,WACA,sBACA;AAEF,yBACE,cACA,cACA,YACA,iBACA,SACA;AAEF,6BACE;AAEF,4BACE,YACA;AAEF,sCACE;AAEF,+BACE,YACA;AAEF,oBACE,kBACA,QACA,SACA,WACA,YACA,YACA,YACA,WACA,kBACA,gBACA,aACA,2BACA;AAEF,yBACE,YACA,WACA,eACA;AAEF,iBACE;AAEF,eAEA,gBACA,cAFA,eAGE;AAEF,eAEA,iBADA,eAEA,cACE;AAEF,eAGA,cAFA,kBACA,gBAEE;AAEF,eACA,kBAEA,cADA,iBAEE;AAEF,mBACE;AAEF,+BACE,UAGA;AAEF,wBACE;AAEF,mCACE;AAEF,gCACE;AAEF,8BACE;AAGF,IACE,kBACA,cACA,SACA,UACA,gBACA,eACA;AAEF,SACE,cACA,kBACA,SACA,UACA;AAEF,kBACE;AAEF,uBACE;AAGF,UADA,SAEA,gBACE,cACA,kBACA,SACA,UACA,gBACA,eACA;AAEF,WACE,cACA,aACA,iBACA,WACA,qBACA,yBACA,mBACA,0BACA,kBACA,sBACA;AAEF,gBACE;AAEF,iBACE,mBACA,eACA;AAEF,gBACE,cACA,kBACA,eACA,WACA,WACA,YACA,aACA,UACA,iBACA,mBACA,gBACA,SACA,eACA,eACA,cACA,kBACA;AAEF,uBACE,YACA,cACA,kBACA,WACA,kBACA;AAEF,6CACE;AAEF,2BACE,wBACA,YACA,WACA;AAEF,kCACE;AAEF,wDACE;AAGF,UADA,gBAEE,aACA,UACA,gBACA,mBACA,0BACA,sBACA;AAEF,UACE,uBACA,iBACA,yBACA,6LACA,uLACA,6KACA,0BACA;AAEF,WACE,kBACA,aACA;AAEF,+BACE;AAEF,sBACE,gDACA;AAKF,gBACE,cACA,WACA,eACA,WACA,SACA,0BACA;AAEF,eACE,UACA;AAEF,iBACA,kBACE,WAEA,uBACA;AAKF,sBACE,cACA,0BACA,mBACA;AAKF,sBACE;AAEF,iBACA,kBACE,eACA,aACA,sBACA;AAGF,YACE,sBACA;AAEF,mBACE,kBACA;AAGF,YACE;AAGF,YACE,cACA,qBACA;AAEF,oBACE;AAGF,WACE,yBACA,WACA;AAEF,YACE,yBACA,YACA;AAEF,eACE,kBACA;AAEF,QACE;AAEF,YACE;AAEF,YACE;AAEF,gBACE;AAEF,OACE;AAEF,QACE;AAEF,cACE;AAGF,wBACE;AAEF,+BACE,eACA,wBACA,eACA,iBACA,WACA,WACA,mBACA;AAEF,uCACE;AAEF,qCACE;AAEF,oCACE;AAEF,uCACE;AAEF,qBACE,6BACA,gCACA,wBAEA,6BACA;AAEF,wBACE,6BACA,gCACA,wBACA,UACA,8BACA,0BACA;AAEF,OAGA,eAFE;AAKF,aACE;AAEF,YACE;AAEF,eACE;AAEF,sBACE;AAEF,yBACE;AAGF,mCACE,gBACA,UACA,2DACA,sEACA,mEACA,YACA,gBACA;AAEF,+BACE;AAEF,+BACE;AAEF,8BACE;AAEF,4BACE;AAGF,eACA,aACE,gBACA,kBACA;AAEF,gBACE,aACA;AAGF,6BACE,2BACA,4BACA;AAEF,cACE,WACA,kBACA,kBACA;AAEF,0BACE,YACA;AAEF,aACE,cACA,eACA,eACA;AAEF,iBACE,0BACA,mBACA;AAEF,mBACE,mBACA,kBACA;AAEF,0CACE;AAEF,aACE,gBACA;AAEF,iCACE,WACA;AAEF,6CACE,YACA;AAEF,UACE;AAEF,YACE,cACA,oBACA;AAEF,cACE,eACA,iBACA;AAEF,iBACA,sBACE;AAEF,kBACE;AAEF,6BACE,WACA;AAEF,aACE;AAEF,YACE;AAEF,YACE;AAEF,YACE,WACA,WACA,kBACA;AAEF,mBACE,cACA,cACA,eACA;AAEF,yBACE;AAEF,gBACE,eACA,eACA;AAEF,uBAGA,sCAFE;AAKF,yBACE,YACE,qBAEA;AAEF,YACE;;AAIJ,oBAEE,UACA,iBACA;AAEF,2BAEE,WACA,cACA;AAEF,mBACE,kBACA,UACA,eACA;AAEF,2BACE,WACA,kBACA,MACA,UACA,YACA,UACA;AAEF,gCACE;AAEF,yCACE;AAEF,iDACE;AAEF,oEACE;AAEF,wFACA,2GACE;AAEF,0CACA,6DACE;AAEF,0CACE,sCACE,eACA;AAEF,6CACE,SACA;;AAGJ,0CACE,oEACE;;AAGJ,yBACE,kBACA;AAEF,+BACE,WACA,cACA;AAEF,qCACE;AAEF,oCACE;AAEF,0CACE,6CACE;AAEF,yDACE;AAEF,wDACE;;AAGJ,wBACE,kBACA,MACA,OACA,WACA,YACA,kBACA,eACA,yBACA;AAEF,0BACE,cACA,WACA,YACA,kBACA,SACA,QACA,kBACA;AAEF,0CACE,4CACE,WACA,YACA,SACA,kBACA,gCACA,mCACA;AAEF,8CACE,kBACA;AAEF,qEACE;;AAGJ,2BACE,kBACA,iBACA,gBACA,oBACA;AAEF,iCACE,WACA,cACA;AAEF,8BACE,gBACA;AAEF,6BACE,aACA;AAEF,0CACE,WACA;AAEF,qBACE,cACA;AAEF,mCACE,WACA,kBACA,SACA,WACA,SACA,QACA,6BACA;AAEF,yCACE,8BACE;AAEF,6BACE;;AAGJ,0CACE,+CACE,cACA,cACA;AAEF,uDACE,SACA,UACA,yBACA;AAEF,oDACE;AAEF,8DACE,kBACA,WACA,UACA,QACA;AAEF,wFACE;AAEF,gGACE,SACA,UACA,WACA,yBACA;AAEF,6FACE;AAEF,uGACE,UACA,WACA;AAEF,wEACE;;AAIJ,4BACE,gBACA,yBACA,kBACA,aACA;AAEF,sCAEA,4CADA,4CAIE;AAEF,6BACE,WACA;AAKF,sCAEA,4CADA,4CAEE,yBACA;AAEF,0BACE;AAEF,sCACE;AAEF,iDACA,kDACE;AAEF,qCACE,eACA;AAEF,sCAEA,qCADA,sCAEE;AAEF,uCACE;AAEF,qCACA,sCACE;AAEF,uCACE,UACA;AAEF,wCACE,UACA;AAGF,yCADA,uCAEE;AAGF,sCADA,qCAEE;AAEF,sCACE;AAEF,yCACE,gBACA;AAEF,2CACE,kCACA,+BACA;AAGF,iDADA,iDAEE,yBACA;AAEF,wCACA,yCACE;AAEF,0CACA,2CACE,eACA,eACA;AAEF,qCACE,WACA;AAEF,0CACE,kBACA,kCACA,+BACA;AAEF,+CAEA,qDADA,qDAEE;AAGF,sCACE,YACA;AAEF,2CACE,iBACA,kCACA,+BACA;AAEF,gDAEA,sDADA,sDAEE,iDAEA;AAGF,sBACE,WACA,YACA,kBACA;AAEF,gBACE,kBACA,aACA,yBACA,kBACA,mBACA,WACA,+BACA,kBACA;AAIF,mBAFA,mBACA,oBAEE,kBACA,UACA,kBACA,mBACA,YACA,WACA,eACA,iBACA,kBACA;AAEF,mBACA,oBACE,WACA;AAEF,mBACE;AAEF,oBACE;AAEF,mBACE,YACA,aACA;AAEF,oBACE,kBACA,OACA,MACA,QACA,SACA;AAEF,yBACE,gBACA,eACA;AAEF,uBACE;AAEF,yBACE,SACA;AAEF,yBACE,MACA;AAEF,mBACE,gBACA,WACA;AAEF,4CACE,WACA,qBACA;AAEF,oEACE;AAEF,4CACE,qBACA;AAEF,iDACE;AAEF,wDACE;AAEF,wDACE,YACA,WACA,qBACA;AAEF,6BACE;AAEF,sDACE;AAEF,sDACE,qBACA,sBACA,iBACA;AAEF,gBACE,YACA,mBACA,kBACA,kBACA;AAEF,6CACE,WACA;AAGF,YADA,8BAEA,kBACE;AAEF,8BACE,gBACA;AAEF,4BACE;AAEF,6CACE,cACA,eACA;AAEF,0BACA,yBACE;AAEF,sCACA,qCACE;AAEF,eACE,sBACA,8BACA,6BACA,gBACA,eACA,SACA,sBACA,aACA,SACA;AAEF,4BACE;AAEF,wCACE,MACA;AAEF,+BACE;AAEF,yCACE;AAEF,yCACE;AAEF,yCACE;AAEF,kCACE;AAEF,oCACE,YACA,kBACA,SACA,gBACA,mBACA,WACA,kBACA;AAEF,2CACE,YACA,mBACA,cACA;AAGF,+CADA,+CAEE;AAEF,mCACE,SACA;AAEF,sCACE,gCACA,kBACA,gBACA;AAEF,oDACE;AAEF,wCACE,qBACA;AAEF,oCACE,kBACA;AAEF,kCACE,mBACA,aACA;AAEF,qCACE,kBACA;AAEF,mCACE;AAEF,iDACE;AAEF,sCACE,qBACA;AAEF,oCACE;AAEF,oDACE,YACA,WACA;AAEF,iCACE,kBACA;AAEF,KACE,oEACA,sBACA,eACA,cACA;AAGF,KAIA,sCADA,iCAJA,KAEE;AASF,kBACE;AAEF,2BACE,yBACA,iBACA,cACA,6CACA,0CACA;AAGF,sCADA,sCAEE;AAEF,OACE;AAEF,OACE,cACA;AAEF,EACE;AAGF,QADA,QAEE;AAEF,eACE;AAEF,WACE;AAEF,aACE;AAEF,gBACE;AAEF,KACE;AAEF,gBACE,8BACA,WACA,sBACA,WACA;AAEF,eACE,gCACA,6BACA,6BACA,gBACA;AAEF,MACE,qBACA;AAGF,kBACE;AAEF,cACE,kBAEA;AAGF,aACE,YACA;AAGF,eACE,4BACA,sBACA,6BACA,kBACA,oCACA,eACA;AAEF,cACE;AAEF,YACE;AAEF,qBACE;AAEF,uBACE,kBACA;AAEF,mCACE,uBACA,mBACA,kBACA;AAEF,sBACE;AAEF,qBACE,eACA;AAEF,cACE;AAGF,SACE,WACA;AAEF,SACE;AAEF,iBACE;AAEF,cACE,eACA,iBACA;AAEF,yBACE,cACE,iBACA,mBACA;;AAGJ,cACE,iBACA;AAEF,iBACA,iBACA,iBACA,iBACA,iBACA,eACA,eACA,eACA,eACA,eACE;AAGF,YADA,YAEE,6BACA;AAEF,UACE,0BACA;AAGF,QACE,uCACA,6BACA,SACA,OACA,kBACA,kBACA;AAEF,mBACE,eACA,SACA,OACA,QACA,aACA,kBACA,gBACA;AAEF,cACE,eACA,SACA,OACA,QACA,aACA,kBACA,gBACA,6BACA;AAGF,0CADA,+BAEE;AAGF,+BADA,2CAEE;AAEF,wDACE;AAEF,8BACE;AAGF,cACE,aACA;AAEF,kBACA,kBACE;AAGF,gBACE;AAGF,4BADA,4BAEE,yBACA,yBACA;AAOF,4BAHA,4BAIA,4BAHA,4BACA,4BAHA,4BAME;AAEF,mBACE;AAOF,mBAHA,mBAIA,mBAHA,mBACA,mBAHA,mBAME,6BACA,oBACA,YACA;AAGF,mBACE,eACA;AAEF,4BACE;AAEF,sBAEA,4BADA,4BAEE,+BACA,6BACA,8BACA,4BACA,eACA,mCACA,sBACA,kBACA,mBACA,iBACA,WACA;AAEF,iBACE,eACA;AAEF,eACE,cACA,gBACA;AAGF,qBADA,qBAEE,yBACA;AAEF,qBACE;AAGF,YACE;AAEF,YACE;AAEF,YACE;AAEF,eACE;AAEF,uBACE,iBACA;AAEF,mBACE,8BACA,+BACA,gBACA;AAEF,aACE,8BACA,kBACA,gBACA;AAEF,cACE,iBACA,+BACA,gBACA;AAEF,YACE;AAEF,YACE,eACA;AAEF,oCACE;AAEF,cACE,eACA;AAEF,2BACE;AAEF,kBACE,sBACA;AAEF,UACE,WACA,WACA,eACA,gBACA,kBACA,kBACA;AAEF,aACE;AAEF,cACE,gBACA;AAEF,uBACE,eACA,cACA;AAEF,qBACE,cAGA,aACA,WACA;AAEF,0BACE;AAEF,YACE,sBACA,UACA;AAKF,oBAHA,iBACE;AAKF,KACE,yBACA,kBACA,cACA,cACA,gBACA;AAEF,MACE,WACA,mBACA,aACA;AAEF,8BACE;AAEF,yCACE;AAEF,2CACE;AAEF,YACA,aACE;AAEF,YACE,+BACA,6BACA,8BACA,4BACA,sBACA,qBACA,kBACA,8BACA,qBACA,cACA,gBACA,sBACA;AAEF,cACE,sBACA,cACA,4BACA,qBACA,kBACA,8BACA;AAEF,aACE,cACA,6BACA,cACA,gBACA;AAEF,uBACE;AAEF,wBACE;AAEF,uBACE;AAGF,YADA,WAEE;AAGF,WADA,aAEE,gBACA;AAGF,SADA,WAEE,gBACA;AAEF,WACE;AAEF,cACE;AAEF,cACE,yBACA;AAEF,iBACE,gBACA;AAEF,eACE,qBACA,eACA,eACA,UACA,uBACA;AAEF,mBACE,WACA;AAEF,YACE,qBACA,YACA,aACA,kBACA;AAEF,cACE,eACA,gBACA;AAEF,0BACE;AAEF,gCACE,iBACA;AAEF,2BACE,UACA;AAGF,SACE;AAEF,UACE;AAEF,SACE,yBACA;AAEF,SACE,yBACA;AAEF,UACE,yBACA;AAEF,WACE,yBACA;AAEF,QACE,yBACA;AAEF,UACE;AAEF,eACE;AAEF,8BACE,yBACA;AAEF,eACE;AAEF,8BACE,yBACA,qBACA;AAEF,YACE;AAEF,2BACE,yBACA,qBACA;AAEF,eACE;AAEF,8BACE,yBACA,qBACA;AAEF,cACE;AAEF,6BACE,yBACA,qBACA;AAEF,cACE;AAEF,gBACA,8BACE;AAGF,eADA,gBAEE;AAEF,eACA,6BACE,WACA;AAEF,yBACE;AAEF,sBACE;AAEF,mBACE;AAEF,sBACE;AAEF,qBACE;AAEF,aACE;AAEF,WACE,kBACA;AAEF,cACE;AAGF,WACE;AAEF,cACE;AAEF,cACE;AAEF,WACE;AAEF,cACE;AAEF,aACE;AAEF,YACE;AAEF,YACE,yBACA,yBACA,kBACA,cACA,eACA,iBACA,eACA,iBACA;AAEF,YACE,qCACA,kCACA;AAiBF,wCAHA,uCACA,uCACA,yCAEA,qCACA,iCACA,mCAjBA,oCACA,oCAIA,sCADA,mCADA,gCADA,2BAIA,oBACA,2BACE;AAYF,oBACE,kBACA,SACA,WACA,aACA;AAEF,0BACE;AAGF,aACE;AAEF,eACE,0BACA,eACA;AAEF,kBACE,yBACA,eACA;AAEF,WACE,gBACA,YACA,YACA;AAEF,gBACE;AAEF,OACE;AAEF,gBACE,aACA;AAGF,OACE;AAEF,MACE;AAEF,MACE;AAEF,KACE;AAEF,MACE;AAEF,MACE;AAEF,MACE;AAEF,OACE;AAEF,MACE;AAEF,MACE;AAEF,GACE;AAEF,MACE;AAEF,MACE;AAEF,MACE;AAEF,KACE;AAEF,UACE;AAEF,QACE;AAEF,QACE;AAEF,KACE;AAEF,QACE;AAEF,QACE;AAEF,QACE;AAEF,WACE;AAEF,UACE;AAEF,UACE;AAEF,OACE;AAEF,UACE;AAEF,UACE;AAEF,UACE;AAEF,UACE;AAEF,SACE;AAEF,QACE;AAEF,QACE;AAEF,KACE;AAEF,QACE;AAEF,QACE;AAEF,QACE;AAEF,WACE;AAEF,UACE;AAEF,UACE;AAEF,OACE;AAEF,UACE;AAEF,UACE;AAEF,UACE;AAEF,UACE;AAEF,SACE;AAEF,QACE;AAEF,QACE;AAEF,KACE;AAEF,QACE;AAEF,QACE;AAEF,QACE;AAEF,WACE;AAEF,UACE;AAEF,UACE;AAEF,OACE;AAEF,UACE;AAEF,UACE;AAEF,UACE;AAEF,UACE;AAEF,SACE;AAEF,QACE;AAEF,QACE;AAEF,KACE;AAEF,QACE;AAEF,QACE;AAEF,QACE;AAEF,WACE;AAEF,UACE;AAEF,UACE;AAEF,OACE;AAEF,UACE;AAEF,UACE;AAEF,UACE;AAEF,UACE;AAEF,UACE;AAEF,UACE;AAEF,UACE;AAEF,gBACE;AAGF,aACE;AAEF,kBACE,gBACA;AAEF,4BACE;AAEF,iBACE,eACA;AAGF,aACE,sBACA,yBACA,aACA;AAEF,eACE;AAGF,sCAGA,wCADA,wCADA,wCAKA,sCAGA,wCADA,wCADA,wCAKA,uCARE;AAWF,2BACE;AAEF,sCACE,6BACA,iBACA;AAGF,YACE,gBACA,YACA,cACA;AAEF,uBACE,YACA;AAEF,wBACE;AAEF,cACE,gBACA,cACA;AAEF,aACE,gBACA,cACA;AAEF,WACE,cACA,gBACA,gBACA,qBACA;AAEF,eACE;AAEF,qBACE;AAEF,WACE,WACA,kBACA,UACA,SACA;AAEF,gBACE,gBACA,gBACA,cACA;AAEF,uBACE;AAGF,kBACE,aACA;AAEF,qBACE,gBACA;AAEF,WACE;AAEF,eACE;AAEF,8CACE;AAEF,wCACE,6BACA,gBACA,eACA,cACA;AAEF,0BACE;AAGF,uBACE,kBACA,MACA,QACA,YACA,WACA,kBACA,6BACA,gCACA,8BACA;AAEF,qBACE,iBACA,YACA,kBACA;AAEF,wBACE,8BACA,6BACA,iBACA;AAEF,8BACE;AAGF,YACA,eACE;AAEF,eACE,gBACA;AAEF,gBAQE;AAEF,sBACE,kBACA,QACA,UACA;AAEF,kBACE,kBACA,WACA,YACA;AAEF,YACE,wBACA;AAEF,kBACE;AAEF,gBAgBE,gCACA,2BACA;AAEF,gCACE,0BACA,+BACA,4BACA,kBACA;AAEF,8BACE,2BACA,gCACA,6BACA,kBACA;AAEF,8BACE,0BACA,+BACA,4BACA,kBACA;AAGF,sBADA,sBAEE,6BACA,0BACA,wBACA,kBACA;AAEF,gBACE,qBACA,WACA,gBACA,cACA,aACA,YACA,YACA,oCACA,iDACA,yCACA,2CACA,uCACA;AAGF,UACE,WACA;AAEF,iBACE;AAEF,cACE,6BACA,SACA;AAEF,kBACE,WACA,cACA;AAEF,gBACE,gCACA;AAEF,kBACE,iBACA;AAEF,oBACE,WACA,cACA;AAEF,kBACE;AAEF,oBACE,iBACA;AAEF,+BACE;AAEF,kCACE;AAEF,+BACE;AAEF,iCACE;AAEF,kCACE;AAEF,2BACE;AAEF,aACE;AAEF,eACE,eACA,yBACA,iBACA,cACA,kBACA,yBACA,iBACA,eACA;AAEF,MACE,yBACA,UACA,sBACA;AAEF,oBACE,wBACA,qBACA,gBACA;AAGF,uDACA,sDAFA,sDAGE;AAGF,uCACA,sCAFA,sCAGE;AAEF,gDACE,eACA,gBACA,kBACA,yBACA,qBACA;AAEF,gCACE,eACA,gBACA,kBACA,yBACA,qBACA;AAEF,8BACE;AAEF,YACA,aACE,aACA;AAEF,YACE,kBACA;AAEF,cACE,cACA,eACA;AAEF,qBACE;AAEF,cACE,eACA;AAEF,iBACE,aACA,yBACA;AAEF,iBACE;AAEF,QACE,kBACA,qBACA,QACA,SACA,cACA,8BACA,gCACA,iCACA,QACA;AAEF,eACE;AAEF,aACE;AAGF,iBACE;AAEF,kCACE;AAEF,0BACE;AAEF,cACE;AAEF,cACA,qBACE;AAEF,cACA,YACE;AAEF,yBACE;AAGF,sCADA,6BAEE,WACA;AAEF,oBACE,yBACA,gBACA,gBACA,kBACA,kBACA,eACA;AAEF,uBACE;AAEF,sBACE;AAEF,YACE,iBACA,kBACA,gBACA,kBACA;AAGF,UACE,sBACA,yBACA,aACA,UACA;AAEF,iBACE,sBACA,yBACA,gBACA;AAEF,oBACE;AAEF,gCACE;AAEF,WACE,6BACA;AAEF,WACE;AAEF,yBACE;AAEF,uBACE;AAEF,sCACE;AAEF,iBACE,6BACA,aACA;AAEF,iBACE,eACA,YACA;AAEF,aACE;AAIF,iBADA,gBAEE,iBACA;AAEF,kBACE,gBACA;AAEF,oBACE,WACA;AAEF,iBACE,eACA,cACA;AAEF,0BACE,gBACA,gCACA,kBACA;AAEF,gCACE,eACA,sBACA,iBACA,cACA,kBACA,yBACA,iBACA,eACA;AAEF,oBACE,eACA,cACA,iBACA;AAGF,UACE,aACA,kBACA;AAEF,cACE,eACA,gBACA,cACA;AAEF,oBACE;AAEF,YACE,gBACA,mBACA,yBACA,kBACA;AAEF,oBACE,mBACA,gBACA,eACA;AAGF,eACE;AAEF,aACE,WACA,YACA,WACA;AAEF,gBACE;AAEF,WACE,iBACA;AAEF,aACE;AAEF,WACE;AAGF,gBADA,YAEE;AAEF,yBAEE,gBADA,YAEE;;AAGJ,yBACE;AAEF,cACE;AAEF,gBACE,YACA,WACA,yBACA,kBACA;AAEF,oDACE,WACA;AAEF,qDACE,YACA;AAEF,SACE,sBACA,yBACA,gBACA,cACA,kBACA,kBACA;AAEF,kDACE;AAEF,mDACE;AAEF,6CACE,gBACA;AAEF,8CACE,iBACA;AAEF,cACE,eACA;AAEF,iBACE;AAEF,iBACE,gBACA,aACA,aACA;AAEF,YACE,gBACA;AAEF,+BACE;AAGF,8CACE;AAEF,kCACE;AAGF,cACE;AAEF,wBACE,kBACA;AAEF,eACE,kBACA;AAEF,0BACE,YACA,sBACA;AAEF,aACE,gBACA;AAEF,mBACE;AAEF,eACE;AAEF,mBACE,WACA,YACA;AAEF,cACE,WACA;AAEF,eACE;AAEF,4CACE;AAEF,+CACE;AAEF,oCAEA,0CADA,0CAEE;AAGF,SACE;AAKF,WAHA,SACE;AAKF,YACE,eACA,gBACA;AAEF,WACE,eACA;AAEF,eACE,kBACA;AAEF,uBACE;AAGF,yBACE;AAEF,gCACE,sBACA;AAEF,YACE;AAEF,cACE,gBACA;AAEF,kBACE;AAGF,cACE;AAEF,6BACE,WACA,YACA;AAGF,eACE;AAEF,YACE,gBACA;AAEF,eACE,mBACA,yBACA,kBACA,aACA;AAEF,qBACE,eACA;AAEF,+BACE;AAEF,8BACE;AAEF,4BACE;AAEF,+BACE;AAEF,cACE,eACA;AAGF,IACE,yBACA;AAEF,IACE;AAGF,aACE,UACA;AAGF,oBADA,mBAEE,6BACA,uCACA,oCACA;AAEF,mBACE,kBACA,eACA,yBACA,cACA;AAEF,sBACE;AAEF,cACE,aACA;AAEF,qBACE;AAEF,oBACE;AAEF,2BACE;AAEF,wBACE;AAEF,cACE,eACA,gBACA,cACA,cACA;AAGF,oBADA,oBAEE;AAEF,eACE,eACA,gBACA,WACA,yBACA,iBACA,kBACA,UACA;AAGF,wCACE;AAEF,sCACE,WACA;AAEF,0CACE,WACA,YACA;AAEF,uDACE,yBACA;AAEF,iBAEE,yBACA,gBACA;AAEF,0BACE,gBACA;AAEF,qCACE,gBACA;AAEF,4BACE,eACA;AAEF,eACE;AAEF,eACE;AAEF,gCACE;AAEF,mBACE,YACA,WACA;AAEF,6BACE,eACA;AAEF,aACE;AAEF,iBACE;AAEF,eACE,6BACA,kBACA;AAEF,mCACE,WACA;AAEF,4BACE;AAEF,gBACE;AAEF,yBACE;AAEF,YACE,eACA,YACA,WACA;AAEF,mBACE,kBACA,SACA;AAEF,iBACE,YACA,WACA,cACA,mBACA,gBACA,kBACA,WACA;AAEF,uBACE,WACA;AAEF,gBACE,aACA,eACA,YACA,WACA,gBACA,yBACA,YACA,aACA;AAEF,8BACE;AAEF,4BACE,YACA;AAEF,uBACE;AAEF,yBACE,mBACA,iBACA,gBACA;AAEF,2BACE,WACA,eACA;AAEF,yBACE;AAEF,sCACE,gBACA,kBACA;AAEF,6BACE;AAEF,uCACE,iBACA,kBACA,eACA,iBACA,cACA,mBACA;AAEF,8CACE,mBACA;AAEF,+BACE,gBACA;AAEF,6CACE;AAEF,gCACE,iBACA;AAEF,8CACE;AAEF,2BACE;AAUF,uBACA,uBACE;AAEF,kBACE,YACA;AAEF,mCACE;AAEF,6CACE;AAEF,4BACE;AAEF,sCACE;AAEF,6BACE;AAEF,uCACE;AAEF,iCACA,iCACE;AAEF,oCACE,gCACA,4BACA;AAEF,2CACE;AAEF,qBACE;AAEF,wBACE;AAEF,uBACE,kBACA,SACA,gBACA,wCACA,gCACA,iCACA,yBACA,8CACA;AAQF,sCACE,WACA,YACA,yBACA,cACA,2DACA;AAEF,kCACE,GACE,iEACA;AAEF,IACE,sEACA;AAEF,KACE,yEACA;;AAGJ,0BACE,GACE,iEACA;AAEF,IACE,sEACA;AAEF,KACE,yEACA;;AAYJ,qCACE,WACA,YACA,kBACA;AAEF,6CACA,6CACE,WACA,YACA,kBACA,yBACA,WACA,kBACA,MACA,OACA,0DACA;AAEF,6CACE,4BACA;AAEF,mCACE,GACA,KACE,2BACA;AAEF,IACE,2BACA;;AAGJ,2BACE,GACA,KACE,2BACA;AAEF,IACE,2BACA;;AAeJ,4BACE,cACA,WACA,YACA,kBACA;AAEF,qBACE,yBACA,YACA,UACA,qBACA,gEACA;AAEF,2BACE,8BACA;AAEF,2BACE,4BACA;AAEF,2BACE,6BACA;AAEF,2BACE,6BACA;AAEF,uCACE,GAEA,KADA,IAEE,6BACA;AAEF,IACE,4BACA;;AAGJ,+BACE,GAEA,KADA,IAEE,6BACA;AAEF,IACE,4BACA;;AAYJ,uCACE,cACA,WACA,YACA;AAEF,sCACA,sCACE,yBACA,WACA,YACA,kBACA,MACA,OACA,iEACA;AAEF,sCACE,6BACA;AAEF,wCACE,IACE,4DACA;AAEF,IAEE,oEACA;AAEF,MACE,oEACA;AAEF,IACE,2EACA;AAEF,KACE,kCACA;;AAGJ,gCACE,IACE,4DACA;AAEF,IAEE,oEACA;AAEF,MACE,oEACA;AAEF,IACE,2EACA;AAEF,KACE,kCACA;;AASJ,6BACE,WACA,YACA,cACA,yBACA,mBACA,2DACA;AAEF,oCACE,GACE,2BACA;AAEF,KACE,2BACA,mBACA;;AAGJ,4BACE,GACE,2BACA;AAEF,KACE,2BACA,mBACA;;AAYJ,oCACE,cACA,WACA,YACA,kBACA,kBACA,0DACA;AAEF,kCACA,kCACE,UACA,WACA,qBACA,kBACA,MACA,yBACA,mBACA,+DACA;AAEF,kCACE,SACA,SACA,4BACA;AAEF,wCACE,KACE,iCACA;;AAGJ,gCACE,KACE,iCACA;;AAGJ,wCACE,GACA,KACE,2BACA;AAEF,IACE,2BACA;;AAGJ,gCACE,GACA,KACE,2BACA;AAEF,IACE,2BACA;;AAaJ,oCACE,cACA,WACA;AAEF,6BACE,WACA,YACA,yBACA,mBACA,qBACA,gEACA,wDAEA,iCACA;AAEF,qCACE,8BACA;AAEF,qCACE,8BACA;AAEF,uCACE,GAEA,KADA,IAEE,2BACA;AAEF,IACE,2BACA;;AAGJ,+BACE,GAEA,KADA,IAEE,2BACA;AAEF,IACE,2BACA;;AAsBJ,8BACE,cACA,WACA,YACA;AAEF,8BACE,WACA,YACA,kBACA,OACA;AAEF,qCACE,WACA,cACA,cACA,UACA,WACA,yBACA,mBACA,iEACA,yDAEA,iCACA;AAEF,+BACE,gCACA,4BACA;AAEF,+BACE,gCACA,4BACA;AAEF,+BACE,gCACA,4BACA;AAEF,+BACE,iCACA,6BACA;AAEF,+BACE,iCACA,6BACA;AAEF,+BACE,iCACA,6BACA;AAEF,+BACE,iCACA,6BACA;AAEF,+BACE,iCACA,6BACA;AAEF,gCACE,iCACA,6BACA;AAEF,gCACE,iCACA,6BACA;AAEF,gCACE,iCACA,6BACA;AAEF,sCACE,8BACA;AAEF,sCACE,4BACA;AAEF,sCACE,6BACA;AAEF,sCACE,6BACA;AAEF,sCACE,6BACA;AAEF,sCACE,6BACA;AAEF,sCACE,6BACA;AAEF,sCACE,6BACA;AAEF,uCACE,6BACA;AAEF,uCACE,6BACA;AAEF,uCACE,6BACA;AAEF,wCACE,GAEA,KADA,IAEE,2BACA;AAEF,IACE,2BACA;;AAGJ,gCACE,GAEA,KADA,IAEE,2BACA;AAEF,IACE,2BACA;;AA2BJ,iCACE,WACA,YACA;AAEF,+BACE,UACA,WACA,yBACA,WACA,kEACA;AAEF,4CACE,4BACA;AAEF,4CACE,4BACA;AAEF,4CACE,4BACA;AAEF,4CACE,4BACA;AAEF,4CACE,4BACA;AAEF,4CACE,4BACA;AAEF,4CACE,2BACA;AAEF,4CACE,4BACA;AAEF,4CACE,4BACA;AAEF,yCACE,GAEA,KADA,IAEE,iCACA;AAEF,IACE,iCACA;;AAGJ,iCACE,GAEA,KADA,IAEE,iCACA;AAEF,IACE,iCACA;;AAWJ,iCACE,yBACA,WACA,YACA,mBACA,kBACA,cACA,oDACA;AAEF,uCACE,cACA,sBACA,UACA,WACA,kBACA,kBACA,QACA;AAEF,kCACE,GACE,4BACA;AAEF,KACE,iCACA;;AAGJ,0BACE,GACE,4BACA;AAEF,KACE,iCACA;;AAsBJ,qCACE,cACA,WACA,YACA;AAEF,qCACE,WACA,YACA,kBACA,OACA;AAEF,4CACE,WACA,cACA,cACA,UACA,WACA,yBACA,mBACA,+DACA,uDAEA,iCACA;AAEF,sCACE,gCACA,4BACA;AAEF,sCACE,gCACA,4BACA;AAEF,sCACE,gCACA,4BACA;AAEF,sCACE,iCACA,6BACA;AAEF,sCACE,iCACA,6BACA;AAEF,sCACE,iCACA,6BACA;AAEF,sCACE,iCACA,6BACA;AAEF,sCACE,iCACA,6BACA;AAEF,uCACE,iCACA,6BACA;AAEF,uCACE,iCACA,6BACA;AAEF,uCACE,iCACA,6BACA;AAEF,6CACE,8BACA;AAEF,6CACE,4BACA;AAEF,6CACE,6BACA;AAEF,6CACE,6BACA;AAEF,6CACE,6BACA;AAEF,6CACE,6BACA;AAEF,6CACE,6BACA;AAEF,6CACE,6BACA;AAEF,8CACE,6BACA;AAEF,8CACE,6BACA;AAEF,8CACE,6BACA;AAEF,sCACE,GAEA,KADA,IAEE;AAEF,IACE;;AAGJ,8BACE,GAEA,KADA,IAEE;AAEF,IACE;;AAGJ,qBACE,yBACA,yBACA;AAGF,wBADA,wBAEE,YACA;AAEF,8BACE,yBACA,YACA,WACA,SACA;AAEF,UACE,oDACA,iDACA;AAEF,SACE;AAEF,SAEE,YACA,eACA,cACA,sBACA,sBACA,gCACA,0BACA,uBACA,kBACA,6CACA,0CACA;AAEF,eACE,iBACA,iBACA;AAEF,6BACE;AAEF,mCACE;AAEF,iBACE;AAEF,qBACE;AAEF,yBACA,qBACE,WACA;AAEF,eACE;AAYF,kCACE,gBACA,eACA,aACA,MACA,OACA,WACA,4BACA,yBACA,uBACA;AAEF,eACE;AAEF,kBACE,cACA,mDACA;AAoBF,wBACE;AAEF,2BACE;AAEF,0BACE,cACA;AAEF,gCACE;AAEF,wBACE,cACA;AAEF,sBACE;AAEF,iBACE,gBACA,eACA;AAEF,yBACE,WACA,WACA,mBACA;AAEF,8BACE,kBACA,MACA,QACA,OACA;AAEF,yCACE,gBACA;AAEF,sCACE,mBACA;AAEF,4CACE,sBACA,kBACA;AAEF,8BACE,6BACA,yBACA;AAEF,wCACE,WACA,mDACA,gBACA,mBACA,yBACA;AAEF,+BACE,iBACA;AAEF,oDACA,0DACE,eACA;AAIF,qDADA,qDAEE,cACA;AAMF,0DACE,eACA;AAEF,wEACE,eACA;AAGF,kDADA,kDAEE,mBACA;AAEF,4CACE,WACA,YACA,cACA,eACA,mBACA,4BACA,0BACA,gBACA;AAEF,sDACE;AAEF,4DACE;AAEF,oDACE,cACA,iBACA,gCACA;AAEF,6BACE,YACA;AAEF,wBACE;AAEF,8CACE;AAEF,oDACA,0DACE;AAEF,gCACE;AAEF,8CACE;AAEF,kDACE,gBACA,kBACA,eACA,aACA;AAEF,2BACE,aACA;AAEF,+BACE;AAEF,+BACE;AAEF,wBACE;AAEF,gCACE;AAEF,8BACE,aACA;AAEF,wCACE,kBACA,MACA,OACA,eACA;AAEF,mDACE,UACA,oCACA;AAEF,qDACE;AAEF,0DACA,2DACE,OACA,UACA;AAEF,wDACA,yDACE;AAEF,+CACE;AAEF,qCACA,sCACE;AAEF,gCACE;AAEF,yCACE;AAEF,8BACE;AAUF,gCACE,kBACA,UACA,OACA,YACA,WACA;AAEF,sCACE;AAEF,8BACE,kBACA,WACA;AAEF,mCACE;AAEF,mCACE,gBACA,eACA,yBACA,iBACA;AAEF,kCACE,gBACA,yBACA;AAEF,4BACE,WACA,iBACA,0BACA;AAEF,kCACE,qBACA;AAEF,wBACE;AAEF,2BACE,eACA,oBACA,gBACA;AAEF,8BAGA,6BAFE;AAKF,8BACE;AAEF,2BACE,eACA;AAEF,mCACE;AAEF,sCACE;AAEF,6BACE,cACA;AAEF,4BACE,gBACA,mBACA,WACA;AAEF,4BACE,mBACA;AAEF,2BACE;AAEF,+BACE;AAEF,6BACE,mBACA,WACA,gBACA,YACA,WACA,cACA;AAEF,mCACE;AAEF,yBACE,YACA;AAEF,4BACE,wBACA;AAEF,qCACE,qBACA;AAEF,+BACE,kBACA,6BACA,kBACA;AAEF,gDACE,gBACA;AAEF,8BACE,eACA,gBACA;AAEF,+BACE,mBACA,WACA,aACA,0BACA,eACA;AAEF,4BACE,iBACA,oBACA,yBACA;AAEF,wBACE;AAEF,kCACE;AAEF,uBACE,kDACA,4BACA,4BACA;AAEF,+BACE;AAEF,+BACE;AAEF,+BACE,gBACA;AAEF,6CACE,gBACA;AAEF,mCACE,WACA,YACA,kBACA;AAEF,sBACE,kBACA,aACA,aACA,gBACA,2BACA,wBACA,mBACA,kBACA;AAEF,4BACE,WACA,kBACA,mBACA,yBACA,8BACA,cACA,QACA,UACA,aACA;AAMF,4CAFA,0CAFA,6CACA,6CAEA,6CAEE;AAEF,2BACE,yBACA,qBACA,WACA,eACA,kBACA;AAKF,kCADA,kCADA,iCADA,iCAIA,iDACE,yBACA,qBACA;AAGF,kCADA,kCAEA,iDACE;AAWF,4CATA,oCAIA,2CADA,2CADA,0CADA,0CAIA,qCAGA,4CADA,2CADA,2CAIA,8CAIA,qDADA,qDADA,oDADA,oDAIE,yBACA;AAEF,yBAME,8BACE;AAEF,yCACE,mBACA;AAEF,sCACE,gBACA,eAGA;AAEF,kCACE,mBACA,eACA;AAEF,kCACE;;AAGJ,yBACE,8BACE;;AAGJ,yBACE,gCACA,sCACE,QACA;AAEF,mCACE;AAEF,kDACE;AAEF,8BACE,sBACA,kBACA;AAEF,0DACE,eACA;AAEF,wCACE;AAEF,8CACE;AAEF,sCACE,cACA,mBACA,gCACA;AAEF,wBACE;AAEF,+BACE;AAEF,oCACE,eACA,kBACA,eACA,aACA;AAEF,sCACE,kBACA;AAEF,oDACA,0DACE;AAEF,gCACE;;AAGJ,yBACE,kCACE;;AAMJ,wBACE;AAEF,iCACE;AAEF,yBACE;AAEF,sBACE;AAEF,qBACE;AAEF,sBAGA,wBAFE;AAKF,wBACE;AAEF,0CAEE;AAEF,6BACE,YACA;AAEF,mCACE,iBACA;AAEF,+BACE;AAEF,iCACE,OACA;AAEF,kCACE,+BACA;AAEF,wBACE,OACA;AAEF,qBACE;AAEF,8BACE;AAEF,yCACE;AAEF,+BACE;AAEF,wDACE,cACA;AAEF,oCACE,UACA,UACA,iBACA;AAEF,oBAGA,sBAFE;AAMF,0BADA,sBAEE,mBACA;AAEF,8BACE;AAEF,eACE,8BACA;AAEF,4BACE,mBACA;AAEF,0BACE;AAEF,4BACE,mBACA;AAEF,iCACE,cACA;AAEF,qBACE,QACA;AAEF,0BACE,kBACA,cAGA;AAEF,wBACE,mBACA;AAEF,yBAEE,0BADA,sBAEE;;AAIJ,0CADA,oCAEE;AAGF,0CADA,kDAEE;AAEF,wCACE,YACA,iBACA;AAEF,+CACE;AAEF,uCACE;AAEF,oCACE;AAEF,kDACE;AAEF,6DACE;AAEF,mEACE;AAEF,mCACE;AAEF,gCACE;AAEF,0CACE;AAEF,wCAKA,wCAJA,oDACA,mDAIA,uCAHE;AAMF,yBACE,0CACE;AAEF,+BACE,iBACA,SACA;AAEF,qCACE,aACA,aACA,kBACA;AAEF,iDACE;AAEF,mDACE,aACA,aACA,eACA;AAEF,+DACE;;AAGJ,mBACE;AAQF,cACE,kBACA,SACA,QACA;AAEF,kBACE,oBACA,kBACA,aACA;AAEF,uBACE;AAEF,WACE,mBACA,kBACA,0BACA,4BACA,eACA,MACA,OACA,WACA,WACA;AAEF,gBACE,YACA,iBACA;AAEF,uBACE,mBACA,kBACA,yBACA,gBACA,cACA,kBACA;AAEF,cACE;AAEF,mBACE;AAEF,sBACE;AAEF,aACE;AAEF,gBACE;AAGF,WADA,cAEA,YACA,aACE;AAEF,cACE,gBACA;AAEF,oBACE;AAEF,WACE,gBACA;AAEF,iBACE;AAEF,aACE,gBACA;AAEF,mBACE;AAEF,YACE,gBACA;AAEF,kBACE;AAQF,2BACE;AAEF,wCACE;AAEF,qCACE;AAEF,mBACE;AAEF,oBACE,mBACA;AAEF,sCACE;AAEF,oBACE;AAEF,sBACE;AAEF,uBACE;AAEF,kBACE;AAEF,yBACE;AAEF,2BACE,mBACA;AAEF,YACE;AAEF,2BACE;AAEF,0BACE,eACA,6BACA,gBACA;AAEF,oCACE;AAEF,yBACE;AAEF,sBACE;AAGF,sBADA,oBAEE;AAEF,iCACE;AAEF,uBACE;AAEF,oBACE;AAEF,sCACE,WACA;AAEF,kDACE,WACA;AAEF,yBACE;AAEF,oBACE;AAGF,wCADA,wCAEE,mBACA;AAEF,kCACE;AAEF,gCACE;AAQF,YACE;AAEF,2BACE;AAEF,wCACE;AAEF,mBACE;AAEF,oBACE,mBACA;AAEF,sCACE;AAEF,oBACE;AAEF,qCACE;AAEF,sBACE,YACA;AAEF,uBACE;AAEF,sBACE;AAEF,kBACA,yBACE;AAGF,8CADA,sCAEE,cACA;AAEF,wBACA,mCACE;AAEF,qBACE;AAEF,2BACE;AAEF,sCACE;AAEF,yBACE;AAEF,gCACE;AAEF,2BACE,mBACA;AAEF,uBACE;AAGF,4BADA,oBAEE;AAEF,qBACE;AAEF,2BACE;AAEF,oCACE,mBACA;AAEF,oBACE;AAEF,0BACE,gCACA,6BACA,gBACA;AAEF,yBACE;AAEF,qCACE;AAEF,oBACE,gCACA,YACA;AAEF,sBACE,gCACA;AAEF,sBACE,mBACA,kFACA,6EACA,8EACA,uEACA;AAGF,sBADA,oBAEE;AAEF,iCACE;AAEF,uBACE;AAEF,sCACE,cACA;AAEF,kDACE,cACA;AAEF,oBACE;AAGF,yCADA,yCAEE;AAGF,wCADA,wCAEE,mBACA;AAEF,kCACE;AAEF,gCACE;AAQF,2BACE;AAEF,wCACE;AAEF,qCACE;AAEF,mBACE;AAEF,kCACE;AAEF,oBACE,mBACA;AAEF,sCACE;AAEF,oBACE;AAEF,sBACE;AAEF,uBACE;AAEF,oCACE;AAEF,kBACE;AAEF,yBACE;AAEF,2BACE,mBACA;AAEF,YACE;AAEF,2BACE;AAEF,0BACE,eACA,6BACA,gBACA;AAEF,yBACE;AAEF,sBACE;AAGF,sBADA,oBAEE;AAEF,iCACE;AAEF,uBACE;AAEF,oBACE;AAEF,sCACE,WACA;AAEF,kDACE,WACA;AAEF,yBACE;AAEF,oBACE;AAGF,wCADA,wCAEE,mBACA;AAEF,kCACE;AAEF,gCACE;AAEF,yBACE,cACE,iBACA,mBACA;AAEF,oBACE,aACA,kBACA;AAIF,mCAFA,qCACA,kCAEE;;AAGJ,yBACE,cACE,iBACA,SACA;AAEF,gCACE,aACA,aACA,kBACA;AAEF,4CACE;AAEF,WAGA,oBAFE;AAKF,eACE,eACA;AAEF,gBACE,UACA,kBACA,WACA;AAEF,0BACE,gBACA;AAEF,8CACE,aACA,aACA,eACA;AAEF,0DACE;AAEF,YACE,WACA,iBACA;;AAGJ,yBACE,qBACE,gBACA,YACA,kBACA;AAEF,uBACE,kBACA,MACA,UACA,YACA,WACA,kBACA,yBACA;AAEF,wBACE,iBACA,6BACA,iBACA;AAEF,kCACE;AAEF,YACE,WACA,gBACA;;AAGJ,WACE,WACA,SACA,YACA;AAEF,sBACE,WACA,SACA;AAEF,uBACE,yBACA;AAEF,qBACE;AAEF,uBACE;AAEF,yBACE,cACA;AAEF,WACE,WACA,kBACA,MACA;AAEF,8CACE;AAEF,SACE;AAEF,mBACE;AAEF,wBACE,WACA,qBACA;AAGF,eADA,gBAEA,gBACA,kBACA,mBACE,mBACA;AAEF,qBACE,SACA,WACA;AAEF,kBACE,WACA;AAEF,iBACE;AAEF,OACE;AAEF,qBACE;AAEF,UACE,aACA;AAEF,iBACE,yBACA,iBACA,kBACA,WACA,cACA,gBACA;AAEF,UACE,mBACA;AASF,cANA,qBACA,kBAFA,iBAGA,OACA,SACE;AAKF,mDACE,gBACA;AAEF,eACE,aACA,YACA;AAEF,kBACE;AAEF,uBACE,aACA;AAEF,yCACE,cACA,WACA,gBACA,yBACA;AAEF,8EACE,qBACA,kBACA;AAEF,kHACE,gBACA;AAEF,6CACE;AAEF,oCACE,YACA;AAEF,MACE,uBACA,cACA,YACA,yBACA,gBACA;AAEF,OACE,yBACA,cACA,cACA,gBACA,mBACA;AAEF,iCACE,SACA", "file": "style.css", "sourcesContent": [null, "/*\n *\n *   INSPINIA - Responsive Admin Theme\n *   version 2.2\n *\n*/\n@import url(\"https://fonts.googleapis.com/css?family=Open+Sans:300,400,600,700&subset=latin,latin-ext\");\nh1,\nh2,\nh3,\nh4,\nh5,\nh6 {\n  font-weight: 100;\n}\nh1 {\n  font-size: 30px;\n}\nh2 {\n  font-size: 24px;\n}\nh3 {\n  font-size: 16px;\n}\nh4 {\n  font-size: 14px;\n}\nh5 {\n  font-size: 12px;\n}\nh6 {\n  font-size: 10px;\n}\nh3,\nh4,\nh5 {\n  margin-top: 5px;\n  font-weight: 600;\n}\n.bold {\n  font-weight: bold;\n}\n.nav > li > a {\n  color: #2b52a4;\n  font-weight: 600;\n  padding: 14px 20px 14px 25px;\n}\n.nav.navbar-right > li > a {\n  color: #999c9e;\n}\n.nav > li.active > a,\n.navbar-default .nav > li > a:focus {\n  color: #ffffff;\n  background: #f87012;\n}\n.navbar-default .nav > li > a:hover {\n  background-color: #ea6407;\n  color: white;\n}\n.nav .open > a,\n.nav .open > a:hover,\n.nav .open > a:focus {\n  background: #fff;\n}\n.nav.navbar-top-links > li > a:hover,\n.nav.navbar-top-links > li > a:focus {\n  background-color: transparent;\n}\n.nav > li > a i {\n  margin-right: 6px;\n}\n.navbar {\n  border: 0;\n}\n.navbar-default {\n  background-color: transparent;\n  border-color: #bdcfe5;\n}\n.navbar-top-links {\n  margin-left: 15px;\n}\n.navbar-top-links li {\n  display: inline-block;\n}\n.navbar-top-links li:last-child {\n  margin-right: 40px;\n}\n.navbar-top-links li a {\n  padding: 20px 10px;\n  min-height: 50px;\n}\n.dropdown-menu {\n  border: medium none;\n  border-radius: 3px;\n  box-shadow: 0 0 3px rgba(86, 96, 117, 0.7);\n  display: none;\n  float: left;\n  font-size: 12px;\n  left: 0;\n  list-style: none outside none;\n  padding: 0;\n  position: absolute;\n  text-shadow: none;\n  top: 100%;\n  z-index: 1000;\n}\n.dropdown-menu > li > a {\n  border-radius: 3px;\n  color: inherit;\n  line-height: 25px;\n  margin: 4px;\n  text-align: left;\n  font-weight: normal;\n}\n.dropdown-menu > li > a.font-bold {\n  font-weight: 600;\n}\n.navbar-top-links .dropdown-menu li {\n  display: block;\n}\n.navbar-top-links .dropdown-menu li:last-child {\n  margin-right: 0;\n}\n.navbar-top-links .dropdown-menu li a {\n  padding: 3px 20px;\n  min-height: 0;\n}\n.navbar-top-links .dropdown-menu li a div {\n  white-space: normal;\n}\n.navbar-top-links .dropdown-messages,\n.navbar-top-links .dropdown-tasks,\n.navbar-top-links .dropdown-alerts {\n  width: 310px;\n  min-width: 0;\n}\n.navbar-top-links .dropdown-messages {\n  margin-left: 5px;\n}\n.navbar-top-links .dropdown-tasks {\n  margin-left: -59px;\n}\n.navbar-top-links .dropdown-alerts {\n  margin-left: -123px;\n}\n.navbar-top-links .dropdown-user {\n  right: 0;\n  left: auto;\n}\n.dropdown-messages,\n.dropdown-alerts {\n  padding: 10px 10px 10px 10px;\n}\n.dropdown-messages li a,\n.dropdown-alerts li a {\n  font-size: 12px;\n}\n.dropdown-messages li em,\n.dropdown-alerts li em {\n  font-size: 10px;\n}\n.nav.navbar-top-links .dropdown-alerts a {\n  font-size: 12px;\n}\n.nav-header {\n  padding: 33px 25px;\n  background: url(\"patterns/header-profile.png\") no-repeat;\n}\n.pace-done .nav-header {\n  transition: all 0.5s;\n}\n.nav > li.active {\n  background: #b2c7e1;\n}\n.nav.nav-second-level > li.active {\n  border: none;\n}\n.nav.nav-second-level.collapse[style] {\n  height: auto !important;\n}\n.nav-header a {\n  color: #DFE4ED;\n}\n.nav-header .text-muted {\n  color: #8095a8;\n}\n.minimalize-styl-2 {\n  padding: 4px 12px;\n  margin: 14px 5px 5px 20px;\n  font-size: 14px;\n  float: left;\n}\n.navbar-form-custom {\n  float: left;\n  height: 50px;\n  padding: 0;\n  width: 200px;\n  display: inline-table;\n}\n.navbar-form-custom .form-group {\n  margin-bottom: 0;\n}\n.navbar-quick-id {\n  width: 75px;\n}\n.nav.navbar-top-links a {\n  font-size: 14px;\n}\n.navbar-form-custom .form-control {\n  background: none repeat scroll 0 0 rgba(0, 0, 0, 0);\n  border: medium none;\n  font-size: 14px;\n  height: 60px;\n  margin: 0;\n  z-index: 2000;\n}\n.count-info .label {\n  line-height: 12px;\n  padding: 2px 5px;\n  position: absolute;\n  right: 6px;\n  top: 12px;\n}\n.arrow {\n  float: right;\n}\n.fa.arrow:before {\n  content: \"\\f104\";\n}\n.active > a > .fa.arrow:before {\n  content: \"\\f107\";\n}\n.nav-second-level li,\n.nav-third-level li {\n  border-bottom: none !important;\n}\n.nav-second-level li a {\n  padding: 7px 10px 7px 10px;\n  padding-left: 52px;\n}\n.nav-third-level li a {\n  padding-left: 62px;\n}\n.nav-second-level li:last-child {\n  margin-bottom: 10px;\n}\nbody:not(.fixed-sidebar):not(.canvas-menu).mini-navbar .nav li:hover > .nav-second-level,\n.mini-navbar .nav li:focus > .nav-second-level {\n  display: block;\n  border-radius: 0 2px 2px 0;\n  min-width: 140px;\n  height: auto;\n}\nbody.mini-navbar .navbar-default .nav > li > .nav-second-level li a {\n  font-size: 12px;\n  border-radius: 0;\n}\n.fixed-nav .slimScrollDiv #side-menu {\n  padding-bottom: 60px;\n}\n.mini-navbar .nav-second-level li a {\n  padding: 10px 10px 10px 15px;\n}\n.mini-navbar .nav-second-level {\n  position: absolute;\n  left: 70px;\n  top: 0px;\n  background-color: #bdcfe5;\n  padding: 10px 10px 10px 10px;\n  font-size: 12px;\n}\n.canvas-menu.mini-navbar .nav-second-level {\n  background: #b2c7e1;\n}\n.mini-navbar li.active .nav-second-level {\n  left: 65px;\n}\n.navbar-default .special_link a {\n  background: #f87012;\n  color: white;\n}\n.navbar-default .special_link a:hover {\n  background: #17987e !important;\n  color: white;\n}\n.navbar-default .special_link a span.label {\n  background: #fff;\n  color: #f87012;\n}\n.navbar-default .landing_link a {\n  background: #f87921;\n  color: white;\n}\n.navbar-default .landing_link a:hover {\n  background: #f87012 !important;\n  color: white;\n}\n.navbar-default .landing_link a span.label {\n  background: #fff;\n  color: #f87921;\n}\n.logo-element {\n  text-align: center;\n  font-size: 18px;\n  font-weight: 600;\n  color: white;\n  display: none;\n  padding: 18px 0;\n}\n.pace-done .navbar-static-side,\n.pace-done .nav-header,\n.pace-done li.active,\n.pace-done #page-wrapper,\n.pace-done .footer {\n  -webkit-transition: all 0.5s;\n  -moz-transition: all 0.5s;\n  -o-transition: all 0.5s;\n  transition: all 0.5s;\n}\n.navbar-fixed-top {\n  background: #fff;\n  transition-duration: 0.5s;\n  z-index: 2030;\n}\n.navbar-fixed-top,\n.navbar-static-top {\n  background: #f3f3f4;\n}\n.fixed-nav #wrapper {\n  margin-top: 0;\n}\nbody.fixed-nav #wrapper .navbar-static-side,\nbody.fixed-nav #wrapper #page-wrapper {\n  margin-top: 60px;\n}\n@media (max-width: 768px) {\n  body.fixed-nav #wrapper .navbar-static-side,\n  body.fixed-nav #wrapper #page-wrapper {\n    margin-top: 110px;\n  }\n}\n.fixed-nav .minimalize-styl-2 {\n  margin: 14px 5px 5px 15px;\n}\n.body-small .navbar-fixed-top {\n  margin-left: 0px;\n}\nbody.mini-navbar .navbar-static-side {\n  width: 70px;\n}\nbody.mini-navbar .profile-element,\nbody.mini-navbar .nav-label,\nbody.mini-navbar .navbar-default .nav li a span {\n  display: none;\n}\nbody.canvas-menu .profile-element {\n  display: block;\n}\nbody:not(.fixed-sidebar):not(.canvas-menu).mini-navbar .nav-second-level {\n  display: none;\n}\nbody.mini-navbar .navbar-default .nav > li > a {\n  font-size: 16px;\n}\nbody.mini-navbar .logo-element {\n  display: block;\n}\nbody.canvas-menu .logo-element {\n  display: none;\n}\nbody.mini-navbar .nav-header {\n  padding: 0;\n  background-color: #f87012;\n}\nbody.canvas-menu .nav-header {\n  padding: 33px 25px;\n}\nbody.mini-navbar #page-wrapper {\n  margin: 0 0 0 70px;\n}\nbody.fixed-sidebar.mini-navbar .footer,\nbody.canvas-menu.mini-navbar .footer {\n  margin: 0 0 0 0 !important;\n}\nbody.canvas-menu.mini-navbar #page-wrapper,\nbody.canvas-menu.mini-navbar .footer {\n  margin: 0 0 0 0;\n}\nbody.fixed-sidebar .navbar-static-side,\nbody.canvas-menu .navbar-static-side {\n  position: fixed;\n  width: 220px;\n  z-index: 2001;\n  height: 100%;\n}\nbody.fixed-sidebar.mini-navbar .navbar-static-side {\n  width: 0px;\n}\nbody.fixed-sidebar.mini-navbar #page-wrapper {\n  margin: 0 0 0 0px;\n}\nbody.body-small.fixed-sidebar.mini-navbar #page-wrapper {\n  margin: 0 0 0 220px;\n}\nbody.body-small.fixed-sidebar.mini-navbar .navbar-static-side {\n  width: 220px;\n}\n.fixed-sidebar.mini-navbar .nav li:focus > .nav-second-level,\n.canvas-menu.mini-navbar .nav li:focus > .nav-second-level {\n  display: block;\n  height: auto;\n}\nbody.fixed-sidebar.mini-navbar .navbar-default .nav > li > .nav-second-level li a {\n  font-size: 12px;\n  border-radius: 0;\n}\nbody.canvas-menu.mini-navbar .navbar-default .nav > li > .nav-second-level li a {\n  font-size: 13px;\n  border-radius: 0;\n}\n.fixed-sidebar.mini-navbar .nav-second-level li a,\n.canvas-menu.mini-navbar .nav-second-level li a {\n  padding: 10px 10px 10px 15px;\n}\n.fixed-sidebar.mini-navbar .nav-second-level,\n.canvas-menu.mini-navbar .nav-second-level {\n  position: relative;\n  padding: 0;\n  font-size: 13px;\n}\n.fixed-sidebar.mini-navbar li.active .nav-second-level,\n.canvas-menu.mini-navbar li.active .nav-second-level {\n  left: 0px;\n}\nbody.fixed-sidebar.mini-navbar .navbar-default .nav > li > a,\nbody.canvas-menu.mini-navbar .navbar-default .nav > li > a {\n  font-size: 13px;\n}\nbody.fixed-sidebar.mini-navbar .nav-label,\nbody.fixed-sidebar.mini-navbar .navbar-default .nav li a span,\nbody.canvas-menu.mini-navbar .nav-label,\nbody.canvas-menu.mini-navbar .navbar-default .nav li a span {\n  display: inline;\n}\nbody.canvas-menu.mini-navbar .navbar-default .nav li .profile-element a span {\n  display: block;\n}\n.canvas-menu.mini-navbar .nav-second-level li a,\n.fixed-sidebar.mini-navbar .nav-second-level li a {\n  padding: 7px 10px 7px 52px;\n}\n.fixed-sidebar.mini-navbar .nav-second-level,\n.canvas-menu.mini-navbar .nav-second-level {\n  left: 0px;\n}\nbody.canvas-menu nav.navbar-static-side {\n  z-index: 2001;\n  background: #2f4050;\n  height: 100%;\n  position: fixed;\n  display: none;\n}\nbody.canvas-menu.mini-navbar nav.navbar-static-side {\n  display: block;\n  width: 220px;\n}\n.nav_tools {\n  padding-bottom: 10px;\n}\n.nav_tools img {\n  display: inline-block;\n  padding-bottom: 2px;\n}\n.top-navigation #page-wrapper {\n  margin-left: 0;\n}\n.top-navigation .navbar-nav .dropdown-menu > .active > a {\n  background: white;\n  color: #f87012;\n  font-weight: bold;\n}\n.white-bg .navbar-fixed-top,\n.white-bg .navbar-static-top {\n  background: #fff;\n}\n.top-navigation .navbar {\n  margin-bottom: 0;\n}\n.top-navigation .nav > li > a {\n  padding: 15px 20px;\n  color: #676a6c;\n}\n.top-navigation .nav > li a:hover,\n.top-navigation .nav > li a:focus {\n  background: #fff;\n  color: #f87012;\n}\n.top-navigation .nav > li.active {\n  background: #fff;\n  border: none;\n}\n.top-navigation .nav > li.active > a {\n  color: #f87012;\n}\n.top-navigation .navbar-right {\n  margin-right: 10px;\n}\n.top-navigation .navbar-nav .dropdown-menu {\n  box-shadow: none;\n  border: 1px solid #e7eaec;\n}\n.top-navigation .dropdown-menu > li > a {\n  margin: 0;\n  padding: 7px 20px;\n}\n.navbar .dropdown-menu {\n  margin-top: 0px;\n}\n.top-navigation .navbar-brand {\n  background: #f87012;\n  color: #fff;\n  padding: 15px 25px;\n}\n.top-navigation .navbar-top-links li:last-child {\n  margin-right: 0;\n}\n.top-navigation.mini-navbar #page-wrapper,\n.top-navigation.body-small.fixed-sidebar.mini-navbar #page-wrapper,\n.mini-navbar .top-navigation #page-wrapper,\n.body-small.fixed-sidebar.mini-navbar .top-navigation #page-wrapper,\n.canvas-menu #page-wrapper {\n  margin: 0;\n}\n.top-navigation.fixed-nav #wrapper,\n.fixed-nav #wrapper.top-navigation {\n  margin-top: 50px;\n}\n.top-navigation .footer.fixed {\n  margin-left: 0 !important;\n}\n.top-navigation .wrapper.wrapper-content {\n  padding: 40px;\n}\n.top-navigation.body-small .wrapper.wrapper-content,\n.body-small .top-navigation .wrapper.wrapper-content {\n  padding: 40px 0px 40px 0px;\n}\n.navbar-toggle {\n  background-color: #f87012;\n  color: #fff;\n  padding: 6px 12px;\n  font-size: 14px;\n}\n.top-navigation .navbar-nav .open .dropdown-menu > li > a,\n.top-navigation .navbar-nav .open .dropdown-menu .dropdown-header {\n  padding: 10px 15px 10px 20px;\n}\n@media (max-width: 768px) {\n  .top-navigation .navbar-header {\n    display: block;\n    float: none;\n  }\n}\n.menu-visible-lg,\n.menu-visible-md {\n  display: none !important;\n}\n@media (min-width: 1200px) {\n  .menu-visible-lg {\n    display: block !important;\n  }\n}\n@media (min-width: 992px) {\n  .menu-visible-md {\n    display: block !important;\n  }\n}\n@media (max-width: 767px) {\n  .menu-visible-md {\n    display: block !important;\n  }\n  .menu-visible-lg {\n    display: block !important;\n  }\n}\n.btn {\n  border-radius: 3px;\n}\n.float-e-margins .btn {\n  margin-bottom: 5px;\n}\n.btn-w-m {\n  min-width: 120px;\n}\n.btn-primary.btn-outline {\n  color: #f87012;\n}\n.btn-success.btn-outline {\n  color: #1c84c6;\n}\n.btn-info.btn-outline {\n  color: #23c6c8;\n}\n.btn-warning.btn-outline {\n  color: #f8ac59;\n}\n.btn-danger.btn-outline {\n  color: #ED5565;\n}\n.btn-primary.btn-outline:hover,\n.btn-success.btn-outline:hover,\n.btn-info.btn-outline:hover,\n.btn-warning.btn-outline:hover,\n.btn-danger.btn-outline:hover {\n  color: #fff;\n}\n.btn-primary {\n  background-color: #f87012;\n  border-color: #f87012;\n  color: #FFFFFF;\n}\n.btn-primary:hover,\n.btn-primary:focus,\n.btn-primary:active,\n.btn-primary.active,\n.open .dropdown-toggle.btn-primary {\n  background-color: #f46807;\n  border-color: #f46807;\n  color: #FFFFFF;\n}\n.btn-primary:active,\n.btn-primary.active,\n.open .dropdown-toggle.btn-primary {\n  background-image: none;\n}\n.btn-primary.disabled,\n.btn-primary.disabled:hover,\n.btn-primary.disabled:focus,\n.btn-primary.disabled:active,\n.btn-primary.disabled.active,\n.btn-primary[disabled],\n.btn-primary[disabled]:hover,\n.btn-primary[disabled]:focus,\n.btn-primary[disabled]:active,\n.btn-primary.active[disabled],\nfieldset[disabled] .btn-primary,\nfieldset[disabled] .btn-primary:hover,\nfieldset[disabled] .btn-primary:focus,\nfieldset[disabled] .btn-primary:active,\nfieldset[disabled] .btn-primary.active {\n  background-color: #f97c26;\n  border-color: #f97c26;\n}\n.btn-success {\n  background-color: #1c84c6;\n  border-color: #1c84c6;\n  color: #FFFFFF;\n}\n.btn-success:hover,\n.btn-success:focus,\n.btn-success:active,\n.btn-success.active,\n.open .dropdown-toggle.btn-success {\n  background-color: #1a7bb9;\n  border-color: #1a7bb9;\n  color: #FFFFFF;\n}\n.btn-success:active,\n.btn-success.active,\n.open .dropdown-toggle.btn-success {\n  background-image: none;\n}\n.btn-success.disabled,\n.btn-success.disabled:hover,\n.btn-success.disabled:focus,\n.btn-success.disabled:active,\n.btn-success.disabled.active,\n.btn-success[disabled],\n.btn-success[disabled]:hover,\n.btn-success[disabled]:focus,\n.btn-success[disabled]:active,\n.btn-success.active[disabled],\nfieldset[disabled] .btn-success,\nfieldset[disabled] .btn-success:hover,\nfieldset[disabled] .btn-success:focus,\nfieldset[disabled] .btn-success:active,\nfieldset[disabled] .btn-success.active {\n  background-color: #1f90d8;\n  border-color: #1f90d8;\n}\n.btn-info {\n  background-color: #23c6c8;\n  border-color: #23c6c8;\n  color: #FFFFFF;\n}\n.btn-info:hover,\n.btn-info:focus,\n.btn-info:active,\n.btn-info.active,\n.open .dropdown-toggle.btn-info {\n  background-color: #21b9bb;\n  border-color: #21b9bb;\n  color: #FFFFFF;\n}\n.btn-info:active,\n.btn-info.active,\n.open .dropdown-toggle.btn-info {\n  background-image: none;\n}\n.btn-info.disabled,\n.btn-info.disabled:hover,\n.btn-info.disabled:focus,\n.btn-info.disabled:active,\n.btn-info.disabled.active,\n.btn-info[disabled],\n.btn-info[disabled]:hover,\n.btn-info[disabled]:focus,\n.btn-info[disabled]:active,\n.btn-info.active[disabled],\nfieldset[disabled] .btn-info,\nfieldset[disabled] .btn-info:hover,\nfieldset[disabled] .btn-info:focus,\nfieldset[disabled] .btn-info:active,\nfieldset[disabled] .btn-info.active {\n  background-color: #26d7d9;\n  border-color: #26d7d9;\n}\n.btn-default {\n  background-color: #c2c2c2;\n  border-color: #c2c2c2;\n  color: #FFFFFF;\n}\n.btn-default:hover,\n.btn-default:focus,\n.btn-default:active,\n.btn-default.active,\n.open .dropdown-toggle.btn-default {\n  background-color: #bababa;\n  border-color: #bababa;\n  color: #FFFFFF;\n}\n.btn-default:active,\n.btn-default.active,\n.open .dropdown-toggle.btn-default {\n  background-image: none;\n}\n.btn-default.disabled,\n.btn-default.disabled:hover,\n.btn-default.disabled:focus,\n.btn-default.disabled:active,\n.btn-default.disabled.active,\n.btn-default[disabled],\n.btn-default[disabled]:hover,\n.btn-default[disabled]:focus,\n.btn-default[disabled]:active,\n.btn-default.active[disabled],\nfieldset[disabled] .btn-default,\nfieldset[disabled] .btn-default:hover,\nfieldset[disabled] .btn-default:focus,\nfieldset[disabled] .btn-default:active,\nfieldset[disabled] .btn-default.active {\n  background-color: #cccccc;\n  border-color: #cccccc;\n}\n.btn-warning {\n  background-color: #f8ac59;\n  border-color: #f8ac59;\n  color: #FFFFFF;\n}\n.btn-warning:hover,\n.btn-warning:focus,\n.btn-warning:active,\n.btn-warning.active,\n.open .dropdown-toggle.btn-warning {\n  background-color: #f7a54a;\n  border-color: #f7a54a;\n  color: #FFFFFF;\n}\n.btn-warning:active,\n.btn-warning.active,\n.open .dropdown-toggle.btn-warning {\n  background-image: none;\n}\n.btn-warning.disabled,\n.btn-warning.disabled:hover,\n.btn-warning.disabled:focus,\n.btn-warning.disabled:active,\n.btn-warning.disabled.active,\n.btn-warning[disabled],\n.btn-warning[disabled]:hover,\n.btn-warning[disabled]:focus,\n.btn-warning[disabled]:active,\n.btn-warning.active[disabled],\nfieldset[disabled] .btn-warning,\nfieldset[disabled] .btn-warning:hover,\nfieldset[disabled] .btn-warning:focus,\nfieldset[disabled] .btn-warning:active,\nfieldset[disabled] .btn-warning.active {\n  background-color: #f9b66d;\n  border-color: #f9b66d;\n}\n.btn-danger {\n  background-color: #ED5565;\n  border-color: #ED5565;\n  color: #FFFFFF;\n}\n.btn-danger:hover,\n.btn-danger:focus,\n.btn-danger:active,\n.btn-danger.active,\n.open .dropdown-toggle.btn-danger {\n  background-color: #ec4758;\n  border-color: #ec4758;\n  color: #FFFFFF;\n}\n.btn-danger:active,\n.btn-danger.active,\n.open .dropdown-toggle.btn-danger {\n  background-image: none;\n}\n.btn-danger.disabled,\n.btn-danger.disabled:hover,\n.btn-danger.disabled:focus,\n.btn-danger.disabled:active,\n.btn-danger.disabled.active,\n.btn-danger[disabled],\n.btn-danger[disabled]:hover,\n.btn-danger[disabled]:focus,\n.btn-danger[disabled]:active,\n.btn-danger.active[disabled],\nfieldset[disabled] .btn-danger,\nfieldset[disabled] .btn-danger:hover,\nfieldset[disabled] .btn-danger:focus,\nfieldset[disabled] .btn-danger:active,\nfieldset[disabled] .btn-danger.active {\n  background-color: #ef6776;\n  border-color: #ef6776;\n}\n.btn-link {\n  color: inherit;\n}\n.btn-link:hover,\n.btn-link:focus,\n.btn-link:active,\n.btn-link.active,\n.open .dropdown-toggle.btn-link {\n  color: #f87012;\n  text-decoration: none;\n}\n.btn-link:active,\n.btn-link.active,\n.open .dropdown-toggle.btn-link {\n  background-image: none;\n}\n.btn-link.disabled,\n.btn-link.disabled:hover,\n.btn-link.disabled:focus,\n.btn-link.disabled:active,\n.btn-link.disabled.active,\n.btn-link[disabled],\n.btn-link[disabled]:hover,\n.btn-link[disabled]:focus,\n.btn-link[disabled]:active,\n.btn-link.active[disabled],\nfieldset[disabled] .btn-link,\nfieldset[disabled] .btn-link:hover,\nfieldset[disabled] .btn-link:focus,\nfieldset[disabled] .btn-link:active,\nfieldset[disabled] .btn-link.active {\n  color: #cacaca;\n}\n.btn-white {\n  color: inherit;\n  background: white;\n  border: 1px solid #dee0e2;\n}\n.btn-white:hover,\n.btn-white:focus,\n.btn-white:active,\n.btn-white.active,\n.open .dropdown-toggle.btn-white {\n  color: inherit;\n  border: 1px solid #d2d2d2;\n}\n.btn-white:active,\n.btn-white.active {\n  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15) inset;\n}\n.btn-white:active,\n.btn-white.active,\n.open .dropdown-toggle.btn-white {\n  background-image: none;\n}\n.btn-white.disabled,\n.btn-white.disabled:hover,\n.btn-white.disabled:focus,\n.btn-white.disabled:active,\n.btn-white.disabled.active,\n.btn-white[disabled],\n.btn-white[disabled]:hover,\n.btn-white[disabled]:focus,\n.btn-white[disabled]:active,\n.btn-white.active[disabled],\nfieldset[disabled] .btn-white,\nfieldset[disabled] .btn-white:hover,\nfieldset[disabled] .btn-white:focus,\nfieldset[disabled] .btn-white:active,\nfieldset[disabled] .btn-white.active {\n  color: #cacaca;\n}\n.form-control,\n.form-control:focus,\n.has-error .form-control:focus,\n.has-success .form-control:focus,\n.has-warning .form-control:focus,\n.navbar-collapse,\n.navbar-form,\n.navbar-form-custom .form-control:focus,\n.navbar-form-custom .form-control:hover,\n.open .btn.dropdown-toggle,\n.panel,\n.popover,\n.progress,\n.progress-bar {\n  box-shadow: none;\n}\n.btn-outline {\n  color: inherit;\n  background-color: transparent;\n  transition: all .5s;\n}\n.btn-rounded {\n  border-radius: 50px;\n}\n.btn-large-dim {\n  width: 90px;\n  height: 90px;\n  font-size: 42px;\n}\nbutton.dim {\n  display: inline-block;\n  color: #fff;\n  text-decoration: none;\n  text-transform: uppercase;\n  text-align: center;\n  padding-top: 6px;\n  margin-right: 10px;\n  position: relative;\n  cursor: pointer;\n  border-radius: 5px;\n  font-weight: 600;\n  margin-bottom: 20px !important;\n}\nbutton.dim:active {\n  top: 3px;\n}\nbutton.btn-primary.dim {\n  box-shadow: inset 0px 0px 0px #e56107, 0px 5px 0px 0px #e56107, 0px 10px 5px #999;\n}\nbutton.btn-primary.dim:active {\n  box-shadow: inset 0px 0px 0px #e56107, 0px 2px 0px 0px #e56107, 0px 5px 3px #999;\n}\nbutton.btn-default.dim {\n  box-shadow: inset 0px 0px 0px #b3b3b3, 0px 5px 0px 0px #b3b3b3, 0px 10px 5px #999;\n}\nbutton.btn-default.dim:active {\n  box-shadow: inset 0px 0px 0px #b3b3b3, 0px 2px 0px 0px #b3b3b3, 0px 5px 3px #999;\n}\nbutton.btn-warning.dim {\n  box-shadow: inset 0px 0px 0px #f79d3c, 0px 5px 0px 0px #f79d3c, 0px 10px 5px #999;\n}\nbutton.btn-warning.dim:active {\n  box-shadow: inset 0px 0px 0px #f79d3c, 0px 2px 0px 0px #f79d3c, 0px 5px 3px #999;\n}\nbutton.btn-info.dim {\n  box-shadow: inset 0px 0px 0px #1eacae, 0px 5px 0px 0px #1eacae, 0px 10px 5px #999;\n}\nbutton.btn-info.dim:active {\n  box-shadow: inset 0px 0px 0px #1eacae, 0px 2px 0px 0px #1eacae, 0px 5px 3px #999;\n}\nbutton.btn-success.dim {\n  box-shadow: inset 0px 0px 0px #1872ab, 0px 5px 0px 0px #1872ab, 0px 10px 5px #999;\n}\nbutton.btn-success.dim:active {\n  box-shadow: inset 0px 0px 0px #1872ab, 0px 2px 0px 0px #1872ab, 0px 5px 3px #999;\n}\nbutton.btn-danger.dim {\n  box-shadow: inset 0px 0px 0px #ea394c, 0px 5px 0px 0px #ea394c, 0px 10px 5px #999;\n}\nbutton.btn-danger.dim:active {\n  box-shadow: inset 0px 0px 0px #ea394c, 0px 2px 0px 0px #ea394c, 0px 5px 3px #999;\n}\nbutton.dim:before {\n  font-size: 50px;\n  line-height: 1em;\n  font-weight: normal;\n  color: #fff;\n  display: block;\n  padding-top: 10px;\n}\nbutton.dim:active:before {\n  top: 7px;\n  font-size: 50px;\n}\na.btn.image-picker__browse,\na.btn .docs-picker__browse {\n  color: #fff;\n  margin-left: 5px;\n  padding: 3px 8px;\n  border-radius: 3px;\n  font-weight: 700;\n}\n.btn.btn--small {\n  font-size: 13px;\n  font-weight: 700;\n  padding: 3px 8px;\n  border-radius: 3px;\n}\n.label {\n  background-color: #D1DADE;\n  color: #5E5E5E;\n  font-family: 'Open Sans';\n  font-size: 10px;\n  font-weight: 600;\n  padding: 3px 8px;\n  text-shadow: none;\n}\n.badge {\n  background-color: #D1DADE;\n  color: #5E5E5E;\n  font-family: 'Open Sans';\n  font-size: 11px;\n  font-weight: 600;\n  padding-bottom: 4px;\n  padding-left: 6px;\n  padding-right: 6px;\n  text-shadow: none;\n}\n.label-primary,\n.badge-primary {\n  background-color: #f87012;\n  color: #FFFFFF;\n}\n.label-success,\n.badge-success {\n  background-color: #1c84c6;\n  color: #FFFFFF;\n}\n.label-warning,\n.badge-warning {\n  background-color: #f8ac59;\n  color: #FFFFFF;\n}\n.label-warning-light,\n.badge-warning-light {\n  background-color: #f8ac59;\n  color: #ffffff;\n}\n.label-danger,\n.badge-danger {\n  background-color: #ED5565;\n  color: #FFFFFF;\n}\n.label-info,\n.badge-info {\n  background-color: #23c6c8;\n  color: #FFFFFF;\n}\n.label-inverse,\n.badge-inverse {\n  background-color: #262626;\n  color: #FFFFFF;\n}\n.label-white,\n.badge-white {\n  background-color: #FFFFFF;\n  color: #5E5E5E;\n}\n.label-white,\n.badge-disable {\n  background-color: #2A2E36;\n  color: #8B91A0;\n}\n/* TOOGLE SWICH */\n.onoffswitch {\n  position: relative;\n  width: 64px;\n  -webkit-user-select: none;\n  -moz-user-select: none;\n  -ms-user-select: none;\n}\n.onoffswitch-checkbox {\n  display: none;\n}\n.onoffswitch-label {\n  display: block;\n  overflow: hidden;\n  cursor: pointer;\n  border: 2px solid #f87012;\n  border-radius: 2px;\n}\n.onoffswitch-inner {\n  width: 200%;\n  margin-left: -100%;\n  -moz-transition: margin 0.3s ease-in 0s;\n  -webkit-transition: margin 0.3s ease-in 0s;\n  -o-transition: margin 0.3s ease-in 0s;\n  transition: margin 0.3s ease-in 0s;\n}\n.onoffswitch-inner:before,\n.onoffswitch-inner:after {\n  float: left;\n  width: 50%;\n  height: 20px;\n  padding: 0;\n  line-height: 20px;\n  font-size: 12px;\n  color: white;\n  font-family: Trebuchet, Arial, sans-serif;\n  font-weight: bold;\n  -moz-box-sizing: border-box;\n  -webkit-box-sizing: border-box;\n  box-sizing: border-box;\n}\n.onoffswitch-inner:before {\n  content: \"ON\";\n  padding-left: 10px;\n  background-color: #f87012;\n  color: #FFFFFF;\n}\n.onoffswitch-inner:after {\n  content: \"OFF\";\n  padding-right: 10px;\n  background-color: #FFFFFF;\n  color: #999999;\n  text-align: right;\n}\n.onoffswitch-switch {\n  width: 20px;\n  margin: 0px;\n  background: #FFFFFF;\n  border: 2px solid #f87012;\n  border-radius: 2px;\n  position: absolute;\n  top: 0;\n  bottom: 0;\n  right: 44px;\n  -moz-transition: all 0.3s ease-in 0s;\n  -webkit-transition: all 0.3s ease-in 0s;\n  -o-transition: all 0.3s ease-in 0s;\n  transition: all 0.3s ease-in 0s;\n}\n.onoffswitch-checkbox:checked + .onoffswitch-label .onoffswitch-inner {\n  margin-left: 0;\n}\n.onoffswitch-checkbox:checked + .onoffswitch-label .onoffswitch-switch {\n  right: 0px;\n}\n/* CHOSEN PLUGIN */\n.chosen-container-single .chosen-single {\n  background: #ffffff;\n  box-shadow: none;\n  -moz-box-sizing: border-box;\n  background-color: #FFFFFF;\n  border: 1px solid #CBD5DD;\n  border-radius: 2px;\n  cursor: text;\n  height: auto !important;\n  margin: 0;\n  min-height: 30px;\n  overflow: hidden;\n  padding: 4px 12px;\n  position: relative;\n  width: 100%;\n}\n.chosen-container-multi .chosen-choices li.search-choice {\n  background: #f1f1f1;\n  border: 1px solid #ededed;\n  border-radius: 2px;\n  box-shadow: none;\n  color: #333333;\n  cursor: default;\n  line-height: 13px;\n  margin: 3px 0 3px 5px;\n  padding: 3px 20px 3px 5px;\n  position: relative;\n}\n/* PAGINATIN */\n.pagination > .active > a,\n.pagination > .active > span,\n.pagination > .active > a:hover,\n.pagination > .active > span:hover,\n.pagination > .active > a:focus,\n.pagination > .active > span:focus {\n  background-color: #f4f4f4;\n  border-color: #DDDDDD;\n  color: inherit;\n  cursor: default;\n  z-index: 2;\n}\n.pagination > li > a,\n.pagination > li > span {\n  background-color: #FFFFFF;\n  border: 1px solid #DDDDDD;\n  color: inherit;\n  float: left;\n  line-height: 1.42857;\n  margin-left: -1px;\n  padding: 4px 10px;\n  position: relative;\n  text-decoration: none;\n}\n/* TOOLTIPS */\n.tooltip-inner {\n  background-color: #2F4050;\n}\n.tooltip.top .tooltip-arrow {\n  border-top-color: #2F4050;\n}\n.tooltip.right .tooltip-arrow {\n  border-right-color: #2F4050;\n}\n.tooltip.bottom .tooltip-arrow {\n  border-bottom-color: #2F4050;\n}\n.tooltip.left .tooltip-arrow {\n  border-left-color: #2F4050;\n}\n/* EASY PIE CHART*/\n.easypiechart {\n  position: relative;\n  text-align: center;\n}\n.easypiechart .h2 {\n  margin-left: 10px;\n  margin-top: 10px;\n  display: inline-block;\n}\n.easypiechart canvas {\n  top: 0;\n  left: 0;\n}\n.easypiechart .easypie-text {\n  line-height: 1;\n  position: absolute;\n  top: 33px;\n  width: 100%;\n  z-index: 1;\n}\n.easypiechart img {\n  margin-top: -4px;\n}\n.jqstooltip {\n  -webkit-box-sizing: content-box;\n  -moz-box-sizing: content-box;\n  box-sizing: content-box;\n}\n/* FULLCALENDAR */\n.fc-state-default {\n  background-color: #ffffff;\n  background-image: none;\n  background-repeat: repeat-x;\n  box-shadow: none;\n  color: #333333;\n  text-shadow: none;\n}\n.fc-state-default {\n  border: 1px solid;\n}\n.fc-button {\n  color: inherit;\n  border: 1px solid #dee0e2;\n  cursor: pointer;\n  display: inline-block;\n  height: 1.9em;\n  line-height: 1.9em;\n  overflow: hidden;\n  padding: 0 0.6em;\n  position: relative;\n  white-space: nowrap;\n}\n.fc-state-active {\n  background-color: #f87012;\n  border-color: #f87012;\n  color: #ffffff;\n}\n.fc-header-title h2 {\n  font-size: 16px;\n  font-weight: 600;\n  color: inherit;\n}\n.fc-content .fc-widget-header,\n.fc-content .fc-widget-content {\n  border-color: #dee0e2;\n  font-weight: normal;\n}\n.fc-border-separate tbody {\n  background-color: #F8F8F8;\n}\n.fc-state-highlight {\n  background: none repeat scroll 0 0 #FCF8E3;\n}\n.external-event {\n  padding: 5px 10px;\n  border-radius: 2px;\n  cursor: pointer;\n  margin-bottom: 5px;\n}\n.fc-ltr .fc-event-hori.fc-event-end,\n.fc-rtl .fc-event-hori.fc-event-start {\n  border-radius: 2px;\n}\n.fc-event,\n.fc-agenda .fc-event-time,\n.fc-event a {\n  padding: 4px 6px;\n  background-color: #f87012;\n  /* background color */\n  border-color: #f87012;\n  /* border color */\n}\n.fc-event-time,\n.fc-event-title {\n  color: #717171;\n  padding: 0 1px;\n}\n.ui-calendar .fc-event-time,\n.ui-calendar .fc-event-title {\n  color: #fff;\n}\n/* Chat */\n.chat-activity-list .chat-element {\n  border-bottom: 1px solid #dee0e2;\n}\n.chat-element:first-child {\n  margin-top: 0;\n}\n.chat-element {\n  padding-bottom: 15px;\n}\n.chat-element,\n.chat-element .media {\n  margin-top: 15px;\n}\n.chat-element,\n.media-body {\n  overflow: hidden;\n}\n.media-body {\n  display: block;\n  width: auto;\n}\n.chat-element > .pull-left {\n  margin-right: 10px;\n}\n.chat-element img.img-circle,\n.dropdown-messages-box img.img-circle {\n  width: 38px;\n  height: 38px;\n}\n.chat-element .well {\n  border: 1px solid #dee0e2;\n  box-shadow: none;\n  margin-top: 10px;\n  margin-bottom: 5px;\n  padding: 10px 20px;\n  font-size: 11px;\n  line-height: 16px;\n}\n.chat-element .actions {\n  margin-top: 10px;\n}\n.chat-element .photos {\n  margin: 10px 0;\n}\n.right.chat-element > .pull-right {\n  margin-left: 10px;\n}\n.chat-photo {\n  max-height: 180px;\n  border-radius: 4px;\n  overflow: hidden;\n  margin-right: 10px;\n  margin-bottom: 10px;\n}\n.chat {\n  margin: 0;\n  padding: 0;\n  list-style: none;\n}\n.chat li {\n  margin-bottom: 10px;\n  padding-bottom: 5px;\n  border-bottom: 1px dotted #B3A9A9;\n}\n.chat li.left .chat-body {\n  margin-left: 60px;\n}\n.chat li.right .chat-body {\n  margin-right: 60px;\n}\n.chat li .chat-body p {\n  margin: 0;\n  color: #777777;\n}\n.panel .slidedown .glyphicon,\n.chat .glyphicon {\n  margin-right: 5px;\n}\n.chat-panel .panel-body {\n  height: 350px;\n  overflow-y: scroll;\n}\n/* LIST GROUP */\na.list-group-item.active,\na.list-group-item.active:hover,\na.list-group-item.active:focus {\n  background-color: #f87012;\n  border-color: #f87012;\n  color: #FFFFFF;\n  z-index: 2;\n}\n.list-group-item-heading {\n  margin-top: 10px;\n}\n.list-group-item-text {\n  margin: 0 0 10px;\n  color: inherit;\n  font-size: 12px;\n  line-height: inherit;\n}\n.no-padding .list-group-item {\n  border-left: none;\n  border-right: none;\n  border-bottom: none;\n}\n.no-padding .list-group-item:first-child {\n  border-left: none;\n  border-right: none;\n  border-bottom: none;\n  border-top: none;\n}\n.no-padding .list-group {\n  margin-bottom: 0;\n}\n.list-group-item {\n  background-color: inherit;\n  border: 1px solid #dee0e2;\n  display: block;\n  margin-bottom: -1px;\n  padding: 10px 15px;\n  position: relative;\n}\n.elements-list .list-group-item {\n  border-left: none;\n  border-right: none;\n  /*border-top: none;*/\n  padding: 15px 25px;\n}\n.elements-list .list-group-item:first-child {\n  border-left: none;\n  border-right: none;\n  border-top: none !important;\n}\n.elements-list .list-group {\n  margin-bottom: 0;\n}\n.elements-list a {\n  color: inherit;\n}\n.elements-list .list-group-item.active,\n.elements-list .list-group-item:hover {\n  background: #f3f3f4;\n  color: inherit;\n  border-color: #e7eaec;\n  /*border-bottom: 1px solid #e7eaec;*/\n  /*border-top: 1px solid #e7eaec;*/\n  border-radius: 0;\n}\n.elements-list li.active {\n  transition: none;\n}\n.element-detail-box {\n  padding: 25px;\n}\n/* FLOT CHART  */\n.flot-chart {\n  display: block;\n  height: 200px;\n}\n.widget .flot-chart.dashboard-chart {\n  display: block;\n  height: 120px;\n  margin-top: 40px;\n}\n.flot-chart.dashboard-chart {\n  display: block;\n  height: 180px;\n  margin-top: 40px;\n}\n.flot-chart-content {\n  width: 100%;\n  height: 100%;\n}\n.flot-chart-pie-content {\n  width: 200px;\n  height: 200px;\n  margin: auto;\n}\n.jqstooltip {\n  position: absolute;\n  display: block;\n  left: 0px;\n  top: 0px;\n  visibility: hidden;\n  background: #2b303a;\n  background-color: rgba(43, 48, 58, 0.8);\n  color: white;\n  text-align: left;\n  white-space: nowrap;\n  z-index: 10000;\n  padding: 5px 5px 5px 5px;\n  min-height: 22px;\n  border-radius: 3px;\n}\n.jqsfield {\n  color: white;\n  text-align: left;\n}\n.h-200 {\n  min-height: 200px;\n}\n.legendLabel {\n  padding-left: 5px;\n}\n.stat-list li:first-child {\n  margin-top: 0;\n}\n.stat-list {\n  list-style: none;\n  padding: 0;\n  margin: 0;\n}\n.stat-percent {\n  float: right;\n}\n.stat-list li {\n  margin-top: 15px;\n  position: relative;\n}\n/* DATATABLES */\ntable.dataTable thead .sorting,\ntable.dataTable thead .sorting_asc:after,\ntable.dataTable thead .sorting_desc,\ntable.dataTable thead .sorting_asc_disabled,\ntable.dataTable thead .sorting_desc_disabled {\n  background: transparent;\n}\ntable.dataTable thead .sorting_asc:after {\n  float: right;\n  font-family: fontawesome;\n}\ntable.dataTable thead .sorting_desc:after {\n  content: \"\\f0dd\";\n  float: right;\n  font-family: fontawesome;\n}\ntable.dataTable thead .sorting:after {\n  content: \"\\f0dc\";\n  float: right;\n  font-family: fontawesome;\n  color: rgba(50, 50, 50, 0.5);\n}\n.dataTables_wrapper {\n  padding-bottom: 30px;\n}\n/* CIRCLE */\n.img-circle {\n  border-radius: 50%;\n}\n.btn-circle {\n  width: 30px;\n  height: 30px;\n  padding: 6px 0;\n  border-radius: 15px;\n  text-align: center;\n  font-size: 12px;\n  line-height: 1.428571429;\n}\n.btn-circle.btn-lg {\n  width: 50px;\n  height: 50px;\n  padding: 10px 16px;\n  border-radius: 25px;\n  font-size: 18px;\n  line-height: 1.33;\n}\n.btn-circle.btn-xl {\n  width: 70px;\n  height: 70px;\n  padding: 10px 16px;\n  border-radius: 35px;\n  font-size: 24px;\n  line-height: 1.33;\n}\n.show-grid [class^=\"col-\"] {\n  padding-top: 10px;\n  padding-bottom: 10px;\n  border: 1px solid #ddd;\n  background-color: #eee !important;\n}\n.show-grid {\n  margin: 15px 0;\n}\n/* ANIMATION */\n.css-animation-box h1 {\n  font-size: 44px;\n}\n.animation-efect-links a {\n  padding: 4px 6px;\n  font-size: 12px;\n}\n#animation_box {\n  background-color: #f9f8f8;\n  border-radius: 16px;\n  width: 80%;\n  margin: 0 auto;\n  padding-top: 80px;\n}\n.animation-text-box {\n  position: absolute;\n  margin-top: 40px;\n  left: 50%;\n  margin-left: -100px;\n  width: 200px;\n}\n.animation-text-info {\n  position: absolute;\n  margin-top: -60px;\n  left: 50%;\n  margin-left: -100px;\n  width: 200px;\n  font-size: 10px;\n}\n.animation-text-box h2 {\n  font-size: 54px;\n  font-weight: 600;\n  margin-bottom: 5px;\n}\n.animation-text-box p {\n  font-size: 12px;\n  text-transform: uppercase;\n}\n/* PEACE */\n.pace {\n  -webkit-pointer-events: none;\n  pointer-events: none;\n  -webkit-user-select: none;\n  -moz-user-select: none;\n  user-select: none;\n}\n.pace-inactive {\n  display: none;\n}\n.pace .pace-progress {\n  background: #f87012;\n  position: fixed;\n  z-index: 2000;\n  top: 0;\n  right: 100%;\n  width: 100%;\n  height: 2px;\n}\n.pace-inactive {\n  display: none;\n}\n/* WIDGETS */\n.widget {\n  border-radius: 5px;\n  padding: 15px 20px;\n  margin-bottom: 10px;\n  margin-top: 10px;\n}\n.widget.style1 h2 {\n  font-size: 30px;\n}\n.widget h2,\n.widget h3 {\n  margin-top: 5px;\n  margin-bottom: 0;\n}\n.widget-text-box {\n  padding: 20px;\n  border: 1px solid #dee0e2;\n  background: #ffffff;\n}\n.widget-head-color-box {\n  border-radius: 5px 5px 0px 0px;\n  margin-top: 10px;\n}\n.widget .flot-chart {\n  height: 100px;\n}\n.vertical-align div {\n  display: inline-block;\n  vertical-align: middle;\n}\n.vertical-align h2,\n.vertical-align h3 {\n  margin: 0;\n}\n.todo-list {\n  list-style: none outside none;\n  margin: 0;\n  padding: 0;\n  font-size: 14px;\n}\n.todo-list.small-list {\n  font-size: 12px;\n}\n.todo-list.small-list > li {\n  background: #f3f3f4;\n  border-left: none;\n  border-right: none;\n  border-radius: 4px;\n  color: inherit;\n  margin-bottom: 2px;\n  padding: 6px 6px 6px 12px;\n}\n.todo-list.small-list .btn-xs,\n.todo-list.small-list .btn-group-xs > .btn {\n  border-radius: 5px;\n  font-size: 10px;\n  line-height: 1.5;\n  padding: 1px 2px 1px 5px;\n}\n.todo-list > li {\n  background: #f3f3f4;\n  border-left: 6px solid #dee0e2;\n  border-right: 6px solid #dee0e2;\n  border-radius: 4px;\n  color: inherit;\n  margin-bottom: 2px;\n  padding: 10px;\n}\n.todo-list .handle {\n  cursor: move;\n  display: inline-block;\n  font-size: 16px;\n  margin: 0 5px;\n}\n.todo-list > li .label {\n  font-size: 9px;\n  margin-left: 10px;\n}\n.check-link {\n  font-size: 16px;\n}\n.todo-completed {\n  text-decoration: line-through;\n}\n.geo-statistic h1 {\n  font-size: 36px;\n  margin-bottom: 0;\n}\n.glyphicon.fa {\n  font-family: \"FontAwesome\";\n}\n/* INPUTS */\n.inline {\n  display: inline-block !important;\n}\n.input-s-sm {\n  width: 120px;\n}\n.input-s {\n  width: 200px;\n}\n.input-s-lg {\n  width: 250px;\n}\n.i-checks {\n  padding-left: 0;\n}\n.form-control,\n.single-line {\n  background-color: #FFFFFF;\n  background-image: none;\n  border: 1px solid #e5e6e7;\n  border-radius: 1px;\n  color: inherit;\n  display: block;\n  padding: 6px 12px;\n  transition: border-color 0.15s ease-in-out 0s, box-shadow 0.15s ease-in-out 0s;\n  width: 100%;\n  font-size: 14px;\n}\n.form-control:focus,\n.single-line:focus {\n  border-color: #f87012 !important;\n}\n.has-success .form-control {\n  border-color: #f87012;\n}\n.has-warning .form-control {\n  border-color: #f8ac59;\n}\n.has-error .form-control {\n  border-color: #ED5565;\n}\n.has-success .control-label {\n  color: #f87012;\n}\n.has-warning .control-label {\n  color: #f8ac59;\n}\n.has-error .control-label {\n  color: #ED5565;\n}\n.input-group-addon {\n  background-color: #fff;\n  border: 1px solid #E5E6E7;\n  border-radius: 1px;\n  color: inherit;\n  font-size: 14px;\n  font-weight: 400;\n  line-height: 1;\n  padding: 6px 12px;\n  text-align: center;\n}\n.spinner-buttons.input-group-btn .btn-xs {\n  line-height: 1.13;\n}\n.spinner-buttons.input-group-btn {\n  width: 20%;\n}\n.noUi-connect {\n  background: none repeat scroll 0 0 #f87012;\n  box-shadow: none;\n}\n.slider_red .noUi-connect {\n  background: none repeat scroll 0 0 #ED5565;\n  box-shadow: none;\n}\n/* UI Sortable */\n.ui-sortable .ibox-title {\n  cursor: move;\n}\n.ui-sortable-placeholder {\n  border: 1px dashed #cecece !important;\n  visibility: visible !important;\n  background: #e7eaec;\n}\n.ibox.ui-sortable-placeholder {\n  margin: 0px 0px 23px !important;\n}\n/* SWITCHES */\n.onoffswitch {\n  position: relative;\n  width: 54px;\n  -webkit-user-select: none;\n  -moz-user-select: none;\n  -ms-user-select: none;\n}\n.onoffswitch-checkbox {\n  display: none;\n}\n.onoffswitch-label {\n  display: block;\n  overflow: hidden;\n  cursor: pointer;\n  border: 2px solid #1AB394;\n  border-radius: 3px;\n}\n.onoffswitch-inner {\n  display: block;\n  width: 200%;\n  margin-left: -100%;\n  -moz-transition: margin 0.3s ease-in 0s;\n  -webkit-transition: margin 0.3s ease-in 0s;\n  -o-transition: margin 0.3s ease-in 0s;\n  transition: margin 0.3s ease-in 0s;\n}\n.onoffswitch-inner:before,\n.onoffswitch-inner:after {\n  display: block;\n  float: left;\n  width: 50%;\n  height: 16px;\n  padding: 0;\n  line-height: 16px;\n  font-size: 10px;\n  color: white;\n  font-family: Trebuchet, Arial, sans-serif;\n  font-weight: bold;\n  -moz-box-sizing: border-box;\n  -webkit-box-sizing: border-box;\n  box-sizing: border-box;\n}\n.onoffswitch-inner:before {\n  content: \"ON\";\n  padding-left: 7px;\n  background-color: #1AB394;\n  color: #FFFFFF;\n}\n.onoffswitch-inner:after {\n  content: \"OFF\";\n  padding-right: 7px;\n  background-color: #FFFFFF;\n  color: #919191;\n  text-align: right;\n}\n.onoffswitch-switch {\n  display: block;\n  width: 18px;\n  margin: 0px;\n  background: #FFFFFF;\n  border: 2px solid #1AB394;\n  border-radius: 3px;\n  position: absolute;\n  top: 0;\n  bottom: 0;\n  right: 36px;\n  -moz-transition: all 0.3s ease-in 0s;\n  -webkit-transition: all 0.3s ease-in 0s;\n  -o-transition: all 0.3s ease-in 0s;\n  transition: all 0.3s ease-in 0s;\n}\n.onoffswitch-checkbox:checked + .onoffswitch-label .onoffswitch-inner {\n  margin-left: 0;\n}\n.onoffswitch-checkbox:checked + .onoffswitch-label .onoffswitch-switch {\n  right: 0px;\n}\n/* jqGrid */\n.ui-jqgrid {\n  -moz-box-sizing: content-box;\n}\n.ui-jqgrid-btable {\n  border-collapse: separate;\n}\n.ui-jqgrid-htable {\n  border-collapse: separate;\n}\n.ui-jqgrid-titlebar {\n  height: 40px;\n  line-height: 15px;\n  color: #676a6c;\n  background-color: #F9F9F9;\n  text-shadow: 0 1px 0 rgba(255, 255, 255, 0.5);\n}\n.ui-jqgrid .ui-jqgrid-title {\n  float: left;\n  margin: 1.1em 1em 0.2em;\n}\n.ui-jqgrid .ui-jqgrid-titlebar {\n  position: relative;\n  border-left: 0px solid;\n  border-right: 0px solid;\n  border-top: 0px solid;\n}\n.ui-widget-header {\n  background: none;\n  background-image: none;\n  background-color: #f5f5f6;\n  text-transform: uppercase;\n  border-top-left-radius: 0px;\n  border-top-right-radius: 0px;\n}\n.ui-jqgrid tr.ui-row-ltr td {\n  border-right-color: inherit;\n  border-right-style: solid;\n  border-right-width: 1px;\n  text-align: left;\n  border-color: #DDDDDD;\n  background-color: inherit;\n}\n.ui-search-toolbar input[type=\"text\"] {\n  font-size: 12px;\n  height: 15px;\n  border: 1px solid #CCCCCC;\n  border-radius: 0px;\n}\n.ui-state-default,\n.ui-widget-content .ui-state-default,\n.ui-widget-header .ui-state-default {\n  background: #F9F9F9;\n  border: 1px solid #DDDDDD;\n  line-height: 15px;\n  font-weight: bold;\n  color: #676a6c;\n  text-shadow: 0 1px 0 rgba(255, 255, 255, 0.5);\n}\n.ui-widget-content {\n  box-sizing: content-box;\n}\n.ui-icon-triangle-1-n {\n  background-position: 1px -16px;\n}\n.ui-jqgrid tr.ui-search-toolbar th {\n  border-top-width: 0px !important;\n  border-top-color: inherit !important;\n  border-top-style: ridge !important;\n}\n.ui-state-hover,\n.ui-widget-content .ui-state-hover,\n.ui-state-focus,\n.ui-widget-content .ui-state-focus,\n.ui-widget-header .ui-state-focus {\n  background: #f5f5f5;\n  border-collapse: separate;\n}\n.ui-state-highlight,\n.ui-widget-content .ui-state-highlight,\n.ui-widget-header .ui-state-highlight {\n  background: #f2fbff;\n}\n.ui-state-active,\n.ui-widget-content .ui-state-active,\n.ui-widget-header .ui-state-active {\n  border: 1px solid #dddddd;\n  background: #ffffff;\n  font-weight: normal;\n  color: #212121;\n}\n.ui-jqgrid .ui-pg-input {\n  font-size: inherit;\n  width: 50px;\n  border: 1px solid #CCCCCC;\n  height: 15px;\n}\n.ui-jqgrid .ui-pg-selbox {\n  display: block;\n  font-size: 1em;\n  height: 25px;\n  line-height: 18px;\n  margin: 0;\n  width: auto;\n}\n.ui-jqgrid .ui-pager-control {\n  position: relative;\n}\n.ui-jqgrid .ui-jqgrid-pager {\n  height: 32px;\n  position: relative;\n}\n.ui-pg-table .navtable .ui-corner-all {\n  border-radius: 0px;\n}\n.ui-jqgrid .ui-pg-button:hover {\n  padding: 1px;\n  border: 0px;\n}\n.ui-jqgrid .loading {\n  position: absolute;\n  top: 45%;\n  left: 45%;\n  width: auto;\n  height: auto;\n  z-index: 101;\n  padding: 6px;\n  margin: 5px;\n  text-align: center;\n  font-weight: bold;\n  display: none;\n  border-width: 2px !important;\n  font-size: 11px;\n}\n.ui-jqgrid .form-control {\n  height: 10px;\n  width: auto;\n  display: inline;\n  padding: 10px 12px;\n}\n.ui-jqgrid-pager {\n  height: 32px;\n}\n.ui-corner-all,\n.ui-corner-top,\n.ui-corner-left,\n.ui-corner-tl {\n  border-top-left-radius: 0;\n}\n.ui-corner-all,\n.ui-corner-top,\n.ui-corner-right,\n.ui-corner-tr {\n  border-top-right-radius: 0;\n}\n.ui-corner-all,\n.ui-corner-bottom,\n.ui-corner-left,\n.ui-corner-bl {\n  border-bottom-left-radius: 0;\n}\n.ui-corner-all,\n.ui-corner-bottom,\n.ui-corner-right,\n.ui-corner-br {\n  border-bottom-right-radius: 0;\n}\n.ui-widget-content {\n  border: 1px solid #ddd;\n}\n.ui-jqgrid .ui-jqgrid-titlebar {\n  padding: 0;\n}\n.ui-jqgrid .ui-jqgrid-titlebar {\n  border-bottom: 1px solid #ddd;\n}\n.ui-jqgrid tr.jqgrow td {\n  padding: 6px;\n}\n.ui-jqdialog .ui-jqdialog-titlebar {\n  padding: 10px 10px;\n}\n.ui-jqdialog .ui-jqdialog-title {\n  float: none !important;\n}\n.ui-jqdialog > .ui-resizable-se {\n  position: absolute;\n}\n/* Nestable list */\n.dd {\n  position: relative;\n  display: block;\n  margin: 0;\n  padding: 0;\n  list-style: none;\n  font-size: 13px;\n  line-height: 20px;\n}\n.dd-list {\n  display: block;\n  position: relative;\n  margin: 0;\n  padding: 0;\n  list-style: none;\n}\n.dd-list .dd-list {\n  padding-left: 30px;\n}\n.dd-collapsed .dd-list {\n  display: none;\n}\n.dd-item,\n.dd-empty,\n.dd-placeholder {\n  display: block;\n  position: relative;\n  margin: 0;\n  padding: 0;\n  min-height: 20px;\n  font-size: 13px;\n  line-height: 20px;\n}\n.dd-handle {\n  display: block;\n  margin: 5px 0;\n  padding: 5px 10px;\n  color: #333;\n  text-decoration: none;\n  border: 1px solid #e7eaec;\n  background: #f5f5f5;\n  -webkit-border-radius: 3px;\n  border-radius: 3px;\n  box-sizing: border-box;\n  -moz-box-sizing: border-box;\n}\n.dd-handle span {\n  font-weight: bold;\n}\n.dd-handle:hover {\n  background: #f0f0f0;\n  cursor: pointer;\n  font-weight: bold;\n}\n.dd-item > button {\n  display: block;\n  position: relative;\n  cursor: pointer;\n  float: left;\n  width: 25px;\n  height: 20px;\n  margin: 5px 0;\n  padding: 0;\n  text-indent: 100%;\n  white-space: nowrap;\n  overflow: hidden;\n  border: 0;\n  background: transparent;\n  font-size: 12px;\n  line-height: 1;\n  text-align: center;\n  font-weight: bold;\n}\n.dd-item > button:before {\n  content: '+';\n  display: block;\n  position: absolute;\n  width: 100%;\n  text-align: center;\n  text-indent: 0;\n}\n.dd-item > button[data-action=\"collapse\"]:before {\n  content: '-';\n}\n#nestable2 .dd-item > button {\n  font-family: FontAwesome;\n  height: 34px;\n  width: 33px;\n  color: #c1c1c1;\n}\n#nestable2 .dd-item > button:before {\n  content: \"\\f067\";\n}\n#nestable2 .dd-item > button[data-action=\"collapse\"]:before {\n  content: \"\\f068\";\n}\n.dd-placeholder,\n.dd-empty {\n  margin: 5px 0;\n  padding: 0;\n  min-height: 30px;\n  background: #f2fbff;\n  border: 1px dashed #b6bcbf;\n  box-sizing: border-box;\n  -moz-box-sizing: border-box;\n}\n.dd-empty {\n  border: 1px dashed #bbb;\n  min-height: 100px;\n  background-color: #e5e5e5;\n  background-image: -webkit-linear-gradient(45deg, #fff 25%, transparent 25%, transparent 75%, #fff 75%, #fff), -webkit-linear-gradient(45deg, #fff 25%, transparent 25%, transparent 75%, #fff 75%, #fff);\n  background-image: -moz-linear-gradient(45deg, #fff 25%, transparent 25%, transparent 75%, #fff 75%, #fff), -moz-linear-gradient(45deg, #fff 25%, transparent 25%, transparent 75%, #fff 75%, #fff);\n  background-image: linear-gradient(45deg, #fff 25%, transparent 25%, transparent 75%, #fff 75%, #fff), linear-gradient(45deg, #fff 25%, transparent 25%, transparent 75%, #fff 75%, #fff);\n  background-size: 60px 60px;\n  background-position: 0 0, 30px 30px;\n}\n.dd-dragel {\n  position: absolute;\n  z-index: 9999;\n  pointer-events: none;\n}\n.dd-dragel > .dd-item .dd-handle {\n  margin-top: 0;\n}\n.dd-dragel .dd-handle {\n  -webkit-box-shadow: 2px 4px 6px 0 rgba(0, 0, 0, 0.1);\n  box-shadow: 2px 4px 6px 0 rgba(0, 0, 0, 0.1);\n}\n/**\n* Nestable Extras\n*/\n.nestable-lists {\n  display: block;\n  clear: both;\n  padding: 30px 0;\n  width: 100%;\n  border: 0;\n  border-top: 2px solid #ddd;\n  border-bottom: 2px solid #ddd;\n}\n#nestable-menu {\n  padding: 0;\n  margin: 10px 0 20px 0;\n}\n#nestable-output,\n#nestable2-output {\n  width: 100%;\n  font-size: 0.75em;\n  line-height: 1.333333em;\n  font-family: open sans, lucida grande, lucida sans unicode, helvetica, arial, sans-serif;\n  padding: 5px;\n  box-sizing: border-box;\n  -moz-box-sizing: border-box;\n}\n#nestable2 .dd-handle {\n  color: inherit;\n  border: 1px dashed #e7eaec;\n  background: #f3f3f4;\n  padding: 10px;\n}\n#nestable2 .dd-handle:hover {\n  /*background: #bbb;*/\n}\n#nestable2 span.label {\n  margin-right: 10px;\n}\n#nestable-output,\n#nestable2-output {\n  font-size: 12px;\n  padding: 25px;\n  box-sizing: border-box;\n  -moz-box-sizing: border-box;\n}\n/* CodeMirror */\n.CodeMirror {\n  border: 1px solid #eee;\n  height: auto;\n}\n.CodeMirror-scroll {\n  overflow-y: hidden;\n  overflow-x: auto;\n}\n/* Google Maps */\n.google-map {\n  height: 300px;\n}\n/* Validation */\nlabel.error {\n  color: #cc5965;\n  display: inline-block;\n  margin-left: 5px;\n}\n.form-control.error {\n  border: 1px dotted #cc5965;\n}\n/* ngGrid */\n.gridStyle {\n  border: 1px solid #d4d4d4;\n  width: 100%;\n  height: 400px;\n}\n.gridStyle2 {\n  border: 1px solid #d4d4d4;\n  width: 500px;\n  height: 300px;\n}\n.ngH eaderCell {\n  border-right: none;\n  border-bottom: 1px solid #e7eaec;\n}\n.ngCell {\n  border-right: none;\n}\n.ngTopPanel {\n  background: #F5F5F6;\n}\n.ngRow.even {\n  background: #f9f9f9;\n}\n.ngRow.selected {\n  background: #EBF2F1;\n}\n.ngRow {\n  border-bottom: 1px solid #e7eaec;\n}\n.ngCell {\n  background-color: transparent;\n}\n.ngHeaderCell {\n  border-right: none;\n}\n/* Toastr custom style */\n#toast-container > .toast {\n  background-image: none !important;\n}\n#toast-container > .toast:before {\n  position: fixed;\n  font-family: FontAwesome;\n  font-size: 24px;\n  line-height: 24px;\n  float: left;\n  color: #FFF;\n  padding-right: 0.5em;\n  margin: auto 0.5em auto -1.5em;\n}\n#toast-container > .toast-warning:before {\n  content: \"\\f0e7\";\n}\n#toast-container > .toast-error:before {\n  content: \"\\f071\";\n}\n#toast-container > .toast-info:before {\n  content: \"\\f005\";\n}\n#toast-container > .toast-success:before {\n  content: \"\\f00C\";\n}\n#toast-container > div {\n  -moz-box-shadow: 0 0 3px #999;\n  -webkit-box-shadow: 0 0 3px #999;\n  box-shadow: 0 0 3px #999;\n  opacity: .9;\n  -ms-filter: alpha(opacity=90);\n  filter: alpha(opacity=90);\n}\n#toast-container > :hover {\n  -moz-box-shadow: 0 0 4px #999;\n  -webkit-box-shadow: 0 0 4px #999;\n  box-shadow: 0 0 4px #999;\n  opacity: 1;\n  -ms-filter: alpha(opacity=100);\n  filter: alpha(opacity=100);\n  cursor: pointer;\n}\n.toast {\n  background-color: #f87012;\n}\n.toast-success {\n  background-color: #f87012;\n}\n.toast-error {\n  background-color: #ED5565;\n}\n.toast-info {\n  background-color: #23c6c8;\n}\n.toast-warning {\n  background-color: #f8ac59;\n}\n.toast-top-full-width {\n  margin-top: 20px;\n}\n.toast-bottom-full-width {\n  margin-bottom: 20px;\n}\n/* Notifie */\n.cg-notify-message.inspinia-notify {\n  background: #fff;\n  padding: 0;\n  box-shadow: 0 0 1px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.2);\n  -webkit-box-shadow: 0 0 1 px rgba(0, 0, 0, 0.1), 0 2 px 4 px rgba(0, 0, 0, 0.2);\n  -moz-box-shadow: 0 0 1 px rgba(0, 0, 0, 0.1), 0 2 px 4 px rgba(0, 0, 0, 0.2);\n  border: none ;\n  margin-top: 30px;\n  color: inherit;\n}\n.inspinia-notify.alert-warning {\n  border-left: 6px solid #f8ac59;\n}\n.inspinia-notify.alert-success {\n  border-left: 6px solid #1c84c6;\n}\n.inspinia-notify.alert-danger {\n  border-left: 6px solid #ED5565;\n}\n.inspinia-notify.alert-info {\n  border-left: 6px solid #f87012;\n}\n/* Image cropper style */\n.img-container,\n.img-preview {\n  overflow: hidden;\n  text-align: center;\n  width: 100%;\n}\n.img-preview-sm {\n  height: 130px;\n  width: 200px;\n}\n/* Forum styles  */\n.forum-post-container .media {\n  margin: 10px 10px 10px 10px;\n  padding: 20px 10px 20px 10px;\n  border-bottom: 1px solid #f1f1f1;\n}\n.forum-avatar {\n  float: left;\n  margin-right: 20px;\n  text-align: center;\n  width: 110px;\n}\n.forum-avatar .img-circle {\n  height: 48px;\n  width: 48px;\n}\n.author-info {\n  color: #676a6c;\n  font-size: 11px;\n  margin-top: 5px;\n  text-align: center;\n}\n.forum-post-info {\n  padding: 9px 12px 6px 12px;\n  background: #f9f9f9;\n  border: 1px solid #f1f1f1;\n}\n.media-body > .media {\n  background: #f9f9f9;\n  border-radius: 3px;\n  border: 1px solid #f1f1f1;\n}\n.forum-post-container .media-body .photos {\n  margin: 10px 0;\n}\n.forum-photo {\n  max-width: 140px;\n  border-radius: 3px;\n}\n.media-body > .media .forum-avatar {\n  width: 70px;\n  margin-right: 10px;\n}\n.media-body > .media .forum-avatar .img-circle {\n  height: 38px;\n  width: 38px;\n}\n.mid-icon {\n  font-size: 66px;\n}\n.forum-item {\n  margin: 10px 0;\n  padding: 10px 0 20px;\n  border-bottom: 1px solid #f1f1f1;\n}\n.views-number {\n  font-size: 24px;\n  line-height: 18px;\n  font-weight: 400;\n}\n.forum-container,\n.forum-post-container {\n  padding: 30px !important;\n}\n.forum-item small {\n  color: #999;\n}\n.forum-item .forum-sub-title {\n  color: #999;\n  margin-left: 50px;\n}\n.forum-title {\n  margin: 15px 0 15px 0;\n}\n.forum-info {\n  text-align: center;\n}\n.forum-desc {\n  color: #999;\n}\n.forum-icon {\n  float: left;\n  width: 30px;\n  margin-right: 20px;\n  text-align: center;\n}\na.forum-item-title {\n  color: inherit;\n  display: block;\n  font-size: 18px;\n  font-weight: 600;\n}\na.forum-item-title:hover {\n  color: inherit;\n}\n.forum-icon .fa {\n  font-size: 30px;\n  margin-top: 8px;\n  color: #9b9b9b;\n}\n.forum-item.active .fa {\n  color: #f87012;\n}\n.forum-item.active a.forum-item-title {\n  color: #f87012;\n}\n@media (max-width: 992px) {\n  .forum-info {\n    margin: 15px 0 10px 0px;\n    /* Comment this is you want to show forum info in small devices */\n    display: none;\n  }\n  .forum-desc {\n    float: none !important;\n  }\n}\n/* New Timeline style */\n.vertical-container {\n  /* this class is used to give a max-width to the element it is applied to, and center it horizontally when it reaches that max-width */\n  width: 90%;\n  max-width: 1170px;\n  margin: 0 auto;\n}\n.vertical-container::after {\n  /* clearfix */\n  content: '';\n  display: table;\n  clear: both;\n}\n#vertical-timeline {\n  position: relative;\n  padding: 0;\n  margin-top: 2em;\n  margin-bottom: 2em;\n}\n#vertical-timeline::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 18px;\n  height: 100%;\n  width: 4px;\n  background: #f1f1f1;\n}\n.vertical-timeline-content .btn {\n  float: right;\n}\n#vertical-timeline.light-timeline:before {\n  background: #e7eaec;\n}\n.dark-timeline .vertical-timeline-content:before {\n  border-color: transparent #f5f5f5 transparent transparent ;\n}\n.dark-timeline.center-orientation .vertical-timeline-content:before {\n  border-color: transparent  transparent transparent #f5f5f5;\n}\n.dark-timeline .vertical-timeline-block:nth-child(2n) .vertical-timeline-content:before,\n.dark-timeline.center-orientation .vertical-timeline-block:nth-child(2n) .vertical-timeline-content:before {\n  border-color: transparent #f5f5f5 transparent transparent;\n}\n.dark-timeline .vertical-timeline-content,\n.dark-timeline.center-orientation .vertical-timeline-content {\n  background: #f5f5f5;\n}\n@media only screen and (min-width: 1170px) {\n  #vertical-timeline.center-orientation {\n    margin-top: 3em;\n    margin-bottom: 3em;\n  }\n  #vertical-timeline.center-orientation:before {\n    left: 50%;\n    margin-left: -2px;\n  }\n}\n@media only screen and (max-width: 1170px) {\n  .center-orientation.dark-timeline .vertical-timeline-content:before {\n    border-color: transparent #f5f5f5 transparent transparent;\n  }\n}\n.vertical-timeline-block {\n  position: relative;\n  margin: 2em 0;\n}\n.vertical-timeline-block:after {\n  content: \"\";\n  display: table;\n  clear: both;\n}\n.vertical-timeline-block:first-child {\n  margin-top: 0;\n}\n.vertical-timeline-block:last-child {\n  margin-bottom: 0;\n}\n@media only screen and (min-width: 1170px) {\n  .center-orientation .vertical-timeline-block {\n    margin: 4em 0;\n  }\n  .center-orientation .vertical-timeline-block:first-child {\n    margin-top: 0;\n  }\n  .center-orientation .vertical-timeline-block:last-child {\n    margin-bottom: 0;\n  }\n}\n.vertical-timeline-icon {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 40px;\n  height: 40px;\n  border-radius: 50%;\n  font-size: 16px;\n  border: 3px solid #f1f1f1;\n  text-align: center;\n}\n.vertical-timeline-icon i {\n  display: block;\n  width: 24px;\n  height: 24px;\n  position: relative;\n  left: 50%;\n  top: 50%;\n  margin-left: -12px;\n  margin-top: -9px;\n}\n@media only screen and (min-width: 1170px) {\n  .center-orientation .vertical-timeline-icon {\n    width: 50px;\n    height: 50px;\n    left: 50%;\n    margin-left: -25px;\n    -webkit-transform: translateZ(0);\n    -webkit-backface-visibility: hidden;\n    font-size: 19px;\n  }\n  .center-orientation .vertical-timeline-icon i {\n    margin-left: -12px;\n    margin-top: -10px;\n  }\n  .center-orientation .cssanimations .vertical-timeline-icon.is-hidden {\n    visibility: hidden;\n  }\n}\n.vertical-timeline-content {\n  position: relative;\n  margin-left: 60px;\n  background: white;\n  border-radius: 0.25em;\n  padding: 1em;\n}\n.vertical-timeline-content:after {\n  content: \"\";\n  display: table;\n  clear: both;\n}\n.vertical-timeline-content h2 {\n  font-weight: 400;\n  margin-top: 4px;\n}\n.vertical-timeline-content p {\n  margin: 1em 0;\n  line-height: 1.6;\n}\n.vertical-timeline-content .vertical-date {\n  float: left;\n  font-weight: 500;\n}\n.vertical-date small {\n  color: #f87012;\n  font-weight: 400;\n}\n.vertical-timeline-content::before {\n  content: '';\n  position: absolute;\n  top: 16px;\n  right: 100%;\n  height: 0;\n  width: 0;\n  border: 7px solid transparent;\n  border-right: 7px solid white;\n}\n@media only screen and (min-width: 768px) {\n  .vertical-timeline-content h2 {\n    font-size: 18px;\n  }\n  .vertical-timeline-content p {\n    font-size: 13px;\n  }\n}\n@media only screen and (min-width: 1170px) {\n  .center-orientation .vertical-timeline-content {\n    margin-left: 0;\n    padding: 1.6em;\n    width: 45%;\n  }\n  .center-orientation .vertical-timeline-content::before {\n    top: 24px;\n    left: 100%;\n    border-color: transparent;\n    border-left-color: white;\n  }\n  .center-orientation .vertical-timeline-content .btn {\n    float: left;\n  }\n  .center-orientation .vertical-timeline-content .vertical-date {\n    position: absolute;\n    width: 100%;\n    left: 122%;\n    top: 2px;\n    font-size: 14px;\n  }\n  .center-orientation .vertical-timeline-block:nth-child(even) .vertical-timeline-content {\n    float: right;\n  }\n  .center-orientation .vertical-timeline-block:nth-child(even) .vertical-timeline-content::before {\n    top: 24px;\n    left: auto;\n    right: 100%;\n    border-color: transparent;\n    border-right-color: white;\n  }\n  .center-orientation .vertical-timeline-block:nth-child(even) .vertical-timeline-content .btn {\n    float: right;\n  }\n  .center-orientation .vertical-timeline-block:nth-child(even) .vertical-timeline-content .vertical-date {\n    left: auto;\n    right: 122%;\n    text-align: right;\n  }\n  .center-orientation .cssanimations .vertical-timeline-content.is-hidden {\n    visibility: hidden;\n  }\n}\n/* Tabs */\n.tabs-container .panel-body {\n  background: #fff;\n  border: 1px solid #dee0e2;\n  border-radius: 2px;\n  padding: 20px;\n  position: relative;\n}\n.tabs-container .nav-tabs > li.active > a,\n.tabs-container .nav-tabs > li.active > a:hover,\n.tabs-container .nav-tabs > li.active > a:focus {\n  border: 1px solid #dee0e2;\n  border-bottom-color: transparent;\n  background-color: #fff;\n}\n.tabs-container .nav-tabs > li {\n  float: left;\n  margin-bottom: -1px;\n}\n.tabs-container .tab-pane .panel-body {\n  border-top: none;\n}\n.tabs-container .nav-tabs > li.active > a,\n.tabs-container .nav-tabs > li.active > a:hover,\n.tabs-container .nav-tabs > li.active > a:focus {\n  border: 1px solid #dee0e2;\n  border-bottom-color: transparent;\n}\n.tabs-container .nav-tabs {\n  border-bottom: 1px solid #dee0e2;\n}\n.tabs-container .tab-pane .panel-body {\n  border-top: none;\n}\n.tabs-container .tabs-left .tab-pane .panel-body,\n.tabs-container .tabs-right .tab-pane .panel-body {\n  border-top: 1px solid #dee0e2;\n}\n.tabs-container .nav-tabs > li a:hover {\n  background: transparent;\n  border-color: transparent;\n}\n.tabs-container .tabs-below > .nav-tabs,\n.tabs-container .tabs-right > .nav-tabs,\n.tabs-container .tabs-left > .nav-tabs {\n  border-bottom: 0;\n}\n.tabs-container .tabs-left .panel-body {\n  position: static;\n}\n.tabs-container .tabs-left > .nav-tabs,\n.tabs-container .tabs-right > .nav-tabs {\n  width: 20%;\n}\n.tabs-container .tabs-left .panel-body {\n  width: 80%;\n  margin-left: 20%;\n}\n.tabs-container .tabs-right .panel-body {\n  width: 80%;\n  margin-right: 20%;\n}\n.tabs-container .tab-content > .tab-pane,\n.tabs-container .pill-content > .pill-pane {\n  display: none;\n}\n.tabs-container .tab-content > .active,\n.tabs-container .pill-content > .active {\n  display: block;\n}\n.tabs-container .tabs-below > .nav-tabs {\n  border-top: 1px solid #dee0e2;\n}\n.tabs-container .tabs-below > .nav-tabs > li {\n  margin-top: -1px;\n  margin-bottom: 0;\n}\n.tabs-container .tabs-below > .nav-tabs > li > a {\n  -webkit-border-radius: 0 0 4px 4px;\n  -moz-border-radius: 0 0 4px 4px;\n  border-radius: 0 0 4px 4px;\n}\n.tabs-container .tabs-below > .nav-tabs > li > a:hover,\n.tabs-container .tabs-below > .nav-tabs > li > a:focus {\n  border-top-color: #dee0e2;\n  border-bottom-color: transparent;\n}\n.tabs-container .tabs-left > .nav-tabs > li,\n.tabs-container .tabs-right > .nav-tabs > li {\n  float: none;\n}\n.tabs-container .tabs-left > .nav-tabs > li > a,\n.tabs-container .tabs-right > .nav-tabs > li > a {\n  min-width: 74px;\n  margin-right: 0;\n  margin-bottom: 3px;\n}\n.tabs-container .tabs-left > .nav-tabs {\n  float: left;\n  margin-right: 19px;\n}\n.tabs-container .tabs-left > .nav-tabs > li > a {\n  margin-right: -1px;\n  -webkit-border-radius: 4px 0 0 4px;\n  -moz-border-radius: 4px 0 0 4px;\n  border-radius: 4px 0 0 4px;\n}\n.tabs-container .tabs-left > .nav-tabs .active > a,\n.tabs-container .tabs-left > .nav-tabs .active > a:hover,\n.tabs-container .tabs-left > .nav-tabs .active > a:focus {\n  border-color: #dee0e2 transparent #dee0e2 #dee0e2;\n  *border-right-color: #ffffff;\n}\n.tabs-container .tabs-right > .nav-tabs {\n  float: right;\n  margin-left: 19px;\n}\n.tabs-container .tabs-right > .nav-tabs > li > a {\n  margin-left: -1px;\n  -webkit-border-radius: 0 4px 4px 0;\n  -moz-border-radius: 0 4px 4px 0;\n  border-radius: 0 4px 4px 0;\n}\n.tabs-container .tabs-right > .nav-tabs .active > a,\n.tabs-container .tabs-right > .nav-tabs .active > a:hover,\n.tabs-container .tabs-right > .nav-tabs .active > a:focus {\n  border-color: #dee0e2 #dee0e2 #dee0e2 transparent;\n  *border-left-color: #ffffff;\n  z-index: 1;\n}\n/* jsvectormap */\n.jvectormap-container {\n  width: 100%;\n  height: 100%;\n  position: relative;\n  overflow: hidden;\n}\n.jvectormap-tip {\n  position: absolute;\n  display: none;\n  border: solid 1px #CDCDCD;\n  border-radius: 3px;\n  background: #292929;\n  color: white;\n  font-family: sans-serif, Verdana;\n  font-size: smaller;\n  padding: 5px;\n}\n.jvectormap-zoomin,\n.jvectormap-zoomout,\n.jvectormap-goback {\n  position: absolute;\n  left: 10px;\n  border-radius: 3px;\n  background: #f87012;\n  padding: 3px;\n  color: white;\n  cursor: pointer;\n  line-height: 10px;\n  text-align: center;\n  box-sizing: content-box;\n}\n.jvectormap-zoomin,\n.jvectormap-zoomout {\n  width: 10px;\n  height: 10px;\n}\n.jvectormap-zoomin {\n  top: 10px;\n}\n.jvectormap-zoomout {\n  top: 30px;\n}\n.jvectormap-goback {\n  bottom: 10px;\n  z-index: 1000;\n  padding: 6px;\n}\n.jvectormap-spinner {\n  position: absolute;\n  left: 0;\n  top: 0;\n  right: 0;\n  bottom: 0;\n  background: center no-repeat url(data:image/gif;base64,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);\n}\n.jvectormap-legend-title {\n  font-weight: bold;\n  font-size: 14px;\n  text-align: center;\n}\n.jvectormap-legend-cnt {\n  position: absolute;\n}\n.jvectormap-legend-cnt-h {\n  bottom: 0;\n  right: 0;\n}\n.jvectormap-legend-cnt-v {\n  top: 0;\n  right: 0;\n}\n.jvectormap-legend {\n  background: black;\n  color: white;\n  border-radius: 3px;\n}\n.jvectormap-legend-cnt-h .jvectormap-legend {\n  float: left;\n  margin: 0 10px 10px 0;\n  padding: 3px 3px 1px 3px;\n}\n.jvectormap-legend-cnt-h .jvectormap-legend .jvectormap-legend-tick {\n  float: left;\n}\n.jvectormap-legend-cnt-v .jvectormap-legend {\n  margin: 10px 10px 0 0;\n  padding: 3px;\n}\n.jvectormap-legend-cnt-h .jvectormap-legend-tick {\n  width: 40px;\n}\n.jvectormap-legend-cnt-h .jvectormap-legend-tick-sample {\n  height: 15px;\n}\n.jvectormap-legend-cnt-v .jvectormap-legend-tick-sample {\n  height: 20px;\n  width: 20px;\n  display: inline-block;\n  vertical-align: middle;\n}\n.jvectormap-legend-tick-text {\n  font-size: 12px;\n}\n.jvectormap-legend-cnt-h .jvectormap-legend-tick-text {\n  text-align: center;\n}\n.jvectormap-legend-cnt-v .jvectormap-legend-tick-text {\n  display: inline-block;\n  vertical-align: middle;\n  line-height: 20px;\n  padding-left: 3px;\n}\n.sidebard-panel {\n  width: 220px;\n  background: #ebebed;\n  padding: 10px 20px;\n  position: absolute;\n  right: 0;\n}\n.sidebard-panel .feed-element img.img-circle {\n  width: 32px;\n  height: 32px;\n}\n.sidebard-panel .feed-element,\n.media-body,\n.sidebard-panel p {\n  font-size: 12px;\n}\n.sidebard-panel .feed-element {\n  margin-top: 20px;\n  padding-bottom: 0;\n}\n.sidebard-panel .list-group {\n  margin-bottom: 10px;\n}\n.sidebard-panel .list-group .list-group-item {\n  padding: 5px 0;\n  font-size: 12px;\n  border: 0;\n}\n.sidebar-content .wrapper,\n.wrapper.sidebar-content {\n  padding-right: 230px !important;\n}\n.body-small .sidebar-content .wrapper,\n.body-small .wrapper.sidebar-content {\n  padding-right: 20px !important;\n}\n#right-sidebar {\n  background-color: #fff;\n  border-left: 1px solid #e7eaec;\n  border-top: 1px solid #e7eaec;\n  overflow: hidden;\n  position: fixed;\n  top: 60px;\n  width: 260px !important;\n  z-index: 1009;\n  bottom: 0;\n  right: -260px;\n}\n#right-sidebar.sidebar-open {\n  right: 0;\n}\n#right-sidebar.sidebar-open.sidebar-top {\n  top: 0;\n  border-top: none;\n}\n.sidebar-container ul.nav-tabs {\n  border: none;\n}\n.sidebar-container ul.nav-tabs.navs-4 li {\n  width: 25%;\n}\n.sidebar-container ul.nav-tabs.navs-3 li {\n  width: 33.3333%;\n}\n.sidebar-container ul.nav-tabs.navs-2 li {\n  width: 50%;\n}\n.sidebar-container ul.nav-tabs li {\n  border: none;\n}\n.sidebar-container ul.nav-tabs li a {\n  border: none;\n  padding: 12px 10px;\n  margin: 0;\n  border-radius: 0;\n  background: #bdcfe5;\n  color: #fff;\n  text-align: center;\n  border-right: 1px solid #c4d4e8;\n}\n.sidebar-container ul.nav-tabs li.active a {\n  border: none;\n  background: #f9f9f9;\n  color: #737578;\n  font-weight: bold;\n}\n.sidebar-container .nav-tabs > li.active > a:hover,\n.sidebar-container .nav-tabs > li.active > a:focus {\n  border: none;\n}\n.sidebar-container ul.sidebar-list {\n  margin: 0;\n  padding: 0;\n}\n.sidebar-container ul.sidebar-list li {\n  border-bottom: 1px solid #dee0e2;\n  padding: 15px 20px;\n  list-style: none;\n  font-size: 12px;\n}\n.sidebar-container .sidebar-message:nth-child(2n+2) {\n  background: #f9f9f9;\n}\n.sidebar-container ul.sidebar-list li a {\n  text-decoration: none;\n  color: inherit;\n}\n.sidebar-container .sidebar-content {\n  padding: 15px 20px ;\n  font-size: 12px;\n}\n.sidebar-container .sidebar-title {\n  background: #f9f9f9;\n  padding: 20px;\n  border-bottom: 1px solid #dee0e2;\n}\n.sidebar-container .sidebar-title h3 {\n  margin-bottom: 3px;\n  padding-left: 2px;\n}\n.sidebar-container .tab-content h4 {\n  margin-bottom: 5px;\n}\n.sidebar-container .sidebar-message > a > .pull-left {\n  margin-right: 10px;\n}\n.sidebar-container .sidebar-message > a {\n  text-decoration: none;\n  color: inherit;\n}\n.sidebar-container .sidebar-message {\n  padding: 15px 20px;\n}\n.sidebar-container .sidebar-message .message-avatar {\n  height: 38px;\n  width: 38px;\n  border-radius: 50%;\n}\n.sidebar-container .setings-item {\n  padding: 15px 20px;\n  border-bottom: 1px solid #dee0e2;\n}\nbody {\n  font-family: \"open sans\", \"Helvetica Neue\", Helvetica, Arial, sans-serif;\n  background-color: #ffffff;\n  font-size: 13px;\n  color: #737578;\n  overflow-x: hidden;\n}\nhtml,\nbody {\n  height: 100%;\n}\nbody.full-height-layout #wrapper,\nbody.full-height-layout #page-wrapper {\n  height: 100%;\n}\n#page-wrapper {\n  min-height: auto;\n}\nbody.boxed-layout {\n  background: url('patterns/shattered.png');\n}\nbody.boxed-layout #wrapper {\n  background-color: #2f4050;\n  max-width: 1200px;\n  margin: 0 auto;\n  -webkit-box-shadow: 0px 0px 5px 0px rgba(0, 0, 0, 0.75);\n  -moz-box-shadow: 0px 0px 5px 0px rgba(0, 0, 0, 0.75);\n  box-shadow: 0px 0px 5px 0px rgba(0, 0, 0, 0.75);\n}\n.top-navigation.boxed-layout #wrapper,\n.boxed-layout #wrapper.top-navigation {\n  max-width: 1300px !important;\n}\n.block {\n  display: block;\n}\n.clear {\n  display: block;\n  overflow: hidden;\n}\na {\n  cursor: pointer;\n}\na:hover,\na:focus {\n  text-decoration: none;\n}\n.border-bottom {\n  border-bottom: 1px solid #dee0e2 !important;\n}\n.font-bold {\n  font-weight: 600;\n}\n.font-noraml {\n  font-weight: 400;\n}\n.text-uppercase {\n  text-transform: uppercase;\n}\n.b-r {\n  border-right: 1px solid #dee0e2;\n}\n.hr-line-dashed {\n  border-top: 1px dashed #dee0e2;\n  color: #ffffff;\n  background-color: #ffffff;\n  height: 1px;\n  margin: 20px 0;\n}\n.hr-line-solid {\n  border-bottom: 1px solid #dee0e2;\n  background-color: rgba(0, 0, 0, 0);\n  border-style: solid !important;\n  margin-top: 15px;\n  margin-bottom: 15px;\n}\nvideo {\n  width: 100%    !important;\n  height: auto   !important;\n}\n/* GALLERY */\n.gallery > .row > div {\n  margin-bottom: 15px;\n}\n.fancybox img {\n  margin-bottom: 5px;\n  /* Only for demo */\n  width: 24%;\n}\n/* Summernote text editor  */\n.note-editor {\n  height: auto;\n  min-height: 300px;\n}\n/* MODAL */\n.modal-content {\n  background-clip: padding-box;\n  background-color: #FFFFFF;\n  border: 1px solid rgba(0, 0, 0, 0);\n  border-radius: 4px;\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);\n  outline: 0 none;\n  position: relative;\n}\n.modal-dialog {\n  z-index: 2200;\n}\n.modal-body {\n  padding: 20px 30px 30px 30px;\n}\n.inmodal .modal-body {\n  background: #f8fafb;\n}\n.inmodal .modal-header {\n  padding: 30px 15px;\n  text-align: center;\n}\n.animated.modal.fade .modal-dialog {\n  -webkit-transform: none;\n  -ms-transform: none;\n  -o-transform: none;\n  transform: none;\n}\n.inmodal .modal-title {\n  font-size: 26px;\n}\n.inmodal .modal-icon {\n  font-size: 84px;\n  color: #e2e3e3;\n}\n.modal-footer {\n  margin-top: 0;\n}\n/* WRAPPERS */\n#wrapper {\n  width: 100%;\n  overflow-x: hidden;\n}\n.wrapper {\n  padding: 0 20px;\n}\n.wrapper-content {\n  padding: 20px 10px 40px;\n}\n#page-wrapper {\n  padding: 0 15px;\n  min-height: 568px;\n  position: relative !important;\n}\n@media (min-width: 768px) {\n  #page-wrapper {\n    position: inherit;\n    margin: 0 0 0 240px;\n    min-height: 1000px;\n  }\n}\n.title-action {\n  text-align: right;\n  padding-top: 30px;\n}\n.ibox-content h1,\n.ibox-content h2,\n.ibox-content h3,\n.ibox-content h4,\n.ibox-content h5,\n.ibox-title h1,\n.ibox-title h2,\n.ibox-title h3,\n.ibox-title h4,\n.ibox-title h5 {\n  margin-top: 5px;\n}\nul.unstyled,\nol.unstyled {\n  list-style: none outside none;\n  margin-left: 0;\n}\n.big-icon {\n  font-size: 160px !important;\n  color: #e5e6e7;\n}\n/* FOOTER */\n.footer {\n  background: none repeat scroll 0 0 white;\n  border-top: 1px solid #dee0e2;\n  bottom: 0;\n  left: 0;\n  padding: 10px 20px;\n  position: absolute;\n  right: 0;\n}\n.footer.fixed_full {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  z-index: 1000;\n  padding: 10px 20px;\n  background: white;\n  border-top: 1px solid #dee0e2;\n}\n.footer.fixed {\n  position: fixed;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  z-index: 1000;\n  padding: 10px 20px;\n  background: white;\n  border-top: 1px solid #dee0e2;\n  margin-left: 220px;\n}\nbody.mini-navbar .footer.fixed,\nbody.body-small.mini-navbar .footer.fixed {\n  margin: 0 0 0 70px;\n}\nbody.mini-navbar.canvas-menu .footer.fixed,\nbody.canvas-menu .footer.fixed {\n  margin: 0 !important;\n}\nbody.fixed-sidebar.body-small.mini-navbar .footer.fixed {\n  margin: 0 0 0 220px;\n}\nbody.body-small .footer.fixed {\n  margin-left: 0px;\n}\n/* PANELS */\n.page-heading {\n  border-top: 0;\n  padding: 0px 10px 14px 10px;\n}\n.panel-heading h1,\n.panel-heading h2 {\n  margin-bottom: 5px;\n}\n/* TABLES */\n.table-bordered {\n  border: 0;\n}\n.table-bordered > thead > tr > th,\n.table-bordered > thead > tr > td {\n  background-color: #F5F5F6;\n  background-color: #2B528A;\n  color: #ffffff;\n  border-bottom-width: 1px;\n}\n.table-bordered > thead > tr > th,\n.table-bordered > tbody > tr > th,\n.table-bordered > tfoot > tr > th,\n.table-bordered > thead > tr > td,\n.table-bordered > tbody > tr > td,\n.table-bordered > tfoot > tr > td {\n  border: 0;\n}\n.table > thead > tr > th {\n  border-bottom: 1px solid #2B528A;\n  vertical-align: bottom;\n}\n.table > thead > tr > th,\n.table > tbody > tr > th,\n.table > tfoot > tr > th,\n.table > thead > tr > td,\n.table > tbody > tr > td,\n.table > tfoot > tr > td {\n  border-top: 1px solid #dee0e2;\n  line-height: 1.42857;\n  padding: 8px;\n  vertical-align: top;\n}\n/* PANELS */\n.panel.blank-panel {\n  background: none;\n  margin: 0;\n}\n.blank-panel .panel-heading {\n  padding-bottom: 0;\n}\n.nav-tabs > li.active > a,\n.nav-tabs > li.active > a:hover,\n.nav-tabs > li.active > a:focus {\n  -moz-border-bottom-colors: none;\n  -moz-border-left-colors: none;\n  -moz-border-right-colors: none;\n  -moz-border-top-colors: none;\n  background: none;\n  border-color: #DDDDDD #DDDDDD rgba(0, 0, 0, 0);\n  border-bottom: #f3f3f4;\n  border-image: none;\n  border-style: solid;\n  border-width: 1px;\n  color: #555555;\n  cursor: default;\n}\n.nav.nav-tabs li {\n  background: none;\n  border: none;\n}\n.nav-tabs > li > a {\n  color: #A7B1C2;\n  font-weight: 600;\n  padding: 10px 20px 10px 25px;\n}\n.nav-tabs > li > a:hover,\n.nav-tabs > li > a:focus {\n  background-color: #e6e6e6;\n  color: #737578;\n}\n.ui-tab .tab-content {\n  padding: 20px 0px;\n}\n/* GLOBAL  */\n.no-padding {\n  padding: 0 !important;\n}\n.no-borders {\n  border: none !important;\n}\n.no-margins {\n  margin: 0 !important;\n}\n.no-top-border {\n  border-top: 0 !important;\n}\n.ibox-content.text-box {\n  padding-bottom: 0px;\n  padding-top: 15px;\n}\n.border-left-right {\n  border-left: 1px solid #dee0e2;\n  border-right: 1px solid #dee0e2;\n  border-top: none;\n  border-bottom: none;\n}\n.border-left {\n  border-left: 1px solid #dee0e2;\n  border-right: none;\n  border-top: none;\n  border-bottom: none;\n}\n.border-right {\n  border-left: none;\n  border-right: 1px solid #dee0e2;\n  border-top: none;\n  border-bottom: none;\n}\n.full-width {\n  width: 100% !important;\n}\n.link-block {\n  font-size: 12px;\n  padding: 10px;\n}\n.nav.navbar-top-links .link-block a {\n  font-size: 12px;\n}\n.link-block a {\n  font-size: 10px;\n  color: inherit;\n}\nbody.mini-navbar .branding {\n  display: none;\n}\nimg.circle-border {\n  border: 6px solid #FFFFFF;\n  border-radius: 50%;\n}\n.branding {\n  float: left;\n  color: #FFFFFF;\n  font-size: 18px;\n  font-weight: 600;\n  padding: 17px 20px;\n  text-align: center;\n  background-color: #f87012;\n}\n.login-panel {\n  margin-top: 25%;\n}\n.icons-box h3 {\n  margin-top: 10px;\n  margin-bottom: 10px;\n}\n.icons-box .infont a i {\n  font-size: 25px;\n  display: block;\n  color: #737578;\n}\n.icons-box .infont a {\n  color: #a6a8a9;\n}\n.icons-box .infont a {\n  padding: 10px;\n  margin: 1px;\n  display: block;\n}\n.ui-draggable .ibox-title {\n  cursor: move;\n}\n.breadcrumb {\n  background-color: #ffffff;\n  padding: 0;\n  margin-bottom: 0;\n}\n.breadcrumb > li a {\n  color: inherit;\n}\n.breadcrumb > .active {\n  color: inherit;\n}\ncode {\n  background-color: #F9F2F4;\n  border-radius: 4px;\n  color: #ca4440;\n  font-size: 90%;\n  padding: 2px 4px;\n  white-space: nowrap;\n}\n.ibox {\n  clear: both;\n  margin-bottom: 25px;\n  margin-top: 0;\n  padding: 0;\n}\n.ibox.collapsed .ibox-content {\n  display: none;\n}\n.ibox.collapsed .fa.fa-chevron-up:before {\n  content: \"\\f078\";\n}\n.ibox.collapsed .fa.fa-chevron-down:before {\n  content: \"\\f077\";\n}\n.ibox:after,\n.ibox:before {\n  display: table;\n}\n.ibox-title {\n  -moz-border-bottom-colors: none;\n  -moz-border-left-colors: none;\n  -moz-border-right-colors: none;\n  -moz-border-top-colors: none;\n  background-color: #ffffff;\n  border-color: #dee0e2;\n  border-image: none;\n  border-style: solid solid none;\n  border-width: 2px 0px 0;\n  color: inherit;\n  margin-bottom: 0;\n  padding: 14px 15px 7px;\n  min-height: 48px;\n}\n.ibox-content {\n  background-color: #ffffff;\n  color: inherit;\n  padding: 15px 20px 20px 20px;\n  border-color: #dee0e2;\n  border-image: none;\n  border-style: solid solid none;\n  border-width: 1px 0px;\n}\n.ibox-footer {\n  color: inherit;\n  border-top: 1px solid #dee0e2;\n  font-size: 90%;\n  background: #ffffff;\n  padding: 10px 15px;\n}\ntable.table-mail tr td {\n  padding: 12px;\n}\n.table-mail .check-mail {\n  padding-left: 20px;\n}\n.table-mail .mail-date {\n  padding-right: 20px;\n}\n.star-mail,\n.check-mail {\n  width: 40px;\n}\n.unread td a,\n.unread td {\n  font-weight: 600;\n  color: inherit;\n}\n.read td a,\n.read td {\n  font-weight: normal;\n  color: inherit;\n}\n.unread td {\n  background-color: #f9f8f8;\n}\n.ibox-content {\n  clear: both;\n}\n.ibox-heading {\n  background-color: #f3f6fb;\n  border-bottom: none;\n}\n.ibox-heading h3 {\n  font-weight: 200;\n  font-size: 24px;\n}\n.ibox-title h5 {\n  display: inline-block;\n  font-size: 14px;\n  margin: 0 0 7px;\n  padding: 0;\n  text-overflow: ellipsis;\n  float: left;\n}\n.ibox-title .label {\n  float: left;\n  margin-left: 4px;\n}\n.ibox-tools {\n  display: inline-block;\n  float: right;\n  margin-top: 0;\n  position: relative;\n  padding: 0;\n}\n.ibox-tools a {\n  cursor: pointer;\n  margin-left: 5px;\n  color: #c4c4c4;\n}\n.ibox-tools a.btn-primary {\n  color: #fff;\n}\n.ibox-tools .dropdown-menu > li > a {\n  padding: 4px 10px;\n  font-size: 12px;\n}\n.ibox .open > .dropdown-menu {\n  left: auto;\n  right: 0;\n}\n/* BACKGROUNDS */\n.gray-bg {\n  background-color: #f3f3f4;\n}\n.white-bg {\n  background-color: #ffffff;\n}\n.navy-bg {\n  background-color: #f87012;\n  color: #ffffff;\n}\n.blue-bg {\n  background-color: #1c84c6;\n  color: #ffffff;\n}\n.lazur-bg {\n  background-color: #23c6c8;\n  color: #ffffff;\n}\n.yellow-bg {\n  background-color: #f8ac59;\n  color: #ffffff;\n}\n.red-bg {\n  background-color: #ED5565;\n  color: #ffffff;\n}\n.black-bg {\n  background-color: #262626;\n}\n.panel-primary {\n  border-color: #f87012;\n}\n.panel-primary > .panel-heading {\n  background-color: #f87012;\n  border-color: #f87012;\n}\n.panel-success {\n  border-color: #1c84c6;\n}\n.panel-success > .panel-heading {\n  background-color: #1c84c6;\n  border-color: #1c84c6;\n  color: #ffffff;\n}\n.panel-info {\n  border-color: #23c6c8;\n}\n.panel-info > .panel-heading {\n  background-color: #23c6c8;\n  border-color: #23c6c8;\n  color: #ffffff;\n}\n.panel-warning {\n  border-color: #f8ac59;\n}\n.panel-warning > .panel-heading {\n  background-color: #f8ac59;\n  border-color: #f8ac59;\n  color: #ffffff;\n}\n.panel-danger {\n  border-color: #ED5565;\n}\n.panel-danger > .panel-heading {\n  background-color: #ED5565;\n  border-color: #ED5565;\n  color: #ffffff;\n}\n.progress-bar {\n  background-color: #f87012;\n}\n.progress-small,\n.progress-small .progress-bar {\n  height: 10px;\n}\n.progress-small,\n.progress-mini {\n  margin-top: 5px;\n}\n.progress-mini,\n.progress-mini .progress-bar {\n  height: 5px;\n  margin-bottom: 0px;\n}\n.progress-bar-navy-light {\n  background-color: #3dc7ab;\n}\n.progress-bar-success {\n  background-color: #1c84c6;\n}\n.progress-bar-info {\n  background-color: #23c6c8;\n}\n.progress-bar-warning {\n  background-color: #f8ac59;\n}\n.progress-bar-danger {\n  background-color: #ED5565;\n}\n.panel-title {\n  font-size: inherit;\n}\n.jumbotron {\n  border-radius: 6px;\n  padding: 40px;\n}\n.jumbotron h1 {\n  margin-top: 0;\n}\n/* COLORS */\n.text-navy {\n  color: #f87012;\n}\n.text-primary {\n  color: inherit;\n}\n.text-success {\n  color: #1c84c6;\n}\n.text-info {\n  color: #23c6c8;\n}\n.text-warning {\n  color: #f8ac59;\n}\n.text-danger {\n  color: #ED5565;\n}\n.text-muted {\n  color: #888888;\n}\n.simple_tag {\n  background-color: #f3f3f4;\n  border: 1px solid #e7eaec;\n  border-radius: 2px;\n  color: inherit;\n  font-size: 10px;\n  margin-right: 5px;\n  margin-top: 5px;\n  padding: 5px 12px;\n  display: inline-block;\n}\n.img-shadow {\n  -webkit-box-shadow: 0px 0px 3px 0px #919191;\n  -moz-box-shadow: 0px 0px 3px 0px #919191;\n  box-shadow: 0px 0px 3px 0px #919191;\n}\n/* For handle diferent bg color in AngularJS version */\n.dashboards\\.dashboard_2 nav.navbar,\n.dashboards\\.dashboard_3 nav.navbar,\n.mailbox\\.inbox nav.navbar,\n.mailbox\\.email_view nav.navbar,\n.mailbox\\.email_compose nav.navbar,\n.dashboards\\.dashboard_4_1 nav.navbar,\n.metrics nav.navbar,\n.metrics\\.index nav.navbar {\n  background: #fff;\n}\n/* For handle diferent bg color in MVC version */\n.Dashboard_2 .navbar.navbar-static-top,\n.Dashboard_3 .navbar.navbar-static-top,\n.Dashboard_4_1 .navbar.navbar-static-top,\n.ComposeEmail .navbar.navbar-static-top,\n.EmailView .navbar.navbar-static-top,\n.Inbox .navbar.navbar-static-top,\n.Metrics .navbar.navbar-static-top {\n  background: #fff;\n}\na.close-canvas-menu {\n  position: absolute;\n  top: 10px;\n  right: 15px;\n  z-index: 1011;\n  color: #a7b1c2;\n}\na.close-canvas-menu:hover {\n  color: #fff;\n}\n/* FULL HEIGHT */\n.full-height {\n  height: 100%;\n}\n.fh-breadcrumb {\n  height: calc(100% - 196px);\n  margin: 0 -15px;\n  position: relative;\n}\n.fh-no-breadcrumb {\n  height: calc(100% - 99px);\n  margin: 0 -15px;\n  position: relative;\n}\n.fh-column {\n  background: #fff;\n  height: 100%;\n  width: 240px;\n  float: left;\n}\n.modal-backdrop {\n  z-index: 2040 !important;\n}\n.modal {\n  z-index: 2050 !important;\n}\n.spiner-example {\n  height: 200px;\n  padding-top: 70px;\n}\n/* MARGINS & PADDINGS */\n.p-xxs {\n  padding: 5px;\n}\n.p-xs {\n  padding: 10px;\n}\n.p-sm {\n  padding: 15px;\n}\n.p-m {\n  padding: 20px;\n}\n.p-md {\n  padding: 25px;\n}\n.p-lg {\n  padding: 30px;\n}\n.p-xl {\n  padding: 40px;\n}\n.m-xxs {\n  margin: 2px 4px;\n}\n.m-xs {\n  margin: 5px;\n}\n.m-sm {\n  margin: 10px;\n}\n.m {\n  margin: 15px;\n}\n.m-md {\n  margin: 20px;\n}\n.m-lg {\n  margin: 30px;\n}\n.m-xl {\n  margin: 50px;\n}\n.m-n {\n  margin: 0 !important;\n}\n.m-l-none {\n  margin-left: 0;\n}\n.m-l-xs {\n  margin-left: 5px;\n}\n.m-l-sm {\n  margin-left: 10px;\n}\n.m-l {\n  margin-left: 15px;\n}\n.m-l-md {\n  margin-left: 20px;\n}\n.m-l-lg {\n  margin-left: 30px;\n}\n.m-l-xl {\n  margin-left: 40px;\n}\n.m-l-n-xxs {\n  margin-left: -1px;\n}\n.m-l-n-xs {\n  margin-left: -5px;\n}\n.m-l-n-sm {\n  margin-left: -10px;\n}\n.m-l-n {\n  margin-left: -15px;\n}\n.m-l-n-md {\n  margin-left: -20px;\n}\n.m-l-n-lg {\n  margin-left: -30px;\n}\n.m-l-n-xl {\n  margin-left: -40px;\n}\n.m-t-none {\n  margin-top: 0;\n}\n.m-t-xxs {\n  margin-top: 1px;\n}\n.m-t-xs {\n  margin-top: 5px;\n}\n.m-t-sm {\n  margin-top: 10px;\n}\n.m-t {\n  margin-top: 15px;\n}\n.m-t-md {\n  margin-top: 20px;\n}\n.m-t-lg {\n  margin-top: 30px;\n}\n.m-t-xl {\n  margin-top: 40px;\n}\n.m-t-n-xxs {\n  margin-top: -1px;\n}\n.m-t-n-xs {\n  margin-top: -5px;\n}\n.m-t-n-sm {\n  margin-top: -10px;\n}\n.m-t-n {\n  margin-top: -15px;\n}\n.m-t-n-md {\n  margin-top: -20px;\n}\n.m-t-n-lg {\n  margin-top: -30px;\n}\n.m-t-n-xl {\n  margin-top: -40px;\n}\n.m-r-none {\n  margin-right: 0;\n}\n.m-r-xxs {\n  margin-right: 1px;\n}\n.m-r-xs {\n  margin-right: 5px;\n}\n.m-r-sm {\n  margin-right: 10px;\n}\n.m-r {\n  margin-right: 15px;\n}\n.m-r-md {\n  margin-right: 20px;\n}\n.m-r-lg {\n  margin-right: 30px;\n}\n.m-r-xl {\n  margin-right: 40px;\n}\n.m-r-n-xxs {\n  margin-right: -1px;\n}\n.m-r-n-xs {\n  margin-right: -5px;\n}\n.m-r-n-sm {\n  margin-right: -10px;\n}\n.m-r-n {\n  margin-right: -15px;\n}\n.m-r-n-md {\n  margin-right: -20px;\n}\n.m-r-n-lg {\n  margin-right: -30px;\n}\n.m-r-n-xl {\n  margin-right: -40px;\n}\n.m-b-none {\n  margin-bottom: 0;\n}\n.m-b-xxs {\n  margin-bottom: 1px;\n}\n.m-b-xs {\n  margin-bottom: 5px;\n}\n.m-b-sm {\n  margin-bottom: 10px;\n}\n.m-b {\n  margin-bottom: 15px;\n}\n.m-b-md {\n  margin-bottom: 20px;\n}\n.m-b-lg {\n  margin-bottom: 30px;\n}\n.m-b-xl {\n  margin-bottom: 40px;\n}\n.m-b-n-xxs {\n  margin-bottom: -1px;\n}\n.m-b-n-xs {\n  margin-bottom: -5px;\n}\n.m-b-n-sm {\n  margin-bottom: -10px;\n}\n.m-b-n {\n  margin-bottom: -15px;\n}\n.m-b-n-md {\n  margin-bottom: -20px;\n}\n.m-b-n-lg {\n  margin-bottom: -30px;\n}\n.m-b-n-xl {\n  margin-bottom: -40px;\n}\n.space-15 {\n  margin: 15px 0;\n}\n.space-20 {\n  margin: 20px 0;\n}\n.space-25 {\n  margin: 25px 0;\n}\n.space-30 {\n  margin: 30px 0;\n}\nbody.modal-open {\n  padding-right: inherit !important;\n}\n/* SEARCH PAGE */\n.search-form {\n  margin-top: 10px;\n}\n.search-result h3 {\n  margin-bottom: 0;\n  color: #1E0FBE;\n}\n.search-result .search-link {\n  color: #006621;\n}\n.search-result p {\n  font-size: 12px;\n  margin-top: 5px;\n}\n/* CONTACTS */\n.contact-box {\n  background-color: #ffffff;\n  border: 1px solid #dee0e2;\n  padding: 20px;\n  margin-bottom: 20px;\n}\n.contact-box a {\n  color: inherit;\n}\n/* INVOICE */\n.invoice-table tbody > tr > td:last-child,\n.invoice-table tbody > tr > td:nth-child(4),\n.invoice-table tbody > tr > td:nth-child(3),\n.invoice-table tbody > tr > td:nth-child(2) {\n  text-align: right;\n}\n.invoice-table thead > tr > th:last-child,\n.invoice-table thead > tr > th:nth-child(4),\n.invoice-table thead > tr > th:nth-child(3),\n.invoice-table thead > tr > th:nth-child(2) {\n  text-align: right;\n}\n.invoice-total > tbody > tr > td:first-child {\n  text-align: right;\n}\n.invoice-total > tbody > tr > td {\n  border: 0 none;\n}\n.invoice-total > tbody > tr > td:last-child {\n  border-bottom: 1px solid #DDDDDD;\n  text-align: right;\n  width: 15%;\n}\n/* ERROR & LOGIN & LOCKSCREEN*/\n.middle-box {\n  max-width: 400px;\n  z-index: 100;\n  margin: 0 auto;\n  padding-top: 40px;\n}\n.lockscreen.middle-box {\n  width: 200px;\n  padding-top: 110px;\n}\n.loginscreen.middle-box {\n  width: 300px;\n}\n.loginColumns {\n  max-width: 800px;\n  margin: 0 auto;\n  padding: 100px 20px 20px 20px;\n}\n.passwordBox {\n  max-width: 460px;\n  margin: 0 auto;\n  padding: 100px 20px 20px 20px;\n}\n.logo-name {\n  color: #e6e6e6;\n  font-size: 180px;\n  font-weight: 800;\n  letter-spacing: -10px;\n  margin-bottom: 0px;\n}\n.middle-box h1 {\n  font-size: 170px;\n}\n.wrapper .middle-box {\n  margin-top: 140px;\n}\n.lock-word {\n  z-index: 10;\n  position: absolute;\n  top: 110px;\n  left: 50%;\n  margin-left: -470px;\n}\n.lock-word span {\n  font-size: 100px;\n  font-weight: 600;\n  color: #e9e9e9;\n  display: inline-block;\n}\n.lock-word .first-word {\n  margin-right: 160px;\n}\n/* DASBOARD */\n.dashboard-header {\n  border-top: 0;\n  padding: 20px 20px 20px 20px;\n}\n.dashboard-header h2 {\n  margin-top: 10px;\n  font-size: 26px;\n}\n.fist-item {\n  border-top: none !important;\n}\n.statistic-box {\n  margin-top: 40px;\n}\n.dashboard-header .list-group-item span.label {\n  margin-right: 10px;\n}\n.list-group.clear-list .list-group-item {\n  border-top: 1px solid #dee0e2;\n  border-bottom: 0;\n  border-right: 0;\n  border-left: 0;\n  padding: 10px 0;\n}\nul.clear-list:first-child {\n  border-top: none !important;\n}\n/* Intimeline */\n.timeline-item .date i {\n  position: absolute;\n  top: 0;\n  right: 0;\n  padding: 5px;\n  width: 30px;\n  text-align: center;\n  border-top: 1px solid #dee0e2;\n  border-bottom: 1px solid #dee0e2;\n  border-left: 1px solid #dee0e2;\n  background: #f8f8f8;\n}\n.timeline-item .date {\n  text-align: right;\n  width: 110px;\n  position: relative;\n  padding-top: 30px;\n}\n.timeline-item .content {\n  border-left: 1px solid #dee0e2;\n  border-top: 1px solid #dee0e2;\n  padding-top: 10px;\n  min-height: 100px;\n}\n.timeline-item .content:hover {\n  background: #f6f6f6;\n}\n/* PIN BOARD */\nul.notes li,\nul.tag-list li {\n  list-style: none;\n}\nul.notes li h4 {\n  margin-top: 20px;\n  font-size: 16px;\n}\nul.notes li div {\n  text-decoration: none;\n  color: #000;\n  background: #ffc;\n  display: block;\n  height: 140px;\n  width: 140px;\n  padding: 1em;\n  position: relative;\n}\nul.notes li div small {\n  position: absolute;\n  top: 5px;\n  right: 5px;\n  font-size: 10px;\n}\nul.notes li div a {\n  position: absolute;\n  right: 10px;\n  bottom: 10px;\n  color: inherit;\n}\nul.notes li {\n  margin: 10px 40px 50px 0px;\n  float: left;\n}\nul.notes li div p {\n  font-size: 12px;\n}\nul.notes li div {\n  text-decoration: none;\n  color: #000;\n  background: #ffc;\n  display: block;\n  height: 140px;\n  width: 140px;\n  padding: 1em;\n  /* Firefox */\n  -moz-box-shadow: 5px 5px 2px #212121;\n  /* Safari+Chrome */\n  -webkit-box-shadow: 5px 5px 2px rgba(33, 33, 33, 0.7);\n  /* Opera */\n  box-shadow: 5px 5px 2px rgba(33, 33, 33, 0.7);\n}\nul.notes li div {\n  -webkit-transform: rotate(-6deg);\n  -o-transform: rotate(-6deg);\n  -moz-transform: rotate(-6deg);\n}\nul.notes li:nth-child(even) div {\n  -o-transform: rotate(4deg);\n  -webkit-transform: rotate(4deg);\n  -moz-transform: rotate(4deg);\n  position: relative;\n  top: 5px;\n}\nul.notes li:nth-child(3n) div {\n  -o-transform: rotate(-3deg);\n  -webkit-transform: rotate(-3deg);\n  -moz-transform: rotate(-3deg);\n  position: relative;\n  top: -5px;\n}\nul.notes li:nth-child(5n) div {\n  -o-transform: rotate(5deg);\n  -webkit-transform: rotate(5deg);\n  -moz-transform: rotate(5deg);\n  position: relative;\n  top: -10px;\n}\nul.notes li div:hover,\nul.notes li div:focus {\n  -webkit-transform: scale(1.1);\n  -moz-transform: scale(1.1);\n  -o-transform: scale(1.1);\n  position: relative;\n  z-index: 5;\n}\nul.notes li div {\n  text-decoration: none;\n  color: #000;\n  background: #ffc;\n  display: block;\n  height: 210px;\n  width: 210px;\n  padding: 1em;\n  -moz-box-shadow: 5px 5px 7px #212121;\n  -webkit-box-shadow: 5px 5px 7px rgba(33, 33, 33, 0.7);\n  box-shadow: 5px 5px 7px rgba(33, 33, 33, 0.7);\n  -moz-transition: -moz-transform 0.15s linear;\n  -o-transition: -o-transform 0.15s linear;\n  -webkit-transition: -webkit-transform 0.15s linear;\n}\n/* FILE MANAGER */\n.file-box {\n  float: left;\n  width: 220px;\n}\n.file-manager h5 {\n  text-transform: uppercase;\n}\n.file-manager {\n  list-style: none outside none;\n  margin: 0;\n  padding: 0;\n}\n.folder-list li a {\n  color: #666666;\n  display: block;\n  padding: 5px 0;\n}\n.folder-list li {\n  border-bottom: 1px solid #dee0e2;\n  display: block;\n}\n.folder-list li i {\n  margin-right: 8px;\n  color: #3d4d5d;\n}\n.category-list li a {\n  color: #666666;\n  display: block;\n  padding: 5px 0;\n}\n.category-list li {\n  display: block;\n}\n.category-list li i {\n  margin-right: 8px;\n  color: #3d4d5d;\n}\n.category-list li a .text-navy {\n  color: #f87012;\n}\n.category-list li a .text-primary {\n  color: #1c84c6;\n}\n.category-list li a .text-info {\n  color: #23c6c8;\n}\n.category-list li a .text-danger {\n  color: #EF5352;\n}\n.category-list li a .text-warning {\n  color: #F8AC59;\n}\n.file-manager h5.tag-title {\n  margin-top: 20px;\n}\n.tag-list li {\n  float: left;\n}\n.tag-list li a {\n  font-size: 10px;\n  background-color: #f3f3f4;\n  padding: 5px 12px;\n  color: inherit;\n  border-radius: 2px;\n  border: 1px solid #dee0e2;\n  margin-right: 5px;\n  margin-top: 5px;\n  display: block;\n}\n.file {\n  border: 1px solid #dee0e2;\n  padding: 0;\n  background-color: #ffffff;\n  position: relative;\n}\n.file.file--primary {\n  -webkit-appearance: none;\n  -moz-appearance: none;\n  appearance: none;\n  outline: none;\n}\n.file.file--primary:hover::-webkit-file-upload-button,\n.file.file--primary:active::-webkit-file-upload-button,\n.file.file--primary:focus::-webkit-file-upload-button {\n  outline: none;\n}\n.file.file--primary:hover::-ms-browse,\n.file.file--primary:active::-ms-browse,\n.file.file--primary:focus::-ms-browse {\n  outline: none;\n}\n.file.file--primary::-webkit-file-upload-button {\n  font-size: 13px;\n  font-weight: 700;\n  border-radius: 3px;\n  background-color: #f87012;\n  border-color: #f87012;\n  color: #FFF;\n}\n.file.file--primary::-ms-browse {\n  font-size: 13px;\n  font-weight: 700;\n  border-radius: 3px;\n  background-color: #f87012;\n  border-color: #f87012;\n  color: #FFF;\n}\n.file-manager .hr-line-dashed {\n  margin: 15px 0;\n}\n.file .icon,\n.file .image {\n  height: 100px;\n  overflow: hidden;\n}\n.file .icon {\n  padding: 15px 10px;\n  text-align: center;\n}\n.file-control {\n  color: inherit;\n  font-size: 11px;\n  margin-right: 10px;\n}\n.file-control.active {\n  text-decoration: underline;\n}\n.file .icon i {\n  font-size: 70px;\n  color: #dadada;\n}\n.file .file-name {\n  padding: 10px;\n  background-color: #f8f8f8;\n  border-top: 1px solid #dee0e2;\n}\n.file-name small {\n  color: #737578;\n}\n.corner {\n  position: absolute;\n  display: inline-block;\n  width: 0;\n  height: 0;\n  line-height: 0;\n  border: 0.6em solid transparent;\n  border-right: 0.6em solid #f1f1f1;\n  border-bottom: 0.6em solid #f1f1f1;\n  right: 0em;\n  bottom: 0em;\n}\na.compose-mail {\n  padding: 8px 10px;\n}\n.mail-search {\n  max-width: 300px;\n}\n/* PROFILE */\n.profile-content {\n  border-top: none !important;\n}\n.feed-activity-list .feed-element {\n  border-bottom: 1px solid #dee0e2;\n}\n.feed-element:first-child {\n  margin-top: 0;\n}\n.feed-element {\n  padding-bottom: 15px;\n}\n.feed-element,\n.feed-element .media {\n  margin-top: 15px;\n}\n.feed-element,\n.media-body {\n  overflow: hidden;\n}\n.feed-element > .pull-left {\n  margin-right: 10px;\n}\n.feed-element img.img-circle,\n.dropdown-messages-box img.img-circle {\n  width: 38px;\n  height: 38px;\n}\n.feed-element .well {\n  border: 1px solid #dee0e2;\n  box-shadow: none;\n  margin-top: 10px;\n  margin-bottom: 5px;\n  padding: 10px 20px;\n  font-size: 11px;\n  line-height: 16px;\n}\n.feed-element .actions {\n  margin-top: 10px;\n}\n.feed-element .photos {\n  margin: 10px 0;\n}\n.feed-photo {\n  max-height: 180px;\n  border-radius: 4px;\n  overflow: hidden;\n  margin-right: 10px;\n  margin-bottom: 10px;\n}\n/* MAILBOX */\n.mail-box {\n  background-color: #ffffff;\n  border: 1px solid #dee0e2;\n  border-top: 0;\n  padding: 0px;\n  margin-bottom: 20px;\n}\n.mail-box-header {\n  background-color: #ffffff;\n  border: 1px solid #dee0e2;\n  border-bottom: 0;\n  padding: 30px 20px 20px 20px;\n}\n.mail-box-header h2 {\n  margin-top: 0px;\n}\n.mailbox-content .tag-list li a {\n  background: #ffffff;\n}\n.mail-body {\n  border-top: 1px solid #dee0e2;\n  padding: 20px;\n}\n.mail-text {\n  border-top: 1px solid #dee0e2;\n}\n.mail-text .note-toolbar {\n  padding: 10px 15px;\n}\n.mail-body .form-group {\n  margin-bottom: 5px;\n}\n.mail-text .note-editor .note-toolbar {\n  background-color: #F9F8F8;\n}\n.mail-attachment {\n  border-top: 1px solid #dee0e2;\n  padding: 20px;\n  font-size: 12px;\n}\n.mailbox-content {\n  background: none;\n  border: none;\n  padding: 10px;\n}\n.mail-ontact {\n  width: 23%;\n}\n/* PROJECTS */\n.project-people,\n.project-actions {\n  text-align: right;\n  vertical-align: middle;\n}\ndd.project-people {\n  text-align: left;\n  margin-top: 5px;\n}\n.project-people img {\n  width: 32px;\n  height: 32px;\n}\n.project-title a {\n  font-size: 14px;\n  color: #737578;\n  font-weight: 600;\n}\n.project-list table tr td {\n  border-top: none;\n  border-bottom: 1px solid #dee0e2;\n  padding: 15px 10px;\n  vertical-align: middle;\n}\n.project-manager .tag-list li a {\n  font-size: 10px;\n  background-color: white;\n  padding: 5px 12px;\n  color: inherit;\n  border-radius: 2px;\n  border: 1px solid #dee0e2;\n  margin-right: 5px;\n  margin-top: 5px;\n  display: block;\n}\n.project-files li a {\n  font-size: 11px;\n  color: #737578;\n  margin-left: 10px;\n  line-height: 22px;\n}\n/* FAQ */\n.faq-item {\n  padding: 20px;\n  margin-bottom: 2px;\n  background: #fff;\n}\n.faq-question {\n  font-size: 18px;\n  font-weight: 600;\n  color: #f87012;\n  display: block;\n}\n.faq-question:hover {\n  color: #ea6407;\n}\n.faq-answer {\n  margin-top: 10px;\n  background: #f3f3f4;\n  border: 1px solid #dee0e2;\n  border-radius: 3px;\n  padding: 15px;\n}\n.faq-item .tag-item {\n  background: #f3f3f4;\n  padding: 2px 6px;\n  font-size: 10px;\n  text-transform: uppercase;\n}\n/* Chat view */\n.message-input {\n  height: 90px !important;\n}\n.chat-avatar {\n  white: 36px;\n  height: 36px;\n  float: left;\n  margin-right: 10px;\n}\n.chat-user-name {\n  padding: 10px;\n}\n.chat-user {\n  padding: 8px 10px;\n  border-bottom: 1px solid #e7eaec;\n}\n.chat-user a {\n  color: inherit;\n}\n.chat-view {\n  z-index: 20012;\n}\n.chat-users,\n.chat-statistic {\n  margin-left: -30px;\n}\n@media (max-width: 992px) {\n  .chat-users,\n  .chat-statistic {\n    margin-left: 0px;\n  }\n}\n.chat-view .ibox-content {\n  padding: 0;\n}\n.chat-message {\n  padding: 10px 20px;\n}\n.message-avatar {\n  height: 48px;\n  width: 48px;\n  border: 1px solid #e7eaec;\n  border-radius: 4px;\n  margin-top: 1px;\n}\n.chat-discussion .chat-message.left .message-avatar {\n  float: left;\n  margin-right: 10px;\n}\n.chat-discussion .chat-message.right .message-avatar {\n  float: right;\n  margin-left: 10px;\n}\n.message {\n  background-color: #fff;\n  border: 1px solid #e7eaec;\n  text-align: left;\n  display: block;\n  padding: 10px 20px;\n  position: relative;\n  border-radius: 4px;\n}\n.chat-discussion .chat-message.left .message-date {\n  float: right;\n}\n.chat-discussion .chat-message.right .message-date {\n  float: left;\n}\n.chat-discussion .chat-message.left .message {\n  text-align: left;\n  margin-left: 55px;\n}\n.chat-discussion .chat-message.right .message {\n  text-align: right;\n  margin-right: 55px;\n}\n.message-date {\n  font-size: 10px;\n  color: #888888;\n}\n.message-content {\n  display: block;\n}\n.chat-discussion {\n  background: #eee;\n  padding: 15px;\n  height: 400px;\n  overflow-y: auto;\n}\n.chat-users {\n  overflow-y: auto;\n  height: 400px;\n}\n.chat-message-form .form-group {\n  margin-bottom: 0;\n}\n/* jsTree */\n.jstree-open > .jstree-anchor > .fa-folder:before {\n  content: \"\\f07c\";\n}\n.jstree-default .jstree-icon.none {\n  width: 0;\n}\n/* CLIENTS */\n.clients-list {\n  margin-top: 20px;\n}\n.clients-list .tab-pane {\n  position: relative;\n  height: 600px;\n}\n.client-detail {\n  position: relative;\n  height: 620px;\n}\n.clients-list table tr td {\n  height: 46px;\n  vertical-align: middle;\n  border: none ;\n}\n.client-link {\n  font-weight: 600;\n  color: inherit;\n}\n.client-link:hover {\n  color: inherit;\n}\n.client-avatar {\n  width: 42px;\n}\n.client-avatar img {\n  width: 28px;\n  height: 28px;\n  border-radius: 50%;\n}\n.contact-type {\n  width: 20px;\n  color: #cecfd0;\n}\n.client-status {\n  text-align: left;\n}\n.client-detail .vertical-timeline-content p {\n  margin: 0;\n}\n.client-detail .vertical-timeline-icon.gray-bg {\n  color: #b4b5b7;\n}\n.clients-list .nav-tabs > li.active > a,\n.clients-list .nav-tabs > li.active > a:hover,\n.clients-list .nav-tabs > li.active > a:focus {\n  border-bottom: 1px solid #fff;\n}\n/* BLOG ARTICLE */\n.blog h2 {\n  font-weight: 700;\n}\n.blog h5 {\n  margin: 0 0 5px 0;\n}\n.blog .btn {\n  margin: 0 0 5px 0;\n}\n.article h1 {\n  font-size: 48px;\n  font-weight: 700;\n  color: #2F4050;\n}\n.article p {\n  font-size: 15px;\n  line-height: 26px;\n}\n.article-title {\n  text-align: center;\n  margin: 40px 0 100px 0;\n}\n.article .ibox-content {\n  padding: 40px;\n}\n/* ISSUE TRACKER */\n.issue-tracker .btn-link {\n  color: #f87012;\n}\ntable.issue-tracker tbody tr td {\n  vertical-align: middle;\n  height: 50px;\n}\n.issue-info {\n  width: 50%;\n}\n.issue-info a {\n  font-weight: 600;\n  color: #737578;\n}\n.issue-info small {\n  display: block;\n}\n/* TEAMS */\n.team-members {\n  margin: 10px 0;\n}\n.team-members img.img-circle {\n  width: 42px;\n  height: 42px;\n  margin-bottom: 5px;\n}\n/* AGILE BOARD */\n.sortable-list {\n  padding: 10px 0;\n}\n.agile-list {\n  list-style: none;\n  margin: 0;\n}\n.agile-list li {\n  background: #FAFAFB;\n  border: 1px solid #e7eaec;\n  margin: 0px 0 10px 0;\n  padding: 10px;\n  border-radius: 2px;\n}\n.agile-list li:hover {\n  cursor: pointer;\n  background: #fff;\n}\n.agile-list li.warning-element {\n  border-left: 3px solid #f8ac59;\n}\n.agile-list li.danger-element {\n  border-left: 3px solid #ED5565;\n}\n.agile-list li.info-element {\n  border-left: 3px solid #1c84c6;\n}\n.agile-list li.success-element {\n  border-left: 3px solid #f87012;\n}\n.agile-detail {\n  margin-top: 5px;\n  font-size: 12px;\n}\n/* DIFF */\nins {\n  background-color: #c6ffc6;\n  text-decoration: none;\n}\ndel {\n  background-color: #ffc6c6;\n}\n/* E-commerce */\n.product-box {\n  padding: 0;\n  border: 1px solid #dee0e2;\n}\n.product-box:hover,\n.product-box.active {\n  border: 1px solid transparent;\n  -webkit-box-shadow: 0px 3px 7px 0px #a8a8a8;\n  -moz-box-shadow: 0px 3px 7px 0px #a8a8a8;\n  box-shadow: 0px 3px 7px 0px #a8a8a8;\n}\n.product-imitation {\n  text-align: center;\n  padding: 90px 0;\n  background-color: #f8f8f9;\n  color: #bebec3;\n  font-weight: 600;\n}\n.product-imitation.xl {\n  padding: 120px 0;\n}\n.product-desc {\n  padding: 20px;\n  position: relative;\n}\n.ecommerce .tag-list {\n  padding: 0;\n}\n.ecommerce .fa-star {\n  color: #D1DADE;\n}\n.ecommerce .fa-star.active {\n  color: #f8ac59;\n}\n.ecommerce .note-editor {\n  border: 1px solid #dee0e2;\n}\n.product-name {\n  font-size: 16px;\n  font-weight: 600;\n  color: #737578;\n  display: block;\n  margin: 2px 0 5px 0;\n}\n.product-name:hover,\n.product-name:focus {\n  color: #f87012;\n}\n.product-price {\n  font-size: 14px;\n  font-weight: 600;\n  color: #ffffff;\n  background-color: #f87012;\n  padding: 6px 12px;\n  position: absolute;\n  top: -32px;\n  right: 0;\n}\n/* Social feed */\n.social-feed-separated .social-feed-box {\n  margin-left: 62px;\n}\n.social-feed-separated .social-avatar {\n  float: left;\n  padding: 0;\n}\n.social-feed-separated .social-avatar img {\n  width: 52px;\n  height: 52px;\n  border: 1px solid #e7eaec;\n}\n.social-feed-separated .social-feed-box .social-avatar {\n  padding: 15px 15px 0 15px;\n  float: none;\n}\n.social-feed-box {\n  /*padding: 15px;*/\n  border: 1px solid #e7eaec;\n  background: #fff;\n  margin-bottom: 15px;\n}\n.article .social-feed-box {\n  margin-bottom: 0;\n  border-bottom: none;\n}\n.article .social-feed-box:last-child {\n  margin-bottom: 0;\n  border-bottom: 1px solid #dee0e2;\n}\n.article .social-feed-box p {\n  font-size: 13px;\n  line-height: 18px;\n}\n.social-action {\n  margin: 15px;\n}\n.social-avatar {\n  padding: 15px 15px 0 15px;\n}\n.social-comment .social-comment {\n  margin-left: 45px;\n}\n.social-avatar img {\n  height: 40px;\n  width: 40px;\n  margin-right: 10px;\n}\n.social-avatar .media-body a {\n  font-size: 14px;\n  display: block;\n}\n.social-body {\n  padding: 15px;\n}\n.social-body img {\n  margin-bottom: 10px;\n}\n.social-footer {\n  border-top: 1px solid #e7eaec;\n  padding: 10px 15px;\n  background: #f9f9f9;\n}\n.social-footer .social-comment img {\n  width: 32px;\n  margin-right: 10px;\n}\n.social-comment:first-child {\n  margin-top: 0;\n}\n.social-comment {\n  margin-top: 15px;\n}\n.social-comment textarea {\n  font-size: 12px;\n}\n#small-chat {\n  position: fixed;\n  bottom: 20px;\n  right: 20px;\n  z-index: 100;\n}\n#small-chat .badge {\n  position: absolute;\n  top: -3px;\n  right: -4px;\n}\n.open-small-chat {\n  height: 38px;\n  width: 38px;\n  display: block;\n  background: #1ab394;\n  padding: 9px 8px;\n  text-align: center;\n  color: #fff;\n  border-radius: 50%;\n}\n.open-small-chat:hover {\n  color: white;\n  background: #1ab394;\n}\n.small-chat-box {\n  display: none;\n  position: fixed;\n  bottom: 20px;\n  right: 75px;\n  background: #fff;\n  border: 1px solid #dee0e2;\n  width: 230px;\n  height: 320px;\n  border-radius: 4px;\n}\n.small-chat-box.ng-small-chat {\n  display: block;\n}\n.body-small .small-chat-box {\n  bottom: 70px;\n  right: 20px;\n}\n.small-chat-box.active {\n  display: block;\n}\n.small-chat-box .heading {\n  background: #bdcfe5;\n  padding: 8px 15px;\n  font-weight: bold;\n  color: #fff;\n}\n.small-chat-box .chat-date {\n  opacity: 0.6;\n  font-size: 10px;\n  font-weight: normal;\n}\n.small-chat-box .content {\n  padding: 15px 15px;\n}\n.small-chat-box .content .author-name {\n  font-weight: bold;\n  margin-bottom: 3px;\n  font-size: 11px;\n}\n.small-chat-box .content > div {\n  padding-bottom: 20px;\n}\n.small-chat-box .content .chat-message {\n  padding: 5px 10px;\n  border-radius: 6px;\n  font-size: 11px;\n  line-height: 14px;\n  max-width: 80%;\n  background: #f3f3f4;\n  margin-bottom: 10px;\n}\n.small-chat-box .content .chat-message.active {\n  background: #1ab394;\n  color: #fff;\n}\n.small-chat-box .content .left {\n  text-align: left;\n  clear: both;\n}\n.small-chat-box .content .left .chat-message {\n  float: left;\n}\n.small-chat-box .content .right {\n  text-align: right;\n  clear: both;\n}\n.small-chat-box .content .right .chat-message {\n  float: right;\n}\n.small-chat-box .form-chat {\n  padding: 10px 10px;\n}\n/*\n * metismenu - v2.0.2\n * A jQuery menu plugin\n * https://github.com/onokumus/metisMenu\n *\n * Made by Osman Nuri Okumus\n * Under MIT License\n */\n.metismenu .plus-minus,\n.metismenu .plus-times {\n  float: right;\n}\n.metismenu .arrow {\n  float: right;\n  line-height: 1.42857;\n}\n.metismenu .glyphicon.arrow:before {\n  content: \"\\e079\";\n}\n.metismenu .active > a > .glyphicon.arrow:before {\n  content: \"\\e114\";\n}\n.metismenu .fa.arrow:before {\n  content: \"\\f104\";\n}\n.metismenu .active > a > .fa.arrow:before {\n  content: \"\\f107\";\n}\n.metismenu .ion.arrow:before {\n  content: \"\\f3d2\";\n}\n.metismenu .active > a > .ion.arrow:before {\n  content: \"\\f3d0\";\n}\n.metismenu .fa.plus-minus:before,\n.metismenu .fa.plus-times:before {\n  content: \"\\f067\";\n}\n.metismenu .active > a > .fa.plus-times {\n  -webkit-transform: rotate(45deg);\n  -ms-transform: rotate(45deg);\n  transform: rotate(45deg);\n}\n.metismenu .active > a > .fa.plus-minus:before {\n  content: \"\\f068\";\n}\n.metismenu .collapse {\n  display: none;\n}\n.metismenu .collapse.in {\n  display: block;\n}\n.metismenu .collapsing {\n  position: relative;\n  height: 0;\n  overflow: hidden;\n  -webkit-transition-timing-function: ease;\n  transition-timing-function: ease;\n  -webkit-transition-duration: .35s;\n  transition-duration: .35s;\n  -webkit-transition-property: height, visibility;\n  transition-property: height, visibility;\n}\n/*\n *  Usage:\n *\n *    <div class=\"sk-spinner sk-spinner-rotating-plane\"></div>\n *\n */\n.sk-spinner-rotating-plane.sk-spinner {\n  width: 30px;\n  height: 30px;\n  background-color: #f87012;\n  margin: 0 auto;\n  -webkit-animation: sk-rotatePlane 1.2s infinite ease-in-out;\n  animation: sk-rotatePlane 1.2s infinite ease-in-out;\n}\n@-webkit-keyframes sk-rotatePlane {\n  0% {\n    -webkit-transform: perspective(120px) rotateX(0deg) rotateY(0deg);\n    transform: perspective(120px) rotateX(0deg) rotateY(0deg);\n  }\n  50% {\n    -webkit-transform: perspective(120px) rotateX(-180.1deg) rotateY(0deg);\n    transform: perspective(120px) rotateX(-180.1deg) rotateY(0deg);\n  }\n  100% {\n    -webkit-transform: perspective(120px) rotateX(-180deg) rotateY(-179.9deg);\n    transform: perspective(120px) rotateX(-180deg) rotateY(-179.9deg);\n  }\n}\n@keyframes sk-rotatePlane {\n  0% {\n    -webkit-transform: perspective(120px) rotateX(0deg) rotateY(0deg);\n    transform: perspective(120px) rotateX(0deg) rotateY(0deg);\n  }\n  50% {\n    -webkit-transform: perspective(120px) rotateX(-180.1deg) rotateY(0deg);\n    transform: perspective(120px) rotateX(-180.1deg) rotateY(0deg);\n  }\n  100% {\n    -webkit-transform: perspective(120px) rotateX(-180deg) rotateY(-179.9deg);\n    transform: perspective(120px) rotateX(-180deg) rotateY(-179.9deg);\n  }\n}\n/*\n *  Usage:\n *\n *    <div class=\"sk-spinner sk-spinner-double-bounce\">\n *      <div class=\"sk-double-bounce1\"></div>\n *      <div class=\"sk-double-bounce2\"></div>\n *    </div>\n *\n */\n.sk-spinner-double-bounce.sk-spinner {\n  width: 40px;\n  height: 40px;\n  position: relative;\n  margin: 0 auto;\n}\n.sk-spinner-double-bounce .sk-double-bounce1,\n.sk-spinner-double-bounce .sk-double-bounce2 {\n  width: 100%;\n  height: 100%;\n  border-radius: 50%;\n  background-color: #f87012;\n  opacity: 0.6;\n  position: absolute;\n  top: 0;\n  left: 0;\n  -webkit-animation: sk-doubleBounce 2s infinite ease-in-out;\n  animation: sk-doubleBounce 2s infinite ease-in-out;\n}\n.sk-spinner-double-bounce .sk-double-bounce2 {\n  -webkit-animation-delay: -1s;\n  animation-delay: -1s;\n}\n@-webkit-keyframes sk-doubleBounce {\n  0%,\n  100% {\n    -webkit-transform: scale(0);\n    transform: scale(0);\n  }\n  50% {\n    -webkit-transform: scale(1);\n    transform: scale(1);\n  }\n}\n@keyframes sk-doubleBounce {\n  0%,\n  100% {\n    -webkit-transform: scale(0);\n    transform: scale(0);\n  }\n  50% {\n    -webkit-transform: scale(1);\n    transform: scale(1);\n  }\n}\n/*\n *  Usage:\n *\n *    <div class=\"sk-spinner sk-spinner-wave\">\n *      <div class=\"sk-rect1\"></div>\n *      <div class=\"sk-rect2\"></div>\n *      <div class=\"sk-rect3\"></div>\n *      <div class=\"sk-rect4\"></div>\n *      <div class=\"sk-rect5\"></div>\n *    </div>\n *\n */\n.sk-spinner-wave.sk-spinner {\n  margin: 0 auto;\n  width: 50px;\n  height: 30px;\n  text-align: center;\n  font-size: 10px;\n}\n.sk-spinner-wave div {\n  background-color: #f87012;\n  height: 100%;\n  width: 6px;\n  display: inline-block;\n  -webkit-animation: sk-waveStretchDelay 1.2s infinite ease-in-out;\n  animation: sk-waveStretchDelay 1.2s infinite ease-in-out;\n}\n.sk-spinner-wave .sk-rect2 {\n  -webkit-animation-delay: -1.1s;\n  animation-delay: -1.1s;\n}\n.sk-spinner-wave .sk-rect3 {\n  -webkit-animation-delay: -1s;\n  animation-delay: -1s;\n}\n.sk-spinner-wave .sk-rect4 {\n  -webkit-animation-delay: -0.9s;\n  animation-delay: -0.9s;\n}\n.sk-spinner-wave .sk-rect5 {\n  -webkit-animation-delay: -0.8s;\n  animation-delay: -0.8s;\n}\n@-webkit-keyframes sk-waveStretchDelay {\n  0%,\n  40%,\n  100% {\n    -webkit-transform: scaleY(0.4);\n    transform: scaleY(0.4);\n  }\n  20% {\n    -webkit-transform: scaleY(1);\n    transform: scaleY(1);\n  }\n}\n@keyframes sk-waveStretchDelay {\n  0%,\n  40%,\n  100% {\n    -webkit-transform: scaleY(0.4);\n    transform: scaleY(0.4);\n  }\n  20% {\n    -webkit-transform: scaleY(1);\n    transform: scaleY(1);\n  }\n}\n/*\n *  Usage:\n *\n *    <div class=\"sk-spinner sk-spinner-wandering-cubes\">\n *      <div class=\"sk-cube1\"></div>\n *      <div class=\"sk-cube2\"></div>\n *    </div>\n *\n */\n.sk-spinner-wandering-cubes.sk-spinner {\n  margin: 0 auto;\n  width: 32px;\n  height: 32px;\n  position: relative;\n}\n.sk-spinner-wandering-cubes .sk-cube1,\n.sk-spinner-wandering-cubes .sk-cube2 {\n  background-color: #f87012;\n  width: 10px;\n  height: 10px;\n  position: absolute;\n  top: 0;\n  left: 0;\n  -webkit-animation: sk-wanderingCubeMove 1.8s infinite ease-in-out;\n  animation: sk-wanderingCubeMove 1.8s infinite ease-in-out;\n}\n.sk-spinner-wandering-cubes .sk-cube2 {\n  -webkit-animation-delay: -0.9s;\n  animation-delay: -0.9s;\n}\n@-webkit-keyframes sk-wanderingCubeMove {\n  25% {\n    -webkit-transform: translateX(42px) rotate(-90deg) scale(0.5);\n    transform: translateX(42px) rotate(-90deg) scale(0.5);\n  }\n  50% {\n    /* Hack to make FF rotate in the right direction */\n    -webkit-transform: translateX(42px) translateY(42px) rotate(-179deg);\n    transform: translateX(42px) translateY(42px) rotate(-179deg);\n  }\n  50.1% {\n    -webkit-transform: translateX(42px) translateY(42px) rotate(-180deg);\n    transform: translateX(42px) translateY(42px) rotate(-180deg);\n  }\n  75% {\n    -webkit-transform: translateX(0px) translateY(42px) rotate(-270deg) scale(0.5);\n    transform: translateX(0px) translateY(42px) rotate(-270deg) scale(0.5);\n  }\n  100% {\n    -webkit-transform: rotate(-360deg);\n    transform: rotate(-360deg);\n  }\n}\n@keyframes sk-wanderingCubeMove {\n  25% {\n    -webkit-transform: translateX(42px) rotate(-90deg) scale(0.5);\n    transform: translateX(42px) rotate(-90deg) scale(0.5);\n  }\n  50% {\n    /* Hack to make FF rotate in the right direction */\n    -webkit-transform: translateX(42px) translateY(42px) rotate(-179deg);\n    transform: translateX(42px) translateY(42px) rotate(-179deg);\n  }\n  50.1% {\n    -webkit-transform: translateX(42px) translateY(42px) rotate(-180deg);\n    transform: translateX(42px) translateY(42px) rotate(-180deg);\n  }\n  75% {\n    -webkit-transform: translateX(0px) translateY(42px) rotate(-270deg) scale(0.5);\n    transform: translateX(0px) translateY(42px) rotate(-270deg) scale(0.5);\n  }\n  100% {\n    -webkit-transform: rotate(-360deg);\n    transform: rotate(-360deg);\n  }\n}\n/*\n *  Usage:\n *\n *    <div class=\"sk-spinner sk-spinner-pulse\"></div>\n *\n */\n.sk-spinner-pulse.sk-spinner {\n  width: 40px;\n  height: 40px;\n  margin: 0 auto;\n  background-color: #f87012;\n  border-radius: 100%;\n  -webkit-animation: sk-pulseScaleOut 1s infinite ease-in-out;\n  animation: sk-pulseScaleOut 1s infinite ease-in-out;\n}\n@-webkit-keyframes sk-pulseScaleOut {\n  0% {\n    -webkit-transform: scale(0);\n    transform: scale(0);\n  }\n  100% {\n    -webkit-transform: scale(1);\n    transform: scale(1);\n    opacity: 0;\n  }\n}\n@keyframes sk-pulseScaleOut {\n  0% {\n    -webkit-transform: scale(0);\n    transform: scale(0);\n  }\n  100% {\n    -webkit-transform: scale(1);\n    transform: scale(1);\n    opacity: 0;\n  }\n}\n/*\n *  Usage:\n *\n *    <div class=\"sk-spinner sk-spinner-chasing-dots\">\n *      <div class=\"sk-dot1\"></div>\n *      <div class=\"sk-dot2\"></div>\n *    </div>\n *\n */\n.sk-spinner-chasing-dots.sk-spinner {\n  margin: 0 auto;\n  width: 40px;\n  height: 40px;\n  position: relative;\n  text-align: center;\n  -webkit-animation: sk-chasingDotsRotate 2s infinite linear;\n  animation: sk-chasingDotsRotate 2s infinite linear;\n}\n.sk-spinner-chasing-dots .sk-dot1,\n.sk-spinner-chasing-dots .sk-dot2 {\n  width: 60%;\n  height: 60%;\n  display: inline-block;\n  position: absolute;\n  top: 0;\n  background-color: #f87012;\n  border-radius: 100%;\n  -webkit-animation: sk-chasingDotsBounce 2s infinite ease-in-out;\n  animation: sk-chasingDotsBounce 2s infinite ease-in-out;\n}\n.sk-spinner-chasing-dots .sk-dot2 {\n  top: auto;\n  bottom: 0px;\n  -webkit-animation-delay: -1s;\n  animation-delay: -1s;\n}\n@-webkit-keyframes sk-chasingDotsRotate {\n  100% {\n    -webkit-transform: rotate(360deg);\n    transform: rotate(360deg);\n  }\n}\n@keyframes sk-chasingDotsRotate {\n  100% {\n    -webkit-transform: rotate(360deg);\n    transform: rotate(360deg);\n  }\n}\n@-webkit-keyframes sk-chasingDotsBounce {\n  0%,\n  100% {\n    -webkit-transform: scale(0);\n    transform: scale(0);\n  }\n  50% {\n    -webkit-transform: scale(1);\n    transform: scale(1);\n  }\n}\n@keyframes sk-chasingDotsBounce {\n  0%,\n  100% {\n    -webkit-transform: scale(0);\n    transform: scale(0);\n  }\n  50% {\n    -webkit-transform: scale(1);\n    transform: scale(1);\n  }\n}\n/*\n *  Usage:\n *\n *    <div class=\"sk-spinner sk-spinner-three-bounce\">\n *      <div class=\"sk-bounce1\"></div>\n *      <div class=\"sk-bounce2\"></div>\n *      <div class=\"sk-bounce3\"></div>\n *    </div>\n *\n */\n.sk-spinner-three-bounce.sk-spinner {\n  margin: 0 auto;\n  width: 70px;\n  text-align: center;\n}\n.sk-spinner-three-bounce div {\n  width: 18px;\n  height: 18px;\n  background-color: #f87012;\n  border-radius: 100%;\n  display: inline-block;\n  -webkit-animation: sk-threeBounceDelay 1.4s infinite ease-in-out;\n  animation: sk-threeBounceDelay 1.4s infinite ease-in-out;\n  /* Prevent first frame from flickering when animation starts */\n  -webkit-animation-fill-mode: both;\n  animation-fill-mode: both;\n}\n.sk-spinner-three-bounce .sk-bounce1 {\n  -webkit-animation-delay: -0.32s;\n  animation-delay: -0.32s;\n}\n.sk-spinner-three-bounce .sk-bounce2 {\n  -webkit-animation-delay: -0.16s;\n  animation-delay: -0.16s;\n}\n@-webkit-keyframes sk-threeBounceDelay {\n  0%,\n  80%,\n  100% {\n    -webkit-transform: scale(0);\n    transform: scale(0);\n  }\n  40% {\n    -webkit-transform: scale(1);\n    transform: scale(1);\n  }\n}\n@keyframes sk-threeBounceDelay {\n  0%,\n  80%,\n  100% {\n    -webkit-transform: scale(0);\n    transform: scale(0);\n  }\n  40% {\n    -webkit-transform: scale(1);\n    transform: scale(1);\n  }\n}\n/*\n *  Usage:\n *\n *    <div class=\"sk-spinner sk-spinner-circle\">\n *      <div class=\"sk-circle1 sk-circle\"></div>\n *      <div class=\"sk-circle2 sk-circle\"></div>\n *      <div class=\"sk-circle3 sk-circle\"></div>\n *      <div class=\"sk-circle4 sk-circle\"></div>\n *      <div class=\"sk-circle5 sk-circle\"></div>\n *      <div class=\"sk-circle6 sk-circle\"></div>\n *      <div class=\"sk-circle7 sk-circle\"></div>\n *      <div class=\"sk-circle8 sk-circle\"></div>\n *      <div class=\"sk-circle9 sk-circle\"></div>\n *      <div class=\"sk-circle10 sk-circle\"></div>\n *      <div class=\"sk-circle11 sk-circle\"></div>\n *      <div class=\"sk-circle12 sk-circle\"></div>\n *    </div>\n *\n */\n.sk-spinner-circle.sk-spinner {\n  margin: 0 auto;\n  width: 22px;\n  height: 22px;\n  position: relative;\n}\n.sk-spinner-circle .sk-circle {\n  width: 100%;\n  height: 100%;\n  position: absolute;\n  left: 0;\n  top: 0;\n}\n.sk-spinner-circle .sk-circle:before {\n  content: '';\n  display: block;\n  margin: 0 auto;\n  width: 20%;\n  height: 20%;\n  background-color: #f87012;\n  border-radius: 100%;\n  -webkit-animation: sk-circleBounceDelay 1.2s infinite ease-in-out;\n  animation: sk-circleBounceDelay 1.2s infinite ease-in-out;\n  /* Prevent first frame from flickering when animation starts */\n  -webkit-animation-fill-mode: both;\n  animation-fill-mode: both;\n}\n.sk-spinner-circle .sk-circle2 {\n  -webkit-transform: rotate(30deg);\n  -ms-transform: rotate(30deg);\n  transform: rotate(30deg);\n}\n.sk-spinner-circle .sk-circle3 {\n  -webkit-transform: rotate(60deg);\n  -ms-transform: rotate(60deg);\n  transform: rotate(60deg);\n}\n.sk-spinner-circle .sk-circle4 {\n  -webkit-transform: rotate(90deg);\n  -ms-transform: rotate(90deg);\n  transform: rotate(90deg);\n}\n.sk-spinner-circle .sk-circle5 {\n  -webkit-transform: rotate(120deg);\n  -ms-transform: rotate(120deg);\n  transform: rotate(120deg);\n}\n.sk-spinner-circle .sk-circle6 {\n  -webkit-transform: rotate(150deg);\n  -ms-transform: rotate(150deg);\n  transform: rotate(150deg);\n}\n.sk-spinner-circle .sk-circle7 {\n  -webkit-transform: rotate(180deg);\n  -ms-transform: rotate(180deg);\n  transform: rotate(180deg);\n}\n.sk-spinner-circle .sk-circle8 {\n  -webkit-transform: rotate(210deg);\n  -ms-transform: rotate(210deg);\n  transform: rotate(210deg);\n}\n.sk-spinner-circle .sk-circle9 {\n  -webkit-transform: rotate(240deg);\n  -ms-transform: rotate(240deg);\n  transform: rotate(240deg);\n}\n.sk-spinner-circle .sk-circle10 {\n  -webkit-transform: rotate(270deg);\n  -ms-transform: rotate(270deg);\n  transform: rotate(270deg);\n}\n.sk-spinner-circle .sk-circle11 {\n  -webkit-transform: rotate(300deg);\n  -ms-transform: rotate(300deg);\n  transform: rotate(300deg);\n}\n.sk-spinner-circle .sk-circle12 {\n  -webkit-transform: rotate(330deg);\n  -ms-transform: rotate(330deg);\n  transform: rotate(330deg);\n}\n.sk-spinner-circle .sk-circle2:before {\n  -webkit-animation-delay: -1.1s;\n  animation-delay: -1.1s;\n}\n.sk-spinner-circle .sk-circle3:before {\n  -webkit-animation-delay: -1s;\n  animation-delay: -1s;\n}\n.sk-spinner-circle .sk-circle4:before {\n  -webkit-animation-delay: -0.9s;\n  animation-delay: -0.9s;\n}\n.sk-spinner-circle .sk-circle5:before {\n  -webkit-animation-delay: -0.8s;\n  animation-delay: -0.8s;\n}\n.sk-spinner-circle .sk-circle6:before {\n  -webkit-animation-delay: -0.7s;\n  animation-delay: -0.7s;\n}\n.sk-spinner-circle .sk-circle7:before {\n  -webkit-animation-delay: -0.6s;\n  animation-delay: -0.6s;\n}\n.sk-spinner-circle .sk-circle8:before {\n  -webkit-animation-delay: -0.5s;\n  animation-delay: -0.5s;\n}\n.sk-spinner-circle .sk-circle9:before {\n  -webkit-animation-delay: -0.4s;\n  animation-delay: -0.4s;\n}\n.sk-spinner-circle .sk-circle10:before {\n  -webkit-animation-delay: -0.3s;\n  animation-delay: -0.3s;\n}\n.sk-spinner-circle .sk-circle11:before {\n  -webkit-animation-delay: -0.2s;\n  animation-delay: -0.2s;\n}\n.sk-spinner-circle .sk-circle12:before {\n  -webkit-animation-delay: -0.1s;\n  animation-delay: -0.1s;\n}\n@-webkit-keyframes sk-circleBounceDelay {\n  0%,\n  80%,\n  100% {\n    -webkit-transform: scale(0);\n    transform: scale(0);\n  }\n  40% {\n    -webkit-transform: scale(1);\n    transform: scale(1);\n  }\n}\n@keyframes sk-circleBounceDelay {\n  0%,\n  80%,\n  100% {\n    -webkit-transform: scale(0);\n    transform: scale(0);\n  }\n  40% {\n    -webkit-transform: scale(1);\n    transform: scale(1);\n  }\n}\n/*\n *  Usage:\n *\n *    <div class=\"sk-spinner sk-spinner-cube-grid\">\n *      <div class=\"sk-cube\"></div>\n *      <div class=\"sk-cube\"></div>\n *      <div class=\"sk-cube\"></div>\n *      <div class=\"sk-cube\"></div>\n *      <div class=\"sk-cube\"></div>\n *      <div class=\"sk-cube\"></div>\n *      <div class=\"sk-cube\"></div>\n *      <div class=\"sk-cube\"></div>\n *      <div class=\"sk-cube\"></div>\n *    </div>\n *\n */\n.sk-spinner-cube-grid {\n  /*\n   * Spinner positions\n   * 1 2 3\n   * 4 5 6\n   * 7 8 9\n   */\n}\n.sk-spinner-cube-grid.sk-spinner {\n  width: 30px;\n  height: 30px;\n  margin: 0 auto;\n}\n.sk-spinner-cube-grid .sk-cube {\n  width: 33%;\n  height: 33%;\n  background-color: #f87012;\n  float: left;\n  -webkit-animation: sk-cubeGridScaleDelay 1.3s infinite ease-in-out;\n  animation: sk-cubeGridScaleDelay 1.3s infinite ease-in-out;\n}\n.sk-spinner-cube-grid .sk-cube:nth-child(1) {\n  -webkit-animation-delay: 0.2s;\n  animation-delay: 0.2s;\n}\n.sk-spinner-cube-grid .sk-cube:nth-child(2) {\n  -webkit-animation-delay: 0.3s;\n  animation-delay: 0.3s;\n}\n.sk-spinner-cube-grid .sk-cube:nth-child(3) {\n  -webkit-animation-delay: 0.4s;\n  animation-delay: 0.4s;\n}\n.sk-spinner-cube-grid .sk-cube:nth-child(4) {\n  -webkit-animation-delay: 0.1s;\n  animation-delay: 0.1s;\n}\n.sk-spinner-cube-grid .sk-cube:nth-child(5) {\n  -webkit-animation-delay: 0.2s;\n  animation-delay: 0.2s;\n}\n.sk-spinner-cube-grid .sk-cube:nth-child(6) {\n  -webkit-animation-delay: 0.3s;\n  animation-delay: 0.3s;\n}\n.sk-spinner-cube-grid .sk-cube:nth-child(7) {\n  -webkit-animation-delay: 0s;\n  animation-delay: 0s;\n}\n.sk-spinner-cube-grid .sk-cube:nth-child(8) {\n  -webkit-animation-delay: 0.1s;\n  animation-delay: 0.1s;\n}\n.sk-spinner-cube-grid .sk-cube:nth-child(9) {\n  -webkit-animation-delay: 0.2s;\n  animation-delay: 0.2s;\n}\n@-webkit-keyframes sk-cubeGridScaleDelay {\n  0%,\n  70%,\n  100% {\n    -webkit-transform: scale3D(1, 1, 1);\n    transform: scale3D(1, 1, 1);\n  }\n  35% {\n    -webkit-transform: scale3D(0, 0, 1);\n    transform: scale3D(0, 0, 1);\n  }\n}\n@keyframes sk-cubeGridScaleDelay {\n  0%,\n  70%,\n  100% {\n    -webkit-transform: scale3D(1, 1, 1);\n    transform: scale3D(1, 1, 1);\n  }\n  35% {\n    -webkit-transform: scale3D(0, 0, 1);\n    transform: scale3D(0, 0, 1);\n  }\n}\n/*\n *  Usage:\n *\n *    <div class=\"sk-spinner sk-spinner-wordpress\">\n *      <span class=\"sk-inner-circle\"></span>\n *    </div>\n *\n */\n.sk-spinner-wordpress.sk-spinner {\n  background-color: #f87012;\n  width: 30px;\n  height: 30px;\n  border-radius: 30px;\n  position: relative;\n  margin: 0 auto;\n  -webkit-animation: sk-innerCircle 1s linear infinite;\n  animation: sk-innerCircle 1s linear infinite;\n}\n.sk-spinner-wordpress .sk-inner-circle {\n  display: block;\n  background-color: #fff;\n  width: 8px;\n  height: 8px;\n  position: absolute;\n  border-radius: 8px;\n  top: 5px;\n  left: 5px;\n}\n@-webkit-keyframes sk-innerCircle {\n  0% {\n    -webkit-transform: rotate(0);\n    transform: rotate(0);\n  }\n  100% {\n    -webkit-transform: rotate(360deg);\n    transform: rotate(360deg);\n  }\n}\n@keyframes sk-innerCircle {\n  0% {\n    -webkit-transform: rotate(0);\n    transform: rotate(0);\n  }\n  100% {\n    -webkit-transform: rotate(360deg);\n    transform: rotate(360deg);\n  }\n}\n/*\n *  Usage:\n *\n *    <div class=\"sk-spinner sk-spinner-fading-circle\">\n *      <div class=\"sk-circle1 sk-circle\"></div>\n *      <div class=\"sk-circle2 sk-circle\"></div>\n *      <div class=\"sk-circle3 sk-circle\"></div>\n *      <div class=\"sk-circle4 sk-circle\"></div>\n *      <div class=\"sk-circle5 sk-circle\"></div>\n *      <div class=\"sk-circle6 sk-circle\"></div>\n *      <div class=\"sk-circle7 sk-circle\"></div>\n *      <div class=\"sk-circle8 sk-circle\"></div>\n *      <div class=\"sk-circle9 sk-circle\"></div>\n *      <div class=\"sk-circle10 sk-circle\"></div>\n *      <div class=\"sk-circle11 sk-circle\"></div>\n *      <div class=\"sk-circle12 sk-circle\"></div>\n *    </div>\n *\n */\n.sk-spinner-fading-circle.sk-spinner {\n  margin: 0 auto;\n  width: 22px;\n  height: 22px;\n  position: relative;\n}\n.sk-spinner-fading-circle .sk-circle {\n  width: 100%;\n  height: 100%;\n  position: absolute;\n  left: 0;\n  top: 0;\n}\n.sk-spinner-fading-circle .sk-circle:before {\n  content: '';\n  display: block;\n  margin: 0 auto;\n  width: 18%;\n  height: 18%;\n  background-color: #f87012;\n  border-radius: 100%;\n  -webkit-animation: sk-circleFadeDelay 1.2s infinite ease-in-out;\n  animation: sk-circleFadeDelay 1.2s infinite ease-in-out;\n  /* Prevent first frame from flickering when animation starts */\n  -webkit-animation-fill-mode: both;\n  animation-fill-mode: both;\n}\n.sk-spinner-fading-circle .sk-circle2 {\n  -webkit-transform: rotate(30deg);\n  -ms-transform: rotate(30deg);\n  transform: rotate(30deg);\n}\n.sk-spinner-fading-circle .sk-circle3 {\n  -webkit-transform: rotate(60deg);\n  -ms-transform: rotate(60deg);\n  transform: rotate(60deg);\n}\n.sk-spinner-fading-circle .sk-circle4 {\n  -webkit-transform: rotate(90deg);\n  -ms-transform: rotate(90deg);\n  transform: rotate(90deg);\n}\n.sk-spinner-fading-circle .sk-circle5 {\n  -webkit-transform: rotate(120deg);\n  -ms-transform: rotate(120deg);\n  transform: rotate(120deg);\n}\n.sk-spinner-fading-circle .sk-circle6 {\n  -webkit-transform: rotate(150deg);\n  -ms-transform: rotate(150deg);\n  transform: rotate(150deg);\n}\n.sk-spinner-fading-circle .sk-circle7 {\n  -webkit-transform: rotate(180deg);\n  -ms-transform: rotate(180deg);\n  transform: rotate(180deg);\n}\n.sk-spinner-fading-circle .sk-circle8 {\n  -webkit-transform: rotate(210deg);\n  -ms-transform: rotate(210deg);\n  transform: rotate(210deg);\n}\n.sk-spinner-fading-circle .sk-circle9 {\n  -webkit-transform: rotate(240deg);\n  -ms-transform: rotate(240deg);\n  transform: rotate(240deg);\n}\n.sk-spinner-fading-circle .sk-circle10 {\n  -webkit-transform: rotate(270deg);\n  -ms-transform: rotate(270deg);\n  transform: rotate(270deg);\n}\n.sk-spinner-fading-circle .sk-circle11 {\n  -webkit-transform: rotate(300deg);\n  -ms-transform: rotate(300deg);\n  transform: rotate(300deg);\n}\n.sk-spinner-fading-circle .sk-circle12 {\n  -webkit-transform: rotate(330deg);\n  -ms-transform: rotate(330deg);\n  transform: rotate(330deg);\n}\n.sk-spinner-fading-circle .sk-circle2:before {\n  -webkit-animation-delay: -1.1s;\n  animation-delay: -1.1s;\n}\n.sk-spinner-fading-circle .sk-circle3:before {\n  -webkit-animation-delay: -1s;\n  animation-delay: -1s;\n}\n.sk-spinner-fading-circle .sk-circle4:before {\n  -webkit-animation-delay: -0.9s;\n  animation-delay: -0.9s;\n}\n.sk-spinner-fading-circle .sk-circle5:before {\n  -webkit-animation-delay: -0.8s;\n  animation-delay: -0.8s;\n}\n.sk-spinner-fading-circle .sk-circle6:before {\n  -webkit-animation-delay: -0.7s;\n  animation-delay: -0.7s;\n}\n.sk-spinner-fading-circle .sk-circle7:before {\n  -webkit-animation-delay: -0.6s;\n  animation-delay: -0.6s;\n}\n.sk-spinner-fading-circle .sk-circle8:before {\n  -webkit-animation-delay: -0.5s;\n  animation-delay: -0.5s;\n}\n.sk-spinner-fading-circle .sk-circle9:before {\n  -webkit-animation-delay: -0.4s;\n  animation-delay: -0.4s;\n}\n.sk-spinner-fading-circle .sk-circle10:before {\n  -webkit-animation-delay: -0.3s;\n  animation-delay: -0.3s;\n}\n.sk-spinner-fading-circle .sk-circle11:before {\n  -webkit-animation-delay: -0.2s;\n  animation-delay: -0.2s;\n}\n.sk-spinner-fading-circle .sk-circle12:before {\n  -webkit-animation-delay: -0.1s;\n  animation-delay: -0.1s;\n}\n@-webkit-keyframes sk-circleFadeDelay {\n  0%,\n  39%,\n  100% {\n    opacity: 0;\n  }\n  40% {\n    opacity: 1;\n  }\n}\n@keyframes sk-circleFadeDelay {\n  0%,\n  39%,\n  100% {\n    opacity: 0;\n  }\n  40% {\n    opacity: 1;\n  }\n}\ntable.page_edit_list {\n  border-collapse: collapse;\n  border: 1px solid #c2c2c2;\n  width: 100%;\n}\ntable.page_edit_list th,\ntable.page_edit_list td {\n  padding: 4px;\n  border: 1px solid #c2c2c2;\n}\ntable.page_edit_list thead th {\n  background-color: #2B528A;\n  padding: 8px;\n  color: #FFF;\n  margin: 0px;\n  font-size: 12px;\n}\n.tt-query {\n  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);\n  -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);\n  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);\n}\n.tt-hint {\n  color: #999;\n}\n.tt-menu {\n  /* used to be tt-dropdown-menu in older versions */\n  width: 422px;\n  margin-top: 4px;\n  padding: 4px 0;\n  background-color: #fff;\n  border: 1px solid #ccc;\n  border: 1px solid rgba(0, 0, 0, 0.2);\n  -webkit-border-radius: 4px;\n  -moz-border-radius: 4px;\n  border-radius: 4px;\n  -webkit-box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);\n  -moz-box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);\n  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);\n}\n.tt-suggestion {\n  padding: 3px 20px;\n  line-height: 24px;\n  margin: 0;\n}\n.tt-suggestion .tt-highlight {\n  font-weight: normal;\n}\n.tt-suggestion .text .tt-highlight {\n  font-weight: bold;\n}\n.tt-suggestion p {\n  margin: 0;\n}\n.tt-suggestion .path {\n  font-size: 9px;\n}\n.tt-suggestion.tt-cursor,\n.tt-suggestion:hover {\n  color: #fff;\n  background-color: #0097cf;\n}\n.empty-message {\n  margin: 10px;\n}\n/*\n *\n *   INSPINIA Landing Page - Responsive Admin Theme\n *   Copyright 2014 Webapplayers.com\n *\n*/\n/* GLOBAL STYLES\n-------------------------------------------------- */\n/* PACE PLUGIN\n-------------------------------------------------- */\n.landing-page.pace .pace-progress {\n  background: #fff;\n  position: fixed;\n  z-index: 2000;\n  top: 0;\n  left: 0;\n  height: 2px;\n  -webkit-transition: width 1s;\n  -moz-transition: width 1s;\n  -o-transition: width 1s;\n  transition: width 1s;\n}\n.pace-inactive {\n  display: none;\n}\nbody.landing-page {\n  color: #737578;\n  font-family: 'Open Sans', helvetica, arial, sans-serif;\n  background-color: #fff;\n}\n.landing-page {\n  /* CUSTOMIZE THE NAVBAR\n  -------------------------------------------------- */\n  /* Flip around the padding for proper display in narrow viewports */\n  /* BACKGROUNDS SLIDER\n  -------------------------------------------------- */\n  /* CUSTOMIZE THE CAROUSEL\n  -------------------------------------------------- */\n  /* Carousel base class */\n  /* Since positioning the image, we need to help out the caption */\n  /* Declare heights because of positioning of img element */\n  /* Sections\n  ------------------------- */\n  /* Buttons - only primary custom button\n  ------------------------- */\n  /* RESPONSIVE CSS\n  -------------------------------------------------- */\n}\n.landing-page span.navy {\n  color: #f87012;\n}\n.landing-page p.text-color {\n  color: #737578;\n}\n.landing-page a.navy-link {\n  color: #f87012;\n  text-decoration: none;\n}\n.landing-page a.navy-link:hover {\n  color: #ea6407;\n}\n.landing-page section p {\n  color: #aeaeae;\n  font-size: 13px;\n}\n.landing-page address {\n  font-size: 13px;\n}\n.landing-page h1 {\n  margin-top: 10px;\n  font-size: 30px;\n  font-weight: 200;\n}\n.landing-page .navy-line {\n  width: 60px;\n  height: 1px;\n  margin: 60px auto 0;\n  border-bottom: 2px solid #f87012;\n}\n.landing-page .navbar-wrapper {\n  position: absolute;\n  top: 0;\n  right: 0;\n  left: 0;\n  z-index: 200;\n}\n.landing-page .navbar-wrapper > .container {\n  padding-right: 0;\n  padding-left: 0;\n}\n.landing-page .navbar-wrapper .navbar {\n  padding-right: 15px;\n  padding-left: 15px;\n}\n.landing-page .navbar-default.navbar-scroll {\n  background-color: #fff;\n  border-color: #fff;\n  padding: 15px 0;\n}\n.landing-page .navbar-default {\n  background-color: transparent;\n  border-color: transparent;\n  transition: all 0.3s ease-in-out 0s;\n}\n.landing-page .navbar-default .nav li a {\n  color: #fff;\n  font-family: 'Open Sans', helvetica, arial, sans-serif;\n  font-weight: 700;\n  letter-spacing: 1px;\n  text-transform: uppercase;\n  font-size: 14px;\n}\n.landing-page .navbar-nav > li > a {\n  padding-top: 25px;\n  border-top: 6px solid transparent;\n}\n.landing-page .navbar-default .navbar-nav > .active > a,\n.landing-page .navbar-default .navbar-nav > .active > a:hover {\n  background: transparent;\n  color: #fff;\n  border-top: 6px solid #f87012;\n}\n.landing-page .navbar-default .navbar-nav > li > a:hover,\n.landing-page .navbar-default .navbar-nav > li > a:focus {\n  color: #f87012;\n  background: inherit;\n}\n.landing-page .navbar-default .navbar-nav > .active > a:focus {\n  background: transparent;\n  color: #fff;\n}\n.landing-page .navbar-default .navbar-nav > .active > a:focus {\n  background: transparent;\n  color: #ffffff;\n}\n.landing-page .navbar-default.navbar-scroll .navbar-nav > .active > a:focus {\n  background: transparent;\n  color: inherit;\n}\n.landing-page .navbar-default .navbar-brand:hover,\n.landing-page .navbar-default .navbar-brand:focus {\n  background: #ea6407;\n  color: #fff;\n}\n.landing-page .navbar-default .navbar-brand {\n  color: #fff;\n  height: auto;\n  display: block;\n  font-size: 14px;\n  background: #f87012;\n  padding: 15px 20px 15px 20px;\n  border-radius: 0 0 5px 5px;\n  font-weight: 700;\n  transition: all 0.3s ease-in-out 0s;\n}\n.landing-page .navbar-scroll.navbar-default .nav li a {\n  color: #737578;\n}\n.landing-page .navbar-scroll.navbar-default .nav li a:hover {\n  color: #f87012;\n}\n.landing-page .navbar-wrapper .navbar.navbar-scroll {\n  padding-top: 0;\n  padding-bottom: 0px;\n  border-bottom: 1px solid #dee0e2;\n  border-radius: 0;\n}\n.landing-page .nav > li.active {\n  border: none;\n  background: inherit;\n}\n.landing-page .nav > li > a {\n  padding: 25px 10px 15px 10px;\n}\n.landing-page .navbar-scroll .navbar-nav > li > a {\n  padding: 20px 10px;\n}\n.landing-page .navbar-default .navbar-nav > .active > a,\n.landing-page .navbar-default .navbar-nav > .active > a:hover {\n  border-top: 6px solid #f87012;\n}\n.landing-page .navbar-fixed-top {\n  border: none !important;\n}\n.landing-page .navbar-fixed-top.navbar-scroll {\n  border-bottom: 1px solid #dee0e2 !important;\n}\n.landing-page .navbar.navbar-scroll .navbar-brand {\n  margin-top: 15px;\n  border-radius: 5px;\n  font-size: 12px;\n  padding: 10px;\n  height: auto;\n}\n.landing-page .header-back {\n  height: 470px;\n  width: 100%;\n}\n.landing-page .header-back.one {\n  background: url('../img/landing/header_one.jpg') 50% 0 no-repeat;\n}\n.landing-page .header-back.two {\n  background: url('../img/landing/header_two.jpg') 50% 0 no-repeat;\n}\n.landing-page .carousel {\n  height: 470px;\n}\n.landing-page .carousel-caption {\n  z-index: 10;\n}\n.landing-page .carousel .item {\n  height: 470px;\n  background-color: #777;\n}\n.landing-page .carousel-inner > .item > img {\n  position: absolute;\n  top: 0;\n  left: 0;\n  min-width: 100%;\n  height: 470px;\n}\n.landing-page .carousel-fade .carousel-inner .item {\n  opacity: 0;\n  -webkit-transition-property: opacity;\n  transition-property: opacity;\n}\n.landing-page .carousel-fade .carousel-inner .active {\n  opacity: 1;\n}\n.landing-page .carousel-fade .carousel-inner .active.left,\n.landing-page .carousel-fade .carousel-inner .active.right {\n  left: 0;\n  opacity: 0;\n  z-index: 1;\n}\n.landing-page .carousel-fade .carousel-inner .next.left,\n.landing-page .carousel-fade .carousel-inner .prev.right {\n  opacity: 1;\n}\n.landing-page .carousel-fade .carousel-control {\n  z-index: 2;\n}\n.landing-page .carousel-control.left,\n.landing-page .carousel-control.right {\n  background: none;\n}\n.landing-page .carousel-control {\n  width: 6%;\n}\n.landing-page .carousel-inner .container {\n  position: relative;\n}\n.landing-page .carousel-inner {\n  overflow: visible;\n}\n.landing-page .carousel-caption {\n  position: absolute;\n  top: 100px;\n  left: 0;\n  bottom: auto;\n  right: auto;\n  text-align: left;\n}\n.landing-page .carousel-caption {\n  position: absolute;\n  top: 100px;\n  left: 0;\n  bottom: auto;\n  right: auto;\n  text-align: left;\n}\n.landing-page .carousel-caption.blank {\n  top: 140px;\n}\n.landing-page .carousel-image {\n  position: absolute;\n  right: 10px;\n  top: 150px;\n}\n.landing-page .carousel-indicators {\n  padding-right: 60px;\n}\n.landing-page .carousel-caption h1 {\n  font-weight: 700;\n  font-size: 38px;\n  text-transform: uppercase;\n  text-shadow: none;\n  letter-spacing: -1.5px;\n}\n.landing-page .carousel-caption p {\n  font-weight: 700;\n  text-transform: uppercase;\n  text-shadow: none;\n}\n.landing-page .caption-link {\n  color: #fff;\n  margin-left: 10px;\n  text-transform: capitalize;\n  font-weight: 400;\n}\n.landing-page .caption-link:hover {\n  text-decoration: none;\n  color: inherit;\n}\n.landing-page .services {\n  padding-top: 60px;\n}\n.landing-page .services h2 {\n  font-size: 20px;\n  letter-spacing: -1px;\n  font-weight: 600;\n  text-transform: uppercase;\n}\n.landing-page .features-block {\n  margin-top: 40px;\n}\n.landing-page .features-text {\n  margin-top: 40px;\n}\n.landing-page .features small {\n  color: #f87012;\n}\n.landing-page .features h2 {\n  font-size: 18px;\n  margin-top: 5px;\n}\n.landing-page .features-text-alone {\n  margin: 40px 0;\n}\n.landing-page .features-text-alone h1 {\n  font-weight: 200;\n}\n.landing-page .features-icon {\n  color: #f87012;\n  font-size: 40px;\n}\n.landing-page .navy-section {\n  margin-top: 60px;\n  background: #f87012;\n  color: #fff;\n  padding: 20px 0;\n}\n.landing-page .gray-section {\n  background: #f4f4f4;\n  margin-top: 60px;\n}\n.landing-page .team-member {\n  text-align: center;\n}\n.landing-page .team-member img {\n  margin: auto;\n}\n.landing-page .social-icon a {\n  background: #f87012;\n  color: #fff;\n  padding: 4px 8px;\n  height: 28px;\n  width: 28px;\n  display: block;\n  border-radius: 50px;\n}\n.landing-page .social-icon a:hover {\n  background: #ea6407;\n}\n.landing-page .img-small {\n  height: 88px;\n  width: 88px;\n}\n.landing-page .pricing-plan {\n  margin: 20px 30px 0 30px;\n  border-radius: 4px;\n}\n.landing-page .pricing-plan.selected {\n  transform: scale(1.1);\n  background: #f4f4f4 ;\n}\n.landing-page .pricing-plan li {\n  padding: 10px 16px;\n  border-top: 1px solid #dee0e2;\n  text-align: center;\n  color: #aeaeae;\n}\n.landing-page .pricing-plan .pricing-price span {\n  font-weight: 700;\n  color: #f87012;\n}\n.landing-page li.pricing-desc {\n  font-size: 13px;\n  border-top: none;\n  padding: 20px 16px ;\n}\n.landing-page li.pricing-title {\n  background: #f87012;\n  color: #fff;\n  padding: 10px;\n  border-radius: 4px 4px 0 0;\n  font-size: 22px;\n  font-weight: 600;\n}\n.landing-page .testimonials {\n  padding-top: 80px;\n  padding-bottom: 90px;\n  background-color: #f87012;\n  background-image: url('../img/landing/avatar_all.png');\n}\n.landing-page .big-icon {\n  font-size: 56px;\n}\n.landing-page .features .big-icon {\n  color: #f87012;\n}\n.landing-page .contact {\n  background-image: url('../img/landing/word_map.png');\n  background-position: 50% 50%;\n  background-repeat: no-repeat;\n  margin-top: 60px;\n}\n.landing-page section.timeline {\n  padding-bottom: 30px;\n}\n.landing-page section.comments {\n  padding-bottom: 80px;\n}\n.landing-page .comments-avatar {\n  margin-top: 25px;\n  margin-left: 22px;\n}\n.landing-page .comments-avatar .commens-name {\n  font-weight: 600;\n  font-size: 14px;\n}\n.landing-page .comments-avatar img {\n  width: 42px;\n  height: 42px;\n  border-radius: 50%;\n  margin-right: 10px;\n}\n.landing-page .bubble {\n  position: relative;\n  height: 120px;\n  padding: 20px;\n  background: #FFFFFF;\n  -webkit-border-radius: 10px;\n  -moz-border-radius: 10px;\n  border-radius: 10px;\n  font-style: italic;\n  font-size: 14px;\n}\n.landing-page .bubble:after {\n  content: '';\n  position: absolute;\n  border-style: solid;\n  border-width: 15px 14px 0;\n  border-color: #FFFFFF transparent;\n  display: block;\n  width: 0;\n  z-index: 1;\n  bottom: -15px;\n  left: 30px;\n}\n.landing-page .btn-primary.btn-outline:hover,\n.landing-page .btn-success.btn-outline:hover,\n.landing-page .btn-info.btn-outline:hover,\n.landing-page .btn-warning.btn-outline:hover,\n.landing-page .btn-danger.btn-outline:hover {\n  color: #fff;\n}\n.landing-page .btn-primary {\n  background-color: #f87012;\n  border-color: #f87012;\n  color: #FFFFFF;\n  font-size: 14px;\n  padding: 10px 20px;\n  font-weight: 600;\n}\n.landing-page .btn-primary:hover,\n.landing-page .btn-primary:focus,\n.landing-page .btn-primary:active,\n.landing-page .btn-primary.active,\n.landing-page .open .dropdown-toggle.btn-primary {\n  background-color: #ea6407;\n  border-color: #ea6407;\n  color: #FFFFFF;\n}\n.landing-page .btn-primary:active,\n.landing-page .btn-primary.active,\n.landing-page .open .dropdown-toggle.btn-primary {\n  background-image: none;\n}\n.landing-page .btn-primary.disabled,\n.landing-page .btn-primary.disabled:hover,\n.landing-page .btn-primary.disabled:focus,\n.landing-page .btn-primary.disabled:active,\n.landing-page .btn-primary.disabled.active,\n.landing-page .btn-primary[disabled],\n.landing-page .btn-primary[disabled]:hover,\n.landing-page .btn-primary[disabled]:focus,\n.landing-page .btn-primary[disabled]:active,\n.landing-page .btn-primary.active[disabled],\n.landing-page fieldset[disabled] .btn-primary,\n.landing-page fieldset[disabled] .btn-primary:hover,\n.landing-page fieldset[disabled] .btn-primary:focus,\n.landing-page fieldset[disabled] .btn-primary:active,\n.landing-page fieldset[disabled] .btn-primary.active {\n  background-color: #1dc5a3;\n  border-color: #1dc5a3;\n}\n@media (min-width: 768px) {\n  .landing-page {\n    /* Navbar positioning foo */\n    /* The navbar becomes detached from the top, so we round the corners */\n    /* Bump up size of carousel content */\n  }\n  .landing-page .navbar-wrapper {\n    margin-top: 20px;\n  }\n  .landing-page .navbar-wrapper .container {\n    padding-right: 15px;\n    padding-left: 15px;\n  }\n  .landing-page .navbar-wrapper .navbar {\n    padding-right: 0;\n    padding-left: 0;\n  }\n  .landing-page .navbar-wrapper .navbar {\n    border-radius: 4px;\n  }\n  .landing-page .carousel-caption p {\n    margin-bottom: 20px;\n    font-size: 14px;\n    line-height: 1.4;\n  }\n  .landing-page .featurette-heading {\n    font-size: 50px;\n  }\n}\n@media (max-width: 992px) {\n  .landing-page .carousel-image {\n    display: none;\n  }\n}\n@media (max-width: 768px) {\n  .landing-page .carousel-caption,\n  .landing-page .carousel-caption.blank {\n    left: 5%;\n    top: 80px;\n  }\n  .landing-page .carousel-caption h1 {\n    font-size: 28px;\n  }\n  .landing-page .navbar.navbar-scroll .navbar-brand {\n    margin-top: 6px;\n  }\n  .landing-page .navbar-default {\n    background-color: #fff;\n    border-color: #fff;\n    padding: 15px 0;\n  }\n  .landing-page .navbar-default .navbar-nav > .active > a:focus {\n    background: transparent;\n    color: inherit;\n  }\n  .landing-page .navbar-default .nav li a {\n    color: #737578;\n  }\n  .landing-page .navbar-default .nav li a:hover {\n    color: #f87012;\n  }\n  .landing-page .navbar-wrapper .navbar {\n    padding-top: 0;\n    padding-bottom: 5px;\n    border-bottom: 1px solid #dee0e2;\n    border-radius: 0;\n  }\n  .landing-page .nav > li > a {\n    padding: 25px 10px 15px 10px;\n  }\n  .landing-page .navbar-nav > li > a {\n    padding: 20px 10px;\n  }\n  .landing-page .navbar .navbar-brand {\n    margin-top: 6px;\n    border-radius: 5px;\n    font-size: 12px;\n    padding: 10px;\n    height: auto;\n  }\n  .landing-page .navbar-wrapper .navbar {\n    padding-left: 15px;\n    padding-right: 5px;\n  }\n  .landing-page .navbar-default .navbar-nav > .active > a,\n  .landing-page .navbar-default .navbar-nav > .active > a:hover {\n    color: inherit;\n  }\n  .landing-page .carousel-control {\n    display: none;\n  }\n}\n@media (min-width: 992px) {\n  .landing-page .featurette-heading {\n    margin-top: 120px;\n  }\n}\nbody.rtls {\n  /* Theme config */\n}\nbody.rtls #page-wrapper {\n  margin: 0 220px 0 0;\n}\nbody.rtls .nav-second-level li a {\n  padding: 7px 35px 7px 10px;\n}\nbody.rtls .ibox-title h5 {\n  float: right;\n}\nbody.rtls .pull-right {\n  float: left !important;\n}\nbody.rtls .pull-left {\n  float: right !important;\n}\nbody.rtls .ibox-tools {\n  float: left;\n}\nbody.rtls .stat-percent {\n  float: left;\n}\nbody.rtls .navbar-right {\n  float: left !important;\n}\nbody.rtls .navbar-top-links li:last-child {\n  margin-left: 40px;\n  margin-right: 0;\n}\nbody.rtls .minimalize-styl-2 {\n  float: right;\n  margin: 14px 20px 5px 5px;\n}\nbody.rtls .feed-element > .pull-left {\n  margin-left: 10px;\n  margin-right: 0;\n}\nbody.rtls .timeline-item .date {\n  text-align: left;\n}\nbody.rtls .timeline-item .date i {\n  left: 0;\n  right: auto;\n}\nbody.rtls .timeline-item .content {\n  border-right: 1px solid #e7eaec;\n  border-left: none;\n}\nbody.rtls .theme-config {\n  left: 0;\n  right: auto;\n}\nbody.rtls .spin-icon {\n  border-radius: 0 20px 20px 0;\n}\nbody.rtls .toast-close-button {\n  float: left;\n}\nbody.rtls #toast-container > .toast:before {\n  margin: auto -1.5em auto 0.5em;\n}\nbody.rtls #toast-container > div {\n  padding: 15px 50px 15px 15px;\n}\nbody.rtls .center-orientation .vertical-timeline-icon i {\n  margin-left: 0;\n  margin-right: -12px;\n}\nbody.rtls .vertical-timeline-icon i {\n  right: 50%;\n  left: auto;\n  margin-left: auto;\n  margin-right: -12px;\n}\nbody.rtls .file-box {\n  float: right;\n}\nbody.rtls ul.notes li {\n  float: right;\n}\nbody.rtls .chat-users,\nbody.rtls .chat-statistic {\n  margin-right: -30px;\n  margin-left: auto;\n}\nbody.rtls .dropdown-menu > li > a {\n  text-align: right;\n}\nbody.rtls .b-r {\n  border-left: 1px solid #e7eaec;\n  border-right: none;\n}\nbody.rtls .dd-list .dd-list {\n  padding-right: 30px;\n  padding-left: 0;\n}\nbody.rtls .dd-item > button {\n  float: right;\n}\nbody.rtls .theme-config-box {\n  margin-left: -220px;\n  margin-right: 0;\n}\nbody.rtls .theme-config-box.show {\n  margin-left: 0;\n  margin-right: 0;\n}\nbody.rtls .spin-icon {\n  right: 0;\n  left: auto;\n}\nbody.rtls .skin-setttings {\n  margin-right: 40px;\n  margin-left: 0;\n}\nbody.rtls .skin-setttings {\n  direction: ltr;\n}\nbody.rtls .footer.fixed {\n  margin-right: 220px;\n  margin-left: 0;\n}\n@media (max-width: 992px) {\n  body.rtls .chat-users,\n  body.rtls .chat-statistic {\n    margin-right: 0px;\n  }\n}\nbody.rtls.mini-navbar .footer.fixed,\nbody.body-small.mini-navbar .footer.fixed {\n  margin: 0 70px 0 0;\n}\nbody.rtls.mini-navbar.fixed-sidebar .footer.fixed,\nbody.body-small.mini-navbar .footer.fixed {\n  margin: 0 0 0 0;\n}\nbody.rtls.top-navigation .navbar-toggle {\n  float: right;\n  margin-left: 15px;\n  margin-right: 15px;\n}\n.body-small.rtls.top-navigation .navbar-header {\n  float: none;\n}\nbody.rtls.top-navigation #page-wrapper {\n  margin: 0;\n}\nbody.rtls.mini-navbar #page-wrapper {\n  margin: 0 70px 0 0;\n}\nbody.rtls.mini-navbar.fixed-sidebar #page-wrapper {\n  margin: 0 0 0 0;\n}\nbody.rtls.body-small.fixed-sidebar.mini-navbar #page-wrapper {\n  margin: 0 220px 0 0;\n}\nbody.rtls.body-small.fixed-sidebar.mini-navbar .navbar-static-side {\n  width: 220px;\n}\n.body-small.rtls .navbar-fixed-top {\n  margin-right: 0px;\n}\n.body-small.rtls .navbar-header {\n  float: right;\n}\nbody.rtls .navbar-top-links li:last-child {\n  margin-left: 20px;\n}\nbody.rtls .top-navigation #page-wrapper,\nbody.rtls.mini-navbar .top-navigation #page-wrapper,\nbody.rtls.mini-navbar.top-navigation #page-wrapper {\n  margin: 0;\n}\nbody.rtls .top-navigation .footer.fixed,\nbody.rtls.top-navigation .footer.fixed {\n  margin: 0;\n}\n@media (max-width: 768px) {\n  body.rtls .navbar-top-links li:last-child {\n    margin-left: 20px;\n  }\n  .body-small.rtls #page-wrapper {\n    position: inherit;\n    margin: 0 0 0 0px;\n    min-height: 1000px;\n  }\n  .body-small.rtls .navbar-static-side {\n    display: none;\n    z-index: 2001;\n    position: absolute;\n    width: 70px;\n  }\n  .body-small.rtls.mini-navbar .navbar-static-side {\n    display: block;\n  }\n  .rtls.fixed-sidebar.body-small .navbar-static-side {\n    display: none;\n    z-index: 2001;\n    position: fixed;\n    width: 220px;\n  }\n  .rtls.fixed-sidebar.body-small.mini-navbar .navbar-static-side {\n    display: block;\n  }\n}\n.rtls .ltr-support {\n  direction: ltr;\n}\n/*\n *\n *   This is style for skin config\n *   Use only in demo theme\n *\n*/\n.theme-config {\n  position: absolute;\n  top: 90px;\n  right: 0px;\n  overflow: hidden;\n}\n.theme-config-box {\n  margin-right: -220px;\n  position: relative;\n  z-index: 2000;\n  transition-duration: 0.8s;\n}\n.theme-config-box.show {\n  margin-right: 0px;\n}\n.spin-icon {\n  background: #f87012;\n  position: absolute;\n  padding: 7px 10px 7px 13px;\n  border-radius: 20px 0px 0px 20px;\n  font-size: 16px;\n  top: 0;\n  left: 0px;\n  width: 40px;\n  color: #fff;\n  cursor: pointer;\n}\n.skin-setttings {\n  width: 220px;\n  margin-left: 40px;\n  background: #f3f3f4;\n}\n.skin-setttings .title {\n  background: #efefef;\n  text-align: center;\n  text-transform: uppercase;\n  font-weight: 600;\n  display: block;\n  padding: 10px 15px;\n  font-size: 12px;\n}\n.setings-item {\n  padding: 10px 30px;\n}\n.setings-item.skin {\n  text-align: center;\n}\n.setings-item .switch {\n  float: right;\n}\n.skin-name a {\n  text-transform: uppercase;\n}\n.setings-item a {\n  color: #fff;\n}\n.default-skin,\n.blue-skin,\n.ultra-skin,\n.yellow-skin {\n  text-align: center;\n}\n.default-skin {\n  font-weight: 600;\n  background: #1ab394;\n}\n.default-skin:hover {\n  background: #199d82;\n}\n.blue-skin {\n  font-weight: 600;\n  background: url(\"../external/patterns/header-profile-skin-1.png\") repeat scroll 0 0;\n}\n.blue-skin:hover {\n  background: #0d8ddb;\n}\n.yellow-skin {\n  font-weight: 600;\n  background: url(\"../external/patterns/header-profile-skin-3.png\") repeat scroll 0 100%;\n}\n.yellow-skin:hover {\n  background: #ce8735;\n}\n.ultra-skin {\n  font-weight: 600;\n  background: url(\"../external/patterns/header-profile-skin-2.png\") repeat scroll 0 0;\n}\n.ultra-skin:hover {\n  background: #1a2d40;\n}\n/*\n *\n *   SKIN 1 - INSPINIA - Responsive Admin Theme\n *   NAME - Blue light\n *\n*/\n.skin-1 .minimalize-styl-2 {\n  margin: 14px 5px 5px 30px;\n}\n.skin-1 .navbar-top-links li:last-child {\n  margin-right: 30px;\n}\n.skin-1.fixed-nav .minimalize-styl-2 {\n  margin: 14px 5px 5px 15px;\n}\n.skin-1 .spin-icon {\n  background: #0e9aef !important;\n}\n.skin-1 .nav-header {\n  background: #0e9aef;\n  background: url('../external/patterns/header-profile-skin-1.png');\n}\n.skin-1.mini-navbar .nav-second-level {\n  background: #3e495f;\n}\n.skin-1 .breadcrumb {\n  background: transparent;\n}\n.skin-1 .page-heading {\n  border: none;\n}\n.skin-1 .nav > li.active {\n  background: #3a4459;\n}\n.skin-1 .nav > li > a {\n  color: #9ea6b9;\n}\n.skin-1 .nav > li.active > a {\n  color: #fff;\n}\n.skin-1 .navbar-minimalize {\n  background: #0e9aef;\n  border-color: #0e9aef;\n}\nbody.skin-1 {\n  background: #3e495f;\n}\n.skin-1 .navbar-static-top {\n  background: #ffffff;\n}\n.skin-1 .dashboard-header {\n  background: transparent;\n  border-bottom: none !important;\n  border-top: none;\n  padding: 20px 30px 10px 30px;\n}\n.fixed-nav.skin-1 .navbar-fixed-top {\n  background: #fff;\n}\n.skin-1 .wrapper-content {\n  padding: 30px 15px;\n}\n.skin-1 #page-wrapper {\n  background: #f4f6fa;\n}\n.skin-1 .ibox-title,\n.skin-1 .ibox-content {\n  border-width: 1px;\n}\n.skin-1 .ibox-content:last-child {\n  border-style: solid solid solid solid;\n}\n.skin-1 .nav > li.active {\n  border: none;\n}\n.skin-1 .nav-header {\n  padding: 35px 25px 25px 25px;\n}\n.skin-1 .nav-header a.dropdown-toggle {\n  color: #fff;\n  margin-top: 10px;\n}\n.skin-1 .nav-header a.dropdown-toggle .text-muted {\n  color: #fff;\n  opacity: 0.8;\n}\n.skin-1 .profile-element {\n  text-align: center;\n}\n.skin-1 .img-circle {\n  border-radius: 5px;\n}\n.skin-1 .navbar-default .nav > li > a:hover,\n.skin-1 .navbar-default .nav > li > a:focus {\n  background: #3a4459;\n  color: #fff;\n}\n.skin-1 .nav.nav-tabs > li.active > a {\n  color: #555;\n}\n.skin-1 .nav.nav-tabs > li.active {\n  background: transparent;\n}\n/*\n *\n *   SKIN 2 - INSPINIA - Responsive Admin Theme\n *   NAME - Inspinia Ultra\n *\n*/\nbody.skin-2 {\n  color: #565758 !important;\n}\n.skin-2 .minimalize-styl-2 {\n  margin: 14px 5px 5px 25px;\n}\n.skin-2 .navbar-top-links li:last-child {\n  margin-right: 25px;\n}\n.skin-2 .spin-icon {\n  background: #23c6c8 !important;\n}\n.skin-2 .nav-header {\n  background: #23c6c8;\n  background: url('../external/patterns/header-profile-skin-2.png');\n}\n.skin-2.mini-navbar .nav-second-level {\n  background: #ededed;\n}\n.skin-2 .breadcrumb {\n  background: transparent;\n}\n.skin-2.fixed-nav .minimalize-styl-2 {\n  margin: 14px 5px 5px 15px;\n}\n.skin-2 .page-heading {\n  border: none;\n  background: rgba(255, 255, 255, 0.7);\n}\n.skin-2 .nav > li.active {\n  background: #e0e0e0;\n}\n.skin-2 .logo-element {\n  padding: 17px 0;\n}\n.skin-2 .nav > li > a,\n.skin-2 .welcome-message {\n  color: #edf6ff;\n}\n.skin-2 #top-search::-moz-placeholder,\n.skin-2 #mainSearchPageName::-moz-placeholder {\n  color: #edf6ff;\n  opacity: 0.5;\n}\n.skin-2 #side-menu > li > a,\n.skin-2 .nav.nav-second-level > li > a {\n  color: #586b7d;\n}\n#side-menu > li > a > i.fa {\n  width: 16px;\n}\n#side-menu .menu-separator {\n  border-top: 1px solid #f87012;\n}\n#side-menu .menu-separator:last-child {\n  border-top: 0;\n}\n.skin-2 .nav > li.active > a {\n  color: #213a53;\n}\n.skin-2.mini-navbar .nav-header {\n  background: #213a53;\n}\n.skin-2 .navbar-minimalize {\n  background: #23c6c8;\n  border-color: #23c6c8;\n}\n.skin-2 .border-bottom {\n  border-bottom: none !important;\n}\n.skin-2 #top-search,\n.skin-2 #mainSearchPageName {\n  color: #fff;\n}\nbody.skin-2 #wrapper {\n  background-color: #ededed;\n}\n.skin-2 .navbar-static-top {\n  background: #213a53;\n}\n.fixed-nav.skin-2 .navbar-fixed-top {\n  background: #213a53;\n  border-bottom: none !important;\n}\n.skin-2 .nav-header {\n  padding: 30px 25px 30px 25px;\n}\n.skin-2 .dashboard-header {\n  background: rgba(255, 255, 255, 0.4);\n  border-bottom: none !important;\n  border-top: none;\n  padding: 20px 30px 20px 30px;\n}\n.skin-2 .wrapper-content {\n  padding: 30px 15px;\n}\n.skin-2 .dashoard-1 .wrapper-content {\n  padding: 0px 30px 25px 30px;\n}\n.skin-2 .ibox-title {\n  background: rgba(255, 255, 255, 0.7);\n  border: none;\n  margin-bottom: 1px;\n}\n.skin-2 .ibox-content {\n  background: rgba(255, 255, 255, 0.4);\n  border: none !important;\n}\n.skin-2 #page-wrapper {\n  background: #f6f6f6;\n  background: -webkit-radial-gradient(center, ellipse cover, #f6f6f6 20%, #d5d5d5 100%);\n  background: -o-radial-gradient(center, ellipse cover, #f6f6f6 20%, #d5d5d5 100%);\n  background: -ms-radial-gradient(center, ellipse cover, #f6f6f6 20%, #d5d5d5 100%);\n  background: radial-gradient(ellipse at center, #f6f6f6 20%, #d5d5d5 100%);\n  -ms-filter: \"progid:DXImageTransform.Microsoft.gradient(startColorstr=#f6f6f6, endColorstr=#d5d5d5)\";\n}\n.skin-2 .ibox-title,\n.skin-2 .ibox-content {\n  border-width: 1px;\n}\n.skin-2 .ibox-content:last-child {\n  border-style: solid solid solid solid;\n}\n.skin-2 .nav > li.active {\n  border: none;\n}\n.skin-2 .nav-header a.dropdown-toggle {\n  color: #edf6ff;\n  margin-top: 10px;\n}\n.skin-2 .nav-header a.dropdown-toggle .text-muted {\n  color: #edf6ff;\n  opacity: 0.8;\n}\n.skin-2 .img-circle {\n  border-radius: 10px;\n}\n.skin-2 .nav.navbar-top-links > li > a:hover,\n.skin-2 .nav.navbar-top-links > li > a:focus {\n  background: #1a2d41;\n}\n.skin-2 .navbar-default .nav > li > a:hover,\n.skin-2 .navbar-default .nav > li > a:focus {\n  background: #e0e0e0;\n  color: #213a53;\n}\n.skin-2 .nav.nav-tabs > li.active > a {\n  color: #555;\n}\n.skin-2 .nav.nav-tabs > li.active {\n  background: transparent;\n}\n/*\n *\n *   SKIN 3 - INSPINIA - Responsive Admin Theme\n *   NAME - Yellow/purple\n *\n*/\n.skin-3 .minimalize-styl-2 {\n  margin: 14px 5px 5px 30px;\n}\n.skin-3 .navbar-top-links li:last-child {\n  margin-right: 30px;\n}\n.skin-3.fixed-nav .minimalize-styl-2 {\n  margin: 14px 5px 5px 15px;\n}\n.skin-3 .spin-icon {\n  background: #ecba52 !important;\n}\nbody.boxed-layout.skin-3 #wrapper {\n  background: #3e2c42;\n}\n.skin-3 .nav-header {\n  background: #ecba52;\n  background: url('../external/patterns/header-profile-skin-3.png');\n}\n.skin-3.mini-navbar .nav-second-level {\n  background: #3e2c42;\n}\n.skin-3 .breadcrumb {\n  background: transparent;\n}\n.skin-3 .page-heading {\n  border: none;\n}\n.skin-3 .nav > li.active {\n  background: #38283c;\n}\n.fixed-nav.skin-3 .navbar-fixed-top {\n  background: #fff;\n}\n.skin-3 .nav > li > a {\n  color: #948b96;\n}\n.skin-3 .nav > li.active > a {\n  color: #fff;\n}\n.skin-3 .navbar-minimalize {\n  background: #ecba52;\n  border-color: #ecba52;\n}\nbody.skin-3 {\n  background: #3e2c42;\n}\n.skin-3 .navbar-static-top {\n  background: #ffffff;\n}\n.skin-3 .dashboard-header {\n  background: transparent;\n  border-bottom: none !important;\n  border-top: none;\n  padding: 20px 30px 10px 30px;\n}\n.skin-3 .wrapper-content {\n  padding: 30px 15px;\n}\n.skin-3 #page-wrapper {\n  background: #f4f6fa;\n}\n.skin-3 .ibox-title,\n.skin-3 .ibox-content {\n  border-width: 1px;\n}\n.skin-3 .ibox-content:last-child {\n  border-style: solid solid solid solid;\n}\n.skin-3 .nav > li.active {\n  border: none;\n}\n.skin-3 .nav-header {\n  padding: 35px 25px 25px 25px;\n}\n.skin-3 .nav-header a.dropdown-toggle {\n  color: #fff;\n  margin-top: 10px;\n}\n.skin-3 .nav-header a.dropdown-toggle .text-muted {\n  color: #fff;\n  opacity: 0.8;\n}\n.skin-3 .profile-element {\n  text-align: center;\n}\n.skin-3 .img-circle {\n  border-radius: 5px;\n}\n.skin-3 .navbar-default .nav > li > a:hover,\n.skin-3 .navbar-default .nav > li > a:focus {\n  background: #38283c;\n  color: #fff;\n}\n.skin-3 .nav.nav-tabs > li.active > a {\n  color: #555;\n}\n.skin-3 .nav.nav-tabs > li.active {\n  background: transparent;\n}\n@media (min-width: 768px) {\n  #page-wrapper {\n    position: inherit;\n    margin: 0 0 0 220px;\n    min-height: 1200px;\n  }\n  .navbar-static-side {\n    z-index: 2001;\n    position: absolute;\n    width: 220px;\n  }\n  .navbar-top-links .dropdown-messages,\n  .navbar-top-links .dropdown-tasks,\n  .navbar-top-links .dropdown-alerts {\n    margin-left: auto;\n  }\n}\n@media (max-width: 768px) {\n  #page-wrapper {\n    position: inherit;\n    margin: 0 0 0 0px;\n    min-height: 1000px;\n  }\n  .body-small .navbar-static-side {\n    display: none;\n    z-index: 2001;\n    position: absolute;\n    width: 70px;\n  }\n  .body-small.mini-navbar .navbar-static-side {\n    display: block;\n  }\n  .lock-word {\n    display: none;\n  }\n  .navbar-form-custom {\n    display: none;\n  }\n  .navbar-header {\n    display: inline;\n    float: left;\n  }\n  .sidebard-panel {\n    z-index: 2;\n    position: relative;\n    width: auto;\n    min-height: 100% !important;\n  }\n  .sidebar-content .wrapper {\n    padding-right: 0px;\n    z-index: 1;\n  }\n  .fixed-sidebar.body-small .navbar-static-side {\n    display: none;\n    z-index: 2001;\n    position: fixed;\n    width: 220px;\n  }\n  .fixed-sidebar.body-small.mini-navbar .navbar-static-side {\n    display: block;\n  }\n  .ibox-tools {\n    float: none;\n    text-align: right;\n    display: block;\n  }\n}\n@media (max-width: 350px) {\n  .timeline-item .date {\n    text-align: left;\n    width: 110px;\n    position: relative;\n    padding-top: 30px;\n  }\n  .timeline-item .date i {\n    position: absolute;\n    top: 0;\n    left: 15px;\n    padding: 5px;\n    width: 30px;\n    text-align: center;\n    border: 1px solid #dee0e2;\n    background: #f8f8f8;\n  }\n  .timeline-item .content {\n    border-left: none;\n    border-top: 1px solid #dee0e2;\n    padding-top: 10px;\n    min-height: 100px;\n  }\n  .nav.navbar-top-links li.dropdown {\n    display: none;\n  }\n  .ibox-tools {\n    float: none;\n    text-align: left;\n    display: inline-block;\n  }\n}\n.textfield {\n  color: #000000;\n  border: 0;\n  padding: 5px;\n  background: #fdf4eb;\n}\n.buxus-table textarea {\n  color: #000000;\n  border: 0;\n  background: #fdf4eb;\n}\ntr.top-links-header td {\n  background-color: #f3f3f3;\n  color: #777777;\n}\ntr.top-links-data td {\n  font-weight: bold;\n}\ntr.top-links-data td a {\n  color: #09558e;\n}\ntr.top-links-data td a i {\n  color: #f87012;\n  padding-left: 10px;\n}\n.info-icon {\n  color: #cccccc;\n  position: absolute;\n  top: 0;\n  right: -14px;\n}\n.tabs > .ui-tabs-panel > .buxus-table > tbody > tr > th {\n  width: 220px;\n}\n.popover {\n  min-width: 300px;\n}\n.pagelist_table th {\n  min-width: 110px;\n}\ndiv.pagelist_delete_all {\n  width: auto;\n  display: inline-block;\n  float: right;\n}\ninput[readonly],\ninput:disabled,\nselect:disabled,\ntextarea:disabled,\ntextarea[readonly] {\n  background: #f6f6f6;\n  cursor: not-allowed;\n}\ninput[type=\"checkbox\"] {\n  margin: 0;\n  width: 30px;\n  display: inline-block;\n}\ninput[type=\"radio\"] {\n  margin: 6px;\n  display: inline-block;\n}\ninput[type=\"file\"] {\n  display: inline-block;\n}\n.alert {\n  margin-bottom: 0;\n}\n#toast-container > div {\n  opacity: 0.9;\n}\n[v-cloak] {\n  display: none;\n  visibility: hidden;\n}\n.tinymce-preview {\n  border: 1px solid #dee0e2;\n  overflow: visible;\n  position: relative;\n  width: auto;\n  margin-left: 0;\n  min-height: auto;\n  padding: 8px;\n}\n.disabled {\n  cursor: not-allowed;\n  background-color: #dee0e2;\n}\ninput[type=text],\ninput[type=checkbox],\ninput[type=radio],\nselect,\ntextarea {\n  background: #fdf4eb;\n}\n.form-control {\n  background: #fdf4eb;\n}\n.eshop-tabs.ui-tabs .ui-tabs-nav li.ui-tabs-active {\n  margin-bottom: 0;\n  padding-bottom: 0;\n}\n#shop #changed {\n  display: none;\n  padding: 6px;\n  background: #f87012;\n}\n.ui-widget-header {\n  text-transform: none;\n}\n.file-browser-dir-list {\n  display: flex;\n  flex-wrap: wrap;\n}\n.file-browser-dir-list .file-browser-dir {\n  flex: 1 1 auto;\n  margin: 2px;\n  padding: 5px 2px;\n  border: 1px solid #b2b2b2;\n  word-wrap: break-word;\n}\n.file-browser-dir-list .file-browser-dir.checkbox-inline input[type=checkbox] {\n  display: inline-block;\n  position: relative;\n  margin-left: 0;\n}\n.file-browser-dir-list .file-browser-dir.checkbox-inline + .file-browser-dir-list .file-browser-dir.checkbox-inline {\n  margin-left: 2px;\n  margin-top: 2px;\n}\n.file-browser-dir-list .file-browser-dir img {\n  vertical-align: baseline;\n}\n.buxus-datetime-input .form-control {\n  width: 300px;\n  display: inline-block;\n}\n.info {\n  border: 1px solid green;\n  padding: 1.2em;\n  color: green;\n  background-color: #e9fee3;\n  margin-top: 12px;\n  margin-bottom: 12px;\n}\n.error {\n  border: 1px solid #b02500;\n  padding: 1.2em;\n  color: #b02500;\n  margin-top: 12px;\n  margin-bottom: 12px;\n  background-color: #fdd7cd;\n}\n#page-wrapper div.mce-fullscreen {\n  top: 61px;\n  z-index: 20000;\n}\n"]}