<?php
namespace Krabica\Migrations;

use Buxus\Migration\AbstractMigration;
use TreeProperty\Migrations\TreePropertyMigration;
use TreeProperty\Property\TreeProperty;

class METAProperties extends AbstractMigration {
    public function dependencies()
    {
        return array(
            TreePropertyMigration::class,
        );
    }

    public function up() {
        // property: META title(meta_title)
        $property_meta_title = $this->propertyManager()->propertyExistsByTag('meta_title');
        if ($property_meta_title === false) {
            $property_meta_title = new TreeProperty();
            $property_meta_title->setTag('meta_title');
            $property_meta_title->setDescription('Titulok stránky');
            $property_meta_title->setExtendedDescription('');
            $property_meta_title->setName('META title');
            $property_meta_title->setClassId('4');
            $property_meta_title->setShowType(NULL);
            $property_meta_title->setShowTypeTag('custom_property');
            $property_meta_title->setValueType('custom_property');
            $property_meta_title->setDefaultValue('');
            $property_meta_title->setMultiOperations(false);
            $property_meta_title->setInputString('');
            $property_meta_title->setAttribute('tab', 'SEO');
            $property_meta_title->setAttribute('class_name', \TreeProperty\BuxusProperty::class);
            $property_meta_title->setAttribute('inherit_value', 'F');
            $this->propertyManager()->saveProperty($property_meta_title);
            
        } else {
            $this->writeLine('Property with tag meta_title already exists');
            $this->setDataKey('property_meta_title_existed', true);
        }
        if ($this->pageTypeExists('homepage')) {
            $this->addPropertyToPageType('meta_title', 'homepage', false);
            \BuxusDB::get()->insert('tblTreeProperties', array(
                'page_id' => $this->getMainPageId(),
                'property_id' => $property_meta_title->getId(),
                'page_type_id' => $this->getPageTypeIdByTag('homepage'),
                'property_value' => '<buxus-prop>title</buxus-prop>',
            ));
        }
        if ($this->pageTypeExists('service_page')) {
            $this->addPropertyToPageType('meta_title', 'service_page', false);
            \BuxusDB::get()->insert('tblTreeProperties', array(
                'page_id' => $this->getMainPageId(),
                'property_id' => $property_meta_title->getId(),
                'page_type_id' => $this->getPageTypeIdByTag('service_page'),
                'property_value' => '<buxus-prop>title</buxus-prop>',
            ));
        }
        if ($this->pageTypeExists('page')) {
            $this->addPropertyToPageType('meta_title', 'page', false);
            \BuxusDB::get()->insert('tblTreeProperties', array(
                'page_id' => $this->getMainPageId(),
                'property_id' => $property_meta_title->getId(),
                'page_type_id' => $this->getPageTypeIdByTag('page'),
                'property_value' => '<buxus-prop>title</buxus-prop>',
            ));
        }

        if ($this->pageTypeExists('page_category')) {
            \BuxusDB::get()->insert('tblTreeProperties', array(
                'page_id' => $this->getMainPageId(),
                'property_id' => $property_meta_title->getId(),
                'page_type_id' => $this->getPageTypeIdByTag('page_category'),
                'property_value' => '<buxus-prop>title</buxus-prop>',
            ));
        }

        if ($this->pageTypeExists('rubric')) {
            \BuxusDB::get()->insert('tblTreeProperties', array(
                'page_id' => $this->getMainPageId(),
                'property_id' => $property_meta_title->getId(),
                'page_type_id' => $this->getPageTypeIdByTag('rubric'),
                'property_value' => '<buxus-prop>title</buxus-prop>',
            ));
        }

        if ($this->pageTypeExists('section')) {
            \BuxusDB::get()->insert('tblTreeProperties', array(
                'page_id' => $this->getMainPageId(),
                'property_id' => $property_meta_title->getId(),
                'page_type_id' => $this->getPageTypeIdByTag('section'),
                'property_value' => '<buxus-prop>title</buxus-prop>',
            ));
        }

        if ($this->pageTypeExists('article')) {
            \BuxusDB::get()->insert('tblTreeProperties', array(
                'page_id' => $this->getMainPageId(),
                'property_id' => $property_meta_title->getId(),
                'page_type_id' => $this->getPageTypeIdByTag('article'),
                'property_value' => '<buxus-prop>title</buxus-prop>',
            ));
        }

        // property: META description(meta_description)
        $property_meta_description = $this->propertyManager()->propertyExistsByTag('meta_description');
        if ($property_meta_description === false) {
            $property_meta_description = new TreeProperty();
            $property_meta_description->setTag('meta_description');
            $property_meta_description->setDescription('Podľa SEO odporúčaní max. 170 znakov.');
            $property_meta_description->setExtendedDescription('');
            $property_meta_description->setName('META description');
            $property_meta_description->setClassId('4');
            $property_meta_description->setShowType(NULL);
            $property_meta_description->setShowTypeTag('custom_property');
            $property_meta_description->setValueType('custom_property');
            $property_meta_description->setDefaultValue('');
            $property_meta_description->setMultiOperations(false);
            $property_meta_description->setInputString(NULL);
            $property_meta_description->setAttribute('tab', 'SEO');
            $property_meta_description->setAttribute('class_name', \TreeProperty\BuxusProperty::class);
            $property_meta_description->setAttribute('inherit_value', 'F');
            $this->propertyManager()->saveProperty($property_meta_description);

        } else {
            $this->writeLine('Property with tag meta_description already exists');
            $this->setDataKey('property_meta_description_existed', true);
        }
        if ($this->pageTypeExists('homepage')) {
            $this->addPropertyToPageType('meta_description', 'homepage', false);
        }
        if ($this->pageTypeExists('page')) {
            $this->addPropertyToPageType('meta_description', 'page', false);
        }

        // regenerate property constants
        $this->propertyManager()->generateConstants();

    }

    public function down() {
        // remove property: META title(meta_title)
        $property_meta_title = $this->propertyManager()->propertyExistsByTag('meta_title');
        if ($property_meta_title != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_meta_title);
            if ((is_null($this->getDataKey('property_meta_title_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_meta_title);
            }
        }

        // remove property: META description(meta_description)
        $property_meta_description = $this->propertyManager()->propertyExistsByTag('meta_description');
        if ($property_meta_description != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_meta_description);
            if ((is_null($this->getDataKey('property_meta_description_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_meta_description);
            }
        }

        // regenerate property constants
        $this->propertyManager()->generateConstants();

    }

}
