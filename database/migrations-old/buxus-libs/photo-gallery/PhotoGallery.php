<?php
namespace PhotoGallery\Migrations;

use Buxus\Migration\AbstractMigration;
use Buxus\Property\Property;

class PhotoGallery extends AbstractMigration {
    public function up() {
        // page type: Fotogalerie (photos)
        $page_type_photos = $this->pageTypesManager()->pageTypeExistsByTag('photos');
        if ($page_type_photos === false) {
            $page_type_photos = new \Buxus\PageType\PageType();
            $page_type_photos->setTag('photos');
            $page_type_photos->setName('Fotogalerie');
            $page_type_photos->setPageClassId('1');
            $page_type_photos->setDefaultTemplateId('2');
            $page_type_photos->setDeleteTrigger('');
            $page_type_photos->setIncludeInSync(NULL);
            $page_type_photos->setPageDetailsLayout('');
            $page_type_photos->setPageSortTypeTag('sort_date_time');
            $page_type_photos->setPageTypeOrder('0');
            $page_type_photos->setPostmoveTrigger('');
            $page_type_photos->setPostsubmitTrigger('');
            $page_type_photos->setPresubmitTrigger('');
            $page_type_photos->setParent(NULL);

        } else {
            $this->writeLine('Page type with tag photos already exists');
            $this->setDataKey('page_type_photos_existed', true);
        }
        $this->pageTypesManager()->savePageType($page_type_photos);

        $this->setPageTemplate($this->getMainPageId(), $this->getPageTypeIdByTag('photos'), 'photo-gallery', 'photo-gallery-list');

        // page type: Foto - kategórie (photos_categories)
        $page_type_photos_categories = $this->pageTypesManager()->pageTypeExistsByTag('photos_categories');
        if ($page_type_photos_categories === false) {
            $page_type_photos_categories = new \Buxus\PageType\PageType();
            $page_type_photos_categories->setTag('photos_categories');
            $page_type_photos_categories->setName('Foto - kategórie');
            $page_type_photos_categories->setPageClassId('1');
            $page_type_photos_categories->setDefaultTemplateId('2');
            $page_type_photos_categories->setDeleteTrigger('');
            $page_type_photos_categories->setIncludeInSync(NULL);
            $page_type_photos_categories->setPageDetailsLayout('');
            $page_type_photos_categories->setPageSortTypeTag('sort_date_time');
            $page_type_photos_categories->setPageTypeOrder('0');
            $page_type_photos_categories->setPostmoveTrigger('');
            $page_type_photos_categories->setPostsubmitTrigger('');
            $page_type_photos_categories->setPresubmitTrigger('');
            $parent = $this->pageTypesManager()->getPageTypeByTag('photos');
            $page_type_photos_categories->setParent($parent);

        } else {
            $this->writeLine('Page type with tag photos_categories already exists');
            $this->setDataKey('page_type_photos_categories_existed', true);
        }
        $this->pageTypesManager()->savePageType($page_type_photos_categories);

        $this->setPageTemplate($this->getMainPageId(), $this->getPageTypeIdByTag('photos_categories'), 'photo-gallery', 'photo-gallery-list');

        // property: Titulok(title)
        $property_title = $this->propertyManager()->propertyExistsByTag('title');
        if ($property_title === false) {
            $property_title = new \Buxus\Property\Types\Input();
            $property_title->setTag('title');
            $property_title->setDescription('Štandardný názov stránky. Text, ktorý sa zobrazí v záložke prehliadača. Podľa SEO odporúčaní max. 65 znakov.');
            $property_title->setExtendedDescription('');
            $property_title->setName('Titulok');
            $property_title->setClassId('4');
            $property_title->setShowType(NULL);
            $property_title->setShowTypeTag('text');
            $property_title->setValueType('oneline_text');
            $property_title->setDefaultValue('');
            $property_title->setMultiOperations(false);
            $property_title->setInputString(NULL);
            $property_title->setAttribute('tab', '');
            $property_title->setAttribute('size', '60');
            $property_title->setAttribute('maxlength', '');
            $property_title->setAttribute('readonly', 'F');
            $property_title->setAttribute('pattern', '');
            $property_title->setAttribute('inherit_value', 'F');
            $property_title->setAttribute('onchange-js', '');
            $property_title->setAttribute('onkeyup-js', '');
            $property_title->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($property_title);
        } else {
            $this->writeLine('Property with tag title already exists');
            $this->setDataKey('property_title_existed', true);
        }

        // property: Anotácia(annotation)
        $property_annotation = $this->propertyManager()->propertyExistsByTag('annotation');
        if ($property_annotation === false) {
            $property_annotation = new \Buxus\Property\Types\Textarea();
            $property_annotation->setTag('annotation');
            $property_annotation->setDescription('Štandardná anotácia stránky.');
            $property_annotation->setExtendedDescription('');
            $property_annotation->setName('Anotácia');
            $property_annotation->setClassId('4');
            $property_annotation->setShowType(NULL);
            $property_annotation->setShowTypeTag('textarea');
            $property_annotation->setValueType('multiline_text');
            $property_annotation->setDefaultValue('');
            $property_annotation->setMultiOperations(false);
            $property_annotation->setInputString(NULL);
            $property_annotation->setAttribute('tab', '');
            $property_annotation->setAttribute('cols', '60');
            $property_annotation->setAttribute('rows', '');
            $property_annotation->setAttribute('dhtml-edit', '1');
            $property_annotation->setAttribute('dhtml-configuration', 'full');
            $property_annotation->setAttribute('import-word', '0');
            $property_annotation->setAttribute('auto', '1');
            $property_annotation->setAttribute('inherit_value', 'F');
            $property_annotation->setAttribute('onchange-js', '');
            $property_annotation->setAttribute('onkeyup-js', '');
            $property_annotation->setAttribute('onkeydown-js', '');
            $property_annotation->setAttribute('pattern', '');
            $this->propertyManager()->saveProperty($property_annotation);

        } else {
            $this->writeLine('Property with tag annotation already exists');
            $this->setDataKey('property_annotation_existed', true);
        }

        // property: Text(text)
        $property_text = $this->propertyManager()->propertyExistsByTag('text');
        if ($property_text === false) {
            $property_text = new \Buxus\Property\Types\Textarea();
            $property_text->setTag('text');
            $property_text->setDescription('Štandardný text stránky.');
            $property_text->setExtendedDescription('');
            $property_text->setName('Text');
            $property_text->setClassId('4');
            $property_text->setShowType(NULL);
            $property_text->setShowTypeTag('textarea');
            $property_text->setValueType('multiline_text');
            $property_text->setDefaultValue('');
            $property_text->setMultiOperations(false);
            $property_text->setInputString(NULL);
            $property_text->setAttribute('tab', '');
            $property_text->setAttribute('cols', '60');
            $property_text->setAttribute('rows', '');
            $property_text->setAttribute('dhtml-edit', '1');
            $property_text->setAttribute('dhtml-configuration', 'full');
            $property_text->setAttribute('import-word', '0');
            $property_text->setAttribute('auto', '1');
            $property_text->setAttribute('inherit_value', 'F');
            $property_text->setAttribute('onchange-js', '');
            $property_text->setAttribute('onkeyup-js', '');
            $property_text->setAttribute('onkeydown-js', '');
            $property_text->setAttribute('pattern', '');
            $this->propertyManager()->saveProperty($property_text);

        } else {
            $this->writeLine('Property with tag text already exists');
            $this->setDataKey('property_text_existed', true);
        }

        // property: Náhľad(photo_album_thumbnail)
        $property_photo_album_thumbnail = $this->propertyManager()->propertyExistsByTag('photo_album_thumbnail');
        if ($property_photo_album_thumbnail === false) {
            $property_photo_album_thumbnail = new \Buxus\Property\Types\Image();
            $property_photo_album_thumbnail->setTag('photo_album_thumbnail');
            $property_photo_album_thumbnail->setDescription('Fotografia sa bude zobrazovať ako náhľad albumu.');
            $property_photo_album_thumbnail->setExtendedDescription('');
            $property_photo_album_thumbnail->setName('Náhľad');
            $property_photo_album_thumbnail->setClassId('4');
            $property_photo_album_thumbnail->setShowType(NULL);
            $property_photo_album_thumbnail->setShowTypeTag('image_name_upload');
            $property_photo_album_thumbnail->setValueType('file');
            $property_photo_album_thumbnail->setDefaultValue('');
            $property_photo_album_thumbnail->setMultiOperations(false);
            $property_photo_album_thumbnail->setInputString(NULL);
            $property_photo_album_thumbnail->setAttribute('tab', '');
            $property_photo_album_thumbnail->setAttribute('with_upload', 'T');
            $property_photo_album_thumbnail->setAttribute('simple_upload', 'T');
            $property_photo_album_thumbnail->setAttribute('show_input_element', 'F');
            $property_photo_album_thumbnail->setAttribute('filename', '');
            $property_photo_album_thumbnail->setAttribute('show_file_name', 'F');
            $property_photo_album_thumbnail->setAttribute('file_type', 'image');
            $property_photo_album_thumbnail->setAttribute('pattern', '');
            $property_photo_album_thumbnail->setAttribute('show_thumbnail', 'T');
            $property_photo_album_thumbnail->setAttribute('max_thumbnail_width', '150');
            $property_photo_album_thumbnail->setAttribute('max_thumbnail_height', '80');
            $property_photo_album_thumbnail->setAttribute('upload_subdir', 'fotogaleria/');
            $property_photo_album_thumbnail->setAttribute('eval-code', '//$this-&gt;parsed_parm[\'upload_subdir\'] = \'my-dir\';');
            $property_photo_album_thumbnail->setAttribute('onchange-js', '');

            $this->propertyManager()->saveProperty($property_photo_album_thumbnail);

        } else {
            $this->writeLine('Property with tag photo_album_thumbnail already exists');
            $this->setDataKey('property_photo_album_thumbnail_existed', true);
        }

        // property: Fotografie v albume(photo_list)
        $property_photo_list = $this->propertyManager()->propertyExistsByTag('photo_list');
        if ($property_photo_list === false) {
            $property_photo_list = new \Buxus\Property\Types\PhotoGallery();
            $property_photo_list->setTag('photo_list');
            $property_photo_list->setDescription('');
            $property_photo_list->setExtendedDescription(NULL);
            $property_photo_list->setName('Fotografie v albume');
            $property_photo_list->setClassId('4');
            $property_photo_list->setShowType(NULL);
            $property_photo_list->setShowTypeTag('page_edit_list');
            $property_photo_list->setValueType('image_list');
            $property_photo_list->setDefaultValue('');
            $property_photo_list->setMultiOperations(false);
            $property_photo_list->setInputString(NULL);
            $property_photo_list->setAttribute('tab', '');
            $property_photo_list->setAttribute('class_name', \BuxusPhotoGalleryEditList::class);
            $property_photo_list->setSubpagePageType('photo');

            $this->propertyManager()->saveProperty($property_photo_list);

        } else {
            $this->writeLine('Property with tag photo_list already exists');
            $this->setDataKey('property_photo_list_existed', true);
        }

        // page type: Fotoalbum (photo_album)
        $page_type_photo_album = $this->pageTypesManager()->pageTypeExistsByTag('photo_album');
        if ($page_type_photo_album === false) {
            $page_type_photo_album = new \Buxus\PageType\PageType();
            $page_type_photo_album->setTag('photo_album');
            $page_type_photo_album->setName('Fotoalbum');
            $page_type_photo_album->setPageClassId('1');
            $page_type_photo_album->setDefaultTemplateId('2');
            $page_type_photo_album->setDeleteTrigger('');
            $page_type_photo_album->setIncludeInSync('0');
            $page_type_photo_album->setPageDetailsLayout('');
            $page_type_photo_album->setPageSortTypeTag('sort_date_time');
            $page_type_photo_album->setPageTypeOrder('0');
            $page_type_photo_album->setPostmoveTrigger('');
            $page_type_photo_album->setPostsubmitTrigger('');
            $page_type_photo_album->setPresubmitTrigger('');
            $parent = $this->pageTypesManager()->getPageTypeByTag('photos_categories');
            $page_type_photo_album->setParent($parent);

        } else {
            $this->writeLine('Page type with tag photo_album already exists');
            $this->setDataKey('page_type_photo_album_existed', true);
        }
        $property = $this->propertyManager()->getPropertyByTag('title');
        $property_id = $property->getId();
        $tmp = $page_type_photo_album->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('1');
            $tmp->setRequired(false);
            $page_type_photo_album->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('annotation');
        $property_id = $property->getId();
        $tmp = $page_type_photo_album->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('2');
            $tmp->setRequired(false);
            $page_type_photo_album->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('text');
        $property_id = $property->getId();
        $tmp = $page_type_photo_album->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('3');
            $tmp->setRequired(false);
            $page_type_photo_album->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('photo_album_thumbnail');
        $property_id = $property->getId();
        $tmp = $page_type_photo_album->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('4');
            $tmp->setRequired(false);
            $page_type_photo_album->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('photo_list');
        $property_id = $property->getId();
        $tmp = $page_type_photo_album->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('5');
            $tmp->setRequired(false);
            $page_type_photo_album->addPropertyItem($tmp);
        }
        if ($this->pageTypeExists('photo_gallery')) {
            $page_type_photo_album->addSuperiorPageType($this->getPageTypeByTag('photo_gallery'));
        }
        $this->pageTypesManager()->savePageType($page_type_photo_album);
        // set template on MAIN PAGE photo-gallery::photo-album
        $this->setPageTemplate($this->getMainPageId(), $this->getPageTypeIdByTag('photo_album'), 'photo-gallery', 'photo-album');

        // property: SEO URL name(seo_url_name)
        $property_seo_url_name = $this->propertyManager()->propertyExistsByTag('seo_url_name');
        if ($property_seo_url_name === false) {
            $property_seo_url_name = new Property();
            $property_seo_url_name->setTag('seo_url_name');
            $property_seo_url_name->setDescription('Podľa SEO odporúčaní max. 35 znakov.');
            $property_seo_url_name->setExtendedDescription('');
            $property_seo_url_name->setName('SEO URL name');
            $property_seo_url_name->setClassId('4');
            $property_seo_url_name->setShowType(NULL);
            $property_seo_url_name->setShowTypeTag('seo_url_name');
            $property_seo_url_name->setValueType('seo_url_name');
            $property_seo_url_name->setDefaultValue('');
            $property_seo_url_name->setMultiOperations(false);
            $property_seo_url_name->setInputString(NULL);
            $property_seo_url_name->setAttribute('tab', 'SEO');
            $property_seo_url_name->setAttribute('size', '80');
            $property_seo_url_name->setAttribute('onchange-js', '');
            $property_seo_url_name->setAttribute('onkeyup-js', '');
            $property_seo_url_name->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($property_seo_url_name);
        } else {
            $this->writeLine('Property with tag seo_url_name already exists');
            $this->setDataKey('property_seo_url_name_existed', true);
        }

        // page type: Fotogaléria (photo_gallery)
        $page_type_photo_gallery = $this->pageTypesManager()->pageTypeExistsByTag('photo_gallery');
        if ($page_type_photo_gallery === false) {
            $page_type_photo_gallery = new \Buxus\PageType\PageType();
            $page_type_photo_gallery->setTag('photo_gallery');
            $page_type_photo_gallery->setName('Fotogaléria');
            $page_type_photo_gallery->setPageClassId('1');
            $page_type_photo_gallery->setDefaultTemplateId('2');
            $page_type_photo_gallery->setDeleteTrigger('');
            $page_type_photo_gallery->setIncludeInSync('0');
            $page_type_photo_gallery->setPageDetailsLayout('');
            $page_type_photo_gallery->setPageSortTypeTag('sort_date_time');
            $page_type_photo_gallery->setPageTypeOrder('40');
            $page_type_photo_gallery->setPostmoveTrigger('');
            $page_type_photo_gallery->setPostsubmitTrigger('');
            $page_type_photo_gallery->setPresubmitTrigger('');
            $parent = $this->pageTypesManager()->getPageTypeByTag('photos_categories');
            $page_type_photo_gallery->setParent($parent);

        } else {
            $this->writeLine('Page type with tag photo_gallery already exists');
            $this->setDataKey('page_type_photo_gallery_existed', true);
        }
        $property = $this->propertyManager()->getPropertyByTag('title');
        $property_id = $property->getId();
        $tmp = $page_type_photo_gallery->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('1');
            $tmp->setRequired(false);
            $page_type_photo_gallery->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('annotation');
        $property_id = $property->getId();
        $tmp = $page_type_photo_gallery->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('2');
            $tmp->setRequired(false);
            $page_type_photo_gallery->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('text');
        $property_id = $property->getId();
        $tmp = $page_type_photo_gallery->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('3');
            $tmp->setRequired(false);
            $page_type_photo_gallery->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('seo_url_name');
        $property_id = $property->getId();
        $tmp = $page_type_photo_gallery->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('4');
            $tmp->setRequired(false);
            $page_type_photo_gallery->addPropertyItem($tmp);
        }
        if ($this->pageTypeExists('main_page')) {
            $page_type_photo_gallery->addSuperiorPageType($this->getPageTypeByTag('main_page'));
        }
        $this->pageTypesManager()->savePageType($page_type_photo_gallery);
        // set template on MAIN PAGE photo-gallery::photo-album-list
        $this->setPageTemplate($this->getMainPageId(), $this->getPageTypeIdByTag('photo_gallery'), 'photo-gallery', 'photo-album-list');

        // property: Súbor fotografie(photo_file)
        $property_photo_file = $this->propertyManager()->propertyExistsByTag('photo_file');
        if ($property_photo_file === false) {
            $property_photo_file = new \Buxus\Property\Types\Image();
            $property_photo_file->setTag('photo_file');
            $property_photo_file->setDescription('Súbor fotografie z prehliadača obrázkov.');
            $property_photo_file->setExtendedDescription('');
            $property_photo_file->setName('Súbor fotografie');
            $property_photo_file->setClassId('4');
            $property_photo_file->setShowType(NULL);
            $property_photo_file->setShowTypeTag('image_name_upload');
            $property_photo_file->setValueType('file');
            $property_photo_file->setDefaultValue('');
            $property_photo_file->setMultiOperations(false);
            $property_photo_file->setInputString(NULL);
            $property_photo_file->setAttribute('tab', '');
            $property_photo_file->setAttribute('with_upload', 'T');
            $property_photo_file->setAttribute('simple_upload', 'F');
            $property_photo_file->setAttribute('show_input_element', 'T');
            $property_photo_file->setAttribute('filename', '');
            $property_photo_file->setAttribute('show_file_name', 'T');
            $property_photo_file->setAttribute('file_type', 'image');
            $property_photo_file->setAttribute('pattern', '');
            $property_photo_file->setAttribute('show_thumbnail', 'T');
            $property_photo_file->setAttribute('max_thumbnail_width', '150');
            $property_photo_file->setAttribute('max_thumbnail_height', '80');
            $property_photo_file->setAttribute('upload_subdir', '');
            $property_photo_file->setAttribute('eval-code', '//$this-&gt;parsed_parm[\'upload_subdir\'] = \'my-dir\';');
            $property_photo_file->setAttribute('onchange-js', '');
            $this->propertyManager()->saveProperty($property_photo_file);

        } else {
            $this->writeLine('Property with tag photo_file already exists');
            $this->setDataKey('property_photo_file_existed', true);
        }

        // property: Alternatívny text(photo_alt)
        $property_photo_alt = $this->propertyManager()->propertyExistsByTag('photo_alt');
        if ($property_photo_alt === false) {
            $property_photo_alt = new \Buxus\Property\Types\Input();
            $property_photo_alt->setTag('photo_alt');
            $property_photo_alt->setDescription('Alternatívny text, ktorý sa zobrazí v prípade, ak obrázok neexistuje.');
            $property_photo_alt->setExtendedDescription(NULL);
            $property_photo_alt->setName('Alternatívny text');
            $property_photo_alt->setClassId('4');
            $property_photo_alt->setShowType(NULL);
            $property_photo_alt->setShowTypeTag('text');
            $property_photo_alt->setValueType('oneline_text');
            $property_photo_alt->setDefaultValue('');
            $property_photo_alt->setMultiOperations(false);
            $property_photo_alt->setInputString(NULL);
            $property_photo_alt->setAttribute('tab', '');
            $property_photo_alt->setAttribute('size', '80');
            $property_photo_alt->setAttribute('maxlength', '');
            $property_photo_alt->setAttribute('readonly', '0');
            $property_photo_alt->setAttribute('pattern', '');
            $property_photo_alt->setAttribute('inherit_value', '0');
            $property_photo_alt->setAttribute('onchange-js', '');
            $property_photo_alt->setAttribute('onkeyup-js', '');
            $property_photo_alt->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($property_photo_alt);

        } else {
            $this->writeLine('Property with tag photo_alt already exists');
            $this->setDataKey('property_photo_alt_existed', true);
        }

        // page type: Fotografia (photo)
        $page_type_photo = $this->pageTypesManager()->pageTypeExistsByTag('photo');
        if ($page_type_photo === false) {
            $page_type_photo = new \Buxus\PageType\PageType();
            $page_type_photo->setTag('photo');
            $page_type_photo->setName('Fotografia');
            $page_type_photo->setPageClassId('1');
            $page_type_photo->setDefaultTemplateId('2');
            $page_type_photo->setDeleteTrigger('');
            $page_type_photo->setIncludeInSync('0');
            $page_type_photo->setPageDetailsLayout('');
            $page_type_photo->setPageSortTypeTag('sort_date_time');
            $page_type_photo->setPageTypeOrder('0');
            $page_type_photo->setPostmoveTrigger('');
            $page_type_photo->setPostsubmitTrigger('');
            $page_type_photo->setPresubmitTrigger('');
            $parent = $this->pageTypesManager()->getPageTypeByTag('photos');
            $page_type_photo->setParent($parent);

        } else {
            $this->writeLine('Page type with tag photo already exists');
            $this->setDataKey('page_type_photo_existed', true);
        }
        $property = $this->propertyManager()->getPropertyByTag('photo_file');
        $property_id = $property->getId();
        $tmp = $page_type_photo->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('1');
            $tmp->setRequired(false);
            $page_type_photo->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('photo_alt');
        $property_id = $property->getId();
        $tmp = $page_type_photo->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('2');
            $tmp->setRequired(false);
            $page_type_photo->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('title');
        $property_id = $property->getId();
        $tmp = $page_type_photo->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('3');
            $tmp->setRequired(false);
            $page_type_photo->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('annotation');
        $property_id = $property->getId();
        $tmp = $page_type_photo->getPropertyItemForPropertyId($property_id);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('4');
            $tmp->setRequired(false);
            $page_type_photo->addPropertyItem($tmp);
        }
        if ($this->pageTypeExists('photo_album')) {
            $page_type_photo->addSuperiorPageType($this->getPageTypeByTag('photo_album'));
        }
        if ($this->pageTypeExists('eshop_product')) {
            $page_type_photo->addSuperiorPageType($this->getPageTypeByTag('eshop_product'));
        }
        $this->pageTypesManager()->savePageType($page_type_photo);
        // set template on MAIN PAGE index::index
        $this->setPageTemplate($this->getMainPageId(), $this->getPageTypeIdByTag('photo'), 'index', 'index');

        // regenerate page types constants
        $this->pageTypesManager()->generateConstants();

        // regenerate property constants
        $this->propertyManager()->generateConstants();

    }

    public function down() {
        // remove page type: Fotografia (photo)
        $page_type_photo = $this->pageTypesManager()->pageTypeExistsByTag('photo');
        if (($page_type_photo != false) && (is_null($this->getDataKey('page_type_photo_existed')))) {
            $this->pageTypesManager()->removePageType($page_type_photo);
        }

        // remove property: Alternatívny text(photo_alt)
        $property_photo_alt = $this->propertyManager()->propertyExistsByTag('photo_alt');
        if ($property_photo_alt != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_photo_alt);
            if ((is_null($this->getDataKey('property_photo_alt_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_photo_alt);
            }
        }

        // remove property: Súbor fotografie(photo_file)
        $property_photo_file = $this->propertyManager()->propertyExistsByTag('photo_file');
        if ($property_photo_file != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_photo_file);
            if ((is_null($this->getDataKey('property_photo_file_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_photo_file);
            }
        }

        // remove page type: Fotogaléria (photo_gallery)
        $page_type_photo_gallery = $this->pageTypesManager()->pageTypeExistsByTag('photo_gallery');
        if (($page_type_photo_gallery != false) && (is_null($this->getDataKey('page_type_photo_gallery_existed')))) {
            $this->pageTypesManager()->removePageType($page_type_photo_gallery);
        }

        // remove property: SEO URL name(seo_url_name)
        $property_seo_url_name = $this->propertyManager()->propertyExistsByTag('seo_url_name');
        if ($property_seo_url_name != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_seo_url_name);
            if ((is_null($this->getDataKey('property_seo_url_name_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_seo_url_name);
            }
        }

        // remove page type: Fotoalbum (photo_album)
        $page_type_photo_album = $this->pageTypesManager()->pageTypeExistsByTag('photo_album');
        if (($page_type_photo_album != false) && (is_null($this->getDataKey('page_type_photo_album_existed')))) {
            $this->pageTypesManager()->removePageType($page_type_photo_album);
        }

        // remove property: Fotografie v albume(photo_list)
        $property_photo_list = $this->propertyManager()->propertyExistsByTag('photo_list');
        if ($property_photo_list != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_photo_list);
            if ((is_null($this->getDataKey('property_photo_list_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_photo_list);
            }
        }

        // remove property: Náhľad(photo_album_thumbnail)
        $property_photo_album_thumbnail = $this->propertyManager()->propertyExistsByTag('photo_album_thumbnail');
        if ($property_photo_album_thumbnail != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_photo_album_thumbnail);
            if ((is_null($this->getDataKey('property_photo_album_thumbnail_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_photo_album_thumbnail);
            }
        }

        // remove property: Text(text)
        $property_text = $this->propertyManager()->propertyExistsByTag('text');
        if ($property_text != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_text);
            if ((is_null($this->getDataKey('property_text_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_text);
            }
        }

        // remove property: Anotácia(annotation)
        $property_annotation = $this->propertyManager()->propertyExistsByTag('annotation');
        if ($property_annotation != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_annotation);
            if ((is_null($this->getDataKey('property_annotation_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_annotation);
            }
        }

        // remove property: Titulok(title)
        $property_title = $this->propertyManager()->propertyExistsByTag('title');
        if ($property_title != false) {
            $supported_page_types = $this->propertyManager()->getSupportedPageTypes($property_title);
            if ((is_null($this->getDataKey('property_title_existed'))) && (count($supported_page_types) == 0)) {
                $this->propertyManager()->removeProperty($property_title);
            }
        }

        // remove page type: Foto - kategórie (photos_categories)
        $page_type_photos_categories = $this->pageTypesManager()->pageTypeExistsByTag('photos_categories');
        if (($page_type_photos_categories != false) && (is_null($this->getDataKey('page_type_photos_categories_existed')))) {
            $this->pageTypesManager()->removePageType($page_type_photos_categories);
        }

        // remove page type: Fotogalerie (photos)
        $page_type_photos = $this->pageTypesManager()->pageTypeExistsByTag('photos');
        if (($page_type_photos != false) && (is_null($this->getDataKey('page_type_photos_existed')))) {
            $this->pageTypesManager()->removePageType($page_type_photos);
        }

        // regenerate page types constants
        $this->pageTypesManager()->generateConstants();

        // regenerate property constants
        $this->propertyManager()->generateConstants();

    }

}
