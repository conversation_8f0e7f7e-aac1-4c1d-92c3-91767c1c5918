const gulp = require('gulp');
const less = require('gulp-less');
const minifyCSS = require('gulp-minify-css');
const jshint = require('gulp-jshint');
const rimraf = require('rimraf');
const sourcemaps = require('gulp-sourcemaps');
const uglify = require('gulp-uglify-es').default;
const gutil = require('gulp-util');
const concat = require('gulp-concat');
const stripDebug = require('gulp-strip-debug');
const concatCss = require('gulp-concat-css');
const data = require('gulp-data');
const exec = require('sync-exec');
const fs = require('fs');
const rjs = require('requirejs');
const through2 = require("through2");

const paths = {
    style_src: 'public/buxus/assets/css/',
    style_dest: 'public/buxus/assets/build/css/',
    scripts_src: 'public/buxus/assets/js/',
    scripts_dest: 'public/buxus/assets/build/js/',
    components_src: 'public/buxus/assets/components/',
    components_dest: 'public/buxus/assets/build/components/',
    production_path: 'public/buxus/assets/build/',
    tmp_path: 'public/buxus/assets/build_tmp/',
    bower_imports: 'public/buxus/assets/bower_imports/',
    amd_modules_defs: 'amd-modules/'
};

function shell_exec(command) {
    var out = exec(command);
    if (out.status) {
        console.log(out.stderr);
        console.log(out.stdout);
        throw out.stderr + out.stdout;
    }

    console.log(out.stdout);
}

function watchStyles()
{
    gulp.watch(paths.style_src + '**/**.less', {delay: 2000}, styles);
}

exports.default = watchStyles;

function clean(cb) {
    rimraf(paths.production_path, cb);
}

exports.clean = clean;

function bower(cb) {
    shell_exec('./node_modules/bower/bin/bower install');
    cb();
}
exports.bower = bower;

function extend(target) {
    var sources = [].slice.call(arguments, 1);
    sources.forEach(function (source) {
        for (var prop in source) {
            target[prop] = source[prop];
        }
    });
    return target;
}

function generate_bower() {
    var source_bower;
    try {
        source_bower = require('./bower_base.json');
    } catch (ex) {
        source_bower = {
            "name": "BUXUS krabica",
            "version": "0.0.1",
            "authors": [
                "ui42 s.r.o."
            ],
            "moduleType": [
                "amd"
            ],
            "license": "MIT",
            "ignore": [
                "**/.*",
                "node_modules",
                "bower_components",
                "test",
                "tests"
            ],
            "dependencies": {
            }
        };
    }
    return gulp.src([paths.bower_imports + '**/**.json'])
        .pipe(data(function(file) {
            var json = require(file.path);
            extend(source_bower.dependencies, json.dependencies);
        })).on('end', function() {
            fs.writeFileSync('bower.json', JSON.stringify(source_bower, null, ' '));
        });
}

function remove_css_import(cb) {
    rimraf(paths.style_src + 'import/_imported_styles.css', cb);
}

const { src, dest, series, parallel } = require('gulp');

function create_css_import() {
    return src([paths.style_src + 'import/**/*.css', paths.style_src + 'import/**/*.less'])
        .pipe(less())
        .pipe(concatCss('import/_imported_styles.css'))
        .pipe(gulp.dest(paths.style_src));
}

function compile_css() {
    if (!fs.existsSync(paths.style_src + 'import/_imported_styles.css')) {
        if (!fs.existsSync(paths.style_src + 'import')) {
            fs.mkdirSync(paths.style_src + 'import');
        }
        fs.writeFileSync(paths.style_src + 'import/_imported_styles.css', '');
    }

    return src([paths.style_src + 'style.less'])
        .pipe(less({
            relativeUrls: true
        }))
        .pipe(sourcemaps.init())
        .pipe(minifyCSS({keepSpecialComments:0, keepBreaks: true}))
        .pipe(sourcemaps.write('.'))
        .pipe(through2.obj(function(file, enc, cb) {
            var date = new Date();
            file.stat.atime = date;
            file.stat.mtime = date;
            cb(null, file);
        }))
        .pipe(gulp.dest(paths.style_src));
}

const styles = series(remove_css_import, create_css_import, compile_css);

exports.styles = styles;

function lint() {
    return gulp.src(paths.scripts_src + '*.js')
        .pipe(jshint())
        .pipe(jshint.reporter('default'));
}

var amd_modules_list = [];

function amd_load_defined_modules() {
    amd_modules_list = [];
    if (fs.existsSync(paths.amd_modules_defs)) {
        var path = require('path');
        var files = fs.readdirSync(paths.amd_modules_defs);

        for (var i in files) {
            if (path.extname(files[i]) === ".json") {
                amd_modules_list.push(path.basename(files[i], '.json'));
            }
        }
    }
}

function amd(cb) {
    amd_load_defined_modules();

    var options = {
        baseUrl: paths.scripts_src,
        modules: [],
        keepBuildDir: true,
        skipDirOptimize: true,
        dir: paths.tmp_path,
        optimize: 'none',
        optimizeCss: 'none'
    };

    for (var i in amd_modules_list) {
        var module = JSON.parse(fs.readFileSync(paths.amd_modules_defs + amd_modules_list[i] + '.json', 'utf8'));
        options.modules.push(module);
    }

    if (fs.existsSync(paths.scripts_src + 'config.js')) {
        options.mainConfigFile = paths.scripts_src + 'config.js';
    }
    rjs.optimize(options, function(buildResponse){
        console.log('build response', buildResponse);
        cb();
    }, cb);
}

function amd_concat_config() {
    var sources = [];
    if (fs.existsSync(paths.scripts_src + 'config.js')) {
        sources.push(paths.scripts_src + 'config.js');
    }
    sources.push(paths.tmp_path + 'generic.js');
    return gulp.src(sources)
        .pipe(concat('generic.js'))
        .pipe(gulp.dest(paths.tmp_path));
}

const build_amd = series(
    amd,
    amd_concat_config,
    amd_uglify,
    amd_clean_temp
);

function amd_uglify() {
    var list = [];

    for (var i in amd_modules_list) {
        list.push(paths.tmp_path + amd_modules_list[i] + '.js')
    }

    return gulp.src(list).
        pipe(uglify()).
        pipe(stripDebug()).
        pipe(gulp.dest(paths.scripts_dest));
}

function amd_clean_temp(cb) {
    rimraf(paths.tmp_path, cb);
}

function copy_all_scripts() {
    return gulp.src([paths.scripts_src + '**'])
        .pipe(gulp.dest(paths.scripts_dest));
}

function copy_all_components() {
    return gulp.src([paths.components_src + '**'])
        .pipe(gulp.dest(paths.components_dest));
}

function copy_all_styles () {
    return gulp.src([paths.style_src + '**', '!' + paths.style_src + '**/*.less'])
        .pipe(gulp.dest(paths.style_dest));
}

const copy_all = parallel(
    copy_all_scripts,
    copy_all_styles,
    copy_all_components
);

function uglify_scripts() {
    return gulp.src([paths.scripts_src + '**/*.js'])
        .pipe(uglify())
        .on('error', gutil.log)
        .pipe(stripDebug())
        .pipe(gulp.dest(paths.scripts_dest));
}

function uglify_components() {
    return gulp.src(['!' + paths.components_src + 'jquery/src/**', paths.components_src + '**/*.js'])
        .pipe(uglify())
        .on('error', gutil.log)
        .pipe(stripDebug())
        .pipe(gulp.dest(paths.components_dest));
}

const do_uglify = series(
    uglify_scripts
);

exports.build = series(
    generate_bower,
    bower,
    styles
);

exports.production = series(
    clean,
    generate_bower,
    bower,
    styles,
    build_amd,
    copy_all,
    do_uglify
);
