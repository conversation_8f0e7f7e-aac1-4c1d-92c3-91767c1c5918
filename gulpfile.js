var gulp = require('gulp');
var less = require('gulp-less');
var clean = require('gulp-clean');
var runSequence = require('run-sequence');
var cssimport = require(`gulp-cssimport`);

var paths = {
    build_dir: 'assets/dist',
    less_src: 'assets/src/less/',
    style_dest: 'assets/dist/css/',
    scripts_src: 'assets/src/js/',
    scripts_dest: 'assets/dist/js/',
    gridstack_src: 'node_modules/gridstack/dist/',
    lodash_src: 'node_modules/lodash/'
};

gulp.task('default', function () {
    gulp.start('less');
    gulp.watch(paths.style_src + '**/*.less', ['build']);
});

gulp.task('clean', function () {
    return gulp.src(paths.build_dir, {read: false})
        .pipe(clean());
});

gulp.task('less', function () {
    return gulp.src([paths.less_src + 'dashboard.less'])
        .pipe(less())
        .pipe(cssimport())
        .pipe(gulp.dest(paths.style_dest));
});

// gulp.task('css', ['less'], function() {
//     return gulp.src([paths.])
// });

gulp.task('external-scripts', function () {
  return gulp.src([
      paths.gridstack_src + 'gridstack.js',
      paths.gridstack_src + 'gridstack.jQueryUI.js',
      paths.lodash_src + 'lodash.min.js'
    ])
    .pipe(gulp.dest(paths.scripts_dest));
});

gulp.task('scripts', function () {
    return gulp.src(paths.scripts_src + '**')
        .pipe(gulp.dest(paths.scripts_dest));
});

gulp.task('build', function () {
    runSequence('clean', 'external-scripts', 'scripts', 'less');
});
