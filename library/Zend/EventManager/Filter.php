<?php
/**
 * Zend Framework
 *
 * LICENSE
 *
 * This source file is subject to the new BSD license that is bundled
 * with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://framework.zend.com/license/new-bsd
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category   Zend
 * @package    Zend_EventManager
 * @copyright  Copyright (c) 2005-2015 Zend Technologies USA Inc. (http://www.zend.com)
 * @license    http://framework.zend.com/license/new-bsd     New BSD License
 */

require_once 'Zend/Stdlib/CallbackHandler.php';

/**
 * Interface for intercepting filter chains
 *
 * @category   Zend
 * @package    Zend_EventManager
 * @copyright  Copyright (c) 2005-2015 Zend Technologies USA Inc. (http://www.zend.com)
 * @license    http://framework.zend.com/license/new-bsd     New BSD License
 */
interface Zend_EventManager_Filter
{
    /**
     * Execute the filter chain
     * 
     * @param  string|object $context 
     * @param  array $params 
     * @return mixed
     */
    public function run($context, array $params = array());

    /**
     * Attach an intercepting filter
     * 
     * @param  callback $callback 
     * @return Zend_Stdlib_CallbackHandler
     */
    public function attach($callback);

    /**
     * Detach an intercepting filter
     * 
     * @param  Zend_Stdlib_CallbackHandler $filter 
     * @return bool
     */
    public function detach(Zend_Stdlib_CallbackHandler $filter);

    /**
     * Get all intercepting filters
     * 
     * @return array
     */
    public function getFilters();

    /**
     * Clear all filters
     * 
     * @return void
     */
    public function clearFilters();

    /**
     * Get all filter responses
     * 
     * @return Zend_EventManager_ResponseCollection
     */
    public function getResponses();
}
