<?php
/**
 * Zend Framework
 *
 * LICENSE
 *
 * This source file is subject to the new BSD license that is bundled
 * with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://framework.zend.com/license/new-bsd
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category   Zend
 * @package    Zend_EventManager
 * @copyright  Copyright (c) 2005-2015 Zend Technologies USA Inc. (http://www.zend.com)
 * @license    http://framework.zend.com/license/new-bsd     New BSD License
 */

/**
 * Representation of an event
 *
 * @category   Zend
 * @package    Zend_EventManager
 * @copyright  Copyright (c) 2005-2015 Zend Technologies USA Inc. (http://www.zend.com)
 * @license    http://framework.zend.com/license/new-bsd     New BSD License
 */
interface Zend_EventManager_EventDescription
{
    /**
     * Get event name
     * 
     * @return string
     */
    public function getName();

    /**
     * Get target/context from which event was triggered
     * 
     * @return null|string|object
     */
    public function getTarget();

    /**
     * Get parameters passed to the event
     * 
     * @return array|ArrayAccess
     */
    public function getParams();

    /**
     * Get a single parameter by name
     * 
     * @param  string $name 
     * @param  mixed $default Default value to return if parameter does not exist
     * @return mixed
     */
    public function getParam($name, $default = null);

    /**
     * Set the event name
     * 
     * @param  string $name 
     * @return void
     */
    public function setName($name);

    /**
     * Set the event target/context
     * 
     * @param  null|string|object $target 
     * @return void
     */
    public function setTarget($target);

    /**
     * Set event parameters
     * 
     * @param  string $params 
     * @return void
     */
    public function setParams($params);

    /**
     * Set a single parameter by key
     * 
     * @param  string $name 
     * @param  mixed $value 
     * @return void
     */
    public function setParam($name, $value);

    /**
     * Indicate whether or not the parent EventCollection should stop propagating events
     * 
     * @param  bool $flag 
     * @return void
     */
    public function stopPropagation($flag = true);

    /**
     * Has this event indicated event propagation should stop?
     * 
     * @return bool
     */
    public function propagationIsStopped();
}
