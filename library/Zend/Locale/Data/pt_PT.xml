<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE ldml SYSTEM "../../common/dtd/ldml.dtd">
<!-- Copyright © 1991-2013 Unicode, Inc.
CLDR data files are interpreted according to the LDML specification (http://unicode.org/reports/tr35/)
For terms of use, see http://www.unicode.org/copyright.html
-->
<ldml>
	<identity>
		<version number="$Revision: 9852 $"/>
		<generation date="$Date: 2014-02-28 23:57:43 -0600 (Fri, 28 Feb 2014) $"/>
		<language type="pt"/>
		<territory type="PT"/>
	</identity>
	<localeDisplayNames>
		<localeDisplayPattern>
			<localePattern>{0} ({1})</localePattern>
			<localeSeparator>{0}, {1}</localeSeparator>
			<localeKeyTypePattern>{0}: {1}</localeKeyTypePattern>
		</localeDisplayPattern>
		<languages>
			<language type="ab">abcázio</language>
			<language type="ady" draft="contributed">adigue</language>
			<language type="ae" draft="contributed">avéstico</language>
			<language type="af">afrikaans</language>
			<language type="agq" draft="contributed">aghem</language>
			<language type="alt" draft="contributed">altai do sul</language>
			<language type="am">amárico</language>
			<language type="anp" draft="contributed">angika</language>
			<language type="ar">árabe</language>
			<language type="as">assamês</language>
			<language type="asa" draft="contributed">asu</language>
			<language type="av" draft="contributed">avaric</language>
			<language type="az">azerbaijano</language>
			<language type="az" alt="short">azeri</language>
			<language type="ba" draft="contributed">bashkir</language>
			<language type="bal" draft="contributed">balúchi</language>
			<language type="bax" draft="contributed">bamun</language>
			<language type="bbj" draft="contributed">ghomala</language>
			<language type="be">bielo-russo</language>
			<language type="bej" draft="contributed">beja</language>
			<language type="bez" draft="contributed">bena</language>
			<language type="bfd" draft="contributed">bafut</language>
			<language type="bg">búlgaro</language>
			<language type="bho" draft="contributed">bhojpuri</language>
			<language type="bkm" draft="contributed">kom</language>
			<language type="bn">bengali</language>
			<language type="bo">tibetano</language>
			<language type="bra" draft="contributed">braj</language>
			<language type="brx" draft="contributed">bodo</language>
			<language type="bs">bósnio</language>
			<language type="bss" draft="contributed">akoose</language>
			<language type="bua" draft="contributed">buriat</language>
			<language type="bug">buginese</language>
			<language type="bum" draft="contributed">bulu</language>
			<language type="byv" draft="contributed">medumba</language>
			<language type="ca">catalão</language>
			<language type="cay" draft="contributed">cayuga</language>
			<language type="ce">chechene</language>
			<language type="cgg" draft="contributed">chiga</language>
			<language type="chm" draft="contributed">mari</language>
			<language type="chn" draft="contributed">jargão chinook</language>
			<language type="ckb" draft="contributed">sorani curdo</language>
			<language type="co" draft="contributed">corso</language>
			<language type="cop" draft="contributed">copta</language>
			<language type="crh" draft="contributed">turco da Crimeia</language>
			<language type="cs">checo</language>
			<language type="cv" draft="contributed">chuvash</language>
			<language type="cy">galês</language>
			<language type="da">dinamarquês</language>
			<language type="dar" draft="contributed">dargwa</language>
			<language type="dav" draft="contributed">taita</language>
			<language type="de">alemão</language>
			<language type="de_AT">alemão austríaco</language>
			<language type="de_CH">alto alemão suíço</language>
			<language type="dje" draft="contributed">zarma</language>
			<language type="doi" draft="contributed">dogri</language>
			<language type="dyo" draft="contributed">jola-fonyi</language>
			<language type="dyu" draft="contributed">diula</language>
			<language type="dzg" draft="contributed">dazaga</language>
			<language type="ee" draft="contributed">ewe</language>
			<language type="egy">egípcio clássico</language>
			<language type="el">grego</language>
			<language type="en">inglês</language>
			<language type="en_AU">inglês australiano</language>
			<language type="en_CA">inglês canadiano</language>
			<language type="en_GB">inglês britânico</language>
			<language type="en_GB" alt="short">inglês (RU)</language>
			<language type="en_US">inglês americano</language>
			<language type="en_US" alt="short">inglês (EUA)</language>
			<language type="enm">inglês medieval</language>
			<language type="eo">esperanto</language>
			<language type="es">espanhol</language>
			<language type="es_419">espanhol latino-americano</language>
			<language type="es_ES">espanhol europeu</language>
			<language type="es_MX" draft="contributed">espanhol do México</language>
			<language type="et">estónio</language>
			<language type="eu">basco</language>
			<language type="fa">persa</language>
			<language type="fi">finlandês</language>
			<language type="fil">filipino</language>
			<language type="fj">fijiano</language>
			<language type="fo">feroês</language>
			<language type="fr">francês</language>
			<language type="fr_CA">francês canadiano</language>
			<language type="fr_CH">francês suíço</language>
			<language type="frm">francês medieval</language>
			<language type="frs" draft="contributed">frísio oriental</language>
			<language type="fy">frísico ocidental</language>
			<language type="ga">irlandês</language>
			<language type="gba" draft="contributed">gbaia</language>
			<language type="gl">galego</language>
			<language type="gmh">alemão medieval alto</language>
			<language type="gn">guarani</language>
			<language type="gon" draft="contributed">gondi</language>
			<language type="grc">grego clássico</language>
			<language type="gsw">alemão suíço</language>
			<language type="gu">guzerate</language>
			<language type="guz" draft="contributed">gusii</language>
			<language type="ha">haúça</language>
			<language type="haw">havaiano</language>
			<language type="he">hebraico</language>
			<language type="hi">hindi</language>
			<language type="hr">croata</language>
			<language type="hsb" draft="contributed">sorbiano superior</language>
			<language type="ht">haitiano</language>
			<language type="hu">húngaro</language>
			<language type="hy">arménio</language>
			<language type="ibb" draft="contributed">ibibio</language>
			<language type="id">indonésio</language>
			<language type="ig">igbo</language>
			<language type="ik">inupiaq</language>
			<language type="inh" draft="contributed">inguche</language>
			<language type="is">islandês</language>
			<language type="it">italiano</language>
			<language type="ja">japonês</language>
			<language type="jgo" draft="contributed">nguemba</language>
			<language type="jmc" draft="contributed">machame</language>
			<language type="jv">javanês</language>
			<language type="ka">georgiano</language>
			<language type="kaa" draft="contributed">kara-kalpak</language>
			<language type="kbd" draft="contributed">kabardiano</language>
			<language type="kbl" draft="contributed">kanembu</language>
			<language type="kde" draft="contributed">makonde</language>
			<language type="kea" draft="unconfirmed">cabo-verdiano</language>
			<language type="kg" draft="contributed">conguês</language>
			<language type="khq" draft="contributed">koyra chiini</language>
			<language type="kk">cazaque</language>
			<language type="kkj" draft="contributed">kako</language>
			<language type="kln" draft="contributed">kalenjin</language>
			<language type="km">cmer</language>
			<language type="kn">canarim</language>
			<language type="ko">coreano</language>
			<language type="kok" draft="contributed">concani</language>
			<language type="krc" draft="contributed">karachay-balkar</language>
			<language type="krl" draft="contributed">idioma carélio</language>
			<language type="kru" draft="contributed">kurukh</language>
			<language type="ks">caxemirense</language>
			<language type="ksb" draft="contributed">shambala</language>
			<language type="ksf" draft="contributed">bafia</language>
			<language type="ku">curdo</language>
			<language type="kum" draft="contributed">kumyk</language>
			<language type="kv" draft="contributed">komi</language>
			<language type="ky">quirguiz</language>
			<language type="la">latim</language>
			<language type="lag" draft="contributed">langi</language>
			<language type="lah" draft="contributed">lahnda</language>
			<language type="lb">luxemburguês</language>
			<language type="lez" draft="contributed">lezghiano</language>
			<language type="lg" draft="contributed">ganda</language>
			<language type="lo">laosiano</language>
			<language type="loz" draft="contributed">lozi</language>
			<language type="lt">lituano</language>
			<language type="lua" draft="contributed">luba-lulua</language>
			<language type="lus" draft="contributed">lushai</language>
			<language type="luy" draft="contributed">luyia</language>
			<language type="lv">letão</language>
			<language type="maf" draft="contributed">mafa</language>
			<language type="mag" draft="contributed">magahi</language>
			<language type="mai" draft="contributed">maithili</language>
			<language type="mde" draft="contributed">maba</language>
			<language type="mdf" draft="contributed">mocsa</language>
			<language type="mer" draft="contributed">meru</language>
			<language type="mfe" draft="contributed">crioulo das Ilhas Maurícias</language>
			<language type="mg">malgaxe</language>
			<language type="mga">irlandês, medieval</language>
			<language type="mgh" draft="contributed">makhuwa-meetto</language>
			<language type="mgo" draft="contributed">meta'</language>
			<language type="mi">maori</language>
			<language type="mk">macedónio</language>
			<language type="ml">malaiala</language>
			<language type="mn" draft="contributed">mongol</language>
			<language type="mni" draft="contributed">manipuri</language>
			<language type="mr">marata</language>
			<language type="ms">malaio</language>
			<language type="mt">maltês</language>
			<language type="mua" draft="contributed">mundang</language>
			<language type="mwr" draft="contributed">marwari</language>
			<language type="my">birmanês</language>
			<language type="mye" draft="contributed">myene</language>
			<language type="myv" draft="contributed">erzya</language>
			<language type="naq" draft="contributed">nama</language>
			<language type="nb">norueguês bokmål</language>
			<language type="nds" draft="contributed">baixo alemão</language>
			<language type="ne">nepali</language>
			<language type="new" draft="contributed">newari</language>
			<language type="nl">holandês</language>
			<language type="nl_BE">flamengo</language>
			<language type="nmg" draft="contributed">kwasio</language>
			<language type="nn">norueguês nynorsk</language>
			<language type="nnh" draft="contributed">ngiemboon</language>
			<language type="nog" draft="contributed">nogai</language>
			<language type="non">norse, old</language>
			<language type="nus" draft="contributed">nuer</language>
			<language type="oc" draft="contributed">provençal</language>
			<language type="or">oriya</language>
			<language type="os" draft="contributed">ossético</language>
			<language type="pa">panjabi</language>
			<language type="pi" draft="contributed">páli</language>
			<language type="pl">polaco</language>
			<language type="pro">provençal, arcaico</language>
			<language type="ps">pastó</language>
			<language type="pt">português</language>
			<language type="pt_BR">português do Brasil</language>
			<language type="pt_PT">português europeu</language>
			<language type="qu">quíchua</language>
			<language type="rm">romanche</language>
			<language type="ro">romeno</language>
			<language type="rom" draft="contributed">romanês</language>
			<language type="root" draft="contributed">root</language>
			<language type="ru">russo</language>
			<language type="sa">sânscrito</language>
			<language type="sah" draft="contributed">sakha</language>
			<language type="saq" draft="contributed">samburu</language>
			<language type="sba" draft="contributed">ngambay</language>
			<language type="sbp" draft="contributed">sangu</language>
			<language type="sd">sindi</language>
			<language type="see" draft="contributed">seneca</language>
			<language type="seh" draft="contributed">sena</language>
			<language type="sel" draft="contributed">selkup</language>
			<language type="ses" draft="contributed">koyraboro senni</language>
			<language type="shi" draft="contributed">tachelhit</language>
			<language type="shu" draft="contributed">árabe do Chade</language>
			<language type="si">cingalês</language>
			<language type="sk">eslovaco</language>
			<language type="sl">esloveno</language>
			<language type="so">somali</language>
			<language type="sq">albanês</language>
			<language type="sr">sérvio</language>
			<language type="ssy" draft="contributed">saho</language>
			<language type="su">sundanês</language>
			<language type="sv">sueco</language>
			<language type="sw">suaili</language>
			<language type="swb">língua comoriana</language>
			<language type="swc" draft="contributed">suaili do Congo</language>
			<language type="ta">tâmil</language>
			<language type="te">telugu</language>
			<language type="teo" draft="contributed">teso</language>
			<language type="tg">tajique</language>
			<language type="th">tailandês</language>
			<language type="ti">tigre</language>
			<language type="tk">turcomano</language>
			<language type="tl" draft="provisional">tagalogue</language>
			<language type="to">tonga</language>
			<language type="tog" draft="contributed">toganês</language>
			<language type="tr">turco</language>
			<language type="trv" draft="contributed">taroko</language>
			<language type="tt">tatar</language>
			<language type="twq" draft="contributed">tasawaq</language>
			<language type="tyv" draft="contributed">tuviniano</language>
			<language type="tzm" draft="contributed">tamazight de Marrocos Central</language>
			<language type="udm" draft="contributed">udmurt</language>
			<language type="ug">uighur</language>
			<language type="ug" alt="variant">uigur</language>
			<language type="uk">ucraniano</language>
			<language type="und">idioma desconhecido</language>
			<language type="ur">urdu</language>
			<language type="uz">usbeque</language>
			<language type="vi">vietnamita</language>
			<language type="vun" draft="contributed">vunjo</language>
			<language type="wo">uólofe</language>
			<language type="xal" draft="contributed">kalmyk</language>
			<language type="xh">xosa</language>
			<language type="xog" draft="contributed">soga</language>
			<language type="yav" draft="contributed">yangben</language>
			<language type="ybb" draft="contributed">yemba</language>
			<language type="yo">ioruba</language>
			<language type="zbl" draft="contributed">símbolos blis</language>
			<language type="zgh">tamaxeque padrão de Marrocos</language>
			<language type="zh">chinês</language>
			<language type="zh_Hans">chinês simplificado</language>
			<language type="zh_Hant">chinês tradicional</language>
			<language type="zu">zulu</language>
			<language type="zxx">sem conteúdo linguístico</language>
			<language type="zza" draft="contributed">zaza</language>
		</languages>
		<scripts>
			<script type="Arab">árabe</script>
			<script type="Arab" alt="variant">perso-árabe</script>
			<script type="Armi" draft="provisional">aramaico imperial</script>
			<script type="Armn">arménio</script>
			<script type="Beng">bengali</script>
			<script type="Blis">símbolos Bliss</script>
			<script type="Bopo">bopomofo</script>
			<script type="Brai">braille</script>
			<script type="Cakm" draft="provisional">chakma</script>
			<script type="Cyrl">cirílico</script>
			<script type="Deva">devanágari</script>
			<script type="Egyd" draft="contributed">egípcio demótico</script>
			<script type="Egyh" draft="contributed">egípcio hierático</script>
			<script type="Ethi">etiópico</script>
			<script type="Geor">georgiano</script>
			<script type="Grek">grego</script>
			<script type="Gujr" draft="contributed">guzerate</script>
			<script type="Guru">gurmuqui</script>
			<script type="Hang">hangul</script>
			<script type="Hani">han</script>
			<script type="Hans">han simplificado</script>
			<script type="Hans" alt="stand-alone">han simplificado</script>
			<script type="Hant">han tradicional</script>
			<script type="Hant" alt="stand-alone">han tradicional</script>
			<script type="Hebr">hebraico</script>
			<script type="Hira">hiragana</script>
			<script type="Inds">indus</script>
			<script type="Jpan">japonês</script>
			<script type="Kana">katakana</script>
			<script type="Khmr">khmer</script>
			<script type="Knda">kannada</script>
			<script type="Kore">coreano</script>
			<script type="Kthi" draft="provisional">kaithi</script>
			<script type="Laoo">lao</script>
			<script type="Latn">latim</script>
			<script type="Lina" draft="contributed">linear A</script>
			<script type="Linb">linear B</script>
			<script type="Mand" draft="provisional">mandeu</script>
			<script type="Mlym">malaiala</script>
			<script type="Mong">mongol</script>
			<script type="Mymr">birmanês</script>
			<script type="Nkoo" draft="provisional">n’ko</script>
			<script type="Orya">oriya</script>
			<script type="Phli" draft="provisional">pahlavi escrito</script>
			<script type="Prti" draft="provisional">parthian escrito</script>
			<script type="Sgnw" draft="provisional">escrita gestual</script>
			<script type="Sinh">cingalês</script>
			<script type="Sylo">siloti nagri</script>
			<script type="Tale">tai le</script>
			<script type="Taml">tâmil</script>
			<script type="Telu">telugu</script>
			<script type="Thaa">thaana</script>
			<script type="Thai">tailandês</script>
			<script type="Tibt">tibetano</script>
			<script type="Xsux">cuneiforme sumero-acadiano</script>
			<script type="Zsym">símbolos</script>
			<script type="Zxxx">não escrito</script>
			<script type="Zyyy">comum</script>
			<script type="Zzzz">escrita desconhecida</script>
		</scripts>
		<territories>
			<territory type="001">Mundo</territory>
			<territory type="002">África</territory>
			<territory type="003">América do Norte</territory>
			<territory type="005">América do Sul</territory>
			<territory type="009">Oceânia</territory>
			<territory type="011">África Ocidental</territory>
			<territory type="013">América Central</territory>
			<territory type="014">África Oriental</territory>
			<territory type="015">Norte de África</territory>
			<territory type="017">África Central</territory>
			<territory type="018">África Austral</territory>
			<territory type="019">Américas</territory>
			<territory type="021">América Setentrional</territory>
			<territory type="029">Caraíbas</territory>
			<territory type="030">Ásia Oriental</territory>
			<territory type="034">Ásia do Sul</territory>
			<territory type="035">Sudeste Asiático</territory>
			<territory type="039">Europa do Sul</territory>
			<territory type="053">Australásia</territory>
			<territory type="054">Melanésia</territory>
			<territory type="057">Região da Micronésia</territory>
			<territory type="061">Polinésia</territory>
			<territory type="142">Ásia</territory>
			<territory type="143">Ásia Central</territory>
			<territory type="145">Ásia Ocidental</territory>
			<territory type="150">Europa</territory>
			<territory type="151">Europa Oriental</territory>
			<territory type="154">Europa do Norte</territory>
			<territory type="155">Europa Ocidental</territory>
			<territory type="419">América Latina</territory>
			<territory type="AC">Ilha de Ascensão</territory>
			<territory type="AD">Andorra</territory>
			<territory type="AE">Emirados Árabes Unidos</territory>
			<territory type="AF">Afeganistão</territory>
			<territory type="AG">Antígua e Barbuda</territory>
			<territory type="AI">Anguila</territory>
			<territory type="AL">Albânia</territory>
			<territory type="AM">Arménia</territory>
			<territory type="AO">Angola</territory>
			<territory type="AQ">Antártida</territory>
			<territory type="AR">Argentina</territory>
			<territory type="AS">Samoa Americana</territory>
			<territory type="AT">Áustria</territory>
			<territory type="AU">Austrália</territory>
			<territory type="AW">Aruba</territory>
			<territory type="AX">Ilhas Åland</territory>
			<territory type="AZ">Azerbaijão</territory>
			<territory type="BA">Bósnia e Herzegovina</territory>
			<territory type="BB">Barbados</territory>
			<territory type="BD">Bangladesh</territory>
			<territory type="BE">Bélgica</territory>
			<territory type="BF">Burquina Faso</territory>
			<territory type="BG">Bulgária</territory>
			<territory type="BH">Barém</territory>
			<territory type="BI">Burundi</territory>
			<territory type="BJ">Benim</territory>
			<territory type="BL">São Bartolomeu</territory>
			<territory type="BM">Bermudas</territory>
			<territory type="BN">Brunei</territory>
			<territory type="BO">Bolívia</territory>
			<territory type="BQ">Países Baixos Caribenhos</territory>
			<territory type="BR">Brasil</territory>
			<territory type="BS">Bahamas</territory>
			<territory type="BT">Butão</territory>
			<territory type="BV">Ilha Bouvet</territory>
			<territory type="BW">Botswana</territory>
			<territory type="BY">Bielorrússia</territory>
			<territory type="BZ">Belize</territory>
			<territory type="CA">Canadá</territory>
			<territory type="CC">Ilhas Cocos</territory>
			<territory type="CD">Congo-Kinshasa</territory>
			<territory type="CD" alt="variant">República Democrática do Congo</territory>
			<territory type="CF">República Centro-Africana</territory>
			<territory type="CG">Congo-Brazzaville</territory>
			<territory type="CG" alt="variant">República do Congo</territory>
			<territory type="CH">Suíça</territory>
			<territory type="CI">Costa do Marfim</territory>
			<territory type="CK">Ilhas Cook</territory>
			<territory type="CL">Chile</territory>
			<territory type="CM">Camarões</territory>
			<territory type="CN">China</territory>
			<territory type="CO">Colômbia</territory>
			<territory type="CP">Ilha de Clipperton</territory>
			<territory type="CR">Costa Rica</territory>
			<territory type="CU">Cuba</territory>
			<territory type="CV">Cabo Verde</territory>
			<territory type="CW">Curaçau</territory>
			<territory type="CX">Ilha do Natal</territory>
			<territory type="CY">Chipre</territory>
			<territory type="CZ">República Checa</territory>
			<territory type="DE">Alemanha</territory>
			<territory type="DG">Diego Garcia</territory>
			<territory type="DJ">Jibuti</territory>
			<territory type="DK">Dinamarca</territory>
			<territory type="DM">Domínica</territory>
			<territory type="DO">República Dominicana</territory>
			<territory type="DZ">Argélia</territory>
			<territory type="EA">Ceuta e Melilha</territory>
			<territory type="EC">Equador</territory>
			<territory type="EE">Estónia</territory>
			<territory type="EG">Egipto</territory>
			<territory type="EH">Saara Ocidental</territory>
			<territory type="ER">Eritreia</territory>
			<territory type="ES">Espanha</territory>
			<territory type="ET">Etiópia</territory>
			<territory type="EU">União Europeia</territory>
			<territory type="FI">Finlândia</territory>
			<territory type="FJ">Fiji</territory>
			<territory type="FK">Ilhas Falkland</territory>
			<territory type="FK" alt="variant">Ilhas Malvinas</territory>
			<territory type="FM">Micronésia</territory>
			<territory type="FO">Ilhas Faroé</territory>
			<territory type="FR">França</territory>
			<territory type="GA">Gabão</territory>
			<territory type="GB">Reino Unido</territory>
			<territory type="GB" alt="short">RU</territory>
			<territory type="GD">Granada</territory>
			<territory type="GE">Geórgia</territory>
			<territory type="GF">Guiana Francesa</territory>
			<territory type="GG">Guernsey</territory>
			<territory type="GH">Gana</territory>
			<territory type="GI">Gibraltar</territory>
			<territory type="GL">Gronelândia</territory>
			<territory type="GM">Gâmbia</territory>
			<territory type="GN">Guiné</territory>
			<territory type="GP">Guadalupe</territory>
			<territory type="GQ">Guiné Equatorial</territory>
			<territory type="GR">Grécia</territory>
			<territory type="GS">Ilhas Geórgia do Sul e Sandwich do Sul</territory>
			<territory type="GT">Guatemala</territory>
			<territory type="GU">Guame</territory>
			<territory type="GW">Guiné-Bissau</territory>
			<territory type="GY">Guiana</territory>
			<territory type="HK">Hong Kong, RAE da China</territory>
			<territory type="HK" alt="short">Hong Kong</territory>
			<territory type="HM">Ilhas Heard e McDonald</territory>
			<territory type="HN">Honduras</territory>
			<territory type="HR">Croácia</territory>
			<territory type="HT">Haiti</territory>
			<territory type="HU">Hungria</territory>
			<territory type="IC">Ilhas Canárias</territory>
			<territory type="ID">Indonésia</territory>
			<territory type="IE">Irlanda</territory>
			<territory type="IL">Israel</territory>
			<territory type="IM">Ilha de Man</territory>
			<territory type="IN">Índia</territory>
			<territory type="IO">Território Britânico do Oceano Índico</territory>
			<territory type="IQ">Iraque</territory>
			<territory type="IR">Irão</territory>
			<territory type="IS">Islândia</territory>
			<territory type="IT">Itália</territory>
			<territory type="JE">Jersey</territory>
			<territory type="JM">Jamaica</territory>
			<territory type="JO">Jordânia</territory>
			<territory type="JP">Japão</territory>
			<territory type="KE">Quénia</territory>
			<territory type="KG">Quirguizistão</territory>
			<territory type="KH">Camboja</territory>
			<territory type="KI">Quiribati</territory>
			<territory type="KM">Comoros</territory>
			<territory type="KN">São Cristóvão e Nevis</territory>
			<territory type="KP">Coreia do Norte</territory>
			<territory type="KR">Coreia do Sul</territory>
			<territory type="KW">Kuwait</territory>
			<territory type="KY">Ilhas Caimão</territory>
			<territory type="KZ">Cazaquistão</territory>
			<territory type="LA">Laos</territory>
			<territory type="LB">Líbano</territory>
			<territory type="LC">Santa Lúcia</territory>
			<territory type="LI">Liechtenstein</territory>
			<territory type="LK" draft="provisional">Sri Lanca</territory>
			<territory type="LR">Libéria</territory>
			<territory type="LS">Lesoto</territory>
			<territory type="LT">Lituânia</territory>
			<territory type="LU">Luxemburgo</territory>
			<territory type="LV">Letónia</territory>
			<territory type="LY">Líbia</territory>
			<territory type="MA">Marrocos</territory>
			<territory type="MC">Mónaco</territory>
			<territory type="MD">Moldávia</territory>
			<territory type="ME">Montenegro</territory>
			<territory type="MF">São Martinho</territory>
			<territory type="MG">Madagáscar</territory>
			<territory type="MH">Ilhas Marshall</territory>
			<territory type="MK">Macedónia</territory>
			<territory type="MK" alt="variant">Antiga República Jugoslava da Macedónia</territory>
			<territory type="ML">Mali</territory>
			<territory type="MM">Mianmar (Birmânia)</territory>
			<territory type="MN">Mongólia</territory>
			<territory type="MO">Macau, RAE da China</territory>
			<territory type="MO" alt="short">Macau</territory>
			<territory type="MP">Ilhas Marianas do Norte</territory>
			<territory type="MQ">Martinica</territory>
			<territory type="MR">Mauritânia</territory>
			<territory type="MS">Monserrate</territory>
			<territory type="MT">Malta</territory>
			<territory type="MU">Maurícia</territory>
			<territory type="MV">Maldivas</territory>
			<territory type="MW">Malawi</territory>
			<territory type="MX">México</territory>
			<territory type="MY">Malásia</territory>
			<territory type="MZ">Moçambique</territory>
			<territory type="NA">Namíbia</territory>
			<territory type="NC">Nova Caledónia</territory>
			<territory type="NE">Níger</territory>
			<territory type="NF">Ilha Norfolk</territory>
			<territory type="NG">Nigéria</territory>
			<territory type="NI">Nicarágua</territory>
			<territory type="NL">Países Baixos</territory>
			<territory type="NO">Noruega</territory>
			<territory type="NP">Nepal</territory>
			<territory type="NR">Nauru</territory>
			<territory type="NU">Niue</territory>
			<territory type="NZ">Nova Zelândia</territory>
			<territory type="OM">Omã</territory>
			<territory type="PA">Panamá</territory>
			<territory type="PE">Peru</territory>
			<territory type="PF">Polinésia Francesa</territory>
			<territory type="PG">Papua-Nova Guiné</territory>
			<territory type="PH">Filipinas</territory>
			<territory type="PK">Paquistão</territory>
			<territory type="PL">Polónia</territory>
			<territory type="PM">Saint Pierre e Miquelon</territory>
			<territory type="PN">Pitcairn</territory>
			<territory type="PR">Porto Rico</territory>
			<territory type="PS">Território Palestiniano</territory>
			<territory type="PS" alt="short">Palestina</territory>
			<territory type="PT">Portugal</territory>
			<territory type="PW">Palau</territory>
			<territory type="PY">Paraguai</territory>
			<territory type="QA">Catar</territory>
			<territory type="QO">Oceânia Insular</territory>
			<territory type="RE">Reunião</territory>
			<territory type="RO">Roménia</territory>
			<territory type="RS">Sérvia</territory>
			<territory type="RU">Rússia</territory>
			<territory type="RW">Ruanda</territory>
			<territory type="SA">Arábia Saudita</territory>
			<territory type="SB">Ilhas Salomão</territory>
			<territory type="SC">Seicheles</territory>
			<territory type="SD">Sudão</territory>
			<territory type="SE">Suécia</territory>
			<territory type="SG">Singapura</territory>
			<territory type="SH">Santa Helena</territory>
			<territory type="SI">Eslovénia</territory>
			<territory type="SJ">Svalbard e Jan Mayen</territory>
			<territory type="SK">Eslováquia</territory>
			<territory type="SL">Serra Leoa</territory>
			<territory type="SM">São Marino</territory>
			<territory type="SN">Senegal</territory>
			<territory type="SO">Somália</territory>
			<territory type="SR">Suriname</territory>
			<territory type="SS">Sudão do Sul</territory>
			<territory type="ST">São Tomé e Príncipe</territory>
			<territory type="SV">El Salvador</territory>
			<territory type="SX">Sint Maarten</territory>
			<territory type="SY">Síria</territory>
			<territory type="SZ">Suazilândia</territory>
			<territory type="TA">Tristão da Cunha</territory>
			<territory type="TC">Ilhas Turcas e Caicos</territory>
			<territory type="TD">Chade</territory>
			<territory type="TF">Territórios Franceses do Sul</territory>
			<territory type="TG">Togo</territory>
			<territory type="TH">Tailândia</territory>
			<territory type="TJ">Tajiquistão</territory>
			<territory type="TK">Toquelau</territory>
			<territory type="TL">Timor-Leste</territory>
			<territory type="TM">Turquemenistão</territory>
			<territory type="TN">Tunísia</territory>
			<territory type="TO">Tonga</territory>
			<territory type="TR">Turquia</territory>
			<territory type="TT">Trindade e Tobago</territory>
			<territory type="TV">Tuvalu</territory>
			<territory type="TW">Taiwan</territory>
			<territory type="TZ">Tanzânia</territory>
			<territory type="UA">Ucrânia</territory>
			<territory type="UG">Uganda</territory>
			<territory type="UM">Ilhas Distantes dos EUA</territory>
			<territory type="US">Estados Unidos</territory>
			<territory type="US" alt="short" draft="contributed">EUA</territory>
			<territory type="UY">Uruguai</territory>
			<territory type="UZ">Uzbequistão</territory>
			<territory type="VA">Cidade do Vaticano</territory>
			<territory type="VC">São Vicente e Granadinas</territory>
			<territory type="VE">Venezuela</territory>
			<territory type="VG">Ilhas Virgens Britânicas</territory>
			<territory type="VI">Ilhas Virgens dos EUA</territory>
			<territory type="VN">Vietname</territory>
			<territory type="VU">Vanuatu</territory>
			<territory type="WF">Wallis e Futuna</territory>
			<territory type="WS">Samoa</territory>
			<territory type="XK">Kosovo</territory>
			<territory type="YE">Iémen</territory>
			<territory type="YT">Maiote</territory>
			<territory type="ZA">África do Sul</territory>
			<territory type="ZM">Zâmbia</territory>
			<territory type="ZW">Zimbabué</territory>
			<territory type="ZZ">Região desconhecida ou inválida</territory>
		</territories>
		<variants>
			<variant type="1901" draft="provisional">Ortografia alemã tradicional</variant>
			<variant type="1994" draft="provisional">Ortografia resiana padronizada</variant>
			<variant type="1996" draft="provisional">Ortografia alemã de 1996</variant>
			<variant type="1606NICT" draft="provisional">Francês antigo de 1606</variant>
			<variant type="1694ACAD" draft="provisional">Francês da idade moderna</variant>
			<variant type="1959ACAD" draft="provisional">Académica</variant>
			<variant type="AREVELA" draft="contributed">arménio oriental</variant>
			<variant type="AREVMDA" draft="contributed">arménio ocidental</variant>
			<variant type="BAKU1926" draft="provisional">Alfabeto latino turco unificado</variant>
			<variant type="BISKE" draft="contributed">dialecto san giorgio/bila</variant>
			<variant type="BOONT" draft="contributed">buntlingue</variant>
			<variant type="FONIPA" draft="provisional">Fonética do Alfabeto Fonético Internacional</variant>
			<variant type="FONUPA" draft="provisional">Fonética do Alfabeto Fonético Urálico</variant>
			<variant type="KKCOR" draft="provisional">Ortografia Comum</variant>
			<variant type="LIPAW" draft="contributed">dialecto lipovaz de Resian</variant>
			<variant type="MONOTON" draft="contributed">monotónico</variant>
			<variant type="NEDIS" draft="contributed">dialecto natisone</variant>
			<variant type="NJIVA" draft="contributed">dialecto gniva/njiva</variant>
			<variant type="OSOJS" draft="contributed">dialecto oseacco/osojane</variant>
			<variant type="PINYIN">Romanização pinyin</variant>
			<variant type="POLYTON" draft="contributed">politónico</variant>
			<variant type="POSIX" draft="provisional">Computador</variant>
			<variant type="REVISED" draft="provisional">Ortografia Modificada</variant>
			<variant type="ROZAJ" draft="provisional">Resiano</variant>
			<variant type="SAAHO" draft="provisional">Saho</variant>
			<variant type="SCOTLAND" draft="contributed">inglês padrão escocês</variant>
			<variant type="SCOUSE" draft="provisional">Scouse</variant>
			<variant type="SOLBA" draft="contributed">dialecto stolvizza/solbica</variant>
			<variant type="TARASK" draft="provisional">Ortografia taraskievica</variant>
			<variant type="UCCOR" draft="provisional">Ortografia Unificada</variant>
			<variant type="UCRCOR" draft="provisional">Ortografia Modificada Unificada</variant>
			<variant type="VALENCIA" draft="provisional">Valenciano</variant>
			<variant type="WADEGILE">Romanização Wade-Giles</variant>
		</variants>
		<keys>
			<key type="calendar">Calendário</key>
			<key type="colAlternate" draft="contributed">Ignorar Ordenação de Símbolos</key>
			<key type="colBackwards" draft="contributed">Ordenação de Acentos Invertida</key>
			<key type="colCaseFirst" draft="contributed">Disposição de Maiúsculas/Minúsculas</key>
			<key type="colCaseLevel" draft="contributed">Ordenação Sensível a Maiúsculas e Minúsculas</key>
			<key type="colHiraganaQuaternary" draft="contributed">Ordenação Kana</key>
			<key type="collation">Ordenação</key>
			<key type="colNormalization" draft="contributed">Ordenação Normalizada</key>
			<key type="colNumeric" draft="contributed">Ordenação Numérica</key>
			<key type="colStrength" draft="contributed">Força da Ordenação</key>
			<key type="currency">Moeda</key>
			<key type="numbers">Números</key>
			<key type="timezone" draft="contributed">Fuso Horário</key>
			<key type="va" draft="contributed">Variante Local</key>
			<key type="variableTop" draft="contributed">Ordenar Como Símbolos</key>
			<key type="x" draft="contributed">Utilização Privada</key>
		</keys>
		<types>
			<type type="arab" key="numbers">Algarismos indo-arábicos</type>
			<type type="arabext" key="numbers">Algarismos indo-arábicos por extenso</type>
			<type type="armn" key="numbers">Algarismos Arménios</type>
			<type type="armnlow" key="numbers">Numeração Arménia Minúscula</type>
			<type type="beng" key="numbers">Algarismos Bengalis</type>
			<type type="chinese" key="calendar">Calendário Chinês</type>
			<type type="coptic" key="calendar" draft="contributed">Calendário Copta</type>
			<type type="deva" key="numbers">Algarismos Devanágaris</type>
			<type type="dictionary" key="collation" draft="contributed">Ordenação do Dicionário</type>
			<type type="ducet" key="collation">Ordenação Unicode Predefinida</type>
			<type type="ethi" key="numbers">Numeração Etíope</type>
			<type type="ethiopic" key="calendar" draft="contributed">Calendário Etíope</type>
			<type type="ethiopic-amete-alem" key="calendar" draft="contributed">Calendário Etíope Amete Alem</type>
			<type type="finance" key="numbers" draft="contributed">Algarismos Financeiros</type>
			<type type="fullwide" key="numbers">Algarismos de Largura Completa</type>
			<type type="geor" key="numbers">Numeração Georgiana</type>
			<type type="gregorian" key="calendar">Calendário Gregoriano</type>
			<type type="grek" key="numbers">Numeração Grega</type>
			<type type="greklow" key="numbers">Numeração Grega Minúscula</type>
			<type type="gujr" key="numbers">Algarismos de Guzerate</type>
			<type type="guru" key="numbers">Algarismos Gurmukhi</type>
			<type type="hanidec" key="numbers">Numeração Decimal Chinesa</type>
			<type type="hans" key="numbers">Numeração Chinês Simplificado</type>
			<type type="hansfin" key="numbers">Numeração Financeira Chinês Simplificado</type>
			<type type="hant" key="numbers">Numeração Chinês Tradicional</type>
			<type type="hantfin" key="numbers">Numeração Financeira Chinês Tradicional</type>
			<type type="hebr" key="numbers">Numeração Hebraica</type>
			<type type="identical" key="colStrength" draft="contributed">Ordenar Tudo</type>
			<type type="islamic-civil" key="calendar" draft="contributed">Calendário Islâmico/Civil</type>
			<type type="jpan" key="numbers">Numeração Japonesa</type>
			<type type="jpanfin" key="numbers">Numeração Financeira Japonesa</type>
			<type type="khmr" key="numbers">Algarismos Khmer</type>
			<type type="knda" key="numbers">Algarismos de Canarim</type>
			<type type="laoo" key="numbers">Algarismos de Laos</type>
			<type type="latn" key="numbers">Algarismos Ocidentais</type>
			<type type="lower" key="colCaseFirst" draft="contributed">Ordenar Minúsculas Primeiro</type>
			<type type="mlym" key="numbers">Algarismos Malaiala</type>
			<type type="mong" key="numbers" draft="contributed">Algarismos Mongóis</type>
			<type type="mymr" key="numbers">Algarismos de Mianmar</type>
			<type type="native" key="numbers" draft="contributed">Dígitos Nativos</type>
			<type type="no" key="colBackwards" draft="contributed">Ordenar Acentos Normalmente</type>
			<type type="no" key="colCaseFirst" draft="contributed">Ordenar Disposição de Tipo de Letra Normal</type>
			<type type="no" key="colCaseLevel" draft="contributed">Ordenar Insensível a Maiúsculas/Minúsculas</type>
			<type type="no" key="colHiraganaQuaternary" draft="contributed">Ordenar Kana Separadamente</type>
			<type type="no" key="colNormalization" draft="contributed">Ordenar Sem Normalização</type>
			<type type="no" key="colNumeric" draft="contributed">Ordenar Dígitos Individualmente</type>
			<type type="non-ignorable" key="colAlternate" draft="contributed">Ordenar Símbolos</type>
			<type type="orya" key="numbers">Algarismos Oriá</type>
			<type type="persian" key="calendar" draft="contributed">Calendário Persa</type>
			<type type="phonebook" key="collation">Ordem da Lista Telefónica</type>
			<type type="phonetic" key="collation" draft="contributed">Sequência de Ordenação Fonética</type>
			<type type="pinyin" key="collation" draft="contributed">Ordem do Chinês Simplificado Pinyin</type>
			<type type="primary" key="colStrength" draft="contributed">Ordenar Apenas Letras Básicas</type>
			<type type="quaternary" key="colStrength" draft="contributed">Ordenar Acentos/Tipo de Letra/Largura/Kana</type>
			<type type="reformed" key="collation" draft="contributed">Reforma da Ordenação</type>
			<type type="roman" key="numbers">Numeração Romana</type>
			<type type="romanlow" key="numbers">Numeração Romana Minúscula</type>
			<type type="search" key="collation">Pesquisa de uso geral</type>
			<type type="searchjl" key="collation" draft="contributed">Pesquisar Por Consoante Inicial Hangul</type>
			<type type="secondary" key="colStrength" draft="contributed">Ordenar Acentos</type>
			<type type="shifted" key="colAlternate" draft="contributed">Ordenar Símbolos Ignorados</type>
			<type type="standard" key="collation">Ordenação Predefinida</type>
			<type type="stroke" key="collation">Ordem por traços</type>
			<type type="taml" key="numbers">Numeração Tâmil</type>
			<type type="tamldec" key="numbers">Algarismos Tâmil</type>
			<type type="telu" key="numbers">Algarismos de Telugu</type>
			<type type="tertiary" key="colStrength" draft="contributed">Ordenar Acentos/Tipo de Letra/Largura</type>
			<type type="thai" key="numbers">Algarismos Tailandeses</type>
			<type type="tibt" key="numbers">Algarismos Tibetanos</type>
			<type type="traditional" key="collation" draft="contributed">Ordem tradicional</type>
			<type type="traditional" key="numbers" draft="contributed">Algarismos Tradicionais</type>
			<type type="unihan" key="collation">Ordem por radical e traços</type>
			<type type="upper" key="colCaseFirst" draft="contributed">Ordenar Maiúsculas Primeiro</type>
			<type type="vaii" key="numbers" draft="contributed">Dígitos Vai</type>
			<type type="yes" key="colBackwards" draft="contributed">Ordenar Acentos Inversamente</type>
			<type type="yes" key="colCaseLevel" draft="contributed">Ordenar Sensível a Maiúsculas/Minúsculas</type>
			<type type="yes" key="colHiraganaQuaternary" draft="contributed">Ordenar Kana Diferentemente</type>
			<type type="yes" key="colNormalization" draft="contributed">Ordenar Unicode Normalizado</type>
			<type type="yes" key="colNumeric" draft="contributed">Ordenar Dígitos Numericamente</type>
		</types>
		<transformNames>
			<transformName type="x-Accents" draft="contributed">Acentos</transformName>
			<transformName type="x-Fullwidth" draft="contributed">Largura Completa</transformName>
			<transformName type="x-Halfwidth" draft="contributed">Meia Largura</transformName>
		</transformNames>
		<measurementSystemNames>
			<measurementSystemName type="metric">Métrico</measurementSystemName>
			<measurementSystemName type="UK">Reino Unido</measurementSystemName>
			<measurementSystemName type="US">Estados Unidos</measurementSystemName>
		</measurementSystemNames>
		<codePatterns>
			<codePattern type="language">Idioma: {0}</codePattern>
			<codePattern type="script">Escrita: {0}</codePattern>
			<codePattern type="territory">Região: {0}</codePattern>
		</codePatterns>
	</localeDisplayNames>
	<characters>
		<exemplarCharacters type="punctuation">[\- ‐ – — , ; \: ! ? . … ' &quot; “ ” « » ( ) \[ \] § @ * / \&amp; # † ‡ ′ ″]</exemplarCharacters>
		<ellipsis type="final">{0}…</ellipsis>
		<ellipsis type="initial">…{0}</ellipsis>
		<ellipsis type="medial">{0}…{1}</ellipsis>
		<ellipsis type="word-final">{0}…</ellipsis>
		<ellipsis type="word-initial">… {0}</ellipsis>
		<ellipsis type="word-medial" draft="contributed">{0} … {1}</ellipsis>
		<moreInformation>?</moreInformation>
	</characters>
	<delimiters>
		<quotationStart>«</quotationStart>
		<quotationEnd>»</quotationEnd>
		<alternateQuotationStart>“</alternateQuotationStart>
		<alternateQuotationEnd>”</alternateQuotationEnd>
	</delimiters>
	<dates>
		<calendars>
			<calendar type="buddhist">
				<dateFormats>
					<dateFormatLength type="short">
						<dateFormat>
							<pattern>d/M/y G</pattern>
						</dateFormat>
					</dateFormatLength>
				</dateFormats>
			</calendar>
			<calendar type="chinese">
				<months>
					<monthContext type="format">
						<monthWidth type="abbreviated">
							<month type="1">M1</month>
							<month type="2">M2</month>
							<month type="3">M3</month>
							<month type="4">M4</month>
							<month type="5">M5</month>
							<month type="6">M6</month>
							<month type="7">M7</month>
							<month type="8">M8</month>
							<month type="9">M9</month>
							<month type="10">M10</month>
							<month type="11">M11</month>
							<month type="12">M12</month>
						</monthWidth>
						<monthWidth type="narrow">
							<month type="1">1</month>
							<month type="2">2</month>
							<month type="3">3</month>
							<month type="4">4</month>
							<month type="5">5</month>
							<month type="6">6</month>
							<month type="7">7</month>
							<month type="8">8</month>
							<month type="9">9</month>
							<month type="10">10</month>
							<month type="11">11</month>
							<month type="12">12</month>
						</monthWidth>
						<monthWidth type="wide">
							<month type="1">Mês 1</month>
							<month type="2">Mês 2</month>
							<month type="3">Mês 3</month>
							<month type="4">Mês 4</month>
							<month type="5">Mês 5</month>
							<month type="6">Mês 6</month>
							<month type="7">Mês 7</month>
							<month type="8">Mês 8</month>
							<month type="9">Mês 9</month>
							<month type="10">Mês 10</month>
							<month type="11">Mês 11</month>
							<month type="12">Mês 12</month>
						</monthWidth>
					</monthContext>
					<monthContext type="stand-alone">
						<monthWidth type="abbreviated">
							<month type="1">M1</month>
							<month type="2">M2</month>
							<month type="3">M3</month>
							<month type="4">M4</month>
							<month type="5">M5</month>
							<month type="6">M6</month>
							<month type="7">M7</month>
							<month type="8">M8</month>
							<month type="9">M9</month>
							<month type="10">M10</month>
							<month type="11">M11</month>
							<month type="12">M12</month>
						</monthWidth>
						<monthWidth type="narrow">
							<month type="1">1</month>
							<month type="2">2</month>
							<month type="3">3</month>
							<month type="4">4</month>
							<month type="5">5</month>
							<month type="6">6</month>
							<month type="7">7</month>
							<month type="8">8</month>
							<month type="9">9</month>
							<month type="10">10</month>
							<month type="11">11</month>
							<month type="12">12</month>
						</monthWidth>
						<monthWidth type="wide">
							<month type="1">Mês 1</month>
							<month type="2">Mês 2</month>
							<month type="3">Mês 3</month>
							<month type="4">Mês 4</month>
							<month type="5">Mês 5</month>
							<month type="6">Mês 6</month>
							<month type="7">Mês 7</month>
							<month type="8">Mês 8</month>
							<month type="9">Mês 9</month>
							<month type="10">Mês 10</month>
							<month type="11">Mês 11</month>
							<month type="12">Mês 12</month>
						</monthWidth>
					</monthContext>
				</months>
				<dateFormats>
					<dateFormatLength type="full">
						<dateFormat>
							<pattern>EEEE, d 'de' MMMM 'de' U</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="long">
						<dateFormat>
							<pattern>d 'de' MMMM 'de' U</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="medium">
						<dateFormat>
							<pattern>d 'de' MMM 'de' U</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="short">
						<dateFormat>
							<pattern>dd/MM/yy</pattern>
						</dateFormat>
					</dateFormatLength>
				</dateFormats>
			</calendar>
			<calendar type="generic">
				<dateFormats>
					<dateFormatLength type="full">
						<dateFormat>
							<pattern>EEEE, d 'de' MMMM 'de' y G</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="long">
						<dateFormat>
							<pattern>d 'de' MMMM 'de' y G</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="medium">
						<dateFormat>
							<pattern>d 'de' MMM, y G</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="short">
						<dateFormat>
							<pattern>d/M/y G</pattern>
						</dateFormat>
					</dateFormatLength>
				</dateFormats>
				<dateTimeFormats>
					<dateTimeFormatLength type="full">
						<dateTimeFormat>
							<pattern>{1} 'às' {0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<dateTimeFormatLength type="long">
						<dateTimeFormat>
							<pattern>{1} 'às' {0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<dateTimeFormatLength type="medium">
						<dateTimeFormat>
							<pattern>{1}, {0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<dateTimeFormatLength type="short">
						<dateTimeFormat>
							<pattern>{1}, {0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<availableFormats>
						<dateFormatItem id="d">d</dateFormatItem>
						<dateFormatItem id="Ed">E, d</dateFormatItem>
						<dateFormatItem id="Gy">y G</dateFormatItem>
						<dateFormatItem id="GyMMM">MMM 'de' y G</dateFormatItem>
						<dateFormatItem id="GyMMMd">d 'de' MMM 'de' y G</dateFormatItem>
						<dateFormatItem id="GyMMMEd">E, d 'de' MMM 'de' y G</dateFormatItem>
						<dateFormatItem id="M">L</dateFormatItem>
						<dateFormatItem id="Md">d/M</dateFormatItem>
						<dateFormatItem id="MEd">E, dd/MM</dateFormatItem>
						<dateFormatItem id="MMM">LLL</dateFormatItem>
						<dateFormatItem id="MMMd">d 'de' MMM</dateFormatItem>
						<dateFormatItem id="MMMEd">E, d 'de' MMM</dateFormatItem>
						<dateFormatItem id="MMMMd">d 'de' MMMM</dateFormatItem>
						<dateFormatItem id="MMMMEd">E, d 'de' MMMM</dateFormatItem>
						<dateFormatItem id="y">y G</dateFormatItem>
						<dateFormatItem id="yyyy">y G</dateFormatItem>
						<dateFormatItem id="yyyyM">MM/y GGGGG</dateFormatItem>
						<dateFormatItem id="yyyyMd">dd/MM/y GGGGG</dateFormatItem>
						<dateFormatItem id="yyyyMEd">E, dd/MM/y GGGGG</dateFormatItem>
						<dateFormatItem id="yyyyMMM">MM/y G</dateFormatItem>
						<dateFormatItem id="yyyyMMMd" draft="contributed">d/MM/y G</dateFormatItem>
						<dateFormatItem id="yyyyMMMEd">E, d/MM/y G</dateFormatItem>
						<dateFormatItem id="yyyyMMMEEEEd">EEEE, d/MM/y</dateFormatItem>
						<dateFormatItem id="yyyyMMMM">MMMM 'de' y G</dateFormatItem>
						<dateFormatItem id="yyyyMMMMd">d 'de' MMMM 'de' y G</dateFormatItem>
						<dateFormatItem id="yyyyMMMMEd">E, d 'de' MMMM 'de' y G</dateFormatItem>
						<dateFormatItem id="yyyyQQQ">QQQQ 'de' y G</dateFormatItem>
						<dateFormatItem id="yyyyQQQQ">QQQQ 'de' y G</dateFormatItem>
					</availableFormats>
					<intervalFormats>
						<intervalFormatFallback>{0} - {1}</intervalFormatFallback>
						<intervalFormatItem id="d">
							<greatestDifference id="d">d - d</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="h">
							<greatestDifference id="a">h a - h a</greatestDifference>
							<greatestDifference id="h">h-h a</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="hm">
							<greatestDifference id="h">h:mm - h:mm a</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="M">
							<greatestDifference id="M">M - M</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="Md">
							<greatestDifference id="d">dd/MM - dd/MM</greatestDifference>
							<greatestDifference id="M">dd/MM - dd/MM</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MEd">
							<greatestDifference id="d">E, dd/MM - E, dd/MM</greatestDifference>
							<greatestDifference id="M">E, dd/MM - E, dd/MM</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MMM">
							<greatestDifference id="M">MMM-MMM</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MMMd">
							<greatestDifference id="d">d-d 'de' MMM</greatestDifference>
							<greatestDifference id="M">d 'de' MMM - d 'de' MMM</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MMMEd">
							<greatestDifference id="d">E, d 'de' MMM - E, d 'de' MMM</greatestDifference>
							<greatestDifference id="M">E, d 'de' MMM - E, d 'de' MMM</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="y">
							<greatestDifference id="y">y-y G</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yM">
							<greatestDifference id="M">MM/y - MM/y G</greatestDifference>
							<greatestDifference id="y">MM/y - MM/y G</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMd">
							<greatestDifference id="d">dd/MM/y - dd/MM/y G</greatestDifference>
							<greatestDifference id="M">dd/MM/y - dd/MM/y G</greatestDifference>
							<greatestDifference id="y">dd/MM/y - dd/MM/y G</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMEd">
							<greatestDifference id="d">E, dd/MM/y - E, dd/MM/y G</greatestDifference>
							<greatestDifference id="M">E, dd/MM/y - E, dd/MM/y G</greatestDifference>
							<greatestDifference id="y">E, dd/MM/y - E, dd/MM/y G</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMMM">
							<greatestDifference id="M">MMM-MMM 'de' y G</greatestDifference>
							<greatestDifference id="y">MMM 'de' y - MMM 'de' y G</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMMMd">
							<greatestDifference id="d">d-d 'de' MMM 'de' y</greatestDifference>
							<greatestDifference id="M">d 'de' MMM - d 'de' MMM 'de' y G</greatestDifference>
							<greatestDifference id="y">d 'de' MMM 'de' y - d 'de' MMM 'de' y G</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMMMEd">
							<greatestDifference id="d">E, d 'de' MMM - E, d 'de' MMM 'de' y G</greatestDifference>
							<greatestDifference id="M">E, d 'de' MMM - E, d 'de' MMM 'de' y G</greatestDifference>
							<greatestDifference id="y">E, d 'de' MMM 'de' y - E, d 'de' MMM 'de' y G</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMMMM">
							<greatestDifference id="M">MMMM - MMMM 'de' y G</greatestDifference>
							<greatestDifference id="y">MMMM 'de' y - MMMM 'de' y G</greatestDifference>
						</intervalFormatItem>
					</intervalFormats>
				</dateTimeFormats>
			</calendar>
			<calendar type="gregorian">
				<months>
					<monthContext type="format">
						<monthWidth type="abbreviated">
							<month type="1">Jan</month>
							<month type="2">Fev</month>
							<month type="3">Mar</month>
							<month type="4">Abr</month>
							<month type="5">Mai</month>
							<month type="6">Jun</month>
							<month type="7">Jul</month>
							<month type="8">Ago</month>
							<month type="9">Set</month>
							<month type="10">Out</month>
							<month type="11">Nov</month>
							<month type="12">Dez</month>
						</monthWidth>
						<monthWidth type="narrow">
							<month type="1">J</month>
							<month type="2">F</month>
							<month type="3">M</month>
							<month type="4">A</month>
							<month type="5">M</month>
							<month type="6">J</month>
							<month type="7">J</month>
							<month type="8">A</month>
							<month type="9">S</month>
							<month type="10">O</month>
							<month type="11">N</month>
							<month type="12">D</month>
						</monthWidth>
						<monthWidth type="wide">
							<month type="1">Janeiro</month>
							<month type="2">Fevereiro</month>
							<month type="3">Março</month>
							<month type="4">Abril</month>
							<month type="5">Maio</month>
							<month type="6">Junho</month>
							<month type="7">Julho</month>
							<month type="8">Agosto</month>
							<month type="9">Setembro</month>
							<month type="10">Outubro</month>
							<month type="11">Novembro</month>
							<month type="12">Dezembro</month>
						</monthWidth>
					</monthContext>
					<monthContext type="stand-alone">
						<monthWidth type="abbreviated">
							<month type="1">Jan</month>
							<month type="2">Fev</month>
							<month type="3">Mar</month>
							<month type="4">Abr</month>
							<month type="5">Mai</month>
							<month type="6">Jun</month>
							<month type="7">Jul</month>
							<month type="8">Ago</month>
							<month type="9">Set</month>
							<month type="10">Out</month>
							<month type="11">Nov</month>
							<month type="12">Dez</month>
						</monthWidth>
						<monthWidth type="narrow">
							<month type="1">J</month>
							<month type="2">F</month>
							<month type="3">M</month>
							<month type="4">A</month>
							<month type="5">M</month>
							<month type="6">J</month>
							<month type="7">J</month>
							<month type="8">A</month>
							<month type="9">S</month>
							<month type="10">O</month>
							<month type="11">N</month>
							<month type="12">D</month>
						</monthWidth>
						<monthWidth type="wide">
							<month type="1">Janeiro</month>
							<month type="2">Fevereiro</month>
							<month type="3">Março</month>
							<month type="4">Abril</month>
							<month type="5">Maio</month>
							<month type="6">Junho</month>
							<month type="7">Julho</month>
							<month type="8">Agosto</month>
							<month type="9">Setembro</month>
							<month type="10">Outubro</month>
							<month type="11">Novembro</month>
							<month type="12">Dezembro</month>
						</monthWidth>
					</monthContext>
				</months>
				<days>
					<dayContext type="format">
						<dayWidth type="abbreviated">
							<day type="sun">dom</day>
							<day type="mon">seg</day>
							<day type="tue">ter</day>
							<day type="wed">qua</day>
							<day type="thu">qui</day>
							<day type="fri">sex</day>
							<day type="sat">sáb</day>
						</dayWidth>
						<dayWidth type="narrow">
							<day type="sun">D</day>
							<day type="mon">S</day>
							<day type="tue">T</day>
							<day type="wed">Q</day>
							<day type="thu">Q</day>
							<day type="fri">S</day>
							<day type="sat">S</day>
						</dayWidth>
						<dayWidth type="short">
							<day type="sun">do</day>
							<day type="mon">sg</day>
							<day type="tue">te</day>
							<day type="wed">qu</day>
							<day type="thu">qi</day>
							<day type="fri">sx</day>
							<day type="sat">sb</day>
						</dayWidth>
						<dayWidth type="wide">
							<day type="sun">domingo</day>
							<day type="mon">segunda-feira</day>
							<day type="tue">terça-feira</day>
							<day type="wed">quarta-feira</day>
							<day type="thu">quinta-feira</day>
							<day type="fri">sexta-feira</day>
							<day type="sat">sábado</day>
						</dayWidth>
					</dayContext>
					<dayContext type="stand-alone">
						<dayWidth type="abbreviated">
							<day type="sun">dom</day>
							<day type="mon">seg</day>
							<day type="tue">ter</day>
							<day type="wed">qua</day>
							<day type="thu">qui</day>
							<day type="fri">sex</day>
							<day type="sat">sáb</day>
						</dayWidth>
						<dayWidth type="narrow">
							<day type="sun">D</day>
							<day type="mon">S</day>
							<day type="tue">T</day>
							<day type="wed">Q</day>
							<day type="thu">Q</day>
							<day type="fri">S</day>
							<day type="sat">S</day>
						</dayWidth>
						<dayWidth type="short">
							<day type="sun">do</day>
							<day type="mon">sg</day>
							<day type="tue">te</day>
							<day type="wed">qu</day>
							<day type="thu">qi</day>
							<day type="fri">sx</day>
							<day type="sat">sb</day>
						</dayWidth>
						<dayWidth type="wide">
							<day type="sun">domingo</day>
							<day type="mon">segunda-feira</day>
							<day type="tue">terça-feira</day>
							<day type="wed">quarta-feira</day>
							<day type="thu">quinta-feira</day>
							<day type="fri">sexta-feira</day>
							<day type="sat">sábado</day>
						</dayWidth>
					</dayContext>
				</days>
				<quarters>
					<quarterContext type="format">
						<quarterWidth type="abbreviated">
							<quarter type="1">T1</quarter>
							<quarter type="2">T2</quarter>
							<quarter type="3">T3</quarter>
							<quarter type="4">T4</quarter>
						</quarterWidth>
						<quarterWidth type="narrow">
							<quarter type="1">1</quarter>
							<quarter type="2">2</quarter>
							<quarter type="3">3</quarter>
							<quarter type="4">4</quarter>
						</quarterWidth>
						<quarterWidth type="wide">
							<quarter type="1">1.º trimestre</quarter>
							<quarter type="2">2.º trimestre</quarter>
							<quarter type="3">3.º trimestre</quarter>
							<quarter type="4">4.º trimestre</quarter>
						</quarterWidth>
					</quarterContext>
					<quarterContext type="stand-alone">
						<quarterWidth type="abbreviated">
							<quarter type="1">T1</quarter>
							<quarter type="2">T2</quarter>
							<quarter type="3">T3</quarter>
							<quarter type="4">T4</quarter>
						</quarterWidth>
						<quarterWidth type="narrow">
							<quarter type="1">1</quarter>
							<quarter type="2">2</quarter>
							<quarter type="3">3</quarter>
							<quarter type="4">4</quarter>
						</quarterWidth>
						<quarterWidth type="wide">
							<quarter type="1">1.º trimestre</quarter>
							<quarter type="2">2.º trimestre</quarter>
							<quarter type="3">3.º trimestre</quarter>
							<quarter type="4">4.º trimestre</quarter>
						</quarterWidth>
					</quarterContext>
				</quarters>
				<dayPeriods>
					<dayPeriodContext type="format">
						<dayPeriodWidth type="abbreviated">
							<dayPeriod type="am" draft="contributed">a.m.</dayPeriod>
							<dayPeriod type="pm" draft="contributed">p.m.</dayPeriod>
						</dayPeriodWidth>
						<dayPeriodWidth type="narrow">
							<dayPeriod type="am" draft="contributed">a.m.</dayPeriod>
							<dayPeriod type="pm" draft="contributed">p.m.</dayPeriod>
						</dayPeriodWidth>
						<dayPeriodWidth type="wide">
							<dayPeriod type="am">da manhã</dayPeriod>
							<dayPeriod type="pm">da tarde</dayPeriod>
						</dayPeriodWidth>
					</dayPeriodContext>
					<dayPeriodContext type="stand-alone">
						<dayPeriodWidth type="abbreviated">
							<dayPeriod type="am">a.m.</dayPeriod>
							<dayPeriod type="pm">p.m.</dayPeriod>
						</dayPeriodWidth>
						<dayPeriodWidth type="wide">
							<dayPeriod type="am" draft="contributed">a.m.</dayPeriod>
							<dayPeriod type="pm" draft="contributed">p.m.</dayPeriod>
						</dayPeriodWidth>
					</dayPeriodContext>
				</dayPeriods>
				<eras>
					<eraAbbr>
						<era type="0">a.C.</era>
						<era type="0" alt="variant">a.E.C.</era>
						<era type="1">d.C.</era>
						<era type="1" alt="variant">E.C.</era>
					</eraAbbr>
				</eras>
				<dateFormats>
					<dateFormatLength type="full">
						<dateFormat>
							<pattern>EEEE, d 'de' MMMM 'de' y</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="long">
						<dateFormat>
							<pattern>d 'de' MMMM 'de' y</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="medium">
						<dateFormat>
							<pattern>dd/MM/y</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="short">
						<dateFormat>
							<pattern>dd/MM/yy</pattern>
						</dateFormat>
					</dateFormatLength>
				</dateFormats>
				<timeFormats>
					<timeFormatLength type="full">
						<timeFormat>
							<pattern>HH:mm:ss zzzz</pattern>
						</timeFormat>
					</timeFormatLength>
					<timeFormatLength type="long">
						<timeFormat>
							<pattern>HH:mm:ss z</pattern>
						</timeFormat>
					</timeFormatLength>
					<timeFormatLength type="medium">
						<timeFormat>
							<pattern>HH:mm:ss</pattern>
						</timeFormat>
					</timeFormatLength>
					<timeFormatLength type="short">
						<timeFormat>
							<pattern>HH:mm</pattern>
						</timeFormat>
					</timeFormatLength>
				</timeFormats>
				<dateTimeFormats>
					<dateTimeFormatLength type="full">
						<dateTimeFormat>
							<pattern>{1} 'às' {0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<dateTimeFormatLength type="long">
						<dateTimeFormat>
							<pattern>{1} 'às' {0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<dateTimeFormatLength type="medium">
						<dateTimeFormat>
							<pattern>{1}, {0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<dateTimeFormatLength type="short">
						<dateTimeFormat>
							<pattern>{1}, {0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<availableFormats>
						<dateFormatItem id="d">d</dateFormatItem>
						<dateFormatItem id="Ed">E, d</dateFormatItem>
						<dateFormatItem id="Ehm">E, h:mm a</dateFormatItem>
						<dateFormatItem id="EHm">E, HH:mm</dateFormatItem>
						<dateFormatItem id="Ehms">E, h:mm:ss a</dateFormatItem>
						<dateFormatItem id="EHms">E, HH:mm:ss</dateFormatItem>
						<dateFormatItem id="Gy">y G</dateFormatItem>
						<dateFormatItem id="GyMMM">MMM 'de' y G</dateFormatItem>
						<dateFormatItem id="GyMMMd">d 'de' MMM 'de' y G</dateFormatItem>
						<dateFormatItem id="GyMMMEd">E, d 'de' MMM 'de' y G</dateFormatItem>
						<dateFormatItem id="h">h a</dateFormatItem>
						<dateFormatItem id="H">HH</dateFormatItem>
						<dateFormatItem id="HHmm">HH:mm</dateFormatItem>
						<dateFormatItem id="HHmmss">HH:mm:ss</dateFormatItem>
						<dateFormatItem id="hm">h:mm a</dateFormatItem>
						<dateFormatItem id="Hm">HH:mm</dateFormatItem>
						<dateFormatItem id="hms">h:mm:ss a</dateFormatItem>
						<dateFormatItem id="Hms">HH:mm:ss</dateFormatItem>
						<dateFormatItem id="M">L</dateFormatItem>
						<dateFormatItem id="Md">d/M</dateFormatItem>
						<dateFormatItem id="MEd">E, dd/MM</dateFormatItem>
						<dateFormatItem id="MMdd">dd/MM</dateFormatItem>
						<dateFormatItem id="MMM">LLL</dateFormatItem>
						<dateFormatItem id="MMMd">d/MM</dateFormatItem>
						<dateFormatItem id="MMMEd">E, d/MM</dateFormatItem>
						<dateFormatItem id="MMMMd">d 'de' MMMM</dateFormatItem>
						<dateFormatItem id="MMMMEd">E, d 'de' MMMM</dateFormatItem>
						<dateFormatItem id="ms">mm:ss</dateFormatItem>
						<dateFormatItem id="y">y</dateFormatItem>
						<dateFormatItem id="yM">MM/y</dateFormatItem>
						<dateFormatItem id="yMd">dd/MM/y</dateFormatItem>
						<dateFormatItem id="yMEd">E, dd/MM/y</dateFormatItem>
						<dateFormatItem id="yMM">MM/y</dateFormatItem>
						<dateFormatItem id="yMMM">MM/y</dateFormatItem>
						<dateFormatItem id="yMMMd">d/MM/y</dateFormatItem>
						<dateFormatItem id="yMMMEd">E, d/MM/y</dateFormatItem>
						<dateFormatItem id="yMMMEEEEd">EEEE, d/MM/y</dateFormatItem>
						<dateFormatItem id="yMMMM">MMMM 'de' y</dateFormatItem>
						<dateFormatItem id="yMMMMd">d 'de' MMMM 'de' y</dateFormatItem>
						<dateFormatItem id="yMMMMEd">E, d 'de' MMMM 'de' y</dateFormatItem>
						<dateFormatItem id="yQQQ">QQQQ 'de' y</dateFormatItem>
						<dateFormatItem id="yQQQQ">QQQQ 'de' y</dateFormatItem>
					</availableFormats>
					<appendItems>
						<appendItem request="Timezone">{0} {1}</appendItem>
					</appendItems>
					<intervalFormats>
						<intervalFormatFallback>{0} - {1}</intervalFormatFallback>
						<intervalFormatItem id="d">
							<greatestDifference id="d">d–d</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="h">
							<greatestDifference id="a">h a - h a</greatestDifference>
							<greatestDifference id="h">h-h a</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="H">
							<greatestDifference id="H">HH–HH</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="hm">
							<greatestDifference id="a">h:mm a - h:mm a</greatestDifference>
							<greatestDifference id="h">h:mm - h:mm a</greatestDifference>
							<greatestDifference id="m">h:mm - h:mm a</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="Hm">
							<greatestDifference id="H">HH:mm - HH:mm</greatestDifference>
							<greatestDifference id="m">HH:mm - HH:mm</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="hmv">
							<greatestDifference id="a">h:mm a – h:mm a v</greatestDifference>
							<greatestDifference id="h">h:mm - h:mm a v</greatestDifference>
							<greatestDifference id="m">h:mm - h:mm a v</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="Hmv">
							<greatestDifference id="H">HH:mm - HH:mm v</greatestDifference>
							<greatestDifference id="m">HH:mm - HH:mm v</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="hv">
							<greatestDifference id="a">h a - h a v</greatestDifference>
							<greatestDifference id="h">h - h a v</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="Hv">
							<greatestDifference id="H">HH - HH v</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="M">
							<greatestDifference id="M">M-M</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="Md">
							<greatestDifference id="d">dd/MM - dd/MM</greatestDifference>
							<greatestDifference id="M">dd/MM - dd/MM</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MEd">
							<greatestDifference id="d">E, dd/MM - E, dd/MM</greatestDifference>
							<greatestDifference id="M">E, dd/MM - E, dd/MM</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MMM">
							<greatestDifference id="M">MMM-MMM</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MMMd">
							<greatestDifference id="d">d-d 'de' MMM</greatestDifference>
							<greatestDifference id="M">d 'de' MMM - d 'de' MMM</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MMMEd">
							<greatestDifference id="d">E, d 'de' MMM - E, d 'de' MMM</greatestDifference>
							<greatestDifference id="M">E, d 'de' MMM - E, d 'de' MMM</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="y">
							<greatestDifference id="y">y-y</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yM">
							<greatestDifference id="M">MM/y - MM/y</greatestDifference>
							<greatestDifference id="y">MM/y - MM/y</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMd">
							<greatestDifference id="d">dd/MM/y - dd/MM/y</greatestDifference>
							<greatestDifference id="M">dd/MM/y - dd/MM/y</greatestDifference>
							<greatestDifference id="y">dd/MM/y - dd/MM/y</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMEd">
							<greatestDifference id="d">E, dd/MM/y - E, dd/MM/y</greatestDifference>
							<greatestDifference id="M">E, dd/MM/y - E, dd/MM/y</greatestDifference>
							<greatestDifference id="y">E, dd/MM/y - E, dd/MM/y</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMMM">
							<greatestDifference id="M">MMM-MMM 'de' y</greatestDifference>
							<greatestDifference id="y">MMM 'de' y - MMM 'de' y</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMMMd">
							<greatestDifference id="d">d-d 'de' MMM 'de' y</greatestDifference>
							<greatestDifference id="M">d 'de' MMM - d 'de' MMM 'de' y</greatestDifference>
							<greatestDifference id="y">d 'de' MMM 'de' y - d 'de' MMM 'de' y</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMMMEd">
							<greatestDifference id="d">E, d 'de' MMM - E, d 'de' MMM 'de' y</greatestDifference>
							<greatestDifference id="M">E, d 'de' MMM - E, d 'de' MMM 'de' y</greatestDifference>
							<greatestDifference id="y">E, d 'de' MMM 'de' y - E, d 'de' MMM 'de' y</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMMMM">
							<greatestDifference id="M">MMMM - MMMM 'de' y</greatestDifference>
							<greatestDifference id="y">MMMM 'de' y - MMMM 'de' y</greatestDifference>
						</intervalFormatItem>
					</intervalFormats>
				</dateTimeFormats>
			</calendar>
			<calendar type="hebrew">
				<dateFormats>
					<dateFormatLength type="short">
						<dateFormat>
							<pattern>d/M/y G</pattern>
						</dateFormat>
					</dateFormatLength>
				</dateFormats>
			</calendar>
			<calendar type="islamic">
				<dateFormats>
					<dateFormatLength type="short">
						<dateFormat>
							<pattern>d/M/y G</pattern>
						</dateFormat>
					</dateFormatLength>
				</dateFormats>
			</calendar>
			<calendar type="japanese">
				<dateFormats>
					<dateFormatLength type="short">
						<dateFormat>
							<pattern>d/M/y G</pattern>
						</dateFormat>
					</dateFormatLength>
				</dateFormats>
			</calendar>
			<calendar type="roc">
				<dateFormats>
					<dateFormatLength type="short">
						<dateFormat>
							<pattern>d/M/y G</pattern>
						</dateFormat>
					</dateFormatLength>
				</dateFormats>
			</calendar>
		</calendars>
		<fields>
			<field type="era">
				<displayName>Era</displayName>
			</field>
			<field type="year">
				<displayName>Ano</displayName>
				<relative type="-1">ano passado</relative>
				<relative type="0">este ano</relative>
				<relative type="1">próximo ano</relative>
				<relativeTime type="future">
					<relativeTimePattern count="one">Dentro de {0} ano</relativeTimePattern>
					<relativeTimePattern count="other">Dentro de {0} anos</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">Há {0} ano</relativeTimePattern>
					<relativeTimePattern count="other">Há {0} anos</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="month">
				<displayName>Mês</displayName>
				<relative type="-1">mês passado</relative>
				<relative type="0">este mês</relative>
				<relative type="1">próximo mês</relative>
				<relativeTime type="future">
					<relativeTimePattern count="one">Dentro de {0} mês</relativeTimePattern>
					<relativeTimePattern count="other">Dentro de {0} meses</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">Há {0} mês</relativeTimePattern>
					<relativeTimePattern count="other">Há {0} meses</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="week">
				<displayName>Semana</displayName>
				<relative type="-1">semana passada</relative>
				<relative type="0">esta semana</relative>
				<relative type="1">próxima semana</relative>
				<relativeTime type="future">
					<relativeTimePattern count="one">Dentro de {0} semana</relativeTimePattern>
					<relativeTimePattern count="other">Dentro de {0} semanas</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">Há {0} semana</relativeTimePattern>
					<relativeTimePattern count="other">Há {0} semanas</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="day">
				<displayName>Dia</displayName>
				<relative type="-2">anteontem</relative>
				<relative type="-1">ontem</relative>
				<relative type="0">hoje</relative>
				<relative type="1">amanhã</relative>
				<relative type="2">depois de amanhã</relative>
				<relativeTime type="future">
					<relativeTimePattern count="one">Dentro de {0} dia</relativeTimePattern>
					<relativeTimePattern count="other">Dentro de {0} dias</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">Há {0} dia</relativeTimePattern>
					<relativeTimePattern count="other">Há {0} dias</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="weekday">
				<displayName>Dia da semana</displayName>
			</field>
			<field type="sun">
				<relative type="-1">domingo passado</relative>
				<relative type="0">este domingo</relative>
				<relative type="1">próximo domingo</relative>
			</field>
			<field type="mon">
				<relative type="-1">segunda-feira passada</relative>
				<relative type="0">esta segunda-feira</relative>
				<relative type="1">próxima segunda-feira</relative>
			</field>
			<field type="tue">
				<relative type="-1">terça-feira passada</relative>
				<relative type="0">esta terça-feira</relative>
				<relative type="1">próxima terça-feira</relative>
			</field>
			<field type="wed">
				<relative type="-1">quarta-feira passada</relative>
				<relative type="0">esta quarta-feira</relative>
				<relative type="1">próxima quarta-feira</relative>
			</field>
			<field type="thu">
				<relative type="-1">quinta-feira passada</relative>
				<relative type="0">esta quinta-feira</relative>
				<relative type="1">próxima quinta-feira</relative>
			</field>
			<field type="fri">
				<relative type="-1">sexta-feira passada</relative>
				<relative type="0">esta sexta-feira</relative>
				<relative type="1">próxima sexta-feira</relative>
			</field>
			<field type="sat">
				<relative type="-1">sábado passado</relative>
				<relative type="0">este sábado</relative>
				<relative type="1">próximo sábado</relative>
			</field>
			<field type="dayperiod">
				<displayName>Da manhã/da tarde</displayName>
			</field>
			<field type="hour">
				<displayName>Hora</displayName>
				<relativeTime type="future">
					<relativeTimePattern count="one">Dentro de {0} hora</relativeTimePattern>
					<relativeTimePattern count="other">Dentro de {0} horas</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">Há {0} hora</relativeTimePattern>
					<relativeTimePattern count="other">Há {0} horas</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="minute">
				<displayName>Minuto</displayName>
				<relativeTime type="future">
					<relativeTimePattern count="one">Dentro de {0} minuto</relativeTimePattern>
					<relativeTimePattern count="other">Dentro de {0} minutos</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">Há {0} minuto</relativeTimePattern>
					<relativeTimePattern count="other">Há {0} minutos</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="second">
				<displayName>Segundo</displayName>
				<relativeTime type="future">
					<relativeTimePattern count="one">Dentro de {0} segundo</relativeTimePattern>
					<relativeTimePattern count="other">Dentro de {0} segundos</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">Há {0} segundo</relativeTimePattern>
					<relativeTimePattern count="other">Há {0} segundos</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="zone">
				<displayName>Fuso horário</displayName>
			</field>
		</fields>
		<timeZoneNames>
			<hourFormat>+HH:mm;-HH:mm</hourFormat>
			<gmtFormat>GMT{0}</gmtFormat>
			<gmtZeroFormat>GMT</gmtZeroFormat>
			<regionFormat>Hora de {0}</regionFormat>
			<regionFormat type="daylight">Hora de Verão de {0}</regionFormat>
			<regionFormat type="standard">Hora Padrão de {0}</regionFormat>
			<fallbackFormat>{1} ({0})</fallbackFormat>
			<zone type="Etc/Unknown">
				<exemplarCity>Cidade desconhecida</exemplarCity>
			</zone>
			<zone type="Europe/Andorra">
				<exemplarCity>Andorra</exemplarCity>
			</zone>
			<zone type="Asia/Dubai">
				<exemplarCity>Dubai</exemplarCity>
			</zone>
			<zone type="Asia/Kabul">
				<exemplarCity>Cabul</exemplarCity>
			</zone>
			<zone type="America/Antigua">
				<exemplarCity>Antígua</exemplarCity>
			</zone>
			<zone type="America/Anguilla">
				<exemplarCity draft="contributed">Anguilla</exemplarCity>
			</zone>
			<zone type="Europe/Tirane">
				<exemplarCity>Tirana</exemplarCity>
			</zone>
			<zone type="Asia/Yerevan">
				<exemplarCity>Erevan</exemplarCity>
			</zone>
			<zone type="Africa/Luanda">
				<exemplarCity>Luanda</exemplarCity>
			</zone>
			<zone type="Antarctica/Rothera">
				<exemplarCity>Rothera</exemplarCity>
			</zone>
			<zone type="Antarctica/Palmer">
				<exemplarCity>Palmer</exemplarCity>
			</zone>
			<zone type="Antarctica/Syowa">
				<exemplarCity draft="provisional">Showa</exemplarCity>
			</zone>
			<zone type="Antarctica/Mawson">
				<exemplarCity>Mawson</exemplarCity>
			</zone>
			<zone type="Antarctica/Davis">
				<exemplarCity>Davis</exemplarCity>
			</zone>
			<zone type="Antarctica/Vostok">
				<exemplarCity>Vostok</exemplarCity>
			</zone>
			<zone type="Antarctica/Casey">
				<exemplarCity>Casey</exemplarCity>
			</zone>
			<zone type="Antarctica/DumontDUrville">
				<exemplarCity>Dumont d’Urville</exemplarCity>
			</zone>
			<zone type="Antarctica/McMurdo">
				<exemplarCity>McMurdo</exemplarCity>
			</zone>
			<zone type="America/Argentina/Rio_Gallegos">
				<exemplarCity>Rio Gallegos</exemplarCity>
			</zone>
			<zone type="America/Mendoza">
				<exemplarCity>Mendoza</exemplarCity>
			</zone>
			<zone type="America/Argentina/San_Juan">
				<exemplarCity>San Juan</exemplarCity>
			</zone>
			<zone type="America/Argentina/Ushuaia">
				<exemplarCity>Ushuaia</exemplarCity>
			</zone>
			<zone type="America/Argentina/La_Rioja">
				<exemplarCity>La Rioja</exemplarCity>
			</zone>
			<zone type="America/Argentina/San_Luis">
				<exemplarCity>San Luis</exemplarCity>
			</zone>
			<zone type="America/Catamarca">
				<exemplarCity>Catamarca</exemplarCity>
			</zone>
			<zone type="America/Argentina/Salta">
				<exemplarCity>Salta</exemplarCity>
			</zone>
			<zone type="America/Jujuy">
				<exemplarCity>Jujuy</exemplarCity>
			</zone>
			<zone type="America/Argentina/Tucuman">
				<exemplarCity>Tucumán</exemplarCity>
			</zone>
			<zone type="America/Cordoba">
				<exemplarCity>Córdoba</exemplarCity>
			</zone>
			<zone type="America/Buenos_Aires">
				<exemplarCity>Buenos Aires</exemplarCity>
			</zone>
			<zone type="Pacific/Pago_Pago">
				<exemplarCity>Pago Pago</exemplarCity>
			</zone>
			<zone type="Europe/Vienna">
				<exemplarCity>Viena</exemplarCity>
			</zone>
			<zone type="Australia/Perth">
				<exemplarCity>Perth</exemplarCity>
			</zone>
			<zone type="Australia/Eucla">
				<exemplarCity>Eucla</exemplarCity>
			</zone>
			<zone type="Australia/Darwin">
				<exemplarCity>Darwin</exemplarCity>
			</zone>
			<zone type="Australia/Adelaide">
				<exemplarCity>Adelaide</exemplarCity>
			</zone>
			<zone type="Australia/Broken_Hill">
				<exemplarCity>Broken Hill</exemplarCity>
			</zone>
			<zone type="Australia/Currie">
				<exemplarCity>Currie</exemplarCity>
			</zone>
			<zone type="Australia/Melbourne">
				<exemplarCity>Melbourne</exemplarCity>
			</zone>
			<zone type="Australia/Hobart">
				<exemplarCity>Hobart</exemplarCity>
			</zone>
			<zone type="Australia/Lindeman">
				<exemplarCity>Lindeman</exemplarCity>
			</zone>
			<zone type="Australia/Sydney">
				<exemplarCity>Sydney</exemplarCity>
			</zone>
			<zone type="Australia/Brisbane">
				<exemplarCity>Brisbane</exemplarCity>
			</zone>
			<zone type="Antarctica/Macquarie">
				<exemplarCity>Macquarie</exemplarCity>
			</zone>
			<zone type="Australia/Lord_Howe">
				<exemplarCity>Ilha de Lord Howe</exemplarCity>
			</zone>
			<zone type="America/Aruba">
				<exemplarCity>Aruba</exemplarCity>
			</zone>
			<zone type="Europe/Mariehamn">
				<exemplarCity>Mariehamn</exemplarCity>
			</zone>
			<zone type="Asia/Baku">
				<exemplarCity>Baku</exemplarCity>
			</zone>
			<zone type="Europe/Sarajevo">
				<exemplarCity>Sarajevo</exemplarCity>
			</zone>
			<zone type="America/Barbados">
				<exemplarCity>Barbados</exemplarCity>
			</zone>
			<zone type="Asia/Dhaka">
				<exemplarCity>Daca</exemplarCity>
			</zone>
			<zone type="Europe/Brussels">
				<exemplarCity>Bruxelas</exemplarCity>
			</zone>
			<zone type="Africa/Ouagadougou">
				<exemplarCity>Ouagadougou</exemplarCity>
			</zone>
			<zone type="Europe/Sofia">
				<exemplarCity>Sófia</exemplarCity>
			</zone>
			<zone type="Asia/Bahrain">
				<exemplarCity>Barém</exemplarCity>
			</zone>
			<zone type="Africa/Bujumbura">
				<exemplarCity>Bujumbura</exemplarCity>
			</zone>
			<zone type="Africa/Porto-Novo">
				<exemplarCity>Porto-Novo</exemplarCity>
			</zone>
			<zone type="America/St_Barthelemy">
				<exemplarCity>São Bartolomeu</exemplarCity>
			</zone>
			<zone type="Atlantic/Bermuda">
				<exemplarCity>Bermudas</exemplarCity>
			</zone>
			<zone type="Asia/Brunei">
				<exemplarCity>Brunei</exemplarCity>
			</zone>
			<zone type="America/La_Paz">
				<exemplarCity>La Paz</exemplarCity>
			</zone>
			<zone type="America/Kralendijk">
				<exemplarCity>Kralendijk</exemplarCity>
			</zone>
			<zone type="America/Eirunepe">
				<exemplarCity>Eirunepé</exemplarCity>
			</zone>
			<zone type="America/Rio_Branco">
				<exemplarCity>Rio Branco</exemplarCity>
			</zone>
			<zone type="America/Porto_Velho">
				<exemplarCity>Porto Velho</exemplarCity>
			</zone>
			<zone type="America/Boa_Vista">
				<exemplarCity>Boa Vista</exemplarCity>
			</zone>
			<zone type="America/Manaus">
				<exemplarCity>Manaus</exemplarCity>
			</zone>
			<zone type="America/Cuiaba">
				<exemplarCity>Cuiabá</exemplarCity>
			</zone>
			<zone type="America/Santarem">
				<exemplarCity>Santarém</exemplarCity>
			</zone>
			<zone type="America/Campo_Grande">
				<exemplarCity>Campo Grande</exemplarCity>
			</zone>
			<zone type="America/Belem">
				<exemplarCity>Belém</exemplarCity>
			</zone>
			<zone type="America/Araguaina">
				<exemplarCity>Araguaina</exemplarCity>
			</zone>
			<zone type="America/Sao_Paulo">
				<exemplarCity>São Paulo</exemplarCity>
			</zone>
			<zone type="America/Bahia">
				<exemplarCity>Baía</exemplarCity>
			</zone>
			<zone type="America/Fortaleza">
				<exemplarCity>Fortaleza</exemplarCity>
			</zone>
			<zone type="America/Maceio">
				<exemplarCity>Maceió</exemplarCity>
			</zone>
			<zone type="America/Recife">
				<exemplarCity>Recife</exemplarCity>
			</zone>
			<zone type="America/Noronha">
				<exemplarCity>Fernando de Noronha</exemplarCity>
			</zone>
			<zone type="America/Nassau">
				<exemplarCity>Nassau</exemplarCity>
			</zone>
			<zone type="Asia/Thimphu">
				<exemplarCity>Timphu</exemplarCity>
			</zone>
			<zone type="Africa/Gaborone">
				<exemplarCity>Gaborone</exemplarCity>
			</zone>
			<zone type="Europe/Minsk">
				<exemplarCity>Minsk</exemplarCity>
			</zone>
			<zone type="America/Belize">
				<exemplarCity>Belize</exemplarCity>
			</zone>
			<zone type="America/Dawson">
				<exemplarCity>Dawson</exemplarCity>
			</zone>
			<zone type="America/Whitehorse">
				<exemplarCity>Whitehorse</exemplarCity>
			</zone>
			<zone type="America/Inuvik">
				<exemplarCity>Inuvik</exemplarCity>
			</zone>
			<zone type="America/Vancouver">
				<exemplarCity>Vancouver</exemplarCity>
			</zone>
			<zone type="America/Dawson_Creek">
				<exemplarCity>Dawson Creek</exemplarCity>
			</zone>
			<zone type="America/Creston">
				<exemplarCity>Creston</exemplarCity>
			</zone>
			<zone type="America/Yellowknife">
				<exemplarCity>Yellowknife</exemplarCity>
			</zone>
			<zone type="America/Edmonton">
				<exemplarCity>Edmonton</exemplarCity>
			</zone>
			<zone type="America/Swift_Current">
				<exemplarCity>Swift Current</exemplarCity>
			</zone>
			<zone type="America/Cambridge_Bay">
				<exemplarCity>Cambridge Bay</exemplarCity>
			</zone>
			<zone type="America/Regina">
				<exemplarCity>Regina</exemplarCity>
			</zone>
			<zone type="America/Winnipeg">
				<exemplarCity>Winnipeg</exemplarCity>
			</zone>
			<zone type="America/Resolute">
				<exemplarCity>Resolute</exemplarCity>
			</zone>
			<zone type="America/Rainy_River">
				<exemplarCity>Rainy River</exemplarCity>
			</zone>
			<zone type="America/Rankin_Inlet">
				<exemplarCity>Rankin Inlet</exemplarCity>
			</zone>
			<zone type="America/Coral_Harbour">
				<exemplarCity>Atikokan</exemplarCity>
			</zone>
			<zone type="America/Thunder_Bay">
				<exemplarCity>Thunder Bay</exemplarCity>
			</zone>
			<zone type="America/Nipigon">
				<exemplarCity>Nipigon</exemplarCity>
			</zone>
			<zone type="America/Toronto">
				<exemplarCity>Toronto</exemplarCity>
			</zone>
			<zone type="America/Iqaluit">
				<exemplarCity>Iqaluit</exemplarCity>
			</zone>
			<zone type="America/Pangnirtung">
				<exemplarCity>Pangnirtung</exemplarCity>
			</zone>
			<zone type="America/Moncton">
				<exemplarCity>Moncton</exemplarCity>
			</zone>
			<zone type="America/Halifax">
				<exemplarCity>Halifax</exemplarCity>
			</zone>
			<zone type="America/Goose_Bay">
				<exemplarCity>Goose Bay</exemplarCity>
			</zone>
			<zone type="America/Glace_Bay">
				<exemplarCity>Glace Bay</exemplarCity>
			</zone>
			<zone type="America/Blanc-Sablon">
				<exemplarCity>Blanc-Sablon</exemplarCity>
			</zone>
			<zone type="America/St_Johns">
				<exemplarCity>St. John’s</exemplarCity>
			</zone>
			<zone type="Indian/Cocos">
				<exemplarCity>Ilhas Coco</exemplarCity>
			</zone>
			<zone type="Africa/Kinshasa">
				<exemplarCity>Kinshasa</exemplarCity>
			</zone>
			<zone type="Africa/Lubumbashi">
				<exemplarCity>Lubumbashi</exemplarCity>
			</zone>
			<zone type="Africa/Bangui">
				<exemplarCity>Bangui</exemplarCity>
			</zone>
			<zone type="Africa/Brazzaville">
				<exemplarCity>Brazzaville</exemplarCity>
			</zone>
			<zone type="Europe/Zurich">
				<exemplarCity>Zurique</exemplarCity>
			</zone>
			<zone type="Africa/Abidjan">
				<exemplarCity>Abidjan</exemplarCity>
			</zone>
			<zone type="Pacific/Rarotonga">
				<exemplarCity>Rarotonga</exemplarCity>
			</zone>
			<zone type="Pacific/Easter">
				<exemplarCity>Ilha de Páscoa</exemplarCity>
			</zone>
			<zone type="America/Santiago">
				<exemplarCity>Santiago</exemplarCity>
			</zone>
			<zone type="Africa/Douala">
				<exemplarCity>Douala</exemplarCity>
			</zone>
			<zone type="Asia/Kashgar">
				<exemplarCity>Kashgar</exemplarCity>
			</zone>
			<zone type="Asia/Urumqi">
				<exemplarCity>Urumqi</exemplarCity>
			</zone>
			<zone type="Asia/Chongqing">
				<exemplarCity>Chongqing</exemplarCity>
			</zone>
			<zone type="Asia/Shanghai">
				<exemplarCity>Xangai</exemplarCity>
			</zone>
			<zone type="Asia/Harbin">
				<exemplarCity>Harbin</exemplarCity>
			</zone>
			<zone type="America/Bogota">
				<exemplarCity>Bogotá</exemplarCity>
			</zone>
			<zone type="America/Costa_Rica">
				<exemplarCity>Costa Rica</exemplarCity>
			</zone>
			<zone type="America/Havana">
				<exemplarCity>Havana</exemplarCity>
			</zone>
			<zone type="Atlantic/Cape_Verde">
				<exemplarCity>Cabo Verde</exemplarCity>
			</zone>
			<zone type="America/Curacao">
				<exemplarCity>Curaçao</exemplarCity>
			</zone>
			<zone type="Indian/Christmas">
				<exemplarCity>Ilha do Natal</exemplarCity>
			</zone>
			<zone type="Asia/Nicosia">
				<exemplarCity>Nicósia</exemplarCity>
			</zone>
			<zone type="Europe/Prague">
				<exemplarCity>Praga</exemplarCity>
			</zone>
			<zone type="Europe/Busingen">
				<exemplarCity>Busingen</exemplarCity>
			</zone>
			<zone type="Europe/Berlin">
				<exemplarCity>Berlim</exemplarCity>
			</zone>
			<zone type="Africa/Djibouti">
				<exemplarCity draft="contributed">Jibuti</exemplarCity>
			</zone>
			<zone type="Europe/Copenhagen">
				<exemplarCity>Copenhaga</exemplarCity>
			</zone>
			<zone type="America/Dominica">
				<exemplarCity>Domínica</exemplarCity>
			</zone>
			<zone type="America/Santo_Domingo">
				<exemplarCity>Santo Domingo</exemplarCity>
			</zone>
			<zone type="Africa/Algiers">
				<exemplarCity>Argel</exemplarCity>
			</zone>
			<zone type="Pacific/Galapagos">
				<exemplarCity>Galápagos</exemplarCity>
			</zone>
			<zone type="America/Guayaquil">
				<exemplarCity>Guaiaquil</exemplarCity>
			</zone>
			<zone type="Europe/Tallinn">
				<exemplarCity>Tallinn</exemplarCity>
			</zone>
			<zone type="Africa/Cairo">
				<exemplarCity>Cairo</exemplarCity>
			</zone>
			<zone type="Africa/El_Aaiun">
				<exemplarCity>El Aaiún</exemplarCity>
			</zone>
			<zone type="Africa/Asmera">
				<exemplarCity>Asmara</exemplarCity>
			</zone>
			<zone type="Atlantic/Canary">
				<exemplarCity>Canárias</exemplarCity>
			</zone>
			<zone type="Africa/Ceuta">
				<exemplarCity>Ceuta</exemplarCity>
			</zone>
			<zone type="Europe/Madrid">
				<exemplarCity>Madrid</exemplarCity>
			</zone>
			<zone type="Africa/Addis_Ababa">
				<exemplarCity>Adis-Abeba</exemplarCity>
			</zone>
			<zone type="Europe/Helsinki">
				<exemplarCity>Helsínquia</exemplarCity>
			</zone>
			<zone type="Pacific/Fiji">
				<exemplarCity>Fiji</exemplarCity>
			</zone>
			<zone type="Atlantic/Stanley">
				<exemplarCity>Stanley</exemplarCity>
			</zone>
			<zone type="Pacific/Truk">
				<exemplarCity>Chuuk</exemplarCity>
			</zone>
			<zone type="Pacific/Ponape">
				<exemplarCity>Pohnpei</exemplarCity>
			</zone>
			<zone type="Pacific/Kosrae">
				<exemplarCity>Kosrae</exemplarCity>
			</zone>
			<zone type="Atlantic/Faeroe">
				<exemplarCity>Faroé</exemplarCity>
			</zone>
			<zone type="Europe/Paris">
				<exemplarCity>Paris</exemplarCity>
			</zone>
			<zone type="Africa/Libreville">
				<exemplarCity>Libreville</exemplarCity>
			</zone>
			<zone type="Europe/London">
				<long>
					<daylight>Hora de Verão Britânico</daylight>
				</long>
				<exemplarCity>Londres</exemplarCity>
			</zone>
			<zone type="America/Grenada">
				<exemplarCity>Granada</exemplarCity>
			</zone>
			<zone type="Asia/Tbilisi">
				<exemplarCity>Tbilisi</exemplarCity>
			</zone>
			<zone type="America/Cayenne">
				<exemplarCity>Caiena</exemplarCity>
			</zone>
			<zone type="Europe/Guernsey">
				<exemplarCity>Guernsey</exemplarCity>
			</zone>
			<zone type="Africa/Accra">
				<exemplarCity>Acra</exemplarCity>
			</zone>
			<zone type="Europe/Gibraltar">
				<exemplarCity>Gibraltar</exemplarCity>
			</zone>
			<zone type="America/Thule">
				<exemplarCity>Thule</exemplarCity>
			</zone>
			<zone type="America/Godthab">
				<exemplarCity>Nuuk</exemplarCity>
			</zone>
			<zone type="America/Scoresbysund">
				<exemplarCity>Ittoqqortoormiit</exemplarCity>
			</zone>
			<zone type="America/Danmarkshavn">
				<exemplarCity>Danmarkshavn</exemplarCity>
			</zone>
			<zone type="Africa/Banjul">
				<exemplarCity>Banjul</exemplarCity>
			</zone>
			<zone type="Africa/Conakry">
				<exemplarCity>Conacri</exemplarCity>
			</zone>
			<zone type="America/Guadeloupe">
				<exemplarCity>Guadalupe</exemplarCity>
			</zone>
			<zone type="Africa/Malabo">
				<exemplarCity>Malabo</exemplarCity>
			</zone>
			<zone type="Europe/Athens">
				<exemplarCity>Atenas</exemplarCity>
			</zone>
			<zone type="Atlantic/South_Georgia">
				<exemplarCity>Geórgia do Sul</exemplarCity>
			</zone>
			<zone type="America/Guatemala">
				<exemplarCity>Guatemala</exemplarCity>
			</zone>
			<zone type="Pacific/Guam">
				<exemplarCity>Guam</exemplarCity>
			</zone>
			<zone type="Africa/Bissau">
				<exemplarCity>Bissau</exemplarCity>
			</zone>
			<zone type="America/Guyana">
				<exemplarCity>Guiana</exemplarCity>
			</zone>
			<zone type="Asia/Hong_Kong">
				<exemplarCity>Hong Kong</exemplarCity>
			</zone>
			<zone type="America/Tegucigalpa">
				<exemplarCity>Tegucigalpa</exemplarCity>
			</zone>
			<zone type="Europe/Zagreb">
				<exemplarCity>Zagreb</exemplarCity>
			</zone>
			<zone type="America/Port-au-Prince">
				<exemplarCity>Port-au-Prince</exemplarCity>
			</zone>
			<zone type="Europe/Budapest">
				<exemplarCity>Budapeste</exemplarCity>
			</zone>
			<zone type="Asia/Jakarta">
				<exemplarCity>Jacarta</exemplarCity>
			</zone>
			<zone type="Asia/Pontianak">
				<exemplarCity>Pontianak</exemplarCity>
			</zone>
			<zone type="Asia/Makassar">
				<exemplarCity>Macassar</exemplarCity>
			</zone>
			<zone type="Asia/Jayapura">
				<exemplarCity>Jayapura</exemplarCity>
			</zone>
			<zone type="Europe/Dublin">
				<long>
					<daylight>Hora de Verão da Irlanda</daylight>
				</long>
				<exemplarCity>Dublin</exemplarCity>
			</zone>
			<zone type="Asia/Jerusalem">
				<exemplarCity>Jerusalém</exemplarCity>
			</zone>
			<zone type="Europe/Isle_of_Man">
				<exemplarCity>Ilha de Man</exemplarCity>
			</zone>
			<zone type="Asia/Calcutta">
				<exemplarCity>Calcutá</exemplarCity>
			</zone>
			<zone type="Indian/Chagos">
				<exemplarCity>Chagos</exemplarCity>
			</zone>
			<zone type="Asia/Baghdad">
				<exemplarCity>Bagdade</exemplarCity>
			</zone>
			<zone type="Asia/Tehran">
				<exemplarCity>Teerão</exemplarCity>
			</zone>
			<zone type="Atlantic/Reykjavik">
				<exemplarCity>Reiquiavique</exemplarCity>
			</zone>
			<zone type="Europe/Rome">
				<exemplarCity>Roma</exemplarCity>
			</zone>
			<zone type="Europe/Jersey">
				<exemplarCity>Jersey</exemplarCity>
			</zone>
			<zone type="America/Jamaica">
				<exemplarCity>Jamaica</exemplarCity>
			</zone>
			<zone type="Asia/Amman">
				<exemplarCity>Amã</exemplarCity>
			</zone>
			<zone type="Asia/Tokyo">
				<exemplarCity>Tóquio</exemplarCity>
			</zone>
			<zone type="Africa/Nairobi">
				<exemplarCity>Nairobi</exemplarCity>
			</zone>
			<zone type="Asia/Bishkek">
				<exemplarCity>Bishkek</exemplarCity>
			</zone>
			<zone type="Asia/Phnom_Penh">
				<exemplarCity>Phnom Penh</exemplarCity>
			</zone>
			<zone type="Pacific/Enderbury">
				<exemplarCity>Enderbury</exemplarCity>
			</zone>
			<zone type="Pacific/Kiritimati">
				<exemplarCity>Kiritimati</exemplarCity>
			</zone>
			<zone type="Pacific/Tarawa">
				<exemplarCity>Tarawa</exemplarCity>
			</zone>
			<zone type="Indian/Comoro">
				<exemplarCity>Comores</exemplarCity>
			</zone>
			<zone type="America/St_Kitts">
				<exemplarCity>São Cristóvão</exemplarCity>
			</zone>
			<zone type="Asia/Pyongyang">
				<exemplarCity>Pyongyang</exemplarCity>
			</zone>
			<zone type="Asia/Seoul">
				<exemplarCity>Seul</exemplarCity>
			</zone>
			<zone type="Asia/Kuwait">
				<exemplarCity>Kuwait</exemplarCity>
			</zone>
			<zone type="America/Cayman">
				<exemplarCity>Caimão</exemplarCity>
			</zone>
			<zone type="Asia/Aqtau">
				<exemplarCity>Aqtau</exemplarCity>
			</zone>
			<zone type="Asia/Oral">
				<exemplarCity>Oral</exemplarCity>
			</zone>
			<zone type="Asia/Aqtobe">
				<exemplarCity>Aqtobe</exemplarCity>
			</zone>
			<zone type="Asia/Qyzylorda">
				<exemplarCity>Qyzylorda</exemplarCity>
			</zone>
			<zone type="Asia/Almaty">
				<exemplarCity>Almaty</exemplarCity>
			</zone>
			<zone type="Asia/Vientiane">
				<exemplarCity>Vientiane</exemplarCity>
			</zone>
			<zone type="Asia/Beirut">
				<exemplarCity>Beirute</exemplarCity>
			</zone>
			<zone type="America/St_Lucia">
				<exemplarCity>Santa Lúcia</exemplarCity>
			</zone>
			<zone type="Europe/Vaduz">
				<exemplarCity>Vaduz</exemplarCity>
			</zone>
			<zone type="Asia/Colombo">
				<exemplarCity>Colombo</exemplarCity>
			</zone>
			<zone type="Africa/Monrovia">
				<exemplarCity>Monróvia</exemplarCity>
			</zone>
			<zone type="Africa/Maseru">
				<exemplarCity>Maseru</exemplarCity>
			</zone>
			<zone type="Europe/Vilnius">
				<exemplarCity>Vilnius</exemplarCity>
			</zone>
			<zone type="Europe/Luxembourg">
				<exemplarCity>Luxemburgo</exemplarCity>
			</zone>
			<zone type="Europe/Riga">
				<exemplarCity>Riga</exemplarCity>
			</zone>
			<zone type="Africa/Tripoli">
				<exemplarCity>Tripoli</exemplarCity>
			</zone>
			<zone type="Africa/Casablanca">
				<exemplarCity>Casablanca</exemplarCity>
			</zone>
			<zone type="Europe/Monaco">
				<exemplarCity>Mónaco</exemplarCity>
			</zone>
			<zone type="Europe/Chisinau">
				<exemplarCity>Chisinau</exemplarCity>
			</zone>
			<zone type="Europe/Podgorica">
				<exemplarCity>Podgóritza</exemplarCity>
			</zone>
			<zone type="America/Marigot">
				<exemplarCity>Marigot</exemplarCity>
			</zone>
			<zone type="Indian/Antananarivo">
				<exemplarCity>Antananarivo</exemplarCity>
			</zone>
			<zone type="Pacific/Kwajalein">
				<exemplarCity>Kwajalein</exemplarCity>
			</zone>
			<zone type="Pacific/Majuro">
				<exemplarCity>Majuro</exemplarCity>
			</zone>
			<zone type="Europe/Skopje">
				<exemplarCity>Skopje</exemplarCity>
			</zone>
			<zone type="Africa/Bamako">
				<exemplarCity>Bamaco</exemplarCity>
			</zone>
			<zone type="Asia/Rangoon">
				<exemplarCity>Yangon</exemplarCity>
			</zone>
			<zone type="Asia/Hovd">
				<exemplarCity>Hovd</exemplarCity>
			</zone>
			<zone type="Asia/Ulaanbaatar">
				<exemplarCity>Ulan Bator</exemplarCity>
			</zone>
			<zone type="Asia/Choibalsan">
				<exemplarCity>Choibalsan</exemplarCity>
			</zone>
			<zone type="Asia/Macau">
				<exemplarCity>Macau</exemplarCity>
			</zone>
			<zone type="Pacific/Saipan">
				<exemplarCity>Saipan</exemplarCity>
			</zone>
			<zone type="America/Martinique">
				<exemplarCity>Martinica</exemplarCity>
			</zone>
			<zone type="Africa/Nouakchott">
				<exemplarCity>Nouakchott</exemplarCity>
			</zone>
			<zone type="America/Montserrat">
				<exemplarCity>Montserrat</exemplarCity>
			</zone>
			<zone type="Europe/Malta">
				<exemplarCity>Malta</exemplarCity>
			</zone>
			<zone type="Indian/Mauritius">
				<exemplarCity>Maurícia</exemplarCity>
			</zone>
			<zone type="Indian/Maldives">
				<exemplarCity>Maldivas</exemplarCity>
			</zone>
			<zone type="Africa/Blantyre">
				<exemplarCity>Blantyre</exemplarCity>
			</zone>
			<zone type="America/Tijuana">
				<exemplarCity>Tijuana</exemplarCity>
			</zone>
			<zone type="America/Santa_Isabel">
				<exemplarCity>Santa Isabel</exemplarCity>
			</zone>
			<zone type="America/Hermosillo">
				<exemplarCity>Hermosillo</exemplarCity>
			</zone>
			<zone type="America/Mazatlan">
				<exemplarCity>Mazatlan</exemplarCity>
			</zone>
			<zone type="America/Chihuahua">
				<exemplarCity>Chihuahua</exemplarCity>
			</zone>
			<zone type="America/Bahia_Banderas">
				<exemplarCity>Bahia Banderas</exemplarCity>
			</zone>
			<zone type="America/Ojinaga">
				<exemplarCity>Ojinaga</exemplarCity>
			</zone>
			<zone type="America/Monterrey">
				<exemplarCity>Monterrey</exemplarCity>
			</zone>
			<zone type="America/Mexico_City">
				<exemplarCity>Cidade do México</exemplarCity>
			</zone>
			<zone type="America/Matamoros">
				<exemplarCity>Matamoros</exemplarCity>
			</zone>
			<zone type="America/Merida">
				<exemplarCity>Mérida</exemplarCity>
			</zone>
			<zone type="America/Cancun">
				<exemplarCity>Cancun</exemplarCity>
			</zone>
			<zone type="Asia/Kuala_Lumpur">
				<exemplarCity draft="provisional">Kuala Lampur</exemplarCity>
			</zone>
			<zone type="Asia/Kuching">
				<exemplarCity>Kuching</exemplarCity>
			</zone>
			<zone type="Africa/Maputo">
				<exemplarCity>Maputo</exemplarCity>
			</zone>
			<zone type="Africa/Windhoek">
				<exemplarCity>Windhoek</exemplarCity>
			</zone>
			<zone type="Pacific/Noumea">
				<exemplarCity>Nouméa</exemplarCity>
			</zone>
			<zone type="Africa/Niamey">
				<exemplarCity>Niamei</exemplarCity>
			</zone>
			<zone type="Pacific/Norfolk">
				<exemplarCity>Norfolk</exemplarCity>
			</zone>
			<zone type="Africa/Lagos">
				<exemplarCity>Lagos</exemplarCity>
			</zone>
			<zone type="America/Managua">
				<exemplarCity>Manágua</exemplarCity>
			</zone>
			<zone type="Europe/Amsterdam">
				<exemplarCity>Amesterdão</exemplarCity>
			</zone>
			<zone type="Europe/Oslo">
				<exemplarCity>Oslo</exemplarCity>
			</zone>
			<zone type="Asia/Katmandu">
				<exemplarCity>Catmandu</exemplarCity>
			</zone>
			<zone type="Pacific/Nauru">
				<exemplarCity>Nauru</exemplarCity>
			</zone>
			<zone type="Pacific/Niue">
				<exemplarCity>Niue</exemplarCity>
			</zone>
			<zone type="Pacific/Chatham">
				<exemplarCity>Chatham</exemplarCity>
			</zone>
			<zone type="Pacific/Auckland">
				<exemplarCity>Auckland</exemplarCity>
			</zone>
			<zone type="Asia/Muscat">
				<exemplarCity>Mascate</exemplarCity>
			</zone>
			<zone type="America/Panama">
				<exemplarCity>Panamá</exemplarCity>
			</zone>
			<zone type="America/Lima">
				<exemplarCity>Lima</exemplarCity>
			</zone>
			<zone type="Pacific/Tahiti">
				<exemplarCity>Taiti</exemplarCity>
			</zone>
			<zone type="Pacific/Marquesas">
				<exemplarCity>Marquesas</exemplarCity>
			</zone>
			<zone type="Pacific/Gambier">
				<exemplarCity>Gambier</exemplarCity>
			</zone>
			<zone type="Pacific/Port_Moresby">
				<exemplarCity>Port Moresby</exemplarCity>
			</zone>
			<zone type="Asia/Manila">
				<exemplarCity>Manila</exemplarCity>
			</zone>
			<zone type="Asia/Karachi">
				<exemplarCity>Carachi</exemplarCity>
			</zone>
			<zone type="Europe/Warsaw">
				<exemplarCity>Varsóvia</exemplarCity>
			</zone>
			<zone type="America/Miquelon">
				<exemplarCity>Miquelon</exemplarCity>
			</zone>
			<zone type="Pacific/Pitcairn">
				<exemplarCity>Ilhas Pitcairn</exemplarCity>
			</zone>
			<zone type="America/Puerto_Rico">
				<exemplarCity>Porto Rico</exemplarCity>
			</zone>
			<zone type="Asia/Gaza">
				<exemplarCity>Gaza</exemplarCity>
			</zone>
			<zone type="Asia/Hebron">
				<exemplarCity>Hebron</exemplarCity>
			</zone>
			<zone type="Atlantic/Azores">
				<exemplarCity>Açores</exemplarCity>
			</zone>
			<zone type="Atlantic/Madeira">
				<exemplarCity>Madeira</exemplarCity>
			</zone>
			<zone type="Europe/Lisbon">
				<exemplarCity>Lisboa</exemplarCity>
			</zone>
			<zone type="Pacific/Palau">
				<exemplarCity>Palau</exemplarCity>
			</zone>
			<zone type="America/Asuncion">
				<exemplarCity>Assunção</exemplarCity>
			</zone>
			<zone type="Asia/Qatar">
				<exemplarCity>Qatar</exemplarCity>
			</zone>
			<zone type="Indian/Reunion">
				<exemplarCity>Reunião</exemplarCity>
			</zone>
			<zone type="Europe/Bucharest">
				<exemplarCity>Bucareste</exemplarCity>
			</zone>
			<zone type="Europe/Belgrade">
				<exemplarCity>Belgrado</exemplarCity>
			</zone>
			<zone type="Europe/Kaliningrad">
				<exemplarCity>Caliningrado</exemplarCity>
			</zone>
			<zone type="Europe/Moscow">
				<exemplarCity>Moscovo</exemplarCity>
			</zone>
			<zone type="Europe/Volgograd">
				<exemplarCity>Volgogrado</exemplarCity>
			</zone>
			<zone type="Europe/Samara">
				<exemplarCity>Samara</exemplarCity>
			</zone>
			<zone type="Asia/Yekaterinburg">
				<exemplarCity>Ecaterimburgo</exemplarCity>
			</zone>
			<zone type="Asia/Omsk">
				<exemplarCity>Omsk</exemplarCity>
			</zone>
			<zone type="Asia/Novosibirsk">
				<exemplarCity>Novosibirsk</exemplarCity>
			</zone>
			<zone type="Asia/Novokuznetsk">
				<exemplarCity>Novokuznetsk</exemplarCity>
			</zone>
			<zone type="Asia/Krasnoyarsk">
				<exemplarCity>Krasnoyarsk</exemplarCity>
			</zone>
			<zone type="Asia/Irkutsk">
				<exemplarCity>Irkutsk</exemplarCity>
			</zone>
			<zone type="Asia/Yakutsk">
				<exemplarCity>Yakutsk</exemplarCity>
			</zone>
			<zone type="Asia/Vladivostok">
				<exemplarCity>Vladivostok</exemplarCity>
			</zone>
			<zone type="Asia/Khandyga">
				<exemplarCity>Khandyga</exemplarCity>
			</zone>
			<zone type="Asia/Sakhalin">
				<exemplarCity>Sacalina</exemplarCity>
			</zone>
			<zone type="Asia/Ust-Nera">
				<exemplarCity>Ust-Nera</exemplarCity>
			</zone>
			<zone type="Asia/Magadan">
				<exemplarCity>Magadan</exemplarCity>
			</zone>
			<zone type="Asia/Kamchatka">
				<exemplarCity>Kamchatka</exemplarCity>
			</zone>
			<zone type="Asia/Anadyr">
				<exemplarCity>Anadyr</exemplarCity>
			</zone>
			<zone type="Africa/Kigali">
				<exemplarCity>Kigali</exemplarCity>
			</zone>
			<zone type="Asia/Riyadh">
				<exemplarCity>Riade</exemplarCity>
			</zone>
			<zone type="Pacific/Guadalcanal">
				<exemplarCity>Guadalcanal</exemplarCity>
			</zone>
			<zone type="Indian/Mahe">
				<exemplarCity>Mahe</exemplarCity>
			</zone>
			<zone type="Africa/Khartoum">
				<exemplarCity>Cartum</exemplarCity>
			</zone>
			<zone type="Europe/Stockholm">
				<exemplarCity>Estocolmo</exemplarCity>
			</zone>
			<zone type="Asia/Singapore">
				<exemplarCity>Singapura</exemplarCity>
			</zone>
			<zone type="Atlantic/St_Helena">
				<exemplarCity>Santa Helena</exemplarCity>
			</zone>
			<zone type="Europe/Ljubljana">
				<exemplarCity>Liubliana</exemplarCity>
			</zone>
			<zone type="Arctic/Longyearbyen">
				<exemplarCity>Longyearbyen</exemplarCity>
			</zone>
			<zone type="Europe/Bratislava">
				<exemplarCity>Bratislava</exemplarCity>
			</zone>
			<zone type="Africa/Freetown">
				<exemplarCity>Freetown</exemplarCity>
			</zone>
			<zone type="Europe/San_Marino">
				<exemplarCity>São Marinho</exemplarCity>
			</zone>
			<zone type="Africa/Dakar">
				<exemplarCity>Dacar</exemplarCity>
			</zone>
			<zone type="Africa/Mogadishu">
				<exemplarCity>Mogadíscio</exemplarCity>
			</zone>
			<zone type="America/Paramaribo">
				<exemplarCity>Paramaribo</exemplarCity>
			</zone>
			<zone type="Africa/Juba">
				<exemplarCity>Juba</exemplarCity>
			</zone>
			<zone type="Africa/Sao_Tome">
				<exemplarCity>São Tomé</exemplarCity>
			</zone>
			<zone type="America/El_Salvador">
				<exemplarCity>El Salvador</exemplarCity>
			</zone>
			<zone type="America/Lower_Princes">
				<exemplarCity>Lower Prince's Quarter</exemplarCity>
			</zone>
			<zone type="Asia/Damascus">
				<exemplarCity>Damasco</exemplarCity>
			</zone>
			<zone type="Africa/Mbabane">
				<exemplarCity>Mbabane</exemplarCity>
			</zone>
			<zone type="America/Grand_Turk">
				<exemplarCity>Grand Turk</exemplarCity>
			</zone>
			<zone type="Africa/Ndjamena">
				<exemplarCity>Ndjamena</exemplarCity>
			</zone>
			<zone type="Indian/Kerguelen">
				<exemplarCity>Kerguelen</exemplarCity>
			</zone>
			<zone type="Africa/Lome">
				<exemplarCity>Lomé</exemplarCity>
			</zone>
			<zone type="Asia/Bangkok">
				<exemplarCity>Banguecoque</exemplarCity>
			</zone>
			<zone type="Asia/Dushanbe">
				<exemplarCity>Duchambe</exemplarCity>
			</zone>
			<zone type="Pacific/Fakaofo">
				<exemplarCity>Fakaofo</exemplarCity>
			</zone>
			<zone type="Asia/Dili">
				<exemplarCity>Dili</exemplarCity>
			</zone>
			<zone type="Asia/Ashgabat">
				<exemplarCity>Asgabate</exemplarCity>
			</zone>
			<zone type="Africa/Tunis">
				<exemplarCity>Tunes</exemplarCity>
			</zone>
			<zone type="Pacific/Tongatapu">
				<exemplarCity>Tongatapu</exemplarCity>
			</zone>
			<zone type="Europe/Istanbul">
				<exemplarCity>Istambul</exemplarCity>
			</zone>
			<zone type="America/Port_of_Spain">
				<exemplarCity>Porto de Espanha</exemplarCity>
			</zone>
			<zone type="Pacific/Funafuti">
				<exemplarCity>Funafuti</exemplarCity>
			</zone>
			<zone type="Asia/Taipei">
				<exemplarCity>Taipé</exemplarCity>
			</zone>
			<zone type="Africa/Dar_es_Salaam">
				<exemplarCity>Dar es Salaam</exemplarCity>
			</zone>
			<zone type="Europe/Uzhgorod">
				<exemplarCity>Uzhgorod</exemplarCity>
			</zone>
			<zone type="Europe/Kiev">
				<exemplarCity>Kiev</exemplarCity>
			</zone>
			<zone type="Europe/Simferopol">
				<exemplarCity>Simferopol</exemplarCity>
			</zone>
			<zone type="Europe/Zaporozhye">
				<exemplarCity>Zaporizhia</exemplarCity>
			</zone>
			<zone type="Africa/Kampala">
				<exemplarCity>Campala</exemplarCity>
			</zone>
			<zone type="Pacific/Midway">
				<exemplarCity>Midway</exemplarCity>
			</zone>
			<zone type="Pacific/Johnston">
				<exemplarCity>Johnston</exemplarCity>
			</zone>
			<zone type="Pacific/Wake">
				<exemplarCity>Wake</exemplarCity>
			</zone>
			<zone type="America/Adak">
				<exemplarCity>Adak</exemplarCity>
			</zone>
			<zone type="America/Nome">
				<exemplarCity>Nome</exemplarCity>
			</zone>
			<zone type="Pacific/Honolulu">
				<exemplarCity>Honolulu</exemplarCity>
			</zone>
			<zone type="America/Anchorage">
				<exemplarCity>Anchorage</exemplarCity>
			</zone>
			<zone type="America/Yakutat">
				<exemplarCity>Yakutat</exemplarCity>
			</zone>
			<zone type="America/Sitka">
				<exemplarCity>Sitka</exemplarCity>
			</zone>
			<zone type="America/Juneau">
				<exemplarCity>Juneau</exemplarCity>
			</zone>
			<zone type="America/Metlakatla">
				<exemplarCity>Metlakatla</exemplarCity>
			</zone>
			<zone type="America/Los_Angeles">
				<exemplarCity>Los Angeles</exemplarCity>
			</zone>
			<zone type="America/Boise">
				<exemplarCity>Boise</exemplarCity>
			</zone>
			<zone type="America/Phoenix">
				<exemplarCity>Phoenix</exemplarCity>
			</zone>
			<zone type="America/Denver">
				<exemplarCity>Denver</exemplarCity>
			</zone>
			<zone type="America/North_Dakota/Beulah">
				<exemplarCity>Beulah, Dakota do Norte</exemplarCity>
			</zone>
			<zone type="America/North_Dakota/New_Salem">
				<exemplarCity>New Salen, Dakota do Norte</exemplarCity>
			</zone>
			<zone type="America/North_Dakota/Center">
				<exemplarCity>Center, Dakota do Norte</exemplarCity>
			</zone>
			<zone type="America/Chicago">
				<exemplarCity>Chicago</exemplarCity>
			</zone>
			<zone type="America/Menominee">
				<exemplarCity>Menominee</exemplarCity>
			</zone>
			<zone type="America/Indiana/Vincennes">
				<exemplarCity>Vincennes, Indiana</exemplarCity>
			</zone>
			<zone type="America/Indiana/Petersburg">
				<exemplarCity>Petersburg, Indiana</exemplarCity>
			</zone>
			<zone type="America/Indiana/Tell_City">
				<exemplarCity>Tell City, Indiana</exemplarCity>
			</zone>
			<zone type="America/Indiana/Knox">
				<exemplarCity>Knox, Indiana</exemplarCity>
			</zone>
			<zone type="America/Indiana/Winamac">
				<exemplarCity>Winamac, Indiana</exemplarCity>
			</zone>
			<zone type="America/Indiana/Marengo">
				<exemplarCity>Marengo, Indiana</exemplarCity>
			</zone>
			<zone type="America/Indianapolis">
				<exemplarCity>Indianápolis</exemplarCity>
			</zone>
			<zone type="America/Louisville">
				<exemplarCity>Louisville</exemplarCity>
			</zone>
			<zone type="America/Indiana/Vevay">
				<exemplarCity>Vevay, Indiana</exemplarCity>
			</zone>
			<zone type="America/Kentucky/Monticello">
				<exemplarCity>Monticello, Kentucky</exemplarCity>
			</zone>
			<zone type="America/Detroit">
				<exemplarCity>Detroit</exemplarCity>
			</zone>
			<zone type="America/New_York">
				<exemplarCity>Nova Iorque</exemplarCity>
			</zone>
			<zone type="America/Montevideo">
				<exemplarCity>Montevideu</exemplarCity>
			</zone>
			<zone type="Asia/Samarkand">
				<exemplarCity>Samarcanda</exemplarCity>
			</zone>
			<zone type="Asia/Tashkent">
				<exemplarCity>Tashkent</exemplarCity>
			</zone>
			<zone type="Europe/Vatican">
				<exemplarCity>Vaticano</exemplarCity>
			</zone>
			<zone type="America/St_Vincent">
				<exemplarCity>São Vicente</exemplarCity>
			</zone>
			<zone type="America/Caracas">
				<exemplarCity>Caracas</exemplarCity>
			</zone>
			<zone type="America/Tortola">
				<exemplarCity>Tortola</exemplarCity>
			</zone>
			<zone type="America/St_Thomas">
				<exemplarCity>St. Thomas</exemplarCity>
			</zone>
			<zone type="Asia/Saigon">
				<exemplarCity>Cidade de Ho Chi Minh</exemplarCity>
			</zone>
			<zone type="Pacific/Efate">
				<exemplarCity>Efate</exemplarCity>
			</zone>
			<zone type="Pacific/Wallis">
				<exemplarCity>Wallis</exemplarCity>
			</zone>
			<zone type="Pacific/Apia">
				<exemplarCity>Ápia</exemplarCity>
			</zone>
			<zone type="Asia/Aden">
				<exemplarCity>Adem</exemplarCity>
			</zone>
			<zone type="Indian/Mayotte">
				<exemplarCity>Mayotte</exemplarCity>
			</zone>
			<zone type="Africa/Johannesburg">
				<exemplarCity>Joanesburgo</exemplarCity>
			</zone>
			<zone type="Africa/Lusaka">
				<exemplarCity>Lusaca</exemplarCity>
			</zone>
			<zone type="Africa/Harare">
				<exemplarCity>Harare</exemplarCity>
			</zone>
			<metazone type="Acre">
				<long>
					<generic>Hora do Acre</generic>
					<standard>Hora Padrão do Acre</standard>
					<daylight>Hora de Verão do Acre</daylight>
				</long>
				<short>
					<generic>∅∅∅</generic>
					<standard>∅∅∅</standard>
					<daylight>∅∅∅</daylight>
				</short>
			</metazone>
			<metazone type="Afghanistan">
				<long>
					<standard>Hora do Afeganistão</standard>
				</long>
			</metazone>
			<metazone type="Africa_Central">
				<long>
					<standard draft="contributed">Hora da África Central</standard>
				</long>
			</metazone>
			<metazone type="Africa_Eastern">
				<long>
					<standard draft="contributed">Hora da África Oriental</standard>
				</long>
			</metazone>
			<metazone type="Africa_Southern">
				<long>
					<standard>Hora da África do Sul</standard>
				</long>
			</metazone>
			<metazone type="Africa_Western">
				<long>
					<generic>Hora da África Ocidental</generic>
					<standard>Hora Padrão da África Ocidental</standard>
					<daylight>Hora de Verão da África Ocidental</daylight>
				</long>
			</metazone>
			<metazone type="Alaska">
				<long>
					<generic>Hora do Alasca</generic>
					<standard>Hora Padrão do Alasca</standard>
					<daylight>Hora de Verão do Alasca</daylight>
				</long>
			</metazone>
			<metazone type="Almaty">
				<long>
					<generic>Hora de Almaty</generic>
					<standard>Hora Padrão de Almaty</standard>
					<daylight>Hora de Verão de Almaty</daylight>
				</long>
			</metazone>
			<metazone type="Amazon">
				<long>
					<generic>Hora do Amazonas</generic>
					<standard>Hora Padrão do Amazonas</standard>
					<daylight>Hora de Verão do Amazonas</daylight>
				</long>
				<short>
					<generic>∅∅∅</generic>
					<standard>∅∅∅</standard>
					<daylight>∅∅∅</daylight>
				</short>
			</metazone>
			<metazone type="America_Central">
				<long>
					<generic>Hora Central</generic>
					<standard>Hora Padrão Central</standard>
					<daylight>Hora de Verão Central</daylight>
				</long>
			</metazone>
			<metazone type="America_Eastern">
				<long>
					<generic>Hora Oriental</generic>
					<standard>Hora Padrão Oriental</standard>
					<daylight>Hora de Verão Oriental</daylight>
				</long>
			</metazone>
			<metazone type="America_Mountain">
				<long>
					<generic>Hora da Montanha</generic>
					<standard>Hora Padrão da Montanha</standard>
					<daylight>Hora de Verão da Montanha</daylight>
				</long>
			</metazone>
			<metazone type="America_Pacific">
				<long>
					<generic>Hora do Pacífico</generic>
					<standard>Hora Padrão do Pacífico</standard>
					<daylight>Hora de Verão do Pacífico</daylight>
				</long>
			</metazone>
			<metazone type="Anadyr">
				<long>
					<generic>Hora de Anadyr</generic>
					<standard>Hora Padrão de Anadyr</standard>
					<daylight>Hora de Verão de Anadyr</daylight>
				</long>
			</metazone>
			<metazone type="Aqtau">
				<long>
					<generic>Hora de Aqtau</generic>
					<standard>Hora Padrão de Aqtau</standard>
					<daylight>Hora de Verão de Aqtau</daylight>
				</long>
			</metazone>
			<metazone type="Aqtobe">
				<long>
					<generic>Hora de Aqtobe</generic>
					<standard>Hora Padrão de Aqtobe</standard>
					<daylight>Hora de Verão de Aqtobe</daylight>
				</long>
			</metazone>
			<metazone type="Arabian">
				<long>
					<generic>Hora da Arábia</generic>
					<standard>Hora Padrão da Arábia</standard>
					<daylight>Hora de Verão da Arábia</daylight>
				</long>
			</metazone>
			<metazone type="Argentina">
				<long>
					<generic>Hora da Argentina</generic>
					<standard>Hora Padrão da Argentina</standard>
					<daylight>Hora de Verão da Argentina</daylight>
				</long>
			</metazone>
			<metazone type="Argentina_Western">
				<long>
					<generic>Hora da Argentina Ocidental</generic>
					<standard>Hora Padrão da Argentina Ocidental</standard>
					<daylight>Hora de Verão da Argentina Ocidental</daylight>
				</long>
			</metazone>
			<metazone type="Armenia">
				<long>
					<generic>Hora da Arménia</generic>
					<standard>Hora Padrão da Arménia</standard>
					<daylight>Hora de Verão da Arménia</daylight>
				</long>
			</metazone>
			<metazone type="Atlantic">
				<long>
					<generic>Hora do Atlântico</generic>
					<standard>Hora Padrão do Atlântico</standard>
					<daylight>Hora de Verão do Atlântico</daylight>
				</long>
			</metazone>
			<metazone type="Australia_Central">
				<long>
					<generic>Hora da Austrália Central</generic>
					<standard>Hora Padrão da Austrália Central</standard>
					<daylight>Hora de Verão da Austrália Central</daylight>
				</long>
			</metazone>
			<metazone type="Australia_CentralWestern">
				<long>
					<generic>Hora da Austrália Central Ocidental</generic>
					<standard>Hora Padrão da Austrália Central Ocidental</standard>
					<daylight>Hora de Verão da Austrália Central Ocidental</daylight>
				</long>
			</metazone>
			<metazone type="Australia_Eastern">
				<long>
					<generic>Hora da Austrália Oriental</generic>
					<standard>Hora Padrão da Austrália Oriental</standard>
					<daylight>Hora de Verão da Austrália Oriental</daylight>
				</long>
			</metazone>
			<metazone type="Australia_Western">
				<long>
					<generic>Hora da Austrália Ocidental</generic>
					<standard>Hora Padrão da Austrália Ocidental</standard>
					<daylight>Hora de Verão da Austrália Ocidental</daylight>
				</long>
			</metazone>
			<metazone type="Azerbaijan">
				<long>
					<generic>Hora do Azerbaijão</generic>
					<standard>Hora Padrão do Azerbaijão</standard>
					<daylight>Hora de Verão do Azerbaijão</daylight>
				</long>
			</metazone>
			<metazone type="Azores">
				<long>
					<generic>Hora dos Açores</generic>
					<standard>Hora Padrão dos Açores</standard>
					<daylight>Hora de Verão dos Açores</daylight>
				</long>
				<short>
					<generic>AZOT</generic>
					<standard>AZOT</standard>
					<daylight>AZOST</daylight>
				</short>
			</metazone>
			<metazone type="Bangladesh">
				<long>
					<generic>Hora do Bangladeche</generic>
					<standard>Hora Padrão do Bangladeche</standard>
					<daylight>Hora de Verão do Bangladeche</daylight>
				</long>
			</metazone>
			<metazone type="Bhutan">
				<long>
					<standard>Hora do Butão</standard>
				</long>
			</metazone>
			<metazone type="Bolivia">
				<long>
					<standard>Hora da Bolívia</standard>
				</long>
			</metazone>
			<metazone type="Brasilia">
				<long>
					<generic>Hora de Brasília</generic>
					<standard>Hora Padrão de Brasília</standard>
					<daylight>Hora de Verão de Brasília</daylight>
				</long>
				<short>
					<generic>∅∅∅</generic>
					<standard>∅∅∅</standard>
					<daylight>∅∅∅</daylight>
				</short>
			</metazone>
			<metazone type="Brunei">
				<long>
					<standard>Hora do Brunei Darussalam</standard>
				</long>
			</metazone>
			<metazone type="Cape_Verde">
				<long>
					<generic draft="contributed">Hora de Cabo Verde</generic>
					<standard draft="contributed">Hora Padrão de Cabo Verde</standard>
					<daylight draft="contributed">Hora de Verão de Cabo Verde</daylight>
				</long>
			</metazone>
			<metazone type="Chamorro">
				<long>
					<standard>Hora Padrão do Chamorro</standard>
				</long>
			</metazone>
			<metazone type="Chatham">
				<long>
					<generic>Hora do Chatham</generic>
					<standard>Hora Padrão do Chatham</standard>
					<daylight>Hora de Verão do Chatham</daylight>
				</long>
			</metazone>
			<metazone type="Chile">
				<long>
					<generic>Hora do Chile</generic>
					<standard>Hora Padrão do Chile</standard>
					<daylight>Hora de Verão do Chile</daylight>
				</long>
			</metazone>
			<metazone type="China">
				<long>
					<generic>Hora da China</generic>
					<standard>Hora Padrão da China</standard>
					<daylight>Hora de Verão da China</daylight>
				</long>
			</metazone>
			<metazone type="Choibalsan">
				<long>
					<generic>Hora de Choibalsan</generic>
					<standard>Hora Padrão de Choibalsan</standard>
					<daylight>Hora de Verão de Choibalsan</daylight>
				</long>
			</metazone>
			<metazone type="Christmas">
				<long>
					<standard>Hora da Ilha do Natal</standard>
				</long>
			</metazone>
			<metazone type="Cocos">
				<long>
					<standard>Hora das Ilhas Cocos</standard>
				</long>
			</metazone>
			<metazone type="Colombia">
				<long>
					<generic>Hora da Colômbia</generic>
					<standard>Hora Padrão da Colômbia</standard>
					<daylight>Hora de Verão da Colômbia</daylight>
				</long>
			</metazone>
			<metazone type="Cook">
				<long>
					<generic>Hora das Ilhas Cook</generic>
					<standard>Hora Padrão das Ilhas Cook</standard>
					<daylight>Hora de Verão das Ilhas Cook</daylight>
				</long>
			</metazone>
			<metazone type="Cuba">
				<long>
					<generic draft="contributed">Hora de Cuba</generic>
					<standard draft="contributed">Hora Padrão de Cuba</standard>
					<daylight draft="contributed">Hora de Verão de Cuba</daylight>
				</long>
			</metazone>
			<metazone type="Davis">
				<long>
					<standard>Hora de Davis</standard>
				</long>
			</metazone>
			<metazone type="DumontDUrville">
				<long>
					<standard>Hora de Dumont-d'Urville</standard>
				</long>
			</metazone>
			<metazone type="East_Timor">
				<long>
					<standard>Hora de Timor Leste</standard>
				</long>
			</metazone>
			<metazone type="Easter">
				<long>
					<generic>Hora da Ilha da Páscoa</generic>
					<standard>Hora Padrão da Ilha da Páscoa</standard>
					<daylight>Hora de Verão da Ilha da Páscoa</daylight>
				</long>
			</metazone>
			<metazone type="Ecuador">
				<long>
					<standard>Hora do Equador</standard>
				</long>
			</metazone>
			<metazone type="Europe_Central">
				<long>
					<generic>Hora da Europa Central</generic>
					<standard>Hora Padrão da Europa Central</standard>
					<daylight>Hora de Verão da Europa Central</daylight>
				</long>
				<short>
					<generic>CET</generic>
					<standard>CET</standard>
					<daylight>CEST</daylight>
				</short>
			</metazone>
			<metazone type="Europe_Eastern">
				<long>
					<generic>Hora da Europa de Leste</generic>
					<standard>Hora Padrão da Europa de Leste</standard>
					<daylight>Hora de Verão da Europa de Leste</daylight>
				</long>
				<short>
					<generic>EET</generic>
					<standard>EET</standard>
					<daylight>EEST</daylight>
				</short>
			</metazone>
			<metazone type="Europe_Western">
				<long>
					<generic>Hora da Europa Ocidental</generic>
					<standard>Hora Padrão da Europa Ocidental</standard>
					<daylight>Hora de Verão da Europa Ocidental</daylight>
				</long>
				<short>
					<generic>WET</generic>
					<standard>WET</standard>
					<daylight>WEST</daylight>
				</short>
			</metazone>
			<metazone type="Falkland">
				<long>
					<generic>Hora das Ilhas Falkland</generic>
					<standard>Hora Padrão das Ilhas Falkland</standard>
					<daylight>Hora de Verão das Ilhas Falkland</daylight>
				</long>
			</metazone>
			<metazone type="Fiji">
				<long>
					<generic>Hora de Fiji</generic>
					<standard>Hora Padrão de Fiji</standard>
					<daylight>Hora de Verão de Fiji</daylight>
				</long>
			</metazone>
			<metazone type="French_Guiana">
				<long>
					<standard>Hora da Guiana Francesa</standard>
				</long>
			</metazone>
			<metazone type="French_Southern">
				<long>
					<standard>Hora das Terras Austrais e Antártidas Francesas</standard>
				</long>
			</metazone>
			<metazone type="Galapagos">
				<long>
					<standard>Hora das Galápagos</standard>
				</long>
			</metazone>
			<metazone type="Gambier">
				<long>
					<standard>Hora de Gambier</standard>
				</long>
			</metazone>
			<metazone type="Georgia">
				<long>
					<generic>Hora da Geórgia</generic>
					<standard>Hora Padrão da Geórgia</standard>
					<daylight>Hora de Verão da Geórgia</daylight>
				</long>
			</metazone>
			<metazone type="Gilbert_Islands">
				<long>
					<standard>Hora das Ilhas Gilbert</standard>
				</long>
			</metazone>
			<metazone type="GMT">
				<long>
					<standard>Hora de Greenwich</standard>
				</long>
			</metazone>
			<metazone type="Greenland_Eastern">
				<long>
					<generic>Hora da Gronelândia Oriental</generic>
					<standard>Hora Padrão da Gronelândia Oriental</standard>
					<daylight>Hora de Verão da Gronelândia Oriental</daylight>
				</long>
			</metazone>
			<metazone type="Greenland_Western">
				<long>
					<generic>Hora da Gronelândia Ocidental</generic>
					<standard>Hora Padrão da Gronelândia Ocidental</standard>
					<daylight>Hora de Verão da Gronelândia Ocidental</daylight>
				</long>
			</metazone>
			<metazone type="Guam">
				<long>
					<standard draft="contributed">Hora Padrão de Guam</standard>
				</long>
			</metazone>
			<metazone type="Gulf">
				<long>
					<standard>Hora Padrão do Golfo</standard>
				</long>
			</metazone>
			<metazone type="Guyana">
				<long>
					<standard>Hora da Guiana</standard>
				</long>
			</metazone>
			<metazone type="Hawaii_Aleutian">
				<long>
					<generic>Hora do Havai e Aleutas</generic>
					<standard>Hora Padrão do Havai e Aleútes</standard>
					<daylight>Hora de Verão do Havai e Aleútes</daylight>
				</long>
			</metazone>
			<metazone type="Hong_Kong">
				<long>
					<generic>Hora de Hong Kong</generic>
					<standard>Hora Padrão de Hong Kong</standard>
					<daylight>Hora de Verão de Hong Kong</daylight>
				</long>
			</metazone>
			<metazone type="Hovd">
				<long>
					<generic>Hora de Hovd</generic>
					<standard>Hora Padrão de Hovd</standard>
					<daylight>Hora de Verão de Hovd</daylight>
				</long>
			</metazone>
			<metazone type="India">
				<long>
					<standard>Hora Padrão da Índia</standard>
				</long>
			</metazone>
			<metazone type="Indian_Ocean">
				<long>
					<standard>Hora do Oceano Índico</standard>
				</long>
			</metazone>
			<metazone type="Indochina">
				<long>
					<standard>Hora da Indochina</standard>
				</long>
			</metazone>
			<metazone type="Indonesia_Central">
				<long>
					<standard>Hora da Indonésia Central</standard>
				</long>
			</metazone>
			<metazone type="Indonesia_Eastern">
				<long>
					<standard>Hora da Indonésia Oriental</standard>
				</long>
			</metazone>
			<metazone type="Indonesia_Western">
				<long>
					<standard>Hora da Indonésia Ocidental</standard>
				</long>
			</metazone>
			<metazone type="Iran">
				<long>
					<generic>Hora do Irão</generic>
					<standard>Hora Padrão do Irão</standard>
					<daylight>Hora de Verão do Irão</daylight>
				</long>
			</metazone>
			<metazone type="Irkutsk">
				<long>
					<generic>Hora de Irkutsk</generic>
					<standard>Hora Padrão de Irkutsk</standard>
					<daylight>Hora de Verão de Irkutsk</daylight>
				</long>
			</metazone>
			<metazone type="Israel">
				<long>
					<generic>Hora de Israel</generic>
					<standard>Hora Padrão de Israel</standard>
					<daylight>Hora de Verão de Israel</daylight>
				</long>
			</metazone>
			<metazone type="Japan">
				<long>
					<generic>Hora do Japão</generic>
					<standard>Hora Padrão do Japão</standard>
					<daylight>Hora de Verão do Japão</daylight>
				</long>
			</metazone>
			<metazone type="Kamchatka">
				<long>
					<generic draft="contributed">Hora de Petropavlovsk-Kamchatski</generic>
					<standard draft="contributed">Hora Padrão de Petropavlovsk-Kamchatski</standard>
					<daylight draft="contributed">Hora de Verão de Petropavlovsk-Kamchatski</daylight>
				</long>
			</metazone>
			<metazone type="Kazakhstan_Eastern">
				<long>
					<standard>Hora do Cazaquistão Oriental</standard>
				</long>
			</metazone>
			<metazone type="Kazakhstan_Western">
				<long>
					<standard>Hora do Cazaquistão Ocidental</standard>
				</long>
			</metazone>
			<metazone type="Korea">
				<long>
					<generic>Hora da Coreia</generic>
					<standard>Hora Padrão da Coreia</standard>
					<daylight>Hora de Verão da Coreia</daylight>
				</long>
			</metazone>
			<metazone type="Kosrae">
				<long>
					<standard>Hora de Kosrae</standard>
				</long>
			</metazone>
			<metazone type="Krasnoyarsk">
				<long>
					<generic>Hora de Krasnoiarsk</generic>
					<standard>Hora Padrão de Krasnoyarsk</standard>
					<daylight>Hora de Verão de Krasnoyarsk</daylight>
				</long>
			</metazone>
			<metazone type="Kyrgystan">
				<long>
					<standard>Hora do Quirguistão</standard>
				</long>
			</metazone>
			<metazone type="Lanka">
				<long>
					<standard draft="contributed">Hora do Sri Lanka</standard>
				</long>
			</metazone>
			<metazone type="Line_Islands">
				<long>
					<standard>Hora das Ilhas Line</standard>
				</long>
			</metazone>
			<metazone type="Lord_Howe">
				<long>
					<generic>Hora de Lord Howe</generic>
					<standard>Hora Padrão de Lord Howe</standard>
					<daylight>Hora de Verão de Lord Howe</daylight>
				</long>
			</metazone>
			<metazone type="Macau">
				<long>
					<generic draft="contributed">Hora de Macau</generic>
					<standard draft="contributed">Hora Padrão de Macau</standard>
					<daylight draft="contributed">Hora de Verão de Macau</daylight>
				</long>
			</metazone>
			<metazone type="Macquarie">
				<long>
					<standard>Hora da Ilha Macquarie</standard>
				</long>
			</metazone>
			<metazone type="Magadan">
				<long>
					<generic>Hora de Magadan</generic>
					<standard>Hora Padrão de Magadan</standard>
					<daylight>Hora de Verão de Magadan</daylight>
				</long>
			</metazone>
			<metazone type="Malaysia">
				<long>
					<standard>Hora da Malásia</standard>
				</long>
			</metazone>
			<metazone type="Maldives">
				<long>
					<standard>Hora das Maldivas</standard>
				</long>
			</metazone>
			<metazone type="Marquesas">
				<long>
					<standard>Hora das Ilhas Marquesas</standard>
				</long>
			</metazone>
			<metazone type="Marshall_Islands">
				<long>
					<standard>Hora das Ilhas Marshall</standard>
				</long>
			</metazone>
			<metazone type="Mauritius">
				<long>
					<generic>Hora das Maurícias</generic>
					<standard>Hora Padrão das Maurícias</standard>
					<daylight>Hora de Verão das Maurícias</daylight>
				</long>
			</metazone>
			<metazone type="Mawson">
				<long>
					<standard>Hora de Mawson</standard>
				</long>
			</metazone>
			<metazone type="Mongolia">
				<long>
					<generic>Hora de Ulan Bator</generic>
					<standard>Hora Padrão de Ulan Bator</standard>
					<daylight>Hora de Verão de Ulan Bator</daylight>
				</long>
			</metazone>
			<metazone type="Moscow">
				<long>
					<generic>Hora de Moscovo</generic>
					<standard>Hora Padrão de Moscovo</standard>
					<daylight>Hora de Verão de Moscovo</daylight>
				</long>
			</metazone>
			<metazone type="Myanmar">
				<long>
					<standard>Hora de Mianmar</standard>
				</long>
			</metazone>
			<metazone type="Nauru">
				<long>
					<standard>Hora de Nauru</standard>
				</long>
			</metazone>
			<metazone type="Nepal">
				<long>
					<standard>Hora do Nepal</standard>
				</long>
			</metazone>
			<metazone type="New_Caledonia">
				<long>
					<generic>Hora da Nova Caledónia</generic>
					<standard>Hora Padrão da Nova Caledónia</standard>
					<daylight>Hora de Verão da Nova Caledónia</daylight>
				</long>
			</metazone>
			<metazone type="New_Zealand">
				<long>
					<generic>Hora da Nova Zelândia</generic>
					<standard>Hora Padrão da Nova Zelândia</standard>
					<daylight>Hora de Verão da Nova Zelândia</daylight>
				</long>
			</metazone>
			<metazone type="Newfoundland">
				<long>
					<generic>Hora da Terra Nova</generic>
					<standard>Hora Padrão da Terra Nova</standard>
					<daylight>Hora de Verão da Terra Nova</daylight>
				</long>
			</metazone>
			<metazone type="Niue">
				<long>
					<standard>Hora de Niue</standard>
				</long>
			</metazone>
			<metazone type="Norfolk">
				<long>
					<standard>Hora das Ilhas Norfolk</standard>
				</long>
			</metazone>
			<metazone type="Noronha">
				<long>
					<generic>Hora de Fernando de Noronha</generic>
					<standard>Hora Padrão de Fernando de Noronha</standard>
					<daylight>Hora de Verão de Fernando de Noronha</daylight>
				</long>
			</metazone>
			<metazone type="North_Mariana">
				<long>
					<standard draft="contributed">Hora das Ilhas Mariana do Norte</standard>
				</long>
			</metazone>
			<metazone type="Novosibirsk">
				<long>
					<generic>Hora de Novosibirsk</generic>
					<standard>Hora Padrão de Novosibirsk</standard>
					<daylight>Hora de Verão de Novosibirsk</daylight>
				</long>
			</metazone>
			<metazone type="Omsk">
				<long>
					<generic>Hora de Omsk</generic>
					<standard>Hora Padrão de Omsk</standard>
					<daylight>Hora de Verão de Omsk</daylight>
				</long>
			</metazone>
			<metazone type="Pakistan">
				<long>
					<generic>Hora do Paquistão</generic>
					<standard>Hora Padrão do Paquistão</standard>
					<daylight>Hora de Verão do Paquistão</daylight>
				</long>
			</metazone>
			<metazone type="Palau">
				<long>
					<standard>Hora de Palau</standard>
				</long>
			</metazone>
			<metazone type="Papua_New_Guinea">
				<long>
					<standard>Hora de Papua Nova Guiné</standard>
				</long>
			</metazone>
			<metazone type="Paraguay">
				<long>
					<generic>Hora do Paraguai</generic>
					<standard>Hora Padrão do Paraguai</standard>
					<daylight>Hora de Verão do Paraguai</daylight>
				</long>
			</metazone>
			<metazone type="Peru">
				<long>
					<generic>Hora do Peru</generic>
					<standard>Hora Padrão do Peru</standard>
					<daylight>Hora de Verão do Peru</daylight>
				</long>
			</metazone>
			<metazone type="Philippines">
				<long>
					<generic>Hora das Filipinas</generic>
					<standard>Hora Padrão das Filipinas</standard>
					<daylight>Hora de Verão das Filipinas</daylight>
				</long>
			</metazone>
			<metazone type="Phoenix_Islands">
				<long>
					<standard>Hora das Ilhas Fénix</standard>
				</long>
			</metazone>
			<metazone type="Pierre_Miquelon">
				<long>
					<generic>Hora de São Pedro e Miquelão</generic>
					<standard>Hora Padrão de São Pedro e Miquelão</standard>
					<daylight>Hora de Verão de São Pedro e Miquelão</daylight>
				</long>
			</metazone>
			<metazone type="Pitcairn">
				<long>
					<standard>Hora de Pitcairn</standard>
				</long>
			</metazone>
			<metazone type="Ponape">
				<long>
					<standard>Hora de Ponape</standard>
				</long>
			</metazone>
			<metazone type="Qyzylorda">
				<long>
					<generic draft="contributed">Hora de Qyzylorda</generic>
					<standard draft="contributed">Hora Padrão de Qyzylorda</standard>
					<daylight draft="contributed">Hora de Verão de Qyzylorda</daylight>
				</long>
			</metazone>
			<metazone type="Reunion">
				<long>
					<standard>Hora de Reunião</standard>
				</long>
			</metazone>
			<metazone type="Rothera">
				<long>
					<standard>Hora de Rothera</standard>
				</long>
			</metazone>
			<metazone type="Sakhalin">
				<long>
					<generic>Hora de Sacalina</generic>
					<standard>Hora Padrão de Sacalina</standard>
					<daylight>Hora de Verão de Sacalina</daylight>
				</long>
			</metazone>
			<metazone type="Samara">
				<long>
					<generic draft="contributed">Hora de Samara</generic>
					<standard draft="contributed">Hora Padrão de Samara</standard>
					<daylight draft="contributed">Hora de Verão de Samara</daylight>
				</long>
			</metazone>
			<metazone type="Samoa">
				<long>
					<generic>Hora de Samoa</generic>
					<standard>Hora Padrão de Samoa</standard>
					<daylight>Hora de Verão de Samoa</daylight>
				</long>
			</metazone>
			<metazone type="Seychelles">
				<long>
					<standard>Hora das Seicheles</standard>
				</long>
			</metazone>
			<metazone type="Singapore">
				<long>
					<standard>Hora Padrão de Singapura</standard>
				</long>
			</metazone>
			<metazone type="Solomon">
				<long>
					<standard>Hora das Ilhas Salomão</standard>
				</long>
			</metazone>
			<metazone type="South_Georgia">
				<long>
					<standard>Hora da Geórgia do Sul</standard>
				</long>
			</metazone>
			<metazone type="Suriname">
				<long>
					<standard>Hora do Suriname</standard>
				</long>
			</metazone>
			<metazone type="Syowa">
				<long>
					<standard>Hora de Syowa</standard>
				</long>
			</metazone>
			<metazone type="Tahiti">
				<long>
					<standard>Hora do Taiti</standard>
				</long>
			</metazone>
			<metazone type="Taipei">
				<long>
					<generic>Hora de Taipé</generic>
					<standard>Hora Padrão de Taipé</standard>
					<daylight>Hora de Verão de Taipé</daylight>
				</long>
			</metazone>
			<metazone type="Tajikistan">
				<long>
					<standard>Hora do Tajiquistão</standard>
				</long>
			</metazone>
			<metazone type="Tokelau">
				<long>
					<standard>Hora de Tokelau</standard>
				</long>
			</metazone>
			<metazone type="Tonga">
				<long>
					<generic>Hora de Tonga</generic>
					<standard>Hora Padrão de Tonga</standard>
					<daylight>Hora de Verão de Tonga</daylight>
				</long>
			</metazone>
			<metazone type="Truk">
				<long>
					<standard>Hora de Chuuk</standard>
				</long>
			</metazone>
			<metazone type="Turkmenistan">
				<long>
					<generic>Hora do Turquemenistão</generic>
					<standard>Hora Padrão do Turquemenistão</standard>
					<daylight>Hora de Verão do Turquemenistão</daylight>
				</long>
			</metazone>
			<metazone type="Tuvalu">
				<long>
					<standard>Hora de Tuvalu</standard>
				</long>
			</metazone>
			<metazone type="Uruguay">
				<long>
					<generic>Hora do Uruguai</generic>
					<standard>Hora Padrão do Uruguai</standard>
					<daylight>Hora de Verão do Uruguai</daylight>
				</long>
			</metazone>
			<metazone type="Uzbekistan">
				<long>
					<generic>Hora do Uzbequistão</generic>
					<standard>Hora Padrão do Uzbequistão</standard>
					<daylight>Hora de Verão do Uzbequistão</daylight>
				</long>
			</metazone>
			<metazone type="Vanuatu">
				<long>
					<generic>Hora do Vanuatu</generic>
					<standard>Hora Padrão do Vanuatu</standard>
					<daylight>Hora de Verão do Vanuatu</daylight>
				</long>
			</metazone>
			<metazone type="Venezuela">
				<long>
					<standard>Hora da Venezuela</standard>
				</long>
			</metazone>
			<metazone type="Vladivostok">
				<long>
					<generic>Hora de Vladivostok</generic>
					<standard>Hora Padrão de Vladivostok</standard>
					<daylight>Hora de Verão de Vladivostok</daylight>
				</long>
			</metazone>
			<metazone type="Volgograd">
				<long>
					<generic>Hora de Volgogrado</generic>
					<standard>Hora Padrão de Volgogrado</standard>
					<daylight>Hora de Verão de Volgogrado</daylight>
				</long>
			</metazone>
			<metazone type="Vostok">
				<long>
					<standard>Hora de Vostok</standard>
				</long>
			</metazone>
			<metazone type="Wake">
				<long>
					<standard>Hora da Ilha Wake</standard>
				</long>
			</metazone>
			<metazone type="Wallis">
				<long>
					<standard>Hora de Wallis e Futuna</standard>
				</long>
			</metazone>
			<metazone type="Yakutsk">
				<long>
					<generic>Hora de Yakutsk</generic>
					<standard>Hora Padrão de Yakutsk</standard>
					<daylight>Hora de Verão de Yakutsk</daylight>
				</long>
			</metazone>
			<metazone type="Yekaterinburg">
				<long>
					<generic>Hora de Ecaterimburgo</generic>
					<standard>Hora Padrão de Ecaterimburgo</standard>
					<daylight>Hora de Verão de Ecaterimburgo</daylight>
				</long>
			</metazone>
		</timeZoneNames>
	</dates>
	<numbers>
		<symbols numberSystem="latn">
			<decimal>,</decimal>
			<group> </group>
			<percentSign>%</percentSign>
			<plusSign>+</plusSign>
			<minusSign>-</minusSign>
			<exponential>E</exponential>
			<superscriptingExponent>×</superscriptingExponent>
			<perMille>‰</perMille>
			<infinity>∞</infinity>
			<nan>NaN</nan>
		</symbols>
		<decimalFormats numberSystem="latn">
			<decimalFormatLength>
				<decimalFormat>
					<pattern>#,##0.###</pattern>
				</decimalFormat>
			</decimalFormatLength>
			<decimalFormatLength type="long">
				<decimalFormat>
					<pattern type="1000" count="one">0 mil</pattern>
					<pattern type="1000" count="other">0 mil</pattern>
					<pattern type="10000" count="one">00 mil</pattern>
					<pattern type="10000" count="other">00 mil</pattern>
					<pattern type="100000" count="one">000 mil</pattern>
					<pattern type="100000" count="other">000 mil</pattern>
					<pattern type="1000000" count="one">0 milhão</pattern>
					<pattern type="1000000" count="other">0 milhões</pattern>
					<pattern type="10000000" count="one">00 milhões</pattern>
					<pattern type="10000000" count="other">00 milhões</pattern>
					<pattern type="100000000" count="one">000 milhões</pattern>
					<pattern type="100000000" count="other">000 milhões</pattern>
					<pattern type="1000000000" count="one">0 mil milhões</pattern>
					<pattern type="1000000000" count="other">0 mil milhões</pattern>
					<pattern type="10000000000" count="one">00 mil milhões</pattern>
					<pattern type="10000000000" count="other">00 mil milhões</pattern>
					<pattern type="100000000000" count="one">000 mil milhões</pattern>
					<pattern type="100000000000" count="other">000 mil milhões</pattern>
					<pattern type="1000000000000" count="one">0 bilião</pattern>
					<pattern type="1000000000000" count="other">0 biliões</pattern>
					<pattern type="10000000000000" count="one">00 biliões</pattern>
					<pattern type="10000000000000" count="other">00 biliões</pattern>
					<pattern type="100000000000000" count="one">000 biliões</pattern>
					<pattern type="100000000000000" count="other">000 biliões</pattern>
				</decimalFormat>
			</decimalFormatLength>
			<decimalFormatLength type="short">
				<decimalFormat>
					<pattern type="1000" count="one">0 mil</pattern>
					<pattern type="1000" count="other">0 mil</pattern>
					<pattern type="10000" count="one">00 mil</pattern>
					<pattern type="10000" count="other">00 mil</pattern>
					<pattern type="100000" count="one">000 mil</pattern>
					<pattern type="100000" count="other">000 mil</pattern>
					<pattern type="1000000" count="one">0 M</pattern>
					<pattern type="1000000" count="other">0 M</pattern>
					<pattern type="10000000" count="one">00 M</pattern>
					<pattern type="10000000" count="other">00 M</pattern>
					<pattern type="100000000" count="one">000 M</pattern>
					<pattern type="100000000" count="other">000 M</pattern>
					<pattern type="1000000000" count="one">0 MM</pattern>
					<pattern type="1000000000" count="other">0 MM</pattern>
					<pattern type="10000000000" count="one">00 MM</pattern>
					<pattern type="10000000000" count="other">00 MM</pattern>
					<pattern type="100000000000" count="one">000 MM</pattern>
					<pattern type="100000000000" count="other">000 MM</pattern>
					<pattern type="1000000000000" count="one">0 Bi</pattern>
					<pattern type="1000000000000" count="other">0 Bi</pattern>
					<pattern type="10000000000000" count="one">00 Bi</pattern>
					<pattern type="10000000000000" count="other">00 Bi</pattern>
					<pattern type="100000000000000" count="one">000 Bi</pattern>
					<pattern type="100000000000000" count="other">000 Bi</pattern>
				</decimalFormat>
			</decimalFormatLength>
		</decimalFormats>
		<scientificFormats numberSystem="latn">
			<scientificFormatLength>
				<scientificFormat>
					<pattern>#E0</pattern>
				</scientificFormat>
			</scientificFormatLength>
		</scientificFormats>
		<percentFormats numberSystem="latn">
			<percentFormatLength>
				<percentFormat>
					<pattern>#,##0%</pattern>
				</percentFormat>
			</percentFormatLength>
		</percentFormats>
		<currencyFormats numberSystem="latn">
			<currencyFormatLength>
				<currencyFormat type="standard">
					<pattern>#,##0.00 ¤</pattern>
				</currencyFormat>
				<currencyFormat type="accounting">
					<pattern>#,##0.00 ¤;(#,##0.00 ¤)</pattern>
				</currencyFormat>
			</currencyFormatLength>
			<unitPattern count="one">{0} {1}</unitPattern>
			<unitPattern count="other">{0} {1}</unitPattern>
		</currencyFormats>
		<currencies>
			<currency type="AED">
				<displayName>Dirham dos Emirados Árabes Unidos</displayName>
				<displayName count="one">Dirham dos Emirados Árabes Unidos</displayName>
				<displayName count="other">Dirhams dos Emirados Árabes Unidos</displayName>
			</currency>
			<currency type="AFA">
				<displayName>Afeghani (1927–2002)</displayName>
			</currency>
			<currency type="AFN">
				<displayName>Afegani do Afeganistão</displayName>
				<displayName count="one">Afegani do Afeganistão</displayName>
				<displayName count="other">Afeganis do Afeganistão</displayName>
			</currency>
			<currency type="ALL">
				<displayName>Lek albanês</displayName>
				<displayName count="one">Lek albanês</displayName>
				<displayName count="other">Leks albaneses</displayName>
			</currency>
			<currency type="AMD">
				<displayName>Dram arménio</displayName>
				<displayName count="one">Dram arménio</displayName>
				<displayName count="other">Drams arménios</displayName>
			</currency>
			<currency type="ANG">
				<displayName>Florim das Antilhas Holandesas</displayName>
				<displayName count="one">Florim das Antilhas Holandesas</displayName>
				<displayName count="other">Florins das Antilhas Holandesas</displayName>
			</currency>
			<currency type="AOA">
				<displayName>Kwanza angolano</displayName>
				<displayName count="one">Kwanza angolano</displayName>
				<displayName count="other">Kwanzas angolanos</displayName>
			</currency>
			<currency type="ARS">
				<displayName>Peso argentino</displayName>
				<displayName count="one">Peso argentino</displayName>
				<displayName count="other">Pesos argentinos</displayName>
			</currency>
			<currency type="AUD">
				<displayName>Dólar australiano</displayName>
				<displayName count="one">Dólar australiano</displayName>
				<displayName count="other">Dólares australianos</displayName>
				<symbol>AU$</symbol>
			</currency>
			<currency type="AWG">
				<displayName>Florim de Aruba</displayName>
				<displayName count="one">Florim de Aruba</displayName>
				<displayName count="other">Florins de Aruba</displayName>
			</currency>
			<currency type="AZN">
				<displayName>Manat do Azerbaijão</displayName>
				<displayName count="one">Manat do Azerbaijão</displayName>
				<displayName count="other">Manats do Azerbaijão</displayName>
			</currency>
			<currency type="BAD">
				<displayName>Dinar da Bósnia-Herzegóvina</displayName>
			</currency>
			<currency type="BAM">
				<displayName>Marco bósnio-herzegóvino conversível</displayName>
				<displayName count="one">Marco bósnio-herzegóvino conversível</displayName>
				<displayName count="other">Marcos bósnio-herzegóvinos conversíveis</displayName>
			</currency>
			<currency type="BBD">
				<displayName>Dólar barbadense</displayName>
				<displayName count="one">Dólar barbadense</displayName>
				<displayName count="other">Dólares barbadenses</displayName>
			</currency>
			<currency type="BDT">
				<displayName>Taka de Bangladesh</displayName>
				<displayName count="one">Taka de Bangladesh</displayName>
				<displayName count="other">Takas de Bangladesh</displayName>
			</currency>
			<currency type="BEC">
				<displayName draft="contributed">Franco belga (convertível)</displayName>
			</currency>
			<currency type="BGN">
				<displayName>Lev búlgaro</displayName>
				<displayName count="one">Lev búlgaro</displayName>
				<displayName count="other">Levs búlgaros</displayName>
			</currency>
			<currency type="BHD">
				<displayName>Dinar baremita</displayName>
				<displayName count="one">Dinar baremita</displayName>
				<displayName count="other">Dinares baremitas</displayName>
			</currency>
			<currency type="BIF">
				<displayName>Franco burundiano</displayName>
				<displayName count="one">Franco burundiano</displayName>
				<displayName count="other">Francos burundianos</displayName>
			</currency>
			<currency type="BMD">
				<displayName>Dólar bermudense</displayName>
				<displayName count="one">Dólar bermudense</displayName>
				<displayName count="other">Dólares bermudenses</displayName>
			</currency>
			<currency type="BND">
				<displayName>Dólar bruneíno</displayName>
				<displayName count="one">Dólar bruneíno</displayName>
				<displayName count="other">Dólares bruneínos</displayName>
			</currency>
			<currency type="BOB">
				<displayName>Boliviano</displayName>
				<displayName count="one">Boliviano</displayName>
				<displayName count="other">Bolivianos</displayName>
			</currency>
			<currency type="BRL">
				<displayName>Real brasileiro</displayName>
				<displayName count="one">Real brasileiro</displayName>
				<displayName count="other">Reais brasileiros</displayName>
				<symbol>R$</symbol>
			</currency>
			<currency type="BSD">
				<displayName>Dólar das Bahamas</displayName>
				<displayName count="one">Dólar das Bahamas</displayName>
				<displayName count="other">Dólares das Bahamas</displayName>
			</currency>
			<currency type="BTN">
				<displayName>Ngultrum do Butão</displayName>
				<displayName count="one">Ngultrum do Butão</displayName>
				<displayName count="other">Ngultruns do Butão</displayName>
				<symbol>BTN</symbol>
			</currency>
			<currency type="BWP">
				<displayName>Pula de Botswana</displayName>
				<displayName count="one">Pula de Botswana</displayName>
				<displayName count="other">Pulas de Botswana</displayName>
			</currency>
			<currency type="BYB">
				<displayName draft="contributed">Rublo novo bielorusso (1994–1999)</displayName>
			</currency>
			<currency type="BYR">
				<displayName>Rublo bielorusso</displayName>
				<displayName count="one">Rublo bielorusso</displayName>
				<displayName count="other">Rublos bielorussos</displayName>
			</currency>
			<currency type="BZD">
				<displayName>Dólar belizense</displayName>
				<displayName count="one">Dólar belizense</displayName>
				<displayName count="other">Dólares belizenses</displayName>
			</currency>
			<currency type="CAD">
				<displayName>Dólar canadiano</displayName>
				<displayName count="one">Dólar canadiano</displayName>
				<displayName count="other">Dólares canadianos</displayName>
				<symbol>CA$</symbol>
			</currency>
			<currency type="CDF">
				<displayName>Franco congolês</displayName>
				<displayName count="one">Franco congolês</displayName>
				<displayName count="other">Francos congoleses</displayName>
			</currency>
			<currency type="CHF">
				<displayName>Franco suíço</displayName>
				<displayName count="one">Franco suíço</displayName>
				<displayName count="other">Francos suíços</displayName>
			</currency>
			<currency type="CLP">
				<displayName>Peso chileno</displayName>
				<displayName count="one">Peso chileno</displayName>
				<displayName count="other">Pesos chilenos</displayName>
			</currency>
			<currency type="CNY">
				<displayName>Yuan chinês</displayName>
				<displayName count="one">Yuan chinês</displayName>
				<displayName count="other">Yuans chineses</displayName>
				<symbol>CN¥</symbol>
			</currency>
			<currency type="COP">
				<displayName>Peso colombiano</displayName>
				<displayName count="one">Peso colombiano</displayName>
				<displayName count="other">Pesos colombianos</displayName>
			</currency>
			<currency type="CRC">
				<displayName>Colon costa-riquenho</displayName>
				<displayName count="one">Colon costa-riquenho</displayName>
				<displayName count="other">Colons costa-riquenhos</displayName>
			</currency>
			<currency type="CUC">
				<displayName>Peso cubano conversível</displayName>
				<displayName count="one">Peso cubano conversível</displayName>
				<displayName count="other">Pesos cubanos conversíveis</displayName>
			</currency>
			<currency type="CUP">
				<displayName>Peso cubano</displayName>
				<displayName count="one">Peso cubano</displayName>
				<displayName count="other">Pesos cubanos</displayName>
			</currency>
			<currency type="CVE">
				<displayName>Escudo cabo-verdiano</displayName>
				<displayName count="one">Escudo cabo-verdiano</displayName>
				<displayName count="other">Escudos cabo-verdianos</displayName>
			</currency>
			<currency type="CYP">
				<displayName>Libra de Chipre</displayName>
			</currency>
			<currency type="CZK">
				<displayName>Coroa checa</displayName>
				<displayName count="one">Coroa checa</displayName>
				<displayName count="other">Coroas checas</displayName>
			</currency>
			<currency type="DJF">
				<displayName>Franco jibutiano</displayName>
				<displayName count="one">Franco jibutiano</displayName>
				<displayName count="other">Francos jibutianos</displayName>
			</currency>
			<currency type="DKK">
				<displayName>Coroa dinamarquesa</displayName>
				<displayName count="one">Coroa dinamarquesa</displayName>
				<displayName count="other">Coroas dinamarquesas</displayName>
			</currency>
			<currency type="DOP">
				<displayName>Peso dominicano</displayName>
				<displayName count="one">Peso dominicano</displayName>
				<displayName count="other">Pesos dominicanos</displayName>
			</currency>
			<currency type="DZD">
				<displayName>Dinar argelino</displayName>
				<displayName count="one">Dinar argelino</displayName>
				<displayName count="other">Dinares argelinos</displayName>
			</currency>
			<currency type="ECV">
				<displayName>Unidad de Valor Constante (UVC) do Equador</displayName>
			</currency>
			<currency type="EGP">
				<displayName>Libra egípcia</displayName>
				<displayName count="one">Libra egípcia</displayName>
				<displayName count="other">Libras egípcias</displayName>
			</currency>
			<currency type="ERN">
				<displayName>Nakfa da Eritreia</displayName>
				<displayName count="one">Nakfa da Eritreia</displayName>
				<displayName count="other">Nakfas da Eritreia</displayName>
			</currency>
			<currency type="ETB">
				<displayName>Birr etíope</displayName>
				<displayName count="one">Birr etíope</displayName>
				<displayName count="other">Birrs etíopes</displayName>
			</currency>
			<currency type="EUR">
				<displayName>Euro</displayName>
				<displayName count="one">Euro</displayName>
				<displayName count="other">Euros</displayName>
				<symbol>€</symbol>
			</currency>
			<currency type="FJD">
				<displayName>Dólar de Fiji</displayName>
				<displayName count="one">Dólar de Fiji</displayName>
				<displayName count="other">Dólares de Fiji</displayName>
			</currency>
			<currency type="FKP">
				<displayName>Libra das Ilhas Falkland</displayName>
				<displayName count="one">Libra das Ilhas Falkland</displayName>
				<displayName count="other">Libras das Ilhas Falkland</displayName>
			</currency>
			<currency type="GBP">
				<displayName>Libra esterlina britânica</displayName>
				<displayName count="one">Libra esterlina britânica</displayName>
				<displayName count="other">Libras esterlinas britânicas</displayName>
				<symbol>£</symbol>
			</currency>
			<currency type="GEL">
				<displayName>Lari georgiano</displayName>
				<displayName count="one">Lari georgiano</displayName>
				<displayName count="other">Laris georgianos</displayName>
			</currency>
			<currency type="GHC">
				<displayName draft="unconfirmed">Cedi do Gana</displayName>
			</currency>
			<currency type="GHS">
				<displayName>Cedi de Gana</displayName>
				<displayName count="one">Cedi de Gana</displayName>
				<displayName count="other">Cedis de Gana</displayName>
			</currency>
			<currency type="GIP">
				<displayName>Libra de Gibraltar</displayName>
				<displayName count="one">Libra de Gibraltar</displayName>
				<displayName count="other">Libras de Gibraltar</displayName>
			</currency>
			<currency type="GMD">
				<displayName>Dalasi da Gâmbia</displayName>
				<displayName count="one">Dalasi da Gâmbia</displayName>
				<displayName count="other">Dalasis da Gâmbia</displayName>
				<symbol>GMD</symbol>
			</currency>
			<currency type="GNF">
				<displayName>Franco Guineense</displayName>
				<displayName count="one">Franco Guineense</displayName>
				<displayName count="other">Francos Guineenses</displayName>
			</currency>
			<currency type="GTQ">
				<displayName>Quetzal da Guatemala</displayName>
				<displayName count="one">Quetzal da Guatemala</displayName>
				<displayName count="other">Quetzales da Guatemala</displayName>
			</currency>
			<currency type="GYD">
				<displayName>Dólar da Guiana</displayName>
				<displayName count="one">Dólar da Guiana</displayName>
				<displayName count="other">Dólares da Guiana</displayName>
			</currency>
			<currency type="HKD">
				<displayName>Dólar de Hong Kong</displayName>
				<displayName count="one">Dólar de Hong Kong</displayName>
				<displayName count="other">Dólares de Hong Kong</displayName>
				<symbol>HK$</symbol>
			</currency>
			<currency type="HNL">
				<displayName>Lempira das Honduras</displayName>
				<displayName count="one">Lempira de Honduras</displayName>
				<displayName count="other">Lempiras das Honduras</displayName>
			</currency>
			<currency type="HRK">
				<displayName>Kuna croata</displayName>
				<displayName count="one">Kuna croata</displayName>
				<displayName count="other">Kunas croatas</displayName>
			</currency>
			<currency type="HTG">
				<displayName>Gourde haitiano</displayName>
				<displayName count="one">Gourde haitiano</displayName>
				<displayName count="other">Gourdes haitianos</displayName>
			</currency>
			<currency type="HUF">
				<displayName>Forint húngaro</displayName>
				<displayName count="one">Forint húngaro</displayName>
				<displayName count="other">Forints húngaros</displayName>
			</currency>
			<currency type="IDR">
				<displayName>Rupia indonésia</displayName>
				<displayName count="one">Rupia indonésia</displayName>
				<displayName count="other">Rupias indonésias</displayName>
			</currency>
			<currency type="ILS">
				<displayName>Sheqel novo israelita</displayName>
				<displayName count="one">Sheqel novo israelita</displayName>
				<displayName count="other">Sheqels novos israelitas</displayName>
				<symbol>₪</symbol>
			</currency>
			<currency type="INR">
				<displayName>Rupia indiana</displayName>
				<displayName count="one">Rupia indiana</displayName>
				<displayName count="other">Rupias indianas</displayName>
				<symbol>₹</symbol>
			</currency>
			<currency type="IQD">
				<displayName>Dinar iraquiano</displayName>
				<displayName count="one">Dinar iraquiano</displayName>
				<displayName count="other">Dinares iraquianos</displayName>
			</currency>
			<currency type="IRR">
				<displayName>Rial iraniano</displayName>
				<displayName count="one">Rial iraniano</displayName>
				<displayName count="other">Riais iranianos</displayName>
			</currency>
			<currency type="ISK">
				<displayName>Coroa islandesa</displayName>
				<displayName count="one">Coroa islandesa</displayName>
				<displayName count="other">Coroas islandesas</displayName>
			</currency>
			<currency type="JMD">
				<displayName>Dólar jamaicano</displayName>
				<displayName count="one">Dólar jamaicano</displayName>
				<displayName count="other">Dólares jamaicanos</displayName>
			</currency>
			<currency type="JOD">
				<displayName>Dinar jordaniano</displayName>
				<displayName count="one">Dinar jordaniano</displayName>
				<displayName count="other">Dinares jordanianos</displayName>
			</currency>
			<currency type="JPY">
				<displayName>Iene japonês</displayName>
				<displayName count="one">Iene japonês</displayName>
				<displayName count="other">Ienes japoneses</displayName>
				<symbol>JP¥</symbol>
			</currency>
			<currency type="KES">
				<displayName>Xelim queniano</displayName>
				<displayName count="one">Xelim queniano</displayName>
				<displayName count="other">Xelins quenianos</displayName>
			</currency>
			<currency type="KGS">
				<displayName>Som do Quirguistão</displayName>
				<displayName count="one">Som do Quirguistão</displayName>
				<displayName count="other">Soms do Quirguistão</displayName>
			</currency>
			<currency type="KHR">
				<displayName>Riel cambojano</displayName>
				<displayName count="one">Riel cambojano</displayName>
				<displayName count="other">Rieles cambojanos</displayName>
			</currency>
			<currency type="KMF">
				<displayName>Franco comoriano</displayName>
				<displayName count="one">Franco comoriano</displayName>
				<displayName count="other">Francos comorianos</displayName>
			</currency>
			<currency type="KPW">
				<displayName>Won norte-coreano</displayName>
				<displayName count="one">Won norte-coreano</displayName>
				<displayName count="other">Wons norte-coreanos</displayName>
			</currency>
			<currency type="KRW">
				<displayName>Won sul-coreano</displayName>
				<displayName count="one">Won sul-coreano</displayName>
				<displayName count="other">Wons sul-coreanos</displayName>
				<symbol>₩</symbol>
			</currency>
			<currency type="KWD">
				<displayName>Dinar kuwaitiano</displayName>
				<displayName count="one">Dinar kuwaitiano</displayName>
				<displayName count="other">Dinares kuwaitianos</displayName>
			</currency>
			<currency type="KYD">
				<displayName>Dólar das Ilhas Caimão</displayName>
				<displayName count="one">Dólar das Ilhas Caimão</displayName>
				<displayName count="other">Dólares das Ilhas Caimão</displayName>
			</currency>
			<currency type="KZT">
				<displayName>Tenge do Cazaquistão</displayName>
				<displayName count="one">Tenge do Cazaquistão</displayName>
				<displayName count="other">Tenges do Cazaquistão</displayName>
			</currency>
			<currency type="LAK">
				<displayName>Kip de Laos</displayName>
				<displayName count="one">Kip de Laos</displayName>
				<displayName count="other">Kips de Laos</displayName>
			</currency>
			<currency type="LBP">
				<displayName>Libra libanesa</displayName>
				<displayName count="one">Libra libanesa</displayName>
				<displayName count="other">Libras libanesas</displayName>
			</currency>
			<currency type="LKR">
				<displayName>Rupia do Sri Lanka</displayName>
				<displayName count="one">Rupia do Sri Lanka</displayName>
				<displayName count="other">Rupias do Sri Lanka</displayName>
			</currency>
			<currency type="LRD">
				<displayName>Dólar liberiano</displayName>
				<displayName count="one">Dólar liberiano</displayName>
				<displayName count="other">Dólares liberianos</displayName>
			</currency>
			<currency type="LTL">
				<displayName>Litas da Lituânia</displayName>
				<displayName count="one">Litas da Lituânia</displayName>
				<displayName count="other">Litas da Lituânia</displayName>
			</currency>
			<currency type="LVL">
				<displayName>Lats da Letónia</displayName>
				<displayName count="one">Lats da Letónia</displayName>
				<displayName count="other">Lats da Letónia</displayName>
			</currency>
			<currency type="LYD">
				<displayName>Dinar líbio</displayName>
				<displayName count="one">Dinar líbio</displayName>
				<displayName count="other">Dinares líbios</displayName>
			</currency>
			<currency type="MAD">
				<displayName>Dirham marroquino</displayName>
				<displayName count="one">Dirham marroquino</displayName>
				<displayName count="other">Dirhams marroquinos</displayName>
			</currency>
			<currency type="MDL">
				<displayName>Leu moldavo</displayName>
				<displayName count="one">Leu moldavo</displayName>
				<displayName count="other">Lei moldavos</displayName>
			</currency>
			<currency type="MGA">
				<displayName>Ariari de Madagáscar</displayName>
				<displayName count="one">Ariari de Madagáscar</displayName>
				<displayName count="other">Ariaris de Madagáscar</displayName>
			</currency>
			<currency type="MKD">
				<displayName>Dinar macedónio</displayName>
				<displayName count="one">Dinar macedónio</displayName>
				<displayName count="other">Dinares macedónios</displayName>
			</currency>
			<currency type="MLF">
				<displayName draft="unconfirmed">Franco do Mali</displayName>
			</currency>
			<currency type="MMK">
				<displayName>Kyat de Mianmar</displayName>
				<displayName count="one">Kyat de Mianmar</displayName>
				<displayName count="other">Kyats de Mianmar</displayName>
			</currency>
			<currency type="MNT">
				<displayName>Tugrik da Mongólia</displayName>
				<displayName count="one">Tugrik da Mongólia</displayName>
				<displayName count="other">Tugriks da Mongólia</displayName>
			</currency>
			<currency type="MOP">
				<displayName>Pataca de Macau</displayName>
				<displayName count="one">Pataca de Macau</displayName>
				<displayName count="other">Patacas de Macau</displayName>
			</currency>
			<currency type="MRO">
				<displayName>Ouguiya da Mauritânia</displayName>
				<displayName count="one">Ouguiya da Mauritânia</displayName>
				<displayName count="other">Ouguiyas da Mauritânia</displayName>
			</currency>
			<currency type="MUR">
				<displayName>Rupia mauriciana</displayName>
				<displayName count="one">Rupia mauriciana</displayName>
				<displayName count="other">Rupias mauricianas</displayName>
			</currency>
			<currency type="MVR">
				<displayName>Rupia das Ilhas Maldivas</displayName>
				<displayName count="one">Rupia das Ilhas Maldivas</displayName>
				<displayName count="other">Rupias das Ilhas Maldivas</displayName>
			</currency>
			<currency type="MWK">
				<displayName>Kwacha do Malawi</displayName>
				<displayName count="one">Kwacha do Malawi</displayName>
				<displayName count="other">Kwachas do Malawi</displayName>
			</currency>
			<currency type="MXN">
				<displayName>Peso mexicano</displayName>
				<displayName count="one">Peso mexicano</displayName>
				<displayName count="other">Pesos mexicanos</displayName>
				<symbol>MX$</symbol>
			</currency>
			<currency type="MXP">
				<displayName>Peso Plata mexicano (1861–1992)</displayName>
			</currency>
			<currency type="MXV">
				<displayName>Unidad de Inversion (UDI) mexicana</displayName>
			</currency>
			<currency type="MYR">
				<displayName>Ringgit malaio</displayName>
				<displayName count="one">Ringgit malaio</displayName>
				<displayName count="other">Ringgits malaios</displayName>
			</currency>
			<currency type="MZN">
				<displayName>Metical de Moçambique</displayName>
				<displayName count="one">Metical de Moçambique</displayName>
				<displayName count="other">Meticales de Moçambique</displayName>
			</currency>
			<currency type="NAD">
				<displayName>Dólar da Namíbia</displayName>
				<displayName count="one">Dólar da Namíbia</displayName>
				<displayName count="other">Dólares da Namíbia</displayName>
			</currency>
			<currency type="NGN">
				<displayName>Naira nigeriana</displayName>
				<displayName count="one">Naira da Nigéria</displayName>
				<displayName count="other">Nairas nigerianas</displayName>
			</currency>
			<currency type="NIC">
				<displayName>Córdoba nicaraguano</displayName>
			</currency>
			<currency type="NIO">
				<displayName>Córdoba Ouro nicaraguano</displayName>
				<displayName count="one">Córdoba de ouro da Nicarágua</displayName>
				<displayName count="other">Córdobas de ouro da Nicarágua</displayName>
			</currency>
			<currency type="NOK">
				<displayName>Coroa norueguesa</displayName>
				<displayName count="one">Coroa norueguesa</displayName>
				<displayName count="other">Coroas norueguesas</displayName>
			</currency>
			<currency type="NPR">
				<displayName>Rupia nepalesa</displayName>
				<displayName count="one">Rupia nepalesa</displayName>
				<displayName count="other">Rupias nepalesas</displayName>
			</currency>
			<currency type="NZD">
				<displayName>Dólar neozelandês</displayName>
				<displayName count="one">Dólar neozelandês</displayName>
				<displayName count="other">Dólares neozelandeses</displayName>
				<symbol>NZ$</symbol>
			</currency>
			<currency type="OMR">
				<displayName>Rial de Omã</displayName>
				<displayName count="one">Rial de Omã</displayName>
				<displayName count="other">Riais de Omã</displayName>
			</currency>
			<currency type="PAB">
				<displayName>Balboa panamenho</displayName>
				<displayName count="one">Balboa panamenho</displayName>
				<displayName count="other">Balboa panamenho</displayName>
			</currency>
			<currency type="PEN">
				<displayName>Novo sol peruano</displayName>
				<displayName count="one">Novo sol peruano</displayName>
				<displayName count="other">Novos soles peruanos</displayName>
			</currency>
			<currency type="PGK">
				<displayName>Kina da Papua-Nova Guiné</displayName>
				<displayName count="one">Kina da Papua-Nova Guiné</displayName>
				<displayName count="other">Kinas da Papua-Nova Guiné</displayName>
			</currency>
			<currency type="PHP">
				<displayName>Peso filipino</displayName>
				<displayName count="one">Peso filipino</displayName>
				<displayName count="other">Pesos filipinos</displayName>
			</currency>
			<currency type="PKR">
				<displayName>Rupia paquistanesa</displayName>
				<displayName count="one">Rupia paquistanesa</displayName>
				<displayName count="other">Rupias paquistanesas</displayName>
			</currency>
			<currency type="PLN">
				<displayName>Zloti polaco</displayName>
				<displayName count="one">Zloti polaco</displayName>
				<displayName count="other">Zlotis polacos</displayName>
			</currency>
			<currency type="PLZ">
				<displayName draft="unconfirmed">Zloti polaco (1950–1995)</displayName>
			</currency>
			<currency type="PTE">
				<pattern>#,##0.00 ¤</pattern>
				<decimal>$</decimal>
				<group>,</group>
			</currency>
			<currency type="PYG">
				<displayName>Guarani paraguaio</displayName>
				<displayName count="one">Guarani paraguaio</displayName>
				<displayName count="other">Guaranis paraguaios</displayName>
			</currency>
			<currency type="QAR">
				<displayName>Rial do Qatar</displayName>
				<displayName count="one">Rial do Qatar</displayName>
				<displayName count="other">Riais do Qatar</displayName>
			</currency>
			<currency type="RON">
				<displayName>Leu romeno</displayName>
				<displayName count="one">Leu romeno</displayName>
				<displayName count="other">Lei romenos</displayName>
			</currency>
			<currency type="RSD">
				<displayName>Dinar sérvio</displayName>
				<displayName count="one">Dinar sérvio</displayName>
				<displayName count="other">Dinares sérvios</displayName>
			</currency>
			<currency type="RUB">
				<displayName>Rublo russo</displayName>
				<displayName count="one">Rublo russo</displayName>
				<displayName count="other">Rublos russos</displayName>
			</currency>
			<currency type="RWF">
				<displayName>Franco ruandês</displayName>
				<displayName count="one">Franco ruandês</displayName>
				<displayName count="other">Francos ruandeses</displayName>
			</currency>
			<currency type="SAR">
				<displayName>Rial saudita</displayName>
				<displayName count="one">Rial saudita</displayName>
				<displayName count="other">Riais sauditas</displayName>
			</currency>
			<currency type="SBD">
				<displayName>Dólar das Ilhas Salomão</displayName>
				<displayName count="one">Dólar das Ilhas Salomão</displayName>
				<displayName count="other">Dólares das Ilhas Salomão</displayName>
			</currency>
			<currency type="SCR">
				<displayName>Rupia seichelense</displayName>
				<displayName count="one">Rupia seichelense</displayName>
				<displayName count="other">Rupias seichelenses</displayName>
			</currency>
			<currency type="SDG">
				<displayName>Libra sudanesa</displayName>
				<displayName count="one">Libra sudanesa</displayName>
				<displayName count="other">Libras sudanesas</displayName>
			</currency>
			<currency type="SEK">
				<displayName>Coroa sueca</displayName>
				<displayName count="one">Coroa sueca</displayName>
				<displayName count="other">Coroas suecas</displayName>
			</currency>
			<currency type="SGD">
				<displayName>Dólar de Singapura</displayName>
				<displayName count="one">Dólar de Singapura</displayName>
				<displayName count="other">Dólares de Singapura</displayName>
			</currency>
			<currency type="SHP">
				<displayName>Libra de Santa Helena</displayName>
				<displayName count="one">Libra de Santa Helena</displayName>
				<displayName count="other">Libras de Santa Helena</displayName>
			</currency>
			<currency type="SLL">
				<displayName>Leone de Serra Leoa</displayName>
				<displayName count="one">Leone de Serra Leoa</displayName>
				<displayName count="other">Leones de Serra Leoa</displayName>
			</currency>
			<currency type="SOS">
				<displayName>Xelim somali</displayName>
				<displayName count="one">Xelim somali</displayName>
				<displayName count="other">Xelins somalis</displayName>
			</currency>
			<currency type="SRD">
				<displayName>Dólar do Suriname</displayName>
				<displayName count="one">Dólar do Suriname</displayName>
				<displayName count="other">Dólares do Suriname</displayName>
			</currency>
			<currency type="SSP">
				<displayName>Libra sul-sudanesa</displayName>
				<displayName count="one">Libra sul-sudanesa</displayName>
				<displayName count="other">Libras sul-sudanesas</displayName>
			</currency>
			<currency type="STD">
				<displayName>Dobra de São Tomé e Príncipe</displayName>
				<displayName count="one">Dobra de São Tomé e Príncipe</displayName>
				<displayName count="other">Dobras de São Tomé e Príncipe</displayName>
			</currency>
			<currency type="SYP">
				<displayName>Libra síria</displayName>
				<displayName count="one">Libra síria</displayName>
				<displayName count="other">Libras sírias</displayName>
			</currency>
			<currency type="SZL">
				<displayName>Lilangeni da Suazilândia</displayName>
				<displayName count="one">Lilangeni da Suazilândia</displayName>
				<displayName count="other">Lilangenis da Suazilândia</displayName>
			</currency>
			<currency type="THB">
				<displayName>Baht da Tailândia</displayName>
				<displayName count="one">Baht da Tailândia</displayName>
				<displayName count="other">Bahts da Tailândia</displayName>
				<symbol>฿</symbol>
			</currency>
			<currency type="TJS">
				<displayName>Somoni do Tajaquistão</displayName>
				<displayName count="one">Somoni do Tajaquistão</displayName>
				<displayName count="other">Somonis do Tajaquistão</displayName>
			</currency>
			<currency type="TMT">
				<displayName>Manat do Turcomenistão</displayName>
				<displayName count="one">Manat do Turcomenistão</displayName>
				<displayName count="other">Manats do Turcomenistão</displayName>
			</currency>
			<currency type="TND">
				<displayName>Dinar tunisino</displayName>
				<displayName count="one">Dinar tunisino</displayName>
				<displayName count="other">Dinares tunisinos</displayName>
			</currency>
			<currency type="TOP">
				<displayName>Paʻanga de Tonga</displayName>
				<displayName count="one">Paʻanga de Tonga</displayName>
				<displayName count="other">Paʻangas de Tonga</displayName>
			</currency>
			<currency type="TRY">
				<displayName>Lira turca</displayName>
				<displayName count="one">Lira turca</displayName>
				<displayName count="other">Liras turcas</displayName>
			</currency>
			<currency type="TTD">
				<displayName>Dólar de Trindade e Tobago</displayName>
				<displayName count="one">Dólar de Trindade e Tobago</displayName>
				<displayName count="other">Dólares de Trindade e Tobago</displayName>
			</currency>
			<currency type="TWD">
				<displayName>Novo dólar taiwanês</displayName>
				<displayName count="one">Novo dólar taiwanês</displayName>
				<displayName count="other">Novos dólares taiwaneses</displayName>
				<symbol>NT$</symbol>
			</currency>
			<currency type="TZS">
				<displayName>Xelim tanzaniano</displayName>
				<displayName count="one">Xelim tanzaniano</displayName>
				<displayName count="other">Xelins tanzanianos</displayName>
			</currency>
			<currency type="UAH">
				<displayName>Hryvnia da Ucrânia</displayName>
				<displayName count="one">Hryvnia da Ucrânia</displayName>
				<displayName count="other">Hryvnias da Ucrânia</displayName>
			</currency>
			<currency type="UGX">
				<displayName>Xelim ugandense</displayName>
				<displayName count="one">Xelim ugandense</displayName>
				<displayName count="other">Xelins ugandenses</displayName>
			</currency>
			<currency type="USD">
				<displayName>Dólar dos Estados Unidos</displayName>
				<displayName count="one">Dólar dos Estados Unidos</displayName>
				<displayName count="other">Dólares dos Estados Unidos</displayName>
				<symbol>US$</symbol>
			</currency>
			<currency type="UYU">
				<displayName>Peso uruguaio</displayName>
				<displayName count="one">Peso uruguaio</displayName>
				<displayName count="other">Pesos uruguaios</displayName>
			</currency>
			<currency type="UZS">
				<displayName>Som do Uzbequistão</displayName>
				<displayName count="one">Som do Uzbequistão</displayName>
				<displayName count="other">Sons do Uzbequistão</displayName>
				<symbol>UZS</symbol>
			</currency>
			<currency type="VEF">
				<displayName draft="contributed">Bolívar venezuelano</displayName>
				<displayName count="one" draft="contributed">Bolívar venezuelano</displayName>
				<displayName count="other" draft="contributed">Bolívares venezuelanos</displayName>
			</currency>
			<currency type="VND">
				<displayName>Dong vietnamita</displayName>
				<displayName count="one">Dong vietnamita</displayName>
				<displayName count="other">Dongs vietnamitas</displayName>
				<symbol>₫</symbol>
			</currency>
			<currency type="VUV">
				<displayName>Vatu de Vanuatu</displayName>
				<displayName count="one">Vatu de Vanuatu</displayName>
				<displayName count="other">Vatus de Vanuatu</displayName>
			</currency>
			<currency type="WST">
				<displayName>Tala samoano</displayName>
				<displayName count="one">Tala samoano</displayName>
				<displayName count="other">Talas samoanos</displayName>
			</currency>
			<currency type="XAF">
				<displayName>Franco CFA de BEAC</displayName>
				<displayName count="one">Franco CFA de BEAC</displayName>
				<displayName count="other">Francos CFA de BEAC</displayName>
				<symbol>FCFA</symbol>
			</currency>
			<currency type="XCD">
				<displayName>Dólar das Caraíbas Orientais</displayName>
				<displayName count="one">Dólar das Caraíbas Orientais</displayName>
				<displayName count="other">Dólares das Caraíbas Orientais</displayName>
				<symbol>EC$</symbol>
			</currency>
			<currency type="XDR">
				<displayName count="one" draft="provisional">direito especial de saque</displayName>
				<displayName count="other" draft="provisional">direitos especiais de saque</displayName>
			</currency>
			<currency type="XEU">
				<displayName draft="unconfirmed">Unidade da Moeda Europeia</displayName>
			</currency>
			<currency type="XOF">
				<displayName>Franco CFA de BCEAO</displayName>
				<displayName count="one">Franco CFA de BCEAO</displayName>
				<displayName count="other">Francos CFA de BCEAO</displayName>
				<symbol>CFA</symbol>
			</currency>
			<currency type="XPF">
				<displayName>Franco CFP</displayName>
				<displayName count="one">Franco CFP</displayName>
				<displayName count="other">Francos CFP</displayName>
				<symbol>CFPF</symbol>
			</currency>
			<currency type="XXX">
				<displayName>Moeda desconhecida</displayName>
				<displayName count="one">(Moeda desconhecida)</displayName>
				<displayName count="other">(Moeda desconhecida)</displayName>
			</currency>
			<currency type="YER">
				<displayName>Rial iemenita</displayName>
				<displayName count="one">Rial iemenita</displayName>
				<displayName count="other">Riais iemenitas</displayName>
			</currency>
			<currency type="YUD">
				<displayName draft="unconfirmed">Dinar forte jugoslavo</displayName>
			</currency>
			<currency type="YUM">
				<displayName draft="unconfirmed">Super Dinar jugoslavo</displayName>
			</currency>
			<currency type="YUN">
				<displayName draft="unconfirmed">Dinar conversível jugoslavo</displayName>
			</currency>
			<currency type="ZAR">
				<displayName>Rand sul-africano</displayName>
				<displayName count="one">Rand sul-africano</displayName>
				<displayName count="other">Rands sul-africanos</displayName>
			</currency>
			<currency type="ZMK">
				<displayName>Kwacha zambiano (1968–2012)</displayName>
				<displayName count="one">Kwacha zambiano (1968–2012)</displayName>
				<displayName count="other">Kwachas zambianos (1968–2012)</displayName>
			</currency>
			<currency type="ZMW">
				<displayName>Kwacha zambiano</displayName>
				<displayName count="one">Kwacha zambiano</displayName>
				<displayName count="other">Kwachas zambianos</displayName>
			</currency>
			<currency type="ZWD">
				<displayName draft="unconfirmed">Dólar do Zimbabwe</displayName>
			</currency>
		</currencies>
		<miscPatterns numberSystem="latn">
			<pattern type="atLeast">+{0}</pattern>
			<pattern type="range">{0} - {1}</pattern>
		</miscPatterns>
	</numbers>
	<units>
		<unitLength type="long">
			<compoundUnit type="per">
				<compoundUnitPattern>{0} por {1}</compoundUnitPattern>
			</compoundUnit>
			<unit type="acceleration-g-force">
				<unitPattern count="one">{0} força G</unitPattern>
				<unitPattern count="other">{0} força G</unitPattern>
			</unit>
			<unit type="angle-arc-minute">
				<unitPattern count="one">{0} minuto</unitPattern>
				<unitPattern count="other">{0} minutos</unitPattern>
			</unit>
			<unit type="angle-arc-second">
				<unitPattern count="one">{0} segundo</unitPattern>
				<unitPattern count="other">{0} segundos</unitPattern>
			</unit>
			<unit type="angle-degree">
				<unitPattern count="one">{0} grau</unitPattern>
				<unitPattern count="other">{0} graus</unitPattern>
			</unit>
			<unit type="area-acre">
				<unitPattern count="one">{0} acre</unitPattern>
				<unitPattern count="other">{0} acres</unitPattern>
			</unit>
			<unit type="area-hectare">
				<unitPattern count="one">{0} hectare</unitPattern>
				<unitPattern count="other">{0} hectares</unitPattern>
			</unit>
			<unit type="area-square-foot">
				<unitPattern count="one">{0} pé quadrado</unitPattern>
				<unitPattern count="other">{0} pés quadrados</unitPattern>
			</unit>
			<unit type="area-square-kilometer">
				<unitPattern count="one">{0} quilómetro quadrado</unitPattern>
				<unitPattern count="other">{0} quilómetros quadrados</unitPattern>
			</unit>
			<unit type="area-square-meter">
				<unitPattern count="one">{0} metro quadrado</unitPattern>
				<unitPattern count="other">{0} metros quadrados</unitPattern>
			</unit>
			<unit type="area-square-mile">
				<unitPattern count="one">{0} milha quadrada</unitPattern>
				<unitPattern count="other">{0} milhas quadradas</unitPattern>
			</unit>
			<unit type="duration-day">
				<unitPattern count="one">{0} dia</unitPattern>
				<unitPattern count="other">{0} dias</unitPattern>
			</unit>
			<unit type="duration-hour">
				<unitPattern count="one">{0} hora</unitPattern>
				<unitPattern count="other">{0} horas</unitPattern>
			</unit>
			<unit type="duration-millisecond">
				<unitPattern count="one">{0} milissegundo</unitPattern>
				<unitPattern count="other">{0} milissegundos</unitPattern>
			</unit>
			<unit type="duration-minute">
				<unitPattern count="one">{0} minuto</unitPattern>
				<unitPattern count="other">{0} minutos</unitPattern>
			</unit>
			<unit type="duration-month">
				<unitPattern count="one">{0} mês</unitPattern>
				<unitPattern count="other">{0} meses</unitPattern>
			</unit>
			<unit type="duration-second">
				<unitPattern count="one">{0} segundo</unitPattern>
				<unitPattern count="other">{0} segundos</unitPattern>
			</unit>
			<unit type="duration-week">
				<unitPattern count="one">{0} semana</unitPattern>
				<unitPattern count="other">{0} semanas</unitPattern>
			</unit>
			<unit type="duration-year">
				<unitPattern count="one">{0} ano</unitPattern>
				<unitPattern count="other">{0} anos</unitPattern>
			</unit>
			<unit type="length-centimeter">
				<unitPattern count="one">{0} centímetro</unitPattern>
				<unitPattern count="other">{0} centímetros</unitPattern>
			</unit>
			<unit type="length-foot">
				<unitPattern count="one">{0} pé</unitPattern>
				<unitPattern count="other">{0} pés</unitPattern>
			</unit>
			<unit type="length-inch">
				<unitPattern count="one">{0} polegada</unitPattern>
				<unitPattern count="other">{0} polegadas</unitPattern>
			</unit>
			<unit type="length-kilometer">
				<unitPattern count="one">{0} quilómetro</unitPattern>
				<unitPattern count="other">{0} quilómetros</unitPattern>
			</unit>
			<unit type="length-light-year">
				<unitPattern count="one">{0} ano-luz</unitPattern>
				<unitPattern count="other">{0} anos-luz</unitPattern>
			</unit>
			<unit type="length-meter">
				<unitPattern count="one">{0} metro</unitPattern>
				<unitPattern count="other">{0} metros</unitPattern>
			</unit>
			<unit type="length-mile">
				<unitPattern count="one">{0} milha</unitPattern>
				<unitPattern count="other">{0} milhas</unitPattern>
			</unit>
			<unit type="length-millimeter">
				<unitPattern count="one">{0} milímetro</unitPattern>
				<unitPattern count="other">{0} milímetros</unitPattern>
			</unit>
			<unit type="length-picometer">
				<unitPattern count="one">{0} picómetro</unitPattern>
				<unitPattern count="other">{0} picómetros</unitPattern>
			</unit>
			<unit type="length-yard">
				<unitPattern count="one">{0} jarda</unitPattern>
				<unitPattern count="other">{0} jardas</unitPattern>
			</unit>
			<unit type="mass-gram">
				<unitPattern count="one">{0} grama</unitPattern>
				<unitPattern count="other">{0} gramas</unitPattern>
			</unit>
			<unit type="mass-kilogram">
				<unitPattern count="one">{0} quilograma</unitPattern>
				<unitPattern count="other">{0} quilogramas</unitPattern>
			</unit>
			<unit type="mass-ounce">
				<unitPattern count="one">{0} onça</unitPattern>
				<unitPattern count="other">{0} onças</unitPattern>
			</unit>
			<unit type="mass-pound">
				<unitPattern count="one">{0} libra</unitPattern>
				<unitPattern count="other">{0} libras</unitPattern>
			</unit>
			<unit type="power-horsepower">
				<unitPattern count="one">{0} cavalo-vapor</unitPattern>
				<unitPattern count="other">{0} cavalos-vapor</unitPattern>
			</unit>
			<unit type="power-kilowatt">
				<unitPattern count="one">{0} quilowatt</unitPattern>
				<unitPattern count="other">{0} quilowatts</unitPattern>
			</unit>
			<unit type="power-watt">
				<unitPattern count="one">{0} watt</unitPattern>
				<unitPattern count="other">{0} watts</unitPattern>
			</unit>
			<unit type="pressure-hectopascal">
				<unitPattern count="one">{0} hectopascal</unitPattern>
				<unitPattern count="other">{0} hectopascals</unitPattern>
			</unit>
			<unit type="pressure-inch-hg">
				<unitPattern count="one">{0} polegada de mercúrio</unitPattern>
				<unitPattern count="other">{0} polegadas de mercúrio</unitPattern>
			</unit>
			<unit type="pressure-millibar">
				<unitPattern count="one">{0} milibar</unitPattern>
				<unitPattern count="other">{0} milibares</unitPattern>
			</unit>
			<unit type="speed-kilometer-per-hour">
				<unitPattern count="one">{0} quilómetro por hora</unitPattern>
				<unitPattern count="other">{0} quilómetros por hora</unitPattern>
			</unit>
			<unit type="speed-meter-per-second">
				<unitPattern count="one">{0} metro por segundo</unitPattern>
				<unitPattern count="other">{0} metros por segundo</unitPattern>
			</unit>
			<unit type="speed-mile-per-hour">
				<unitPattern count="one">{0} milha por hora</unitPattern>
				<unitPattern count="other">{0} milhas por hora</unitPattern>
			</unit>
			<unit type="temperature-celsius">
				<unitPattern count="one">{0} grau Celsius</unitPattern>
				<unitPattern count="other">{0} graus Celsius</unitPattern>
			</unit>
			<unit type="temperature-fahrenheit">
				<unitPattern count="one">{0} grau Fahrenheit</unitPattern>
				<unitPattern count="other">{0} graus Fahrenheit</unitPattern>
			</unit>
			<unit type="volume-cubic-kilometer">
				<unitPattern count="one">{0} quilómetro cúbico</unitPattern>
				<unitPattern count="other">{0} quilómetros cúbicos</unitPattern>
			</unit>
			<unit type="volume-cubic-mile">
				<unitPattern count="one">{0} milha cúbica</unitPattern>
				<unitPattern count="other">{0} milhas cúbicas</unitPattern>
			</unit>
			<unit type="volume-liter">
				<unitPattern count="one">{0} litro</unitPattern>
				<unitPattern count="other">{0} litros</unitPattern>
			</unit>
		</unitLength>
		<unitLength type="short">
			<compoundUnit type="per">
				<compoundUnitPattern>{0}/{1}</compoundUnitPattern>
			</compoundUnit>
			<unit type="acceleration-g-force">
				<unitPattern count="one">{0} G</unitPattern>
				<unitPattern count="other">{0} G</unitPattern>
			</unit>
			<unit type="angle-arc-minute">
				<unitPattern count="one">{0} min</unitPattern>
				<unitPattern count="other">{0} min</unitPattern>
			</unit>
			<unit type="angle-arc-second">
				<unitPattern count="one">{0} s</unitPattern>
				<unitPattern count="other">{0} s</unitPattern>
			</unit>
			<unit type="angle-degree">
				<unitPattern count="one">{0}°</unitPattern>
				<unitPattern count="other">{0}°</unitPattern>
			</unit>
			<unit type="area-acre">
				<unitPattern count="one">{0} acre</unitPattern>
				<unitPattern count="other">{0} acres</unitPattern>
			</unit>
			<unit type="area-hectare">
				<unitPattern count="one">{0} ha</unitPattern>
				<unitPattern count="other">{0} ha</unitPattern>
			</unit>
			<unit type="area-square-foot">
				<unitPattern count="one">{0} ft²</unitPattern>
				<unitPattern count="other">{0} ft²</unitPattern>
			</unit>
			<unit type="area-square-kilometer">
				<unitPattern count="one">{0} km²</unitPattern>
				<unitPattern count="other">{0} km²</unitPattern>
			</unit>
			<unit type="area-square-meter">
				<unitPattern count="one">{0} m²</unitPattern>
				<unitPattern count="other">{0} m²</unitPattern>
			</unit>
			<unit type="area-square-mile">
				<unitPattern count="one">{0} mi²</unitPattern>
				<unitPattern count="other">{0} mi²</unitPattern>
			</unit>
			<unit type="duration-day">
				<unitPattern count="one">{0} dia</unitPattern>
				<unitPattern count="other">{0} dias</unitPattern>
			</unit>
			<unit type="duration-hour">
				<unitPattern count="one">{0} h</unitPattern>
				<unitPattern count="other">{0} h</unitPattern>
			</unit>
			<unit type="duration-millisecond">
				<unitPattern count="one">{0} ms</unitPattern>
				<unitPattern count="other">{0} ms</unitPattern>
			</unit>
			<unit type="duration-minute">
				<unitPattern count="one">{0} min</unitPattern>
				<unitPattern count="other">{0} min</unitPattern>
			</unit>
			<unit type="duration-month">
				<unitPattern count="one">{0} mês</unitPattern>
				<unitPattern count="other">{0} meses</unitPattern>
			</unit>
			<unit type="duration-second">
				<unitPattern count="one">{0} s</unitPattern>
				<unitPattern count="other">{0} s</unitPattern>
			</unit>
			<unit type="duration-week">
				<unitPattern count="one">{0} sem.</unitPattern>
				<unitPattern count="other">{0} sem.</unitPattern>
			</unit>
			<unit type="duration-year">
				<unitPattern count="one">{0} ano</unitPattern>
				<unitPattern count="other">{0} anos</unitPattern>
			</unit>
			<unit type="length-centimeter">
				<unitPattern count="one">{0} cm</unitPattern>
				<unitPattern count="other">{0} cm</unitPattern>
			</unit>
			<unit type="length-foot">
				<unitPattern count="one">{0} pé</unitPattern>
				<unitPattern count="other">{0} pés</unitPattern>
			</unit>
			<unit type="length-inch">
				<unitPattern count="one">{0} pol.</unitPattern>
				<unitPattern count="other">{0} pol.</unitPattern>
			</unit>
			<unit type="length-kilometer">
				<unitPattern count="one">{0} km</unitPattern>
				<unitPattern count="other">{0} km</unitPattern>
			</unit>
			<unit type="length-light-year">
				<unitPattern count="one">{0} ano-luz</unitPattern>
				<unitPattern count="other">{0} anos-luz</unitPattern>
			</unit>
			<unit type="length-meter">
				<unitPattern count="one">{0} m</unitPattern>
				<unitPattern count="other">{0} m</unitPattern>
			</unit>
			<unit type="length-mile">
				<unitPattern count="one">{0} milha</unitPattern>
				<unitPattern count="other">{0} milhas</unitPattern>
			</unit>
			<unit type="length-millimeter">
				<unitPattern count="one">{0} mm</unitPattern>
				<unitPattern count="other">{0} mm</unitPattern>
			</unit>
			<unit type="length-picometer">
				<unitPattern count="one">{0} pm</unitPattern>
				<unitPattern count="other">{0} pm</unitPattern>
			</unit>
			<unit type="length-yard">
				<unitPattern count="one">{0} yd</unitPattern>
				<unitPattern count="other">{0} yd</unitPattern>
			</unit>
			<unit type="mass-gram">
				<unitPattern count="one">{0} g</unitPattern>
				<unitPattern count="other">{0} g</unitPattern>
			</unit>
			<unit type="mass-kilogram">
				<unitPattern count="one">{0} kg</unitPattern>
				<unitPattern count="other">{0} kg</unitPattern>
			</unit>
			<unit type="mass-ounce">
				<unitPattern count="one">{0} oz</unitPattern>
				<unitPattern count="other">{0} oz</unitPattern>
			</unit>
			<unit type="mass-pound">
				<unitPattern count="one">{0} lb</unitPattern>
				<unitPattern count="other">{0} lb</unitPattern>
			</unit>
			<unit type="power-horsepower">
				<unitPattern count="one">{0} cv</unitPattern>
				<unitPattern count="other">{0} cv</unitPattern>
			</unit>
			<unit type="power-kilowatt">
				<unitPattern count="one">{0} kW</unitPattern>
				<unitPattern count="other">{0} kW</unitPattern>
			</unit>
			<unit type="power-watt">
				<unitPattern count="one">{0} W</unitPattern>
				<unitPattern count="other">{0} W</unitPattern>
			</unit>
			<unit type="pressure-hectopascal">
				<unitPattern count="one">{0} hPa</unitPattern>
				<unitPattern count="other">{0} hPa</unitPattern>
			</unit>
			<unit type="pressure-inch-hg">
				<unitPattern count="one">{0} inHg</unitPattern>
				<unitPattern count="other">{0} inHg</unitPattern>
			</unit>
			<unit type="pressure-millibar">
				<unitPattern count="one">{0} mb</unitPattern>
				<unitPattern count="other">{0} mb</unitPattern>
			</unit>
			<unit type="speed-kilometer-per-hour">
				<unitPattern count="one">{0} km/h</unitPattern>
				<unitPattern count="other">{0} km/h</unitPattern>
			</unit>
			<unit type="speed-meter-per-second">
				<unitPattern count="one">{0} m/s</unitPattern>
				<unitPattern count="other">{0} m/s</unitPattern>
			</unit>
			<unit type="speed-mile-per-hour">
				<unitPattern count="one">{0} mi/h</unitPattern>
				<unitPattern count="other">{0} mi/h</unitPattern>
			</unit>
			<unit type="temperature-celsius">
				<unitPattern count="one">{0}°C</unitPattern>
				<unitPattern count="other">{0}°C</unitPattern>
			</unit>
			<unit type="temperature-fahrenheit">
				<unitPattern count="one">{0}°F</unitPattern>
				<unitPattern count="other">{0}°F</unitPattern>
			</unit>
			<unit type="volume-cubic-kilometer">
				<unitPattern count="one">{0} km³</unitPattern>
				<unitPattern count="other">{0} km³</unitPattern>
			</unit>
			<unit type="volume-cubic-mile">
				<unitPattern count="one">{0} mi³</unitPattern>
				<unitPattern count="other">{0} mi³</unitPattern>
			</unit>
			<unit type="volume-liter">
				<unitPattern count="one">{0} l</unitPattern>
				<unitPattern count="other">{0} l</unitPattern>
			</unit>
		</unitLength>
		<unitLength type="narrow">
			<compoundUnit type="per">
				<compoundUnitPattern>{0}/{1}</compoundUnitPattern>
			</compoundUnit>
			<unit type="acceleration-g-force">
				<unitPattern count="one">{0} G</unitPattern>
				<unitPattern count="other">{0} G</unitPattern>
			</unit>
			<unit type="angle-arc-minute">
				<unitPattern count="one">{0}'</unitPattern>
				<unitPattern count="other">{0}'</unitPattern>
			</unit>
			<unit type="angle-arc-second">
				<unitPattern count="one">{0}&quot;</unitPattern>
				<unitPattern count="other">{0}&quot;</unitPattern>
			</unit>
			<unit type="angle-degree">
				<unitPattern count="one">{0}°</unitPattern>
				<unitPattern count="other">{0}°</unitPattern>
			</unit>
			<unit type="area-acre">
				<unitPattern count="one">{0} acre</unitPattern>
				<unitPattern count="other">{0} acres</unitPattern>
			</unit>
			<unit type="area-hectare">
				<unitPattern count="one">{0} ha</unitPattern>
				<unitPattern count="other">{0} ha</unitPattern>
			</unit>
			<unit type="area-square-foot">
				<unitPattern count="one">{0} ft²</unitPattern>
				<unitPattern count="other">{0} ft²</unitPattern>
			</unit>
			<unit type="area-square-kilometer">
				<unitPattern count="one">{0} km²</unitPattern>
				<unitPattern count="other">{0} km²</unitPattern>
			</unit>
			<unit type="area-square-meter">
				<unitPattern count="one">{0} m²</unitPattern>
				<unitPattern count="other">{0} m²</unitPattern>
			</unit>
			<unit type="area-square-mile">
				<unitPattern count="one">{0} mi²</unitPattern>
				<unitPattern count="other">{0} mi²</unitPattern>
			</unit>
			<unit type="duration-day">
				<unitPattern count="one">{0} dia</unitPattern>
				<unitPattern count="other">{0} dias</unitPattern>
			</unit>
			<unit type="duration-hour">
				<unitPattern count="one">{0} h</unitPattern>
				<unitPattern count="other">{0} h</unitPattern>
			</unit>
			<unit type="duration-millisecond">
				<unitPattern count="one">{0} ms</unitPattern>
				<unitPattern count="other">{0} ms</unitPattern>
			</unit>
			<unit type="duration-minute">
				<unitPattern count="one">{0} min</unitPattern>
				<unitPattern count="other">{0} min</unitPattern>
			</unit>
			<unit type="duration-month">
				<unitPattern count="one">{0} mês</unitPattern>
				<unitPattern count="other">{0} meses</unitPattern>
			</unit>
			<unit type="duration-second">
				<unitPattern count="one">{0} s</unitPattern>
				<unitPattern count="other">{0} s</unitPattern>
			</unit>
			<unit type="duration-week">
				<unitPattern count="one">{0} sem.</unitPattern>
				<unitPattern count="other">{0} sem.</unitPattern>
			</unit>
			<unit type="duration-year">
				<unitPattern count="one">{0} ano</unitPattern>
				<unitPattern count="other">{0} anos</unitPattern>
			</unit>
			<unit type="length-centimeter">
				<unitPattern count="one">{0} cm</unitPattern>
				<unitPattern count="other">{0} cm</unitPattern>
			</unit>
			<unit type="length-foot">
				<unitPattern count="one">{0}′</unitPattern>
				<unitPattern count="other">{0}′</unitPattern>
			</unit>
			<unit type="length-inch">
				<unitPattern count="one">{0}″</unitPattern>
				<unitPattern count="other">{0}″</unitPattern>
			</unit>
			<unit type="length-kilometer">
				<unitPattern count="one">{0} km</unitPattern>
				<unitPattern count="other">{0} km</unitPattern>
			</unit>
			<unit type="length-light-year">
				<unitPattern count="one">{0} ano-luz</unitPattern>
				<unitPattern count="other">{0} anos-luz</unitPattern>
			</unit>
			<unit type="length-meter">
				<unitPattern count="one">{0} m</unitPattern>
				<unitPattern count="other">{0} m</unitPattern>
			</unit>
			<unit type="length-mile">
				<unitPattern count="one">{0} milha</unitPattern>
				<unitPattern count="other">{0} milhas</unitPattern>
			</unit>
			<unit type="length-millimeter">
				<unitPattern count="one">{0} mm</unitPattern>
				<unitPattern count="other">{0} mm</unitPattern>
			</unit>
			<unit type="length-picometer">
				<unitPattern count="one">{0} pm</unitPattern>
				<unitPattern count="other">{0} pm</unitPattern>
			</unit>
			<unit type="length-yard">
				<unitPattern count="one">{0} yd</unitPattern>
				<unitPattern count="other">{0} yd</unitPattern>
			</unit>
			<unit type="mass-gram">
				<unitPattern count="one">{0} g</unitPattern>
				<unitPattern count="other">{0} g</unitPattern>
			</unit>
			<unit type="mass-kilogram">
				<unitPattern count="one">{0} kg</unitPattern>
				<unitPattern count="other">{0} kg</unitPattern>
			</unit>
			<unit type="mass-ounce">
				<unitPattern count="one">{0} oz</unitPattern>
				<unitPattern count="other">{0} oz</unitPattern>
			</unit>
			<unit type="mass-pound">
				<unitPattern count="one">{0} lb</unitPattern>
				<unitPattern count="other">{0} lb</unitPattern>
			</unit>
			<unit type="power-horsepower">
				<unitPattern count="one">{0} cv</unitPattern>
				<unitPattern count="other">{0} cv</unitPattern>
			</unit>
			<unit type="power-kilowatt">
				<unitPattern count="one">{0} kW</unitPattern>
				<unitPattern count="other">{0} kW</unitPattern>
			</unit>
			<unit type="power-watt">
				<unitPattern count="one">{0} W</unitPattern>
				<unitPattern count="other">{0} W</unitPattern>
			</unit>
			<unit type="pressure-hectopascal">
				<unitPattern count="one">{0} hPa</unitPattern>
				<unitPattern count="other">{0} hPa</unitPattern>
			</unit>
			<unit type="pressure-inch-hg">
				<unitPattern count="one">{0}&quot; Hg</unitPattern>
				<unitPattern count="other">{0}&quot; Hg</unitPattern>
			</unit>
			<unit type="pressure-millibar">
				<unitPattern count="one">{0} mb</unitPattern>
				<unitPattern count="other">{0} mb</unitPattern>
			</unit>
			<unit type="speed-kilometer-per-hour">
				<unitPattern count="one">{0} km/h</unitPattern>
				<unitPattern count="other">{0} km/h</unitPattern>
			</unit>
			<unit type="speed-meter-per-second">
				<unitPattern count="one">{0} m/s</unitPattern>
				<unitPattern count="other">{0} m/s</unitPattern>
			</unit>
			<unit type="speed-mile-per-hour">
				<unitPattern count="one">{0} mi/h</unitPattern>
				<unitPattern count="other">{0} mi/h</unitPattern>
			</unit>
			<unit type="temperature-celsius">
				<unitPattern count="one">{0} °</unitPattern>
				<unitPattern count="other">{0} °</unitPattern>
			</unit>
			<unit type="temperature-fahrenheit">
				<unitPattern count="one">{0} °F</unitPattern>
				<unitPattern count="other">{0} °F</unitPattern>
			</unit>
			<unit type="volume-cubic-kilometer">
				<unitPattern count="one">{0} km³</unitPattern>
				<unitPattern count="other">{0} km³</unitPattern>
			</unit>
			<unit type="volume-cubic-mile">
				<unitPattern count="one">{0} mi³</unitPattern>
				<unitPattern count="other">{0} mi³</unitPattern>
			</unit>
			<unit type="volume-liter">
				<unitPattern count="one">{0} l</unitPattern>
				<unitPattern count="other">{0} l</unitPattern>
			</unit>
		</unitLength>
		<durationUnit type="hm">
			<durationUnitPattern>h:mm</durationUnitPattern>
		</durationUnit>
		<durationUnit type="hms">
			<durationUnitPattern>h:mm:ss</durationUnitPattern>
		</durationUnit>
		<durationUnit type="ms">
			<durationUnitPattern>m:ss</durationUnitPattern>
		</durationUnit>
	</units>
	<listPatterns>
		<listPattern>
			<listPatternPart type="start">{0}, {1}</listPatternPart>
			<listPatternPart type="middle">{0}, {1}</listPatternPart>
			<listPatternPart type="end">{0} e {1}</listPatternPart>
			<listPatternPart type="2">{0} e {1}</listPatternPart>
		</listPattern>
		<listPattern type="unit">
			<listPatternPart type="start">{0}, {1}</listPatternPart>
			<listPatternPart type="middle">{0}, {1}</listPatternPart>
			<listPatternPart type="end">{0} e {1}</listPatternPart>
			<listPatternPart type="2">{0} e {1}</listPatternPart>
		</listPattern>
		<listPattern type="unit-narrow">
			<listPatternPart type="start" draft="contributed">{0}, {1}</listPatternPart>
			<listPatternPart type="middle" draft="contributed">{0}, {1}</listPatternPart>
			<listPatternPart type="end" draft="contributed">{0} e {1}</listPatternPart>
			<listPatternPart type="2" draft="contributed">{0} e {1}</listPatternPart>
		</listPattern>
		<listPattern type="unit-short">
			<listPatternPart type="start">{0}, {1}</listPatternPart>
			<listPatternPart type="middle">{0}, {1}</listPatternPart>
			<listPatternPart type="end">{0} e {1}</listPatternPart>
			<listPatternPart type="2">{0} e {1}</listPatternPart>
		</listPattern>
	</listPatterns>
	<posix>
		<messages>
			<yesstr>sim:s</yesstr>
			<nostr>não:n</nostr>
		</messages>
	</posix>
</ldml>
<!-- Comments without bases
per cldrbug 2835, added from gregorian and adjusted for G	 - was on: //ldml/dates/calendars/calendar[@type="roc"]/dateTimeFormats/availableFormats/dateFormatItem[@id="yQ"]
per
cldrbug 2835, added from gregorian and adjusted for G	 - was on: //ldml/dates/calendars/calendar[@type="japanese"]/dateTimeFormats/availableFormats/dateFormatItem[@id="yQ"]
per
cldrbug 2835, added from gregorian and adjusted for G	 - was on: //ldml/dates/calendars/calendar[@type="buddhist"]/dateTimeFormats/availableFormats/dateFormatItem[@id="yQ"]
per
cldrbug 2835, added from gregorian and adjusted for G	 - was on: //ldml/dates/calendars/calendar[@type="islamic"]/dateTimeFormats/availableFormats/dateFormatItem[@id="yQ"]
-->

