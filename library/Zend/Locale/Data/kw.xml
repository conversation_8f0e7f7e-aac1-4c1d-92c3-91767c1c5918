<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE ldml SYSTEM "../../common/dtd/ldml.dtd">
<!-- Copyright © 1991-2013 Unicode, Inc.
CLDR data files are interpreted according to the LDML specification (http://unicode.org/reports/tr35/)
For terms of use, see http://www.unicode.org/copyright.html
-->
<ldml>
	<identity>
		<version number="$Revision: 9287 $"/>
		<generation date="$Date: 2013-08-28 21:32:04 -0500 (Wed, 28 Aug 2013) $"/>
		<language type="kw"/>
	</identity>
	<localeDisplayNames>
		<languages>
			<language type="ar" draft="unconfirmed">Arabek</language>
			<language type="cy" draft="unconfirmed">Kembrek</language>
			<language type="da" draft="unconfirmed">Danek</language>
			<language type="de" draft="unconfirmed">Almaynek</language>
			<language type="en" draft="unconfirmed">Sowsnek</language>
			<language type="es" draft="unconfirmed">Spaynek</language>
			<language type="eu" draft="unconfirmed">Baskek</language>
			<language type="fr" draft="unconfirmed">Frenkek</language>
			<language type="ga" draft="unconfirmed">Wordhonek</language>
			<language type="it" draft="unconfirmed">Italek</language>
			<language type="ja" draft="unconfirmed">Japanek</language>
			<language type="kw">kernewek</language>
			<language type="nl" draft="unconfirmed">Iseldiryek</language>
			<language type="pt" draft="unconfirmed">Portyngalek</language>
			<language type="pt_BR" draft="unconfirmed">Portyngalek Brasil</language>
			<language type="ru" draft="unconfirmed">Russek</language>
			<language type="yue" draft="unconfirmed">Kantonek</language>
			<language type="zh" draft="unconfirmed">Chinek</language>
			<language type="zh_Hans" draft="unconfirmed">Chinek sempel</language>
			<language type="zh_Hant" draft="unconfirmed">Chînek Tradycyonal</language>
		</languages>
		<scripts>
			<script type="Arab" draft="unconfirmed">Arabek</script>
		</scripts>
		<territories>
			<territory type="BR" draft="unconfirmed">Brasil</territory>
			<territory type="CN" draft="unconfirmed">China</territory>
			<territory type="DE" draft="unconfirmed">Almayn</territory>
			<territory type="FR" draft="unconfirmed">Pow Frenk</territory>
			<territory type="GB">Rywvaneth Unys</territory>
			<territory type="IN" draft="unconfirmed">Eynda</territory>
			<territory type="IT" draft="unconfirmed">Itali</territory>
			<territory type="JP" draft="unconfirmed">Japan</territory>
			<territory type="RU" draft="unconfirmed">Russi</territory>
			<territory type="US" draft="unconfirmed">Statys Unys</territory>
		</territories>
		<keys>
			<key type="calendar" draft="unconfirmed">kalans</key>
		</keys>
		<types>
			<type type="gregorian" key="calendar" draft="unconfirmed">Kalans gregorek</type>
		</types>
	</localeDisplayNames>
	<characters>
		<exemplarCharacters>[a b c d e f g h i j k l m n o p q r s t u v w x y z]</exemplarCharacters>
		<exemplarCharacters type="index" draft="unconfirmed">[A B C D E F G H I J K L M N O P Q R S T U V W X Y Z]</exemplarCharacters>
	</characters>
	<dates>
		<calendars>
			<calendar type="generic">
				<dateFormats>
					<dateFormatLength type="full">
						<dateFormat>
							<pattern draft="unconfirmed">EEEE d MMMM y G</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="long">
						<dateFormat>
							<pattern draft="unconfirmed">d MMMM y G</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="medium">
						<dateFormat>
							<pattern draft="unconfirmed">d MMM y G</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="short">
						<dateFormat>
							<pattern draft="unconfirmed">dd/MM/y GGGGG</pattern>
						</dateFormat>
					</dateFormatLength>
				</dateFormats>
			</calendar>
			<calendar type="gregorian">
				<months>
					<monthContext type="format">
						<monthWidth type="abbreviated">
							<month type="1">Gen</month>
							<month type="2">Whe</month>
							<month type="3">Mer</month>
							<month type="4">Ebr</month>
							<month type="5">Me</month>
							<month type="6">Efn</month>
							<month type="7">Gor</month>
							<month type="8">Est</month>
							<month type="9">Gwn</month>
							<month type="10">Hed</month>
							<month type="11">Du</month>
							<month type="12">Kev</month>
						</monthWidth>
						<monthWidth type="wide">
							<month type="1">Mys Genver</month>
							<month type="2">Mys Whevrel</month>
							<month type="3">Mys Merth</month>
							<month type="4">Mys Ebrel</month>
							<month type="5">Mys Me</month>
							<month type="6">Mys Efan</month>
							<month type="7">Mys Gortheren</month>
							<month type="8">Mye Est</month>
							<month type="9">Mys Gwyngala</month>
							<month type="10">Mys Hedra</month>
							<month type="11">Mys Du</month>
							<month type="12">Mys Kevardhu</month>
						</monthWidth>
					</monthContext>
				</months>
				<days>
					<dayContext type="format">
						<dayWidth type="abbreviated">
							<day type="sun">Sul</day>
							<day type="mon">Lun</day>
							<day type="tue">Mth</day>
							<day type="wed">Mhr</day>
							<day type="thu">Yow</day>
							<day type="fri">Gwe</day>
							<day type="sat">Sad</day>
						</dayWidth>
						<dayWidth type="wide">
							<day type="sun">De Sul</day>
							<day type="mon">De Lun</day>
							<day type="tue">De Merth</day>
							<day type="wed">De Merher</day>
							<day type="thu">De Yow</day>
							<day type="fri">De Gwener</day>
							<day type="sat">De Sadorn</day>
						</dayWidth>
					</dayContext>
				</days>
				<dayPeriods>
					<dayPeriodContext type="format">
						<dayPeriodWidth type="wide">
							<dayPeriod type="am">a.m.</dayPeriod>
							<dayPeriod type="pm">p.m.</dayPeriod>
						</dayPeriodWidth>
					</dayPeriodContext>
				</dayPeriods>
				<eras>
					<eraAbbr>
						<era type="0">RC</era>
						<era type="1">AD</era>
					</eraAbbr>
				</eras>
				<dateFormats>
					<dateFormatLength type="full">
						<dateFormat>
							<pattern draft="unconfirmed">EEEE d MMMM y</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="long">
						<dateFormat>
							<pattern draft="unconfirmed">d MMMM y</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="medium">
						<dateFormat>
							<pattern draft="unconfirmed">d MMM y</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="short">
						<dateFormat>
							<pattern draft="unconfirmed">dd/MM/y</pattern>
						</dateFormat>
					</dateFormatLength>
				</dateFormats>
				<timeFormats>
					<timeFormatLength type="full">
						<timeFormat>
							<pattern draft="unconfirmed">HH:mm:ss zzzz</pattern>
						</timeFormat>
					</timeFormatLength>
					<timeFormatLength type="long">
						<timeFormat>
							<pattern draft="unconfirmed">HH:mm:ss z</pattern>
						</timeFormat>
					</timeFormatLength>
					<timeFormatLength type="medium">
						<timeFormat>
							<pattern draft="unconfirmed">HH:mm:ss</pattern>
						</timeFormat>
					</timeFormatLength>
					<timeFormatLength type="short">
						<timeFormat>
							<pattern draft="unconfirmed">HH:mm</pattern>
						</timeFormat>
					</timeFormatLength>
				</timeFormats>
			</calendar>
		</calendars>
		<fields>
			<field type="year">
				<displayName draft="unconfirmed">Bledhen</displayName>
			</field>
			<field type="month">
				<displayName draft="unconfirmed">Mis</displayName>
			</field>
			<field type="week">
				<displayName draft="unconfirmed">Seythun</displayName>
			</field>
			<field type="day">
				<displayName draft="unconfirmed">Dedh</displayName>
			</field>
			<field type="weekday">
				<displayName draft="unconfirmed">Dedh an seythun</displayName>
			</field>
			<field type="dayperiod">
				<displayName draft="unconfirmed">AM/PM</displayName>
			</field>
			<field type="hour">
				<displayName draft="unconfirmed">Eur</displayName>
			</field>
		</fields>
		<timeZoneNames>
			<zone type="Etc/Unknown">
				<exemplarCity draft="unconfirmed">Ankoth</exemplarCity>
			</zone>
			<metazone type="Europe_Central">
				<short>
					<generic draft="unconfirmed">CET</generic>
					<standard draft="unconfirmed">CET</standard>
					<daylight draft="unconfirmed">CEST</daylight>
				</short>
			</metazone>
			<metazone type="Europe_Eastern">
				<short>
					<generic draft="unconfirmed">EET</generic>
					<standard draft="unconfirmed">EET</standard>
					<daylight draft="unconfirmed">EEST</daylight>
				</short>
			</metazone>
			<metazone type="Europe_Western">
				<short>
					<generic draft="unconfirmed">WET</generic>
					<standard draft="unconfirmed">WET</standard>
					<daylight draft="unconfirmed">WEST</daylight>
				</short>
			</metazone>
			<metazone type="GMT">
				<short>
					<standard draft="unconfirmed">GMT</standard>
				</short>
			</metazone>
		</timeZoneNames>
	</dates>
	<numbers>
		<currencyFormats numberSystem="latn">
			<currencyFormatLength>
				<currencyFormat type="standard">
					<pattern>¤#,##0.00</pattern>
				</currencyFormat>
			</currencyFormatLength>
		</currencyFormats>
		<currencies>
			<currency type="EUR">
				<displayName draft="unconfirmed">Euro</displayName>
			</currency>
		</currencies>
	</numbers>
</ldml>

