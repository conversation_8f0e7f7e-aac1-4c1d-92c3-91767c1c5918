<?php
return [
    'dynamic_query' => 'Dynamic query',
    'filter_mask' => 'Filter mask for dynamic category',
    'query_type_attribute' => 'Query type',
    'min_price' => 'Minimum',
    'max_price' => 'Maximum',
    'invert' => 'Invert',
    'save' => 'Save',
    'remove' => 'Remove',
    'close' => 'Close',
    'add_item' => 'Add Item',
    'add' => 'Add',
    'undo' => 'Undo',
    'producer' => 'Producer',
    'apply_type' => 'Apply type',
    'from' => 'from',
    'to' => 'to',
    'require_all' => 'Require all',
    'one_of_many' => 'Require one of selected',
    'exact_match' => 'Require exact match',
];