<?php
return array (
  'AddNewItem' => '___[AddNewItem]',
  'OrderPostPrice' => 'Poštovné a balné',
  'OrderPostPriceVat' => 'Poštovné a balné DPH',
  'OrderCurrency' => 'Mena',
  'OrderType' => 'Typ objednávky',
  'OrderPaymentType' => 'Typ platby',
  'OrderDeliveryType' => 'Typ dodávky',
  'OrderTitle' => 'Titul',
  'OrderFirstName' => 'Meno',
  'OrderSurname' => 'Priezvisko',
  'OrderCompanyName' => 'Firma',
  'OrderStreet' => 'Ulica',
  'OrderCity' => 'Mesto',
  'OrderZip' => 'PSČ',
  'OrderPhone' => 'Telefón',
  'OrderFax' => 'Fax',
  'OrderEmail' => 'Email',
  'OrderInvCompanyName' => 'Fakt. adresa - Firma',
  'OrderInvStreet' => 'Fakt. adresa - Ulica',
  'OrderInvCity' => 'Fakt. adresa - Mesto',
  'OrderInvZip' => 'Fakt. adresa - PSČ',
  'OrderDic' => 'DIČ',
  'OrderIco' => 'IČO',
  'OrderDrc' => 'IČ DPH',
  'OrderAccountNumber' => 'Číslo účtu',
  'OrderBankNumber' => 'Banka',
  'Note' => 'Poznámka',
  'Customer' => 'Zákazník',
  'ShoppingItems' => 'Položky',
  'Documents' => 'Doklady',
  'Invoices' => 'Faktúry',
  'EmailNotification' => 'E-mailová notifikácia',
  'ChangesLog' => 'Záznamy zmien',
  'EmailLog' => 'Záznamy emailov',
  'EShopPageTitle' => 'E-Obchod - Informácie o objednávke',
  'PageTitle' => 'Detaily objednávky',
  'PageAnotation' => 'Upravte požadované vlastnosti objednávky.',
  'Order_Deleted' => 'Táto objednávka bola odstránená. Jej obnova je možná iba ručným zásahom do databázy.',
  'OrderSource' => 'Zdroj objednávky',
  'VariableSymbol' => 'Var. Sym.',
  'InvoiceID' => 'Číslo faktúry',
  'OrderState' => 'Stav',
  'Order_date' => 'Objednané',
  'OrderPrice' => 'Cena',
  'Payment_date' => 'Zaplatené',
  'Payment_user' => 'Zaplatil užív.',
  'Order_user' => 'Používateľ',
  'Choose_Labels' => 'Vyberte štítky ...',
  'CustomerInformation' => 'Údaje zákazníka',
  'CustomerInformationExtended' => 'Ďalšie údaje',
  'Discount' => 'Zľava %',
  'PricePerItem' => 'Cena za kus',
  'With' => 's',
  'Without' => 'bez',
  'Vat' => 'DPH',
  'NrOfItems' => 'Počet kusov',
  'VatPerItem' => 'DPH',
  'ItemState' => 'Stav',
  'DeliveredItems' => 'Dodané kusy',
  'Delete' => 'Odstrániť',
  'InternalNote' => 'Interná poznámka',
  'ExternalNote' => 'Externá poznámka',
  'AdvanceInvoice' => 'Zálohová faktúra',
  'Regenerate' => 'Znovu vygenerovať',
  'Show' => 'Zobraziť',
  'Remove' => 'Odstrániť',
  'Generate' => 'Vygenerovať',
  'Invoice' => 'Faktúra',
  'NoteAnnotation' => 'Poznámka sa uloží spolu s vykonanými zmenami. Poznámky si môžete pozrieť na karte <em>Záznamy zmien</em>.',
  'SendEmailNotification' => 'Zaslať e-mailovú notifikáciu',
  'EmailRecipient' => 'Príjemca',
  'EmailSubject' => 'Predmet',
  'EmailSubjectDefault' => 'Informácie o objednávke',
  'EmailBody' => 'Správa',
  'AttachedAdvanceInvoice' => 'Pripojiť zálohovú faktúru',
  'AttachedInvoice' => 'Pripojiť faktúru',
  'AttachFile' => 'Priložiť súbor',
  'LoadDefaultStateEmail' => 'Nahrať štandardný e-mail pre tento stav',
  'ChangeOrderNotice' => 'V objednávke boli vykonané zmeny. Kliknite na tlačidlo &quot;Uložiť objednávku&quot;, aby sa uložili.',
  'TableHeader' => 'Objednávka',
  'SaveOrder' => 'Uložiť objednávku',
  'Delete_Order' => 'Odstrániť objednávku',
  'Delete_Order_Question' => 'Skutočne chcete odstrániť objednávku s VS ',
  'Delete_Order_Explain' => 'Takýmto spôsobom odstránená objednávka sa nezobrazuje v zozname objednávok a nie je možné s ňou ďalej pracovať. Hoci objednávka nie je touto akciou úplne vymazaná, na jej obnovu je potrebný ručný zásah do databázy.\\n\\nAk naozaj chcete odstrániť objednávku, kliknite na tlačítko OK.\\nAk nechcete objednávku odstrániť, kliknite na tlačítko Cancel/Zrušiť.',
  'NoAccessRights' => 'Nemáte prístupové práva pre obchod.',
  'ChangeFinalPriceDialogTitle' => 'Zmena ceny objednávky',
  'ChangeFinalPriceDeleteItem' => 'Po uložení objednávky z nej bude odstránený zvolený produkt, čo môže ovplyvniť jej cenu. Skontrolujte a prípadne upravte cenu objednávky.',
  'ChangeFinalPriceAddItem' => 'Do objednávky bol pridaný produkt , čo môže ovplyvniť jej cenu. Skontrolujte a prípadne upravte cenu objednávky.',
  'ChangeFinalPriceChangeOrder' => 'V objednávke boli vykonané zmeny, ktoré môžu ovplyvniť jej cenu. Skontrolujte a prípadne upravte cenu objednávky.',
  'ProductWasAdded' => 'Produkt "<product_name>" bol pridaný do objednávky.',
  'oznacit_ako_zaplatene' => 'Označiť ako zaplatené',
  'message_content' => 'Obsah správy',
  'message_subject' => 'Predmet správy',

);
