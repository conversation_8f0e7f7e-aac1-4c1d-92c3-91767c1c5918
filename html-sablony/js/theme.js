
var tempoUvod = 6000;
var tempoLekarne = 3000;

var pocetBanner, vyska, vyskatextu;
var pocetFotiek, vyskaFotiek;
var animujem = "ee";
var sirka;

$(document).ready(function($) {


/* 	HEADER POZICIA	 */


	
		
	sirka = $(window).width();
	var znak = "<span class='-x-'>&ndash;</span>";
	
	$(".page-title, .formular-title, .block-title").prepend(znak).append(znak);
	


	if(window.screen.width < 768){
		document.querySelector('meta[name="viewport"]').content = 'user-scalable=yes,  width=480';
		//alert("bam");

	}else{
		if(!$.browser.msie) document.querySelector('meta[name="viewport"]').content = 'user-scalable=yes,  width=1100';
	}
	
	
/* 	BANNER NA UVODE */
	
	if($("#banner_uvod").length > 0 && window.screen.width > 600){
		
		var pocetBanner = $("#banner_uvod .row").length;

		$(window).load(function(){
			
			var i = 1;
			var canvas = $(".banner .inside");
	   		
	   		canvas.append("<span class='hack'></span>");
	   		
	   		$("#banner_uvod .row1 h2").css({paddingTop: (vyska - vyskatextu) / 2})
	   		
	   		vyska = $("#banner_uvod .row img").css({position: "absolute", left: 0}).eq(0).height();
		    vyskatextu = $("#banner_uvod .row h2").eq(0).height();
			
			var prepo = 0;
		    $("#banner_uvod .row img").each(function(index) {
		        $(this).css({top: prepo})
		        prepo += $(this).height() + 50;
		      });
		    
		    canvas.height(vyska).width(950);
	   		
	   		$(".banner .text").eq(0).animate({left: 500}, 1500, "easeInOutBack");
	   		
	   		 $("#banner_uvod .row").show();
	   		
	   		var animuj = setInterval(function(){ 
	
			    vyska = $("#banner_uvod .row img").eq(i).height();
			    vyskatextu = $("#banner_uvod .row h2").eq(i).height();
			    
			   	var before = i - 1; if(i == 0) before = pocetBanner - 1;
			   			   
			    $(".banner .text").eq(i).animate({left: 500}, 1500, "easeInOutBack");
			    $(".banner .text").eq(before).animate({left: 950}, 1500, "easeInOutBack");
			    
			    $(".banner .row img").eq(i).animate({top: 0}, 1500, "easeInOutBack");
			    $(".banner .row img").eq(before).animate({top: -1*vyska}, 1500, "easeInOutBack");
			    
			    canvas.animate({height: vyska}, 500);
			    
			    $(".banner .pager span").removeClass("active");
			    $(".banner .pager span").eq(i).addClass("active");
			    
			    setTimeout(function(){
					$(".banner .text").eq(before).css({left: 10}); 
					$(".banner .row img").eq(before).css({top: vyska+100});    
			    }, 1600);
			    
			    //console.log(i, before, vyska);
			    
			    
			    
			    i++;
			    if(i == (pocetBanner)) i = 0; 
				
				
				
			}, tempoUvod); 
			
			$(".banner .pager span").click(function(){
				
				
				
				clearInterval(animuj);
				if(animujem == "ee"){
				animujem = "jj";
				var i = ($(this).html())*1 - 1;
				
				//console.log(i)
				
				
				
				vyska = $("#banner_uvod .row img").eq(i).height();
			    vyskatextu = $("#banner_uvod .row h2").eq(i).height();
			    
			   	var before = i - 1; if(i == 0) before = pocetBanner - 1;
			   			   
			    $(".banner .text").eq(i).animate({left: 500}, 1500, "easeInOutBack");
			    $(".banner .text").eq(before).animate({left: 950}, 1500, "easeInOutBack");
			    
			    $(".banner .row img").eq(i).animate({top: 0}, 1500, "easeInOutBack");
			    $(".banner .row img").eq(before).animate({top: -1*vyska}, 1500, "easeInOutBack");
			    
			    canvas.animate({height: vyska}, 500);
			    
			    $(".banner .pager span").removeClass("active");
			    $(".banner .pager span").eq(i).addClass("active");
			    
			    setTimeout(function(){
					$(".banner .text").eq(before).css({left: 10}); 
					$(".banner .row img").eq(before).css({top: vyska+100});    
					animujem = "ee";
			    }, 1800);
				}
				
			})
			
			
			
		})
		
	}else if($("#banner_uvod").length > 0 && window.screen.width < 600){
		var i = 1;
		var pocetBanner = $("#banner_uvod .row").length;
		var animuj = setInterval(function(){ 
			
			   	var before = i - 1; if(i == 0) before = pocetBanner - 1;
			   			   
			    $(".banner .row").slideUp("slow");
			    $(".banner .row").eq(i).slideDown("slow");
			    
			    $(".banner .pager span").removeClass("active");
			    $(".banner .pager span").eq(i).addClass("active");
			    
			    
			    i++;
			    if(i == (pocetBanner)) i = 0; 
				
				
				
			}, tempoUvod);
	}
	
	if($("#lekarne_slideshow").length > 0 && window.screen.width > 600){
		
		var pocetFotiek = $("#lekarne_slideshow .row").length;
		var velkosti = Array();
		
		$(window).load(function(){
			
			
			
			$("#lekarne_slideshow .row").each(function(index) {
		        if(index > 0) $(this).css({left: 470});
		        
		        velkosti.push($(this).css({position: "absolute"}).height());
		      }).show();
			var i = 1;
			
			//console.log(pocetFotiek, "bam", velkosti);
			
			var animuj = setInterval(function(){ 
				
				$("#lekarne_slideshow").animate({height: velkosti[i] + 30}, 1000);
				
			    var before = i - 1; if(i == 0) before = pocetFotiek - 1;
			   			   
			    $("#lekarne_slideshow .row").eq(i).animate({left: "-=" + 470}, 1500, "easeInOutBack");
			    $("#lekarne_slideshow .row").eq(before).animate({left: "-=" + 470}, 1500, "easeInOutBack");
			    
			    setTimeout(function(){
					$("#lekarne_slideshow .row").eq(before).css({left: 470});    
			    }, 1600);
			    
			    //console.log(i, before, pocetFotiek);
			    
			    $("#lekarne_slideshow .pager span").removeClass("active");
			    $("#lekarne_slideshow .pager span").eq(i).addClass("active");
			    
			    i++;
			    if(i == (pocetFotiek)) i = 0; 
				
				
				
			}, tempoLekarne); 
			
			$("#lekarne_slideshow .pager span").click(function(){
				
				clearInterval(animuj);
				if(animujem == "ee"){
					animujem = "jj";
					var i = ($(this).html())*1 - 1;
					
					//console.log(i)
					
					$("#lekarne_slideshow").animate({height: velkosti[i] + 30}, 1000);
					
				    var before = i - 1; if(i == 0) before = pocetFotiek - 1;
				   			   
				    $("#lekarne_slideshow .row").eq(i).animate({left: "-=" + 470}, 1500, "easeInOutBack");
				    $("#lekarne_slideshow .row:not(:eq("+i+"))").animate({left: "-=" + 470}, 1500, "easeInOutBack");
				    
				    setTimeout(function(){
						$("#lekarne_slideshow .row:not(:eq("+i+"))").css({left: 470});  
						animujem = "ee";  
				    }, 1800);
				    
				    //console.log(i, before, pocetFotiek);
				    
				    $("#lekarne_slideshow .pager span").removeClass("active");
				    $("#lekarne_slideshow .pager span").eq(i).addClass("active");
			    
				}
				
			})
			
			
		})		
		
	}else if($("#lekarne_slideshow").length > 0 && window.screen.width < 600){
		var i = 1;
		var pocetBanner = $("#lekarne_slideshow .row").length;
		var animuj = setInterval(function(){ 
			
			   	var before = i - 1; if(i == 0) before = pocetBanner - 1;
			   			   
			    $("#lekarne_slideshow .row").slideUp("slow");
			    $("#lekarne_slideshow .row").eq(i).slideDown("slow");
			    
			    $("#lekarne_slideshow .pager span").removeClass("active");
			    $("#lekarne_slideshow .pager span").eq(i).addClass("active");
			    
			    
			    i++;
			    if(i == (pocetBanner)) i = 0; 
				
				
				
			}, tempoUvod);
	}
	
	

});






















