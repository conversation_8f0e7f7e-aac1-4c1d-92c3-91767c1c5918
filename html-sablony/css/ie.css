/* --------------------------------------------------------------

   ie.css

   Contains every hack for Internet Explorer,
   so that our core files stay sweet and nimble.

-------------------------------------------------------------- */

/* Make sure the layout is centered in IE5 */
body { text-align: center; }
.container { text-align: left; }

/* Fixes IE margin bugs */
* html .column, * html .span-1, * html .span-2,
* html .span-3, * html .span-4, * html .span-5,
* html .span-6, * html .span-7, * html .span-8,
* html .span-9, * html .span-10, * html .span-11,
* html .span-12, * html .span-13, * html .span-14,
* html .span-15, * html .span-16, * html .span-17,
* html .span-18, * html .span-19, * html .span-20,
* html .span-21, * html .span-22, * html .span-23,
* html .span-24 { display:inline; overflow-x: hidden; }


/* Elements
-------------------------------------------------------------- */

/* Fixes incorrect styling of legend in IE6. */
* html legend { margin:0px -8px 16px 0; padding:0; }

/* Fixes wrong line-height on sup/sub in IE. */
sup { vertical-align:text-top; }
sub { vertical-align:text-bottom; }

/* Fixes IE7 missing wrapping of code elements. */
html>body p code { *white-space: normal; }

/* IE 6&7 has problems with setting proper <hr> margins. */
hr  { margin:-8px auto 11px; }

/* Explicitly set interpolation, allowing dynamically resized images to not look horrible */
img { -ms-interpolation-mode:bicubic; }

/* Clearing
-------------------------------------------------------------- */

/* Makes clearfix actually work in IE */
.clearfix, .container { display:inline-block; }
* html .clearfix,
* html .container { height:1%; }


/* Forms
-------------------------------------------------------------- */

/* Fixes padding on fieldset */
fieldset { padding-top:0; }
legend { margin-top:-0.2em; margin-bottom:1em; margin-left:-0.5em; }

/* Makes classic textareas in IE 6 resemble other browsers */
textarea { overflow:auto; }

/* Makes labels behave correctly in IE 6 and 7 */
label { vertical-align:middle; position:relative; top:-0.25em; }

/* Fixes rule that IE 6 ignores */
input.text, input.title, textarea { background-color:#fff; border:1px solid #bbb; }
input.text:focus, input.title:focus { border-color:#666; }
input.text, input.title, textarea, select { margin:0.5em 0; }
input.checkbox, input.radio { position:relative; top:.25em; }

/* Fixes alignment of inline form elements */
form.inline div, form.inline p { vertical-align:middle; }
form.inline input.checkbox, form.inline input.radio,
form.inline input.button, form.inline button {
  margin:0.5em 0;
}
button, input.button { position:relative;top:0.25em; }
