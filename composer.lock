{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "0111482f1012adfe85fd753a3451768b", "packages": [{"name": "buxus-libs/analytics", "version": "1.1.14", "source": {"type": "git", "url": "******************:buxus-libs/analytics.git", "reference": "f3ae06dc881e065c4c6124cfb1fe9d7af7143dd3"}, "require": {"buxus/bx-dashboard": "^1.0.0", "buxus/bx-testing": "^1.1.0", "buxus/cache": "^3.0.0", "buxus/core": "^7.1.0", "buxus/eshop": "^1.4.37", "buxus/events": "^1.0.0", "buxus/gdpr": "^1.0.7", "buxus/legacy-base": "^2.1.0", "buxus/modules": "^1.5.0", "buxus/pages": "~3.3.12 || ^4.0.0", "ext-date": "*", "ext-json": "*", "ext-pcre": "*", "google/apiclient": "^2.2.0", "illuminate/contracts": "5.8.* || ^6.0", "illuminate/http": "5.8.* || ^6.0", "illuminate/support": "5.8.* || ^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}, "laravel": {"providers": ["Analytics\\AnalyticsModule"]}}, "autoload": {"psr-4": {"Analytics\\": "src/"}}, "notification-url": "https://packagist.ui42.sk/downloads/", "license": ["proprietary"], "description": "Google analytics module for CMS BUXUS", "time": "2020-07-01T10:30:27+02:00"}, {"name": "buxus-libs/authentication", "version": "3.2.16", "source": {"type": "git", "url": "******************:buxus-libs/authentication.git", "reference": "d23f3235e67465435de41c4f25feb833882be868"}, "require": {"buxus-libs/email": "^2.0.0", "buxus-libs/form-base": "^1.5.0", "buxus-libs/seo": "^1.0.0", "buxus/captcha": "^1.1.3", "buxus/core": "^7.1.0", "buxus/eshop": "^1.4.0", "buxus/events": "^1.3.0", "buxus/lib-support": "~1.3 || ^2.0.0", "buxus/modules": "^1.5.0", "buxus/webuser": "^2.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.2.x-dev"}, "laravel": {"providers": ["Authentication\\AuthenticationModule"]}}, "autoload": {"psr-4": {"Authentication\\": "src/"}}, "notification-url": "https://packagist.ui42.sk/downloads/", "license": ["proprietary"], "description": "Autenti<PERSON><PERSON><PERSON><PERSON>, regis<PERSON><PERSON><PERSON>, prihlásenie užívateľa, obnova hesla", "homepage": "http://www.buxus.sk/", "keywords": ["Autentifikacia", "BUXUS", "ui42"], "time": "2021-01-21T10:39:54+01:00"}, {"name": "buxus-libs/ciselniky", "version": "1.2.14", "source": {"type": "git", "url": "******************:buxus-libs/lib-cisleniky.git", "reference": "bf17a2bd1b5a1d040f7c4e440ab5ef50cfa29071"}, "require": {"buxus/cache": "^3.0.0", "buxus/core": "^7.2.22", "buxus/events": "^1.1.0", "buxus/legacy-base": "^2.1.1", "buxus/migrations": "^1.0.0", "buxus/modules": "^1.5.0", "buxus/multisite": "^1.0.0", "buxus/pages": "^4.0.12", "buxus/translate": "~2.1.21 || ^2.2.1", "buxus/util": "^2.0.0", "ext-json": "*", "ext-mbstring": "*", "ext-pcre": "*", "ext-spl": "*", "illuminate/contracts": "5.8.* || ^6.0", "illuminate/support": "5.8.* || ^6.0", "malkusch/lock": "^2.1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}, "laravel": {"providers": ["Buxus\\Ciselniky\\CiselnikyModule"], "aliases": {"Ciselniky": "Buxus\\C<PERSON>lniky\\Facades\\Ciselniky"}}}, "autoload": {"psr-4": {"Buxus\\Ciselniky\\": "src/"}}, "notification-url": "https://packagist.ui42.sk/downloads/", "license": ["proprietary"], "description": "Module for handling of value lists (číselníky) for CMS BUXUS", "homepage": "http://www.buxus.sk/", "keywords": ["BUXUS", "ciselnik", "ui42"], "time": "2021-02-10T23:22:52+01:00"}, {"name": "buxus-libs/contact-form", "version": "2.2.2", "source": {"type": "git", "url": "******************:buxus-libs/contact-form.git", "reference": "165fb6f7011ee3683eca34079b5f176440881f51"}, "require": {"buxus-libs/email": "^2.1.2", "buxus-libs/form-base": "^1.1", "buxus/core": "^7.1.0", "buxus/modules": "^1.5.0", "buxus/util": "^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2.x-dev"}, "laravel": {"providers": ["ContactForm\\ContactFormModule"]}}, "autoload": {"psr-4": {"ContactForm\\": "src/"}}, "notification-url": "https://packagist.ui42.sk/downloads/", "license": ["proprietary"], "description": "Contact form CMS BUXUS", "homepage": "http://www.buxus.sk/", "keywords": ["BUXUS", "contact form", "form", "ui42"], "time": "2020-02-15T23:04:45+01:00"}, {"name": "buxus-libs/devel", "version": "1.1.17", "source": {"type": "git", "url": "******************:buxus-libs/lib-devel.git", "reference": "65a29830519b2f78b28cc568f0cc629e4f8dd706"}, "dist": {"type": "zip", "url": "https://packages.ui42.sk/api/v4/projects/buxus-libs%2Flib-devel/repository/archive.zip?sha=65a29830519b2f78b28cc568f0cc629e4f8dd706", "reference": "65a29830519b2f78b28cc568f0cc629e4f8dd706", "shasum": ""}, "require": {"buxus/image-processor": "^1.2 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev", "dev-main": "1.1.x-dev"}, "laravel": {"providers": ["Buxus\\Devel\\DevelModule"]}}, "autoload": {"psr-4": {"Buxus\\Devel\\": "src/"}}, "notification-url": "https://packagist.ui42.sk/downloads/", "license": ["proprietary"], "description": "Devel helper module", "homepage": "http://www.buxus.sk/", "keywords": ["devel", "developement", "local"], "time": "2023-04-21T09:08:32+00:00"}, {"name": "buxus-libs/dynamic-categories", "version": "2.2.9", "source": {"type": "git", "url": "******************:buxus-libs/dynamic-categories.git", "reference": "8c45b2ec2e2fd3d9ee66e1e2f40daec792519f6a"}, "require": {"buxus-libs/fs": "^5.1.5", "buxus-libs/product-catalog": "^1.2.0", "buxus/core": "^7.1.0", "buxus/legacy-base": "^2.1.15", "buxus/modules": "^1.5.0", "buxus/util": "^2.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2.x-dev"}, "buxus-docs": {"Popis IA": "https://docs.google.com/document/d/1uf7czT7u7VehpH051Lgvhp-61sCxtDKC9Iz6q1a7Cwc", "Technická dokumentácia": "https://phabricator.ui42.sk/w/buxus/moduly/dynamic-categories/", "Use-case": "https://phabricator.ui42.sk/w/buxus/knowledge-base/dynamic-categories/"}, "laravel": {"providers": ["DynamicCategory\\DynamicCategoryModule"]}}, "autoload": {"psr-4": {"DynamicCategory\\": "src/"}}, "notification-url": "https://packagist.ui42.sk/downloads/", "license": ["proprietary"], "description": "Dynamic categories for CMS BUXUS", "homepage": "http://www.buxus.sk/", "keywords": ["BUXUS", "dynamic", "dynamic categories", "faceted search", "ui42"], "time": "2020-09-21T21:06:15+02:00"}, {"name": "buxus-libs/email", "version": "2.1.3", "source": {"type": "git", "url": "******************:buxus-libs/email.git", "reference": "43195e418eebfad461a2b3a8ad039576f218eb8c"}, "require": {"buxus/core": "^7.1.0", "buxus/email-core": "^1.1.0", "buxus/modules": "^1.5.0", "buxus/util": "^2.0.0", "mustache/mustache": "^2.10.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1.x-dev"}, "laravel": {"providers": ["Email\\EmailModule"], "aliases": {"Email": "Email\\Facade\\Email"}}}, "autoload": {"psr-4": {"Email\\": "src/"}}, "notification-url": "https://packagist.ui42.sk/downloads/", "license": ["proprietary"], "description": "Page backed emails for CMS BUXUS", "homepage": "http://www.buxus.sk/", "keywords": ["BUXUS", "e-mail", "email", "ui42"], "time": "2019-08-13T13:22:07+02:00"}, {"name": "buxus-libs/fat-footer", "version": "1.0.3", "source": {"type": "git", "url": "******************:buxus-libs/fat-footer.git", "reference": "4be24b15d8fac828cb41220d0267dde6f9606b79"}, "require": {"buxus-libs/layouts": "^2.1.0", "buxus/modules": "^1.2.0"}, "type": "buxus-module", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}, "laravel": {"providers": ["FatFooter\\FatFooterModule"]}}, "autoload": {"psr-4": {"FatFooter\\": "src/"}}, "notification-url": "https://packagist.ui42.sk/downloads/", "license": ["proprietary"], "description": "Fat footer for Buxus CMS", "homepage": "http://www.buxus.sk/", "keywords": ["BUXUS", "footer"], "time": "2020-10-20T12:17:39+02:00"}, {"name": "buxus-libs/form-base", "version": "1.5.2", "source": {"type": "git", "url": "******************:buxus-libs/form-base.git", "reference": "2722042980713dff09cad9e5cc60e8f03f351e7b"}, "require": {"buxus/core": "^7.1.0", "illuminate/http": "5.8.* || ^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.5.x-dev"}, "laravel": {"providers": ["FormBase\\FormBaseModule"]}}, "autoload": {"psr-4": {"FormBase\\": "src/"}}, "notification-url": "https://packagist.ui42.sk/downloads/", "license": ["proprietary"], "description": "Base package for forms", "homepage": "http://www.buxus.sk/", "keywords": ["BUXUS", "form", "ui42"], "time": "2021-02-24T10:11:56+01:00"}, {"name": "buxus-libs/fs", "version": "5.2.9", "source": {"type": "git", "url": "******************:buxus-libs/fs.git", "reference": "f2ec82fdaeeff8372d9058b4b0b707c4ff681a60"}, "require": {"buxus-libs/product-catalog": "^1.4.4", "buxus-libs/seo": "^1.0.9", "buxus/cache": "^3.0.0", "buxus/core": "^7.1.0", "buxus/eshop": "^1.4.38", "buxus/events": "^1.4.0", "buxus/legacy-base": "^2.1.15", "buxus/migrations": "^1.6.0", "buxus/modules": "^1.5.0", "buxus/multisite": "^1.2.0", "buxus/pages": "~3.3 || ^4.0.0", "buxus/substrate": "^1.1.3", "buxus/translate": "^2.1.0", "buxus/util": "^2.2.3", "ext-date": "*", "ext-json": "*", "ext-pcre": "*", "ext-spl": "*", "ext-zlib": "*", "illuminate/bus": "5.8.* || ^6.0", "illuminate/contracts": "5.8.* || ^6.0", "illuminate/support": "5.8.* || ^6.0", "solarium/solarium": "^5.1.0", "symfony/console": "^4.2", "zendframework/zendframework1": "^1.13.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.2.x-dev"}, "buxus-docs": {"Popis IA": "https://docs.google.com/document/d/1L6oIw6kPyZ8GJ0PPxgRkKMQmdTek4izN5CzByAObLNo", "Technická dokumentácia": "https://phabricator.ui42.sk/w/buxus/moduly/facet/", "Use-case": "https://phabricator.ui42.sk/w/buxus/knowledge-base/faceted-search/"}, "laravel": {"providers": ["FS\\FSModule"]}}, "autoload": {"psr-4": {"FS\\": "src/"}}, "notification-url": "https://packagist.ui42.sk/downloads/", "license": ["proprietary"], "description": "Faceted search library for CMS BUXUS", "homepage": "http://www.buxus.sk/", "keywords": ["BUXUS", "FS", "faceted search", "ui42"], "time": "2021-02-15T16:42:05+01:00"}, {"name": "buxus-libs/fulltext-search", "version": "3.2.3", "source": {"type": "git", "url": "******************:buxus-libs/fulltext-search.git", "reference": "8a24a27b9d30f53bd711bb5897136264e2d2a681"}, "require": {"buxus/core": "^7.1.0", "buxus/eshop": "^1.4.0", "buxus/events": "^1.2", "buxus/legacy-base": "^2.1.0", "buxus/migrations": "^1.0.0", "buxus/modules": "^1.5.0", "buxus/multisite": "^1.0.0", "buxus/pages": "~3.3 || ^4.0.0", "buxus/translate": "^2.0.0", "buxus/util": "^2.0.0", "ext-date": "*", "ext-json": "*", "ext-mbstring": "*", "ext-pcre": "*", "illuminate/contracts": "5.8.* || ^6.0", "php": ">=7.1.0", "solarium/solarium": "^5.1.0", "symfony/console": "^4.2", "zendframework/zendframework1": "^1.13.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.2.x-dev"}, "buxus-docs": {"Popis IA": "https://docs.google.com/a/ui42.com/document/d/1cStbbkO1zmR3qNKPBDGnEDtYgpiCuB7MiLrmImQG-Gs", "Technická dokumentácia": "https://phabricator.ui42.sk/w/buxus/moduly/fulltextove-vyhladavanie/", "Use-case": "https://phabricator.ui42.sk/w/buxus/knowledge-base/fulltext-search/"}, "laravel": {"providers": ["FullTextSearch\\FullTextSearchModule"]}}, "autoload": {"psr-4": {"FullTextSearch\\": "src/"}}, "notification-url": "https://packagist.ui42.sk/downloads/", "license": ["proprietary"], "description": "Fulltext search support for CMS BUXUS", "time": "2021-02-05T06:28:01+01:00"}, {"name": "buxus-libs/google-sitemap", "version": "2.0.12", "source": {"type": "git", "url": "******************:buxus-libs/google-sitemap.git", "reference": "1dfed4ced04f28384bb98340ee4d320f2ad5835d"}, "require": {"buxus-libs/seo": "^1.0.0", "buxus/core": "^7.1.0", "buxus/legacy-base": "^2.1.0", "buxus/modules": "^1.5.0", "buxus/multisite": "~1.1.18 || ^1.2.5", "buxus/pages": "~3.3 || ^4.0.0", "buxus/translate": "^2.1.2", "spatie/laravel-sitemap": "^5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}, "laravel": {"providers": ["GoogleSitemap\\GoogleSitemapModule"]}}, "autoload": {"psr-4": {"GoogleSitemap\\": "src/"}}, "notification-url": "https://packagist.ui42.sk/downloads/", "license": ["proprietary"], "description": "Google XML sitemap generator for CMS BUXUS", "homepage": "http://www.buxus.sk/", "keywords": ["BUXUS", "google", "seo", "sitemap", "ui42", "xml"], "time": "2021-02-04T13:51:50+01:00"}, {"name": "buxus-libs/layouts", "version": "2.3.2", "source": {"type": "git", "url": "******************:buxus-libs/layouts.git", "reference": "c0f826b04094cb72b8ab1320e10f88f4fc9a21cc"}, "require": {"buxus-libs/analytics": "^1.0.0", "buxus/cache": "^3.1.0", "buxus/core": "^7.1.0", "buxus/eshop": "^1.0.0", "buxus/events": "^1.4.0", "buxus/legacy-base": "^2.1.0", "buxus/migrations": "^1.0.0", "buxus/modules": "^1.5.0", "buxus/multisite": "^1.3.0", "buxus/pages": "^4.0.3", "buxus/substrate": "^1.0.6", "buxus/translate": "^2.2.0", "buxus/util": "^2.0.0", "ext-curl": "*", "ext-json": "*", "ext-pcre": "*", "ext-spl": "*", "illuminate/contracts": "5.8.* || ^6.0", "illuminate/http": "5.8.* || ^6.0", "illuminate/support": "5.8.* || ^6.0", "symfony/console": "^4.2", "zendframework/zendframework1": "^1.13.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.3.x-dev"}, "laravel": {"providers": ["Layout\\LayoutModule"], "aliases": {"Layouts": "Layout\\Facades\\Layouts"}}}, "autoload": {"psr-4": {"Layout\\": "src/"}}, "notification-url": "https://packagist.ui42.sk/downloads/", "license": ["proprietary"], "description": "Layout library for CMS BUXUS", "homepage": "http://www.buxus.sk/", "keywords": ["BUXUS", "dynamic layouts", "layout", "ui42"], "time": "2020-09-22T07:14:10+02:00"}, {"name": "buxus-libs/layouts-dynamic-list", "version": "1.5.6", "source": {"type": "git", "url": "******************:buxus-libs/layouts-dynamic-list.git", "reference": "08a2c4b524bd8950f7806036464a5525a2bc03d5"}, "require": {"buxus-libs/analytics": "^1.0.0", "buxus-libs/dynamic-categories": "^2.1.0", "buxus-libs/layouts": "^2.0.0", "buxus/core": "^7.1.0", "buxus/eshop": "^1.0.0", "buxus/migrations": "^1.0.0", "buxus/modules": "^1.5.0", "buxus/pages": "~3.0 || ^4.0.0", "buxus/util": "^2.0.0", "illuminate/support": "5.8.* || ^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.5.x-dev"}, "buxus-docs": {"Popis IA": "https://docs.google.com/document/d/1ibbJwnFy_gKMJUWJfQiI9szvaB76F5i9lOfRzx88I40", "Technická dokumentácia": "https://phabricator.ui42.sk/w/buxus/moduly/dynamic-products-list/", "Use-case": "https://phabricator.ui42.sk/w/buxus/knowledge-base/dynamic-categories/"}, "laravel": {"providers": ["Layout\\LayoutDynamicListModule"]}}, "autoload": {"psr-4": {"Layout\\": "src/"}}, "notification-url": "https://packagist.ui42.sk/downloads/", "license": ["proprietary"], "description": "Dynamic list for layout library in CMS BUXUS", "homepage": "http://www.buxus.sk/", "keywords": ["BUXUS", "dynamic layouts", "layout", "ui42"], "time": "2020-10-20T12:19:37+02:00"}, {"name": "buxus-libs/mailinglist", "version": "3.3.4", "source": {"type": "git", "url": "******************:buxus-libs/mailinglist.git", "reference": "523da1f728e4e830dc800dbc73c7374b77c2650b"}, "require": {"buxus-libs/email": "^2.0", "buxus-libs/form-base": "~1.3", "buxus-libs/layouts": "^2.0.0", "buxus-libs/seo": "^1.0.0", "buxus/captcha": "^1.1.3", "buxus/core": "^7.2.1", "buxus/events": "^1.3.4", "buxus/legacy-base": "^2.1.0", "buxus/lib-support": "^2.0.0", "buxus/migrations": "^1.0.0", "buxus/modules": "^1.5.0", "buxus/multisite": "^1.0.0", "buxus/pages": "^4.0.0", "buxus/translate": "^2.0.0", "buxus/user": "^2.1.0", "buxus/util": "~2.0", "buxus/webuser": "^2.1.7", "ext-date": "*", "ext-mbstring": "*", "ext-pcre": "*", "illuminate/contracts": "5.8.* || ^6.0", "illuminate/http": "5.8.* || ^6.0", "illuminate/support": "5.8.* || ^6.0", "mustache/mustache": "~2.10.0", "zendframework/zendframework1": "^1.13.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.3.x-dev"}, "laravel": {"providers": ["Mailinglist\\MailinglistModule"]}}, "autoload": {"psr-4": {"Mailinglist\\": "src/"}, "classmap": ["buxus/"]}, "notification-url": "https://packagist.ui42.sk/downloads/", "license": ["proprietary"], "description": "Provides mailinglist support for CMS BUXUS", "time": "2020-10-05T14:00:23+02:00"}, {"name": "buxus-libs/mega-menu", "version": "2.2.5", "source": {"type": "git", "url": "******************:buxus-libs/mega-menu.git", "reference": "4d3a11de54c6db0bb106b987a0f27c408b6b3fbf"}, "require": {"buxus-libs/layouts": "^2.0", "buxus/core": "^7.1.0", "buxus/legacy-base": "^2.1.0", "buxus/migrations": "^1.0.0", "buxus/modules": "^1.5.0", "buxus/pages": "~3.3 || ^4.0.0", "buxus/substrate": "^1.0.6", "buxus/util": "^2.0.0", "ext-date": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2.x-dev"}, "buxus-docs": {"Popis IA": "https://docs.google.com/document/d/1ByntnssX_DdZh6Y5H3MvBP28UDgmc6T3wUo8jrjtrlI", "Technická dokumentácia": "https://phabricator.ui42.sk/w/buxus/moduly/megamenu/", "Use-case": "https://phabricator.ui42.sk/w/buxus/knowledge-base/megamenu/"}, "laravel": {"providers": ["Krabica\\MegaMenu\\MegaMenuModule"]}}, "autoload": {"psr-4": {"Krabica\\MegaMenu\\": "src/"}}, "notification-url": "https://packagist.ui42.sk/downloads/", "license": ["proprietary"], "description": "Mega menu for CMS BUXUS", "homepage": "http://www.buxus.sk/", "keywords": ["BUXUS", "krabica", "mega-menu", "menu", "ui42"], "time": "2020-10-20T12:21:37+02:00"}, {"name": "buxus-libs/persistence", "version": "1.2.3", "source": {"type": "git", "url": "******************:buxus/lib-persistence.git", "reference": "b32c39b56750f161446144b1b3fa9e34cd5fd88b"}, "require": {"buxus/core": "^7.1.0", "buxus/legacy-base": "^2.1.0", "buxus/migrations": "^1.0.0", "buxus/modules": "^1.5.0", "ext-date": "*", "illuminate/support": "5.8.* || ^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}, "laravel": {"providers": ["Persistence\\PersistenceModule"], "aliases": {"Persistence": "Persistence\\Facade\\Persistence"}}}, "autoload": {"psr-4": {"Persistence\\": "src/"}}, "notification-url": "https://packagist.ui42.sk/downloads/", "license": ["proprietary"], "description": "Module adding support for persisting data for user sessions", "time": "2020-06-19T07:35:33+02:00"}, {"name": "buxus-libs/photo-gallery", "version": "2.2.0", "source": {"type": "git", "url": "******************:buxus-libs/photo-gallery.git", "reference": "01c1d454fcc8a0eb9d11f7f47169781a30529e8f"}, "require": {"buxus/core": "^7.1.0", "buxus/legacy-base": "^2.0.0", "buxus/lib-support": "^2.0.0", "buxus/migrations": "^1.0.0", "buxus/modules": "^1.5.0", "buxus/pages": "^4.0.0", "buxus/substrate": "^1.0.6", "buxus/util": "^2.0.0", "zendframework/zendframework1": "^1.13.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2.x-dev"}, "laravel": {"providers": ["PhotoGallery\\PhotoGalleryModule"]}}, "autoload": {"psr-4": {"PhotoGallery\\": "src/"}}, "notification-url": "https://packagist.ui42.sk/downloads/", "license": ["proprietary"], "description": "Photo gallery support for CMS BUXUS", "time": "2020-04-26T21:37:29+02:00"}, {"name": "buxus-libs/product-catalog", "version": "1.4.28", "source": {"type": "git", "url": "******************:buxus-libs/product-catalog.git", "reference": "04107e8c2ca7750f342e9007c20fc96ad5e6bf89"}, "require": {"buxus-libs/analytics": "^1.1.10", "buxus-libs/ciselniky": "^1.1.0", "buxus-libs/dynamic-categories": "^2.2.8", "buxus-libs/fs": "^5.1", "buxus-libs/fulltext-search": "^3.0.12", "buxus-libs/seo": "^1.0.3", "buxus-libs/tree-property": "^2.0.0", "buxus/core": "^7.1.0", "buxus/eshop": "^1.4.31", "buxus/legacy-base": "^2.1.27", "buxus/lib-support": "~1.3 || ^2.0.0", "buxus/migrations": "^1.0.0", "buxus/modules": "^1.5.0", "buxus/multisite": "^1.0.0", "buxus/pages": "~3.3 || ^4.0.0", "buxus/translate": "^2.0.0", "buxus/util": "^2.2.16", "ext-json": "*", "ext-pcre": "*", "illuminate/support": "5.8.* || ^6.0", "zendframework/zendframework1": "^1.13.0"}, "suggest": {"buxus-libs/opengraph": "^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4.x-dev"}, "laravel": {"providers": ["Eshop\\Catalog\\ProductCatalogModule"]}}, "autoload": {"psr-4": {"Eshop\\Catalog\\": "src/"}}, "notification-url": "https://packagist.ui42.sk/downloads/", "license": ["proprietary"], "description": "Product catalog/portfolio for CMS BUXUS", "time": "2021-01-29T10:52:39+01:00"}, {"name": "buxus-libs/seo", "version": "1.0.13", "source": {"type": "git", "url": "******************:buxus-libs/seo-module.git", "reference": "8dcc457e0040490cc03806001502eedcb47a6c87"}, "require": {"buxus/core": "^7.0.0", "buxus/modules": "^1.2.0", "buxus/multisite": "~1.1.18 || ^1.2.5", "buxus/pages": "~3.0 || ^4.0.0", "buxus/translate": "~2.0.39 || ^2.1.6", "buxus/util": "^2.0.0", "spatie/schema-org": "^2.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}, "laravel": {"providers": ["Buxus\\SEO\\SEOModule"], "aliases": {"BuxusSEO": "Buxus\\SEO\\Facade\\BuxusSEO"}}}, "autoload": {"psr-4": {"Buxus\\SEO\\": "src/"}}, "notification-url": "https://packagist.ui42.sk/downloads/", "license": ["proprietary"], "description": "SEO module for CMS BUXUS", "time": "2021-02-22T16:54:04+01:00"}, {"name": "buxus-libs/shopping-cart", "version": "2.2.21", "source": {"type": "git", "url": "******************:buxus-libs/shopping-cart.git", "reference": "fe50e5c78e804294d4a2a94a6c3a4b187198f3da"}, "require": {"buxus-libs/analytics": "^1.0.0", "buxus-libs/email": "^2.0.0", "buxus-libs/form-base": "^1.3", "buxus-libs/persistence": "^1.0.0", "buxus-libs/product-catalog": "^1.1.0", "buxus-libs/seo": "^1.0.0", "buxus/core": "^7.1.0", "buxus/email-core": "^1.0.0", "buxus/eshop": "~1.5.7 || ^1.6.11", "buxus/events": "^1.1.1", "buxus/legacy-base": "^2.1.1", "buxus/migrations": "^1.0.0", "buxus/modules": "^1.5.0", "buxus/multisite": "^1.0.0", "buxus/pages": "~3.3 || ^4.0.0", "buxus/substrate": "~1.0.19 || ^1.1.13", "buxus/translate": "^2.0.0", "buxus/util": "^2.0.0", "buxus/webuser": "^2.3.8", "ext-date": "*", "ext-pcre": "*", "illuminate/http": "5.8.* || ^6.0", "zendframework/zendframework1": "^1.13.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2.x-dev"}, "laravel": {"providers": ["Eshop\\ShoppingCart\\ShoppingCartModule"]}}, "autoload": {"psr-4": {"Eshop\\ShoppingCart\\": "src/"}}, "notification-url": "https://packagist.ui42.sk/downloads/", "license": ["proprietary"], "description": "Shopping cart module for CMS BUXUS", "time": "2020-12-28T16:13:45+01:00"}, {"name": "buxus-libs/tree-property", "version": "2.1.8", "source": {"type": "git", "url": "******************:buxus/lib-tree-property.git", "reference": "11fd34df8db33d95ffb51b19b5f8ffa44fd1de38"}, "require": {"buxus/cache": "^3.0.0", "buxus/core": "~7.1.16 || ^7.2.26", "buxus/legacy-base": "^2.1.1", "buxus/migrations": "^1.0.0", "buxus/modules": "^1.5.0", "buxus/pages": "~3.3 || ^4.0.0", "illuminate/support": "5.8.* || ^6.0 || ^7.0 || ^8.0"}, "conflict": {"buxus/legacy": "<1.0.9"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1.x-dev"}, "laravel": {"providers": ["TreeProperty\\TreePropertyModule"]}}, "autoload": {"psr-4": {"TreeProperty\\": "src/"}}, "notification-url": "https://packagist.ui42.sk/downloads/", "license": ["proprietary"], "description": "Support for tree overrideable template based property values", "time": "2021-01-28T14:26:15+01:00"}, {"name": "buxus/buxus-registrator-plugin", "version": "1.2.2", "source": {"type": "git", "url": "******************:buxus-libs/buxus-registrator-plugin.git", "reference": "a5161672f713ba6c2dacbd299ac941a84710e580"}, "dist": {"type": "zip", "url": "https://packages.ui42.sk/api/v4/projects/buxus-libs%2Fbuxus-registrator-plugin/repository/archive.zip?sha=a5161672f713ba6c2dacbd299ac941a84710e580", "reference": "a5161672f713ba6c2dacbd299ac941a84710e580", "shasum": ""}, "require": {"composer-plugin-api": "^1.0 || ^2.0", "vlucas/phpdotenv": "^3.3"}, "type": "composer-plugin", "extra": {"class": "\\Buxus\\Composer\\Registrator\\BuxusRegistratorPlugin", "branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"psr-4": {"Buxus\\Composer\\Registrator\\": "src/"}}, "notification-url": "https://packagist.ui42.sk/downloads/", "license": ["proprietary"], "description": "BUXUS registrator composer plugin", "time": "2023-11-06T10:03:54+00:00"}, {"name": "buxus/bx-api", "version": "1.1.3", "source": {"type": "git", "url": "******************:buxus7/bx-api.git", "reference": "f9e0450c6559709de8ae5de686a03a5214fe5610"}, "require": {"buxus/core": "^7.1.0", "buxus/modules": "^1.3.2", "dingo/api": "~2.4.0", "tymon/jwt-auth": "^1.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}, "laravel": {"providers": ["Buxus\\Api\\BuxusJWTAuthServiceProvider", "Buxus\\Api\\BuxusDingoApiServiceProvider", "Buxus\\Api\\BuxusApiModule"], "aliases": {"API": "Dingo\\Api\\Facade\\API", "APIRoute": "Dingo\\Api\\Facade\\Route", "JWTAuth": "Tymon\\JWTAuth\\Facades\\JWTAuth", "JWTFactory": "Tymon\\JWTAuth\\Facades\\JWTFactory"}, "dont-discover": ["dingo/api", "tymon/jwt-auth"]}}, "autoload": {"psr-4": {"Buxus\\Api\\": "src/"}}, "notification-url": "https://packagist.ui42.sk/downloads/", "license": ["proprietary"], "description": "BUXUS CMS API package", "homepage": "http://www.buxus.sk/", "keywords": ["API", "BUXUS", "backend"], "time": "2021-01-15T15:43:15+01:00"}, {"name": "buxus/bx-dashboard", "version": "1.0.4", "source": {"type": "git", "url": "******************:buxus/bx-dashboard.git", "reference": "ba804ba4d4c2c8597da8037f363f50086586f328"}, "require": {"buxus/bx-html": "^1.0.0", "buxus/core": "^7.2.21", "buxus/modules": "^1.3.2", "buxus/user": "^2.3.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Buxus\\Dashboard\\": "src/"}}, "notification-url": "https://packagist.ui42.sk/downloads/", "license": ["proprietary"], "description": "BUXUS CMS admin dashboard", "homepage": "http://www.buxus.sk/", "keywords": ["BUXUS", "admin", "backend", "dashboard"], "time": "2020-08-25T11:15:05+02:00"}, {"name": "buxus/bx-html", "version": "1.1.1", "source": {"type": "git", "url": "******************:buxus7/bx-html.git", "reference": "0e58faa81859481d81f70245839065fb4e6951bd"}, "require": {"buxus/core": "^7.1.0", "illuminate/support": "5.8.* || ^6.0", "illuminate/view": "5.8.* || ^6.0", "laravelcollective/html": "5.8.* || ^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Buxus\\Html\\": "src/"}}, "notification-url": "https://packagist.ui42.sk/downloads/", "license": ["proprietary"], "time": "2019-12-03T23:54:14+01:00"}, {"name": "buxus/bx-testing", "version": "1.1.6", "source": {"type": "git", "url": "******************:buxus/bx-testing.git", "reference": "ba6e4eb42e530ee87c695110561a47925aeb682d"}, "require": {"buxus/eshop": "^1.6.6", "illuminate/support": "5.8.* || ^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}, "laravel": {"providers": ["Buxus\\Testing\\TestingModule"], "aliases": {"AcceptanceTesting": "Buxus\\Testing\\Facades\\AcceptanceTesting"}}}, "autoload": {"psr-4": {"Buxus\\Testing\\": "src/"}}, "notification-url": "https://packagist.ui42.sk/downloads/", "license": ["proprietary"], "description": "Library to support testing in Buxus", "keywords": ["acceptance", "selenium", "test", "testing"], "time": "2020-05-22T07:23:56+02:00"}, {"name": "buxus/bx-widgets", "version": "1.0.2", "source": {"type": "git", "url": "******************:buxus7/bx-widgets.git", "reference": "c5cccb7cded64db36f72d8c5e75672a9c91d203d"}, "require": {"buxus/core": "^7.1.0", "illuminate/console": "5.8.* || ^6.0", "illuminate/support": "5.8.* || ^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}, "laravel": {"providers": ["Buxus\\Widgets\\BuxusWidgetsServiceProvider"], "aliases": {"Widget": "Buxus\\Widgets\\Facade"}}}, "autoload": {"psr-4": {"Buxus\\Widgets\\": "src/"}}, "notification-url": "https://packagist.ui42.sk/downloads/", "license": ["proprietary"], "description": "widgets for BUXUS CMS", "homepage": "http://www.buxus.sk/", "keywords": ["BUXUS", "admin", "blade", "view", "widget"], "time": "2020-10-29T08:27:52+01:00"}, {"name": "buxus/cache", "version": "3.1.3", "source": {"type": "git", "url": "******************:buxus/lib-cache.git", "reference": "c8092ab398b16247b5c8af7093cef80e6c1afc11"}, "require": {"buxus/core": "^7.1.9 || ^7.2.3", "buxus/events": "^1.0.0", "buxus/legacy-base": "^2.1.0", "buxus/modules": "^1.5.0", "buxus/multisite": "^1.3.0", "buxus/util": "^2.0.0", "ext-date": "*", "ext-gd": "*", "ext-memcached": "*", "ext-pcre": "*", "ext-spl": "*", "illuminate/cache": "5.8.* || ^6.0", "illuminate/contracts": "5.8.* || ^6.0", "illuminate/queue": "5.8.* || ^6.0", "illuminate/support": "5.8.* || ^6.0", "php": "^7.1", "symfony/console": "^4.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1.x-dev"}, "laravel": {"aliases": {"BuxusCache": "Buxus\\Cache\\Facades\\BuxusCache", "PageBoundCache": "Buxus\\Cache\\Facades\\PageBoundCache", "GenericValueCache": "Buxus\\Cache\\Facades\\GenericValueCache", "CacheKeyManager": "Buxus\\Cache\\Facades\\CacheKeyManager"}}}, "autoload": {"psr-4": {"Buxus\\Cache\\": "src/"}}, "notification-url": "https://packagist.ui42.sk/downloads/", "license": ["proprietary"], "description": "Cache support for CMS BUXUS", "homepage": "http://www.buxus.sk/", "keywords": ["BUXUS", "buxus utils", "cache", "ui42"], "time": "2021-02-16T06:15:15+01:00"}, {"name": "buxus/captcha", "version": "1.1.8", "source": {"type": "git", "url": "******************:buxus-libs/captcha.git", "reference": "eba8eb81dfe00f5323a8adea90807538353c13b1"}, "require": {"buxus/core": "^7.1.0", "buxus/modules": "^1.5.0", "guzzlehttp/guzzle": "^6.3.0", "illuminate/support": "^5.0 || ^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}, "laravel": {"providers": ["Buxus\\Captcha\\CaptchaModule"]}}, "autoload": {"psr-4": {"Buxus\\Captcha\\": "src/"}}, "notification-url": "https://packagist.ui42.sk/downloads/", "license": ["proprietary"], "description": "CAPTCHA support for BUXUS CMS", "homepage": "http://www.buxus.sk/", "keywords": ["BUXUS", "<PERSON><PERSON>a", "form", "turing test", "ui42"], "time": "2021-02-12T15:20:45+01:00"}, {"name": "buxus/code-generator", "version": "1.0.11", "source": {"type": "git", "url": "******************:buxus/lib-code-generator.git", "reference": "7311ac83ad22ecf5cbc7096e7fc5aa915cc32fcb"}, "require-dev": {"phpunit/phpunit": "^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Buxus\\Util\\CodeGenerator\\": "src/"}}, "notification-url": "https://packagist.ui42.sk/downloads/", "license": ["proprietary"], "description": "Utility code generator for CMS BUXUS", "homepage": "http://www.buxus.sk/", "keywords": ["BUXUS", "code generation", "ui42"], "time": "2019-12-10T23:49:36+01:00"}, {"name": "buxus/core", "version": "7.2.26", "source": {"type": "git", "url": "******************:buxus7/core.git", "reference": "fecaf99cee50d718b66d96b6c8212cd5cb5664f6"}, "require": {"buxus/buxus-registrator-plugin": "^1.2.0", "buxus/bx-dashboard": "^1.0.0", "buxus/bx-html": "^1.1.0", "buxus/cache": "^3.0.0", "buxus/events": "^1.5.0", "buxus/image-processor": "^1.2.0", "buxus/legacy": "^1.1.0", "buxus/legacy-base": "^2.1.49", "buxus/migrations": "^1.7.0", "buxus/modules": "^1.5.0", "buxus/multisite": "^1.2.0", "buxus/pages": "~3.3 || ^4.0.0", "buxus/queue": "^1.3.0", "buxus/seourl-legacy": "^2.1.0", "buxus/toolbar": "^1.0.6", "buxus/user": "^2.3.3", "buxus/util": "^2.2.0", "facade/ignition": "^1.4", "graham-campbell/exceptions": "^12.0.0", "illuminate/container": "^6.0", "illuminate/database": "^6.0", "illuminate/session": "^6.0", "symfony/console": "^4.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "7.2.x-dev"}, "laravel": {"providers": ["Buxus\\BuxusBaseModules"], "aliases": {"Buxus": "Buxus\\Core\\Facades\\Buxus", "BuxusMVC": "Buxus\\Core\\Facades\\BuxusMVC", "BuxusAuthentication": "Buxus\\Core\\Facades\\BuxusAuthentication", "BuxusForm": "Buxus\\Html\\BuxusFormFacade", "BuxusHtml": "Buxus\\Html\\BuxusHtmlFacade"}}}, "autoload": {"psr-4": {"Buxus\\": "src/"}, "files": ["src/buxus_init.php", "src/buxus_helpers.php"]}, "notification-url": "https://packagist.ui42.sk/downloads/", "license": ["proprietary"], "description": "Core CMS BUXUS module", "time": "2021-01-26T10:10:40+01:00"}, {"name": "buxus/email-core", "version": "1.1.10", "source": {"type": "git", "url": "******************:buxus/email-core.git", "reference": "81e2e217d07cba65d30c8c5d951505aca4f4103f"}, "require": {"buxus/modules": "^1.2.0", "buxus/util": "~2.0.29 || ~2.1.5 || ^2.2.13", "phpmailer/phpmailer": "^5.2.7"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Buxus\\Email\\": "src/"}}, "notification-url": "https://packagist.ui42.sk/downloads/", "license": ["proprietary"], "description": "Core e-mail support for CMS BUXUS", "homepage": "http://www.buxus.sk/", "keywords": ["BUXUS", "e-mail", "email", "smtp", "ui42"], "time": "2020-11-05T15:14:47+01:00"}, {"name": "buxus/eshop", "version": "1.6.45", "source": {"type": "git", "url": "******************:buxus/eshop.git", "reference": "0314e45081d9b6cde70b617ba3d10905e73f16db"}, "require": {"buxus-libs/analytics": "^1.1.10", "buxus-libs/email": "^2.0", "buxus-libs/form-base": "~1.3", "buxus-libs/persistence": "~1.1", "buxus/bx-api": "^1.0", "buxus/bx-testing": "^1.1.3", "buxus/cache": "^3.0.0", "buxus/core": "^7.2.3 || ~7.1.9", "buxus/email-core": "~1.1", "buxus/gdpr": "^1.0.0", "buxus/legacy-base": "^1.1.9 || ^2.0.0", "buxus/modules": "^1.3.2", "buxus/multisite": "^1.2.5 || ~1.1.18", "buxus/pages": "~3.3.22 || ^4.0.13", "buxus/substrate": "^1.1.19", "buxus/translate": "~2.0", "buxus/util": "^2.2.13", "illuminate/support": "5.8.* || ^6.0", "mpdf/mpdf": "^7.0.0 || ^8.0.0", "php": "^7.1.0"}, "conflict": {"buxus-libs/shopping-cart": "<=2.2.10"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.6.x-dev"}, "laravel": {"providers": ["Buxus\\Eshop\\EshopModule"], "aliases": {"ShoppingCart": "Buxus\\Eshop\\Facades\\ShoppingCart", "Oraculum": "Buxus\\Eshop\\Facades\\Oraculum", "EshopRenderer": "Buxus\\Eshop\\Facades\\EshopRenderer", "ProductFactory": "Buxus\\Eshop\\Facades\\ProductFactory", "OrderFactory": "Buxus\\Eshop\\Facades\\OrderFactory", "PriceViewer": "Buxus\\Eshop\\Facades\\PriceViewer", "EshopTesting": "Buxus\\Eshop\\Facades\\EshopTesting"}}}, "autoload": {"psr-4": {"Buxus\\Eshop\\": "src/"}, "classmap": ["buxus"]}, "notification-url": "https://packagist.ui42.sk/downloads/", "license": ["proprietary"], "description": "Basic eshop module for CMS BUXUS", "keywords": ["BUXUS", "commerce", "e-commerce", "eshop", "shop", "ui42"], "time": "2021-02-17T15:43:55+01:00"}, {"name": "buxus/eshop-legacy", "version": "1.2.2", "source": {"type": "git", "url": "******************:buxus-libs/eshop-legacy.git", "reference": "57dc8a64b60508377341dc3f6c74d1593df53c5a"}, "require": {"buxus/core": "^7.1.0", "buxus/email-core": "^1.1.0", "buxus/modules": "^1.5.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}, "laravel": {"providers": ["Buxus\\EshopLegacy\\BuxusEshopLegacyModule"]}}, "autoload": {"psr-4": {"Buxus\\EshopLegacy\\": "src/"}, "classmap": ["buxus"]}, "notification-url": "https://packagist.ui42.sk/downloads/", "license": ["proprietary"], "description": "Legacy eshop functions for CMS BUXUS", "time": "2020-04-16T15:46:02+02:00"}, {"name": "buxus/events", "version": "1.5.1", "source": {"type": "git", "url": "******************:buxus/lib-events.git", "reference": "65d820400f5e2af26cfcf6bbd8e47f533571aede"}, "require": {"buxus/modules": "^1.5.0", "illuminate/events": "5.8.* || ^6.0"}, "conflict": {"buxus/core": "<=7.2.17"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.5.x-dev"}, "laravel": {"aliases": {"BuxusEvent": "Buxus\\Event\\BuxusEvent"}}}, "autoload": {"psr-4": {"Buxus\\Event\\": "src/"}}, "notification-url": "https://packagist.ui42.sk/downloads/", "license": ["proprietary"], "description": "Events for CMS BUXUS", "homepage": "http://www.buxus.sk/", "keywords": ["BUXUS", "event", "ui42"], "time": "2021-02-12T10:16:21+01:00"}, {"name": "buxus/gdpr", "version": "1.2.5", "source": {"type": "git", "url": "******************:buxus-libs/gdpr.git", "reference": "e8bddc859a5fb74b9e5e0793743b9962baaacccc"}, "require": {"buxus-libs/email": "^2.0.0", "buxus-libs/form-base": "^1.0.0", "buxus-libs/seo": "^1.0.0", "buxus/core": "^7.1.0", "buxus/gdpr-core": "^1.0.0", "buxus/queue": "^1.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}, "laravel": {"providers": ["Buxus\\GDPR\\BuxusGDPRModule"], "aliases": {"GDPR": "Buxus\\GDPR\\Facade\\GDPRFacade"}}}, "autoload": {"psr-4": {"Buxus\\GDPR\\": "src/"}, "files": ["src/gdpr_helpers.php"]}, "notification-url": "https://packagist.ui42.sk/downloads/", "license": ["proprietary"], "description": "BUXUS GDPR integration", "keywords": ["BUXUS", "gdpr", "ui42"], "time": "2020-04-08T13:32:22+02:00"}, {"name": "buxus/gdpr-core", "version": "1.0.2", "source": {"type": "git", "url": "******************:buxus-libs/gdpr-core.git", "reference": "d88fa9c38d147be1c9baef191bde3792478a917c"}, "require": {"elasticsearch/elasticsearch": "^1.4.1 || ^2.3.2 || ^5.3.2 || ^6.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Buxus\\GDPR\\Core\\": "src/"}}, "notification-url": "https://packagist.ui42.sk/downloads/", "license": ["proprietary"], "description": "GDPR core API for BUXUS", "time": "2018-06-18T15:07:49+02:00"}, {"name": "buxus/image-processor", "version": "1.2.30", "source": {"type": "git", "url": "******************:buxus/lib-image-processor.git", "reference": "9e664e86fefa65a86cd9609949bd8098d7026a5b"}, "require": {"buxus/modules": "^1.2.0", "buxus/util": "^2.0.0", "ext-imagick": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"psr-4": {"Buxus\\ImageProcessor\\": "src/"}}, "notification-url": "https://packagist.ui42.sk/downloads/", "license": ["proprietary"], "description": "Image processling library for resizing and other image operations", "homepage": "http://www.buxus.sk/", "keywords": ["BUXUS", "image", "imageoperations", "imagick", "resize", "ui42"], "time": "2021-03-01T11:13:41+01:00"}, {"name": "buxus/legacy", "version": "1.1.20", "source": {"type": "git", "url": "******************:buxus7/buxus-legacy.git", "reference": "21f249cceae40bf6708522874dc476183dfc4bae"}, "dist": {"type": "zip", "url": "https://packages.ui42.sk/api/v4/projects/buxus7%2Fbuxus-legacy/repository/archive.zip?sha=21f249cceae40bf6708522874dc476183dfc4bae", "reference": "21f249cceae40bf6708522874dc476183dfc4bae", "shasum": ""}, "require": {"buxus/core": "^7.2.23", "buxus/legacy-base": "^2.1.15", "buxus/modules": "^1.5.0", "buxus/pages": "~3.3 || ^4.0.0", "buxus/user": "^2.2.0", "zendframework/zendframework1": "^1.14"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Buxus\\Legacy\\": "src/"}}, "notification-url": "https://packagist.ui42.sk/downloads/", "license": ["proprietary"], "description": "BUXUS7 module containing support for legacy code", "time": "2023-10-31T16:49:24+00:00"}, {"name": "buxus/legacy-base", "version": "2.1.65", "source": {"type": "git", "url": "******************:buxus7/buxus-legacy-base.git", "reference": "eba625d0c71b254dc4b5236de5ad67a0c473297a"}, "require": {"buxus/buxus-registrator-plugin": "^1.2.0", "buxus/bx-dashboard": "^1.0.4", "buxus/core": "~7.1.13 || ^7.2.16", "buxus/email-core": "^1.1.8", "buxus/legacy": "^1.1.3", "buxus/modules": "^1.5.0", "buxus/multisite": "^1.2.2", "buxus/pages": "^4.0.0", "buxus/seourl-legacy": "^2.1.0", "buxus/toolbar": "^1.0.1", "buxus/user": "^2.3.3", "buxus/util": "^2.2.3", "buxus/webuser": "^2.3.0", "doctrine/dbal": "~2.6", "ext-bcmath": "*", "illuminate/pagination": "5.8.* || ^6.0", "sentry/sentry": "^1.6.2", "solarium/solarium": "^5.1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1.x-dev"}}, "autoload": {"psr-4": {"Buxus\\": "src/Buxus/"}, "psr-0": {"Buxus_": "src/"}, "classmap": ["buxus/"], "exclude-from-classmap": ["/buxus/system/convert_image_resizer_to_procesor.php"], "files": ["src/legacy_base_init.php"]}, "notification-url": "https://packagist.ui42.sk/downloads/", "include-path": ["buxus/includes/"], "license": ["proprietary"], "time": "2021-02-22T10:11:37+01:00"}, {"name": "buxus/lib-support", "version": "2.0.1", "source": {"type": "git", "url": "******************:buxus/lib-support.git", "reference": "eb41f74fc7823c96e91ee53650cf07767bb564db"}, "require": {"buxus/core": "^7.1.0", "buxus/legacy-base": "^2.0.0", "buxus/modules": "^1.2.0", "buxus/pages": "^4.0.0", "buxus/util": "^2.2.0", "illuminate/contracts": "5.8.* || ^6.0", "illuminate/support": "5.8.* || ^6.0", "zendframework/zendframework1": "^1.13.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}, "laravel": {"providers": ["Buxus\\LibrarySupport\\LibrarySupportModule"]}}, "autoload": {"psr-4": {"Buxus\\LibrarySupport\\": "src/"}}, "notification-url": "https://packagist.ui42.sk/downloads/", "license": ["proprietary"], "description": "Support module for other packages", "time": "2020-05-06T12:30:27+02:00"}, {"name": "buxus/migrations", "version": "1.7.8", "source": {"type": "git", "url": "******************:buxus/lib-migrations.git", "reference": "85858bed0a2555430bacc35b4929f8cd1cd69b4a"}, "require": {"buxus/code-generator": "^1.0.8", "buxus/core": "^7.1.0", "buxus/legacy-base": "^2.1.0", "buxus/modules": "^1.5.0", "buxus/pages": "~3.3 || ^4.0.0", "buxus/util": "^2.0.21", "ext-date": "*", "ext-json": "*", "ext-pcre": "*", "ext-reflection": "*", "illuminate/console": "^6.0", "illuminate/contracts": "^6.0", "illuminate/database": "^6.0", "illuminate/filesystem": "^6.0", "illuminate/http": "^6.0", "illuminate/support": "^6.0", "symfony/console": "^4.2", "symfony/finder": "^4.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.7.x-dev"}}, "autoload": {"psr-4": {"Buxus\\Migration\\": "src/"}}, "notification-url": "https://packagist.ui42.sk/downloads/", "license": ["proprietary"], "description": "Migrations for CMS BUXUS", "homepage": "http://www.buxus.sk/", "keywords": ["BUXUS", "migrations", "ui42"], "time": "2020-11-20T18:55:42+01:00"}, {"name": "buxus/modules", "version": "1.5.1", "source": {"type": "git", "url": "******************:buxus/lib-modules.git", "reference": "e0c45ea3f2c16f78f0cd29b9f801484ad2ef57fc"}, "require": {"buxus/core": "^7.1.0", "ext-spl": "*", "illuminate/container": "5.8.* || ^6.0", "illuminate/support": "5.8.* || ^6.0", "zendframework/zendframework1": "^1.13.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.5.x-dev"}}, "autoload": {"psr-4": {"Buxus\\Module\\": "src/"}}, "notification-url": "https://packagist.ui42.sk/downloads/", "license": ["proprietary"], "description": "Module support for CMS BUXUS", "homepage": "http://www.buxus.sk/", "keywords": ["BUXUS", "module", "ui42"], "time": "2019-12-03T23:42:42+01:00"}, {"name": "buxus/multisite", "version": "1.3.2", "source": {"type": "git", "url": "******************:buxus/multisite.git", "reference": "6a562a057c45dca0e1544c9636d4579b124c65d3"}, "require": {"buxus/cache": "^3.1.0", "buxus/core": "^7.1.0", "buxus/events": "^1.0.0", "buxus/legacy-base": "^2.1.23", "buxus/lib-support": "~1.0 || ^2.0.0", "buxus/migrations": "^1.0.0", "buxus/modules": "^1.5.0", "buxus/pages": "~3.3.13 || ^4.0.0", "buxus/substrate": "^1.1.17", "buxus/util": "^2.0.0", "ext-date": "*", "ext-pcre": "*", "illuminate/support": "5.8.* || ^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}, "laravel": {"providers": ["Buxus\\Site\\BuxusMultisiteModule"], "aliases": {"BuxusSite": "Buxus\\Site\\Facade\\BuxusSite"}}}, "autoload": {"psr-4": {"Buxus\\Site\\": "src/"}, "files": ["src/helpers.php"]}, "notification-url": "https://packagist.ui42.sk/downloads/", "license": ["proprietary"], "description": "Multisite support CMS BUXUS", "keywords": ["BUXUS", "domains", "multidomain", "multisite", "sites", "ui42"], "time": "2020-07-27T10:58:05+02:00"}, {"name": "buxus/pages", "version": "4.0.14", "source": {"type": "git", "url": "******************:buxus/lib-pages.git", "reference": "9f11feb443377762de8787892e35d90477d1eca9"}, "require": {"buxus/cache": "^3.0.0", "buxus/core": "^7.2.22", "buxus/events": "~1.2", "buxus/legacy-base": "^2.1.0", "buxus/migrations": "^1.0.0", "buxus/modules": "^1.5.0", "buxus/user": "^2.0.0", "buxus/util": "~2.0.29 || ~2.1.5 || ^2.2.13", "ext-date": "*", "ext-igbinary": "*", "ext-json": "*", "ext-mbstring": "*", "ext-pcre": "*", "ext-posix": "*", "ext-reflection": "*", "ext-simplexml": "*", "ext-spl": "*", "illuminate/container": "5.8.* || ^6.0", "illuminate/contracts": "5.8.* || ^6.0", "illuminate/database": "5.8.* || ^6.0", "illuminate/support": "5.8.* || ^6.0", "symfony/console": "^4.2"}, "require-dev": {"phpunit/phpunit": "^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0.x-dev"}, "buxus-docs": {"Wiki - typy a vlastnosti": "https://phabricator.ui42.sk/w/buxus/knowledge-base/vlastnosti-a-typy-stranok/", "Wiki - práca so stránkami": "https://phabricator.ui42.sk/w/buxus/knowledge-base/praca-so-strankami/", "Wiki - konštanty": "https://phabricator.ui42.sk/w/buxus/knowledge-base/konstanty/"}, "laravel": {"aliases": {"PageFactory": "Buxus\\Page\\Facades\\PageFactory", "PageHelper": "Buxus\\Page\\Facades\\PageHelper"}}}, "autoload": {"psr-4": {"Buxus\\": "src/Buxus/"}}, "notification-url": "https://packagist.ui42.sk/downloads/", "license": ["proprietary"], "description": "Pages, types and properties support for CMS BUXUS", "homepage": "http://www.buxus.sk/", "keywords": ["BUXUS", "buxus page", "page", "page type", "property", "ui42"], "time": "2021-02-23T15:51:48+01:00"}, {"name": "buxus/queue", "version": "1.3.4", "source": {"type": "git", "url": "******************:buxus-libs/buxus-queue.git", "reference": "0c19659d8196ad149e8d8bfa2aa6f1667353f214"}, "require": {"buxus/cache": "^3.0.0", "buxus/core": "^7.1.14 || ^7.2.17", "buxus/modules": "^1.5.0", "illuminate/console": "5.8.* || ^6.0", "illuminate/contracts": "5.8.* || ^6.0", "illuminate/support": "5.8.* || ^6.0", "symfony/console": "^4.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"psr-4": {"Buxus\\Queue\\": "src/"}, "files": ["src/queue_helpers.php"]}, "notification-url": "https://packagist.ui42.sk/downloads/", "license": ["proprietary"], "description": "Queue support for CMS BUXUS", "homepage": "http://www.buxus.sk/", "keywords": ["BUXUS", "async", "parallel", "queue", "ui42"], "time": "2020-07-28T07:44:33+02:00"}, {"name": "buxus/scheduler", "version": "3.1.2", "source": {"type": "git", "url": "******************:buxus/lib-scheduler.git", "reference": "146e77b440bb05b398e7889a8e515249f9274c2a"}, "require": {"guzzlehttp/guzzle": "~5.3|~6.0", "illuminate/console": "5.8.* || ^6.0", "symfony/process": "^4.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1.x-dev"}}, "autoload": {"psr-4": {"Buxus\\Scheduler\\": "src/"}}, "notification-url": "https://packagist.ui42.sk/downloads/", "license": ["proprietary"], "description": "Scheduler for CMS BUXUS", "homepage": "http://www.buxus.sk/", "keywords": ["BUXUS", "cron", "scheduler", "ui42"], "time": "2020-05-22T14:22:52+02:00"}, {"name": "buxus/seourl-legacy", "version": "2.1.23", "source": {"type": "git", "url": "******************:buxus/lib-seourl-legacy.git", "reference": "7ebd5d5962b9d4faf1da7d5afc39fd47db5d1fda"}, "require": {"buxus/cache": "^3.0.0", "buxus/core": "^7.1.0", "buxus/events": "^1.0.0", "buxus/legacy-base": "^2.1.0", "buxus/migrations": "^1.0.0", "buxus/modules": "^1.5.0", "buxus/multisite": "^1.1", "buxus/pages": "~3.3 || ^4.0.0", "buxus/translate": "^2.1.1", "buxus/util": "~2.0.29 || ~2.1.5 || ^2.2.13", "ext-ctype": "*", "ext-iconv": "*", "ext-intl": "*", "ext-pcre": "*", "illuminate/contracts": "5.8.* || ^6.0", "illuminate/support": "5.8.* || ^6.0", "symfony/polyfill-php73": "^1.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1.x-dev"}}, "autoload": {"psr-4": {"Buxus\\SeoUrlLegacy\\": "src/"}}, "notification-url": "https://packagist.ui42.sk/downloads/", "license": ["proprietary"], "description": "SEO url module for CMS BUXUS", "time": "2021-02-22T15:36:15+01:00"}, {"name": "buxus/substrate", "version": "1.1.21", "source": {"type": "git", "url": "******************:buxus/buxus-substrate.git", "reference": "7acbd344752edaf92da46d4d1fd980cc8045b08f"}, "require": {"buxus/bx-widgets": "^1.0.0", "buxus/core": "^7.1.0", "buxus/legacy": "^1.1.8", "buxus/modules": "^1.5.0", "buxus/multisite": "^1.2.5 || ~1.1.18", "buxus/pages": "~3.3 || ^4.0.0", "buxus/translate": "~2.0.40 || ^2.1.13", "buxus/util": "^2.2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}, "laravel": {"providers": ["Buxus\\Substrate\\BuxusSubstrateModule"], "dont-discover": ["laravelcollective/html"], "aliases": {"Form": "Buxus\\Substrate\\Forms\\Facade\\FormBuilder", "Html": "Buxus\\Substrate\\Forms\\Facade\\HtmlBuilder", "SleepManager": "Buxus\\Substrate\\Sleep\\SleepManager"}}}, "autoload": {"psr-4": {"Buxus\\Substrate\\": "src/"}, "files": ["src/substrate.php"]}, "notification-url": "https://packagist.ui42.sk/downloads/", "license": ["proprietary"], "description": "Base functionality for all BUXUS projects", "homepage": "http://www.buxus.sk/", "keywords": ["BUXUS", "krabica", "main wizard", "substrate", "ui42"], "time": "2021-02-12T17:13:24+01:00"}, {"name": "buxus/toolbar", "version": "1.0.15", "source": {"type": "git", "url": "******************:buxus7/toolbar.git", "reference": "1bb0a5600d0369e208d734555f289c363efb3a15"}, "require": {"buxus/legacy": "^1.0.0", "buxus/pages": "~3.1.25 || ~3.2.39 || ~3.3 || ^4.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Buxus\\Toolbar\\": "src/"}, "classmap": ["buxus/"]}, "notification-url": "https://packagist.ui42.sk/downloads/", "license": ["proprietary"], "description": "BUXUS CMS toolbar package for in page editing", "time": "2020-12-11T16:24:00+01:00"}, {"name": "buxus/translate", "version": "2.2.5", "source": {"type": "git", "url": "******************:buxus/lib-translate.git", "reference": "ba2dc4528dd04b7bfcd49df9e948f9e4f74829ad"}, "require": {"buxus/cache": "^3.1.0", "buxus/code-generator": "^1.0.0", "buxus/core": "^7.1.0", "buxus/legacy-base": "^2.1.0", "buxus/lib-support": "~1.2 || ^2.0.0", "buxus/migrations": "^1.4.0", "buxus/modules": "^1.5.0", "buxus/multisite": "^1.0.0", "buxus/pages": "~3.3.13 || ^4.0.0", "buxus/seourl-legacy": "^2.1.0", "buxus/substrate": "^1.1.17", "buxus/util": "^2.0.0", "ext-date": "*", "ext-json": "*", "ext-mbstring": "*", "ext-pcre": "*", "ext-spl": "*", "ext-zend-opcache": "*", "illuminate/contracts": "5.8.* || ^6.0", "illuminate/database": "5.8.* || ^6.0", "illuminate/http": "5.8.* || ^6.0", "illuminate/support": "5.8.* || ^6.0", "illuminate/translation": "5.8.* || ^6.0", "phpoffice/phpspreadsheet": "^1.1.0", "symfony/console": "^4.2", "zendframework/zendframework1": "^1.13.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2.x-dev"}, "laravel": {"aliases": {"Trans": "Buxus\\Translate\\Facade\\Trans"}}}, "autoload": {"psr-4": {"Buxus\\Translate\\": "src/"}, "files": ["src/setup_translate.php"]}, "notification-url": "https://packagist.ui42.sk/downloads/", "license": ["proprietary"], "description": "Language translation for CMS BUXUS", "homepage": "http://www.buxus.sk/", "keywords": ["BUXUS", "language", "multilingual", "translation", "ui42"], "time": "2021-02-08T10:22:29+01:00"}, {"name": "buxus/user", "version": "2.3.3", "source": {"type": "git", "url": "******************:buxus/lib-user.git", "reference": "99275b0841be97a67261db4b0252c6b3d91fe46d"}, "require": {"buxus/core": "^7.1.0", "buxus/modules": "^1.5.0"}, "conflict": {"buxus/legacy-base": "<2.1.51"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.3.x-dev"}, "laravel": {"aliases": {"BuxusUserManager": "Buxus\\User\\Facades\\BuxusUserManager"}}}, "autoload": {"psr-4": {"Buxus\\User\\": "src/"}}, "notification-url": "https://packagist.ui42.sk/downloads/", "license": ["proprietary"], "description": "CMS users (editors) for CMS BUXUS", "homepage": "http://www.buxus.sk/", "keywords": ["BUXUS", "BUXUS user", "CMS user", "buxus utils", "editor", "ui42"], "time": "2020-11-11T17:54:38+01:00"}, {"name": "buxus/util", "version": "2.2.19", "source": {"type": "git", "url": "******************:buxus/util-lib.git", "reference": "4ca1ad7c9527ca1b316216dc18d3a2773f258cfc"}, "require": {"buxus/core": "^7.1.6", "buxus/image-processor": "^1.0.0", "buxus/legacy-base": "^2.1.0", "buxus/modules": "^1.5.0", "buxus/multisite": "^1.2.2", "buxus/pages": "~3.3 || ^4.0.0", "ext-curl": "*", "ext-date": "*", "ext-mbstring": "*", "ext-pcre": "*", "ext-spl": "*", "ext-tokenizer": "*", "illuminate/container": "5.8.* || ^6.0", "illuminate/support": "5.8.* || ^6.0", "league/flysystem": "^1.0.0", "ralouphie/mimey": "^1.0.8", "zendframework/zendframework1": "^1.13.0"}, "require-dev": {"phpunit/phpunit": "^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2.x-dev"}}, "autoload": {"psr-4": {"Buxus\\View\\": "src/Buxus/View/", "Buxus\\Util\\": "src/Buxus/Util/", "Buxus\\Stdlib\\": "src/Buxus/Stdlib/", "Buxus\\Queue\\": "src/<PERSON>uxus/Queue/"}}, "notification-url": "https://packagist.ui42.sk/downloads/", "license": ["proprietary"], "description": "Utilities for CMS BUXUS", "homepage": "http://www.buxus.sk/", "keywords": ["BUXUS", "buxus utils", "ui42", "utility"], "time": "2020-08-27T18:48:40+02:00"}, {"name": "buxus/webuser", "version": "2.3.13", "source": {"type": "git", "url": "******************:buxus/lib-webuser.git", "reference": "993f07910553f30c1c5a605cd7bbed1fff494d7d"}, "require": {"buxus-libs/email": "^2.1.0", "buxus/core": "^7.1.0", "buxus/email-core": "^1.1.0", "buxus/events": "^1.4.0", "buxus/gdpr": "^1.2.0", "buxus/legacy-base": "^2.1.0", "buxus/modules": "^1.5.0", "illuminate/support": "5.8.* || ^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.3.x-dev"}, "laravel": {"providers": ["Buxus\\WebUser\\WebUserModule"], "aliases": {"WebUserFactory": "Buxus\\WebUser\\Facades\\WebUserFactory", "WebUserAuthentication": "Buxus\\WebUser\\Facades\\WebUserAuthentication"}}}, "autoload": {"psr-4": {"Buxus\\WebUser\\": "src/"}, "classmap": ["buxus"]}, "notification-url": "https://packagist.ui42.sk/downloads/", "license": ["proprietary"], "description": "WebUser module for Buxus CMS", "homepage": "http://www.buxus.sk/", "keywords": ["BUXUS", "WebUser", "authentications", "ui42", "users"], "time": "2021-01-30T17:03:06+01:00"}, {"name": "dingo/api", "version": "v2.4.7", "source": {"type": "git", "url": "https://github.com/dingo/api.git", "reference": "669a5a9f39cf4f499af1b6fcf43c15e364312ad3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dingo/api/zipball/669a5a9f39cf4f499af1b6fcf43c15e364312ad3", "reference": "669a5a9f39cf4f499af1b6fcf43c15e364312ad3", "shasum": ""}, "require": {"dingo/blueprint": "^0.3", "illuminate/routing": "^5.5 || ^6.0", "illuminate/support": "^5.5 || ^6.0", "league/fractal": "^0.17", "php": "^7.1"}, "require-dev": {"friendsofphp/php-cs-fixer": "~2", "illuminate/auth": "^5.5 || ^6.0", "illuminate/cache": "^5.5 || ^6.0", "illuminate/console": "^5.5 || ^6.0", "illuminate/database": "^5.5 || ^6.0", "illuminate/events": "^5.5 || ^6.0", "illuminate/filesystem": "^5.5 || ^6.0", "illuminate/log": "^5.5 || ^6.0", "illuminate/pagination": "^5.5 || ^6.0", "laravel/lumen-framework": "^5.5 || ^6.0", "mockery/mockery": "~1.0", "phpdocumentor/reflection-docblock": "3.3.2", "phpunit/phpunit": "^4.8.35 || ^5.4.3 || ^6.5", "squizlabs/php_codesniffer": "~2.0", "tymon/jwt-auth": "1.0.*"}, "suggest": {"tymon/jwt-auth": "Protect your API with JSON Web Tokens."}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}, "laravel": {"providers": ["Dingo\\Api\\Provider\\LaravelServiceProvider"], "aliases": {"API": "Dingo\\Api\\Facade\\API"}}}, "autoload": {"psr-4": {"Dingo\\Api\\": "src/"}, "files": ["src/helpers.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A RESTful API package for the Laravel and Lumen frameworks.", "keywords": ["api", "dingo", "laravel", "restful"], "support": {"issues": "https://github.com/dingo/api/issues", "source": "https://github.com/dingo/api/tree/v2"}, "time": "2020-03-19T01:36:11+00:00"}, {"name": "dingo/blueprint", "version": "v0.3.1", "source": {"type": "git", "url": "https://github.com/dingo/blueprint.git", "reference": "45bbc59385310de7604e35ea4e27dc1756be9396"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dingo/blueprint/zipball/45bbc59385310de7604e35ea4e27dc1756be9396", "reference": "45bbc59385310de7604e35ea4e27dc1756be9396", "shasum": ""}, "require": {"doctrine/annotations": "~1.2", "illuminate/filesystem": "^5.5 || ^6.0", "illuminate/support": "^5.5 || ^6.0", "php": "^7.1", "phpdocumentor/reflection-docblock": "^3.1|^4.1"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.4.3 || ^6.5", "squizlabs/php_codesniffer": "~2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.2-dev"}}, "autoload": {"psr-4": {"Dingo\\Blueprint\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "API Blueprint documentation generator.", "keywords": ["api", "blueprint", "dingo", "docs", "laravel"], "support": {"issues": "https://github.com/dingo/blueprint/issues", "source": "https://github.com/dingo/blueprint/tree/v3.0.1"}, "time": "2019-10-07T10:14:17+00:00"}, {"name": "dnoegel/php-xdg-base-dir", "version": "v0.1.1", "source": {"type": "git", "url": "https://github.com/dnoegel/php-xdg-base-dir.git", "reference": "8f8a6e48c5ecb0f991c2fdcf5f154a47d85f9ffd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dnoegel/php-xdg-base-dir/zipball/8f8a6e48c5ecb0f991c2fdcf5f154a47d85f9ffd", "reference": "8f8a6e48c5ecb0f991c2fdcf5f154a47d85f9ffd", "shasum": ""}, "require": {"php": ">=5.3.2"}, "require-dev": {"phpunit/phpunit": "~7.0|~6.0|~5.0|~4.8.35"}, "type": "library", "autoload": {"psr-4": {"XdgBaseDir\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "implementation of xdg base directory specification for php", "support": {"issues": "https://github.com/dnoegel/php-xdg-base-dir/issues", "source": "https://github.com/dnoegel/php-xdg-base-dir/tree/v0.1.1"}, "time": "2019-12-04T15:06:13+00:00"}, {"name": "doctrine/annotations", "version": "1.12.1", "source": {"type": "git", "url": "https://github.com/doctrine/annotations.git", "reference": "b17c5014ef81d212ac539f07a1001832df1b6d3b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/annotations/zipball/b17c5014ef81d212ac539f07a1001832df1b6d3b", "reference": "b17c5014ef81d212ac539f07a1001832df1b6d3b", "shasum": ""}, "require": {"doctrine/lexer": "1.*", "ext-tokenizer": "*", "php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/cache": "1.*", "doctrine/coding-standard": "^6.0 || ^8.1", "phpstan/phpstan": "^0.12.20", "phpunit/phpunit": "^7.5 || ^9.1.5"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Annotations\\": "lib/Doctrine/Common/Annotations"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Docblock Annotations Parser", "homepage": "https://www.doctrine-project.org/projects/annotations.html", "keywords": ["annotations", "doc<PERSON>", "parser"], "support": {"issues": "https://github.com/doctrine/annotations/issues", "source": "https://github.com/doctrine/annotations/tree/1.12.1"}, "time": "2021-02-21T21:00:45+00:00"}, {"name": "doctrine/cache", "version": "1.10.2", "source": {"type": "git", "url": "https://github.com/doctrine/cache.git", "reference": "13e3381b25847283a91948d04640543941309727"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/cache/zipball/13e3381b25847283a91948d04640543941309727", "reference": "13e3381b25847283a91948d04640543941309727", "shasum": ""}, "require": {"php": "~7.1 || ^8.0"}, "conflict": {"doctrine/common": ">2.2,<2.4"}, "require-dev": {"alcaeus/mongo-php-adapter": "^1.1", "doctrine/coding-standard": "^6.0", "mongodb/mongodb": "^1.1", "phpunit/phpunit": "^7.0", "predis/predis": "~1.0"}, "suggest": {"alcaeus/mongo-php-adapter": "Required to use legacy MongoDB driver"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.9.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\Cache\\": "lib/Doctrine/Common/Cache"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Cache library is a popular cache implementation that supports many different drivers such as redis, memcache, apc, mongodb and others.", "homepage": "https://www.doctrine-project.org/projects/cache.html", "keywords": ["abstraction", "apcu", "cache", "caching", "couchdb", "memcached", "php", "redis", "xcache"], "support": {"issues": "https://github.com/doctrine/cache/issues", "source": "https://github.com/doctrine/cache/tree/1.10.x"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fcache", "type": "tidelift"}], "time": "2020-07-07T18:54:01+00:00"}, {"name": "doctrine/dbal", "version": "2.12.1", "source": {"type": "git", "url": "https://github.com/doctrine/dbal.git", "reference": "adce7a954a1c2f14f85e94aed90c8489af204086"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/dbal/zipball/adce7a954a1c2f14f85e94aed90c8489af204086", "reference": "adce7a954a1c2f14f85e94aed90c8489af204086", "shasum": ""}, "require": {"doctrine/cache": "^1.0", "doctrine/event-manager": "^1.0", "ext-pdo": "*", "php": "^7.3 || ^8"}, "require-dev": {"doctrine/coding-standard": "^8.1", "jetbrains/phpstorm-stubs": "^2019.1", "phpstan/phpstan": "^0.12.40", "phpunit/phpunit": "^9.4", "psalm/plugin-phpunit": "^0.10.0", "symfony/console": "^2.0.5|^3.0|^4.0|^5.0", "vimeo/psalm": "^3.17.2"}, "suggest": {"symfony/console": "For helpful console commands such as SQL execution and import of files."}, "bin": ["bin/doctrine-dbal"], "type": "library", "extra": {"branch-alias": {"dev-master": "4.0.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\DBAL\\": "lib/Doctrine/DBAL"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Powerful PHP database abstraction layer (DBAL) with many features for database schema introspection and management.", "homepage": "https://www.doctrine-project.org/projects/dbal.html", "keywords": ["abstraction", "database", "db2", "dbal", "ma<PERSON>b", "mssql", "mysql", "oci8", "oracle", "pdo", "pgsql", "postgresql", "queryobject", "sasql", "sql", "sqlanywhere", "sqlite", "sqlserver", "sqlsrv"], "support": {"issues": "https://github.com/doctrine/dbal/issues", "source": "https://github.com/doctrine/dbal/tree/2.12.1"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fdbal", "type": "tidelift"}], "time": "2020-11-14T20:26:58+00:00"}, {"name": "doctrine/event-manager", "version": "1.1.1", "source": {"type": "git", "url": "https://github.com/doctrine/event-manager.git", "reference": "41370af6a30faa9dc0368c4a6814d596e81aba7f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/event-manager/zipball/41370af6a30faa9dc0368c4a6814d596e81aba7f", "reference": "41370af6a30faa9dc0368c4a6814d596e81aba7f", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "conflict": {"doctrine/common": "<2.9@dev"}, "require-dev": {"doctrine/coding-standard": "^6.0", "phpunit/phpunit": "^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\": "lib/Doctrine/Common"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Doctrine Event Manager is a simple PHP event system that was built to be used with the various Doctrine projects.", "homepage": "https://www.doctrine-project.org/projects/event-manager.html", "keywords": ["event", "event dispatcher", "event manager", "event system", "events"], "support": {"issues": "https://github.com/doctrine/event-manager/issues", "source": "https://github.com/doctrine/event-manager/tree/1.1.x"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fevent-manager", "type": "tidelift"}], "time": "2020-05-29T18:28:51+00:00"}, {"name": "doctrine/inflector", "version": "2.0.3", "source": {"type": "git", "url": "https://github.com/doctrine/inflector.git", "reference": "9cf661f4eb38f7c881cac67c75ea9b00bf97b210"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/inflector/zipball/9cf661f4eb38f7c881cac67c75ea9b00bf97b210", "reference": "9cf661f4eb38f7c881cac67c75ea9b00bf97b210", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^7.0", "phpstan/phpstan": "^0.11", "phpstan/phpstan-phpunit": "^0.11", "phpstan/phpstan-strict-rules": "^0.11", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Inflector\\": "lib/Doctrine/Inflector"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Inflector is a small library that can perform string manipulations with regard to upper/lowercase and singular/plural forms of words.", "homepage": "https://www.doctrine-project.org/projects/inflector.html", "keywords": ["inflection", "inflector", "lowercase", "manipulation", "php", "plural", "singular", "strings", "uppercase", "words"], "support": {"issues": "https://github.com/doctrine/inflector/issues", "source": "https://github.com/doctrine/inflector/tree/2.0.x"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Finflector", "type": "tidelift"}], "time": "2020-05-29T15:13:26+00:00"}, {"name": "doctrine/lexer", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/doctrine/lexer.git", "reference": "e864bbf5904cb8f5bb334f99209b48018522f042"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/lexer/zipball/e864bbf5904cb8f5bb334f99209b48018522f042", "reference": "e864bbf5904cb8f5bb334f99209b48018522f042", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^6.0", "phpstan/phpstan": "^0.11.8", "phpunit/phpunit": "^8.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.2.x-dev"}}, "autoload": {"psr-4": {"Doctrine\\Common\\Lexer\\": "lib/Doctrine/Common/Lexer"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Lexer parser library that can be used in Top-Down, Recursive Descent Parsers.", "homepage": "https://www.doctrine-project.org/projects/lexer.html", "keywords": ["annotations", "doc<PERSON>", "lexer", "parser", "php"], "support": {"issues": "https://github.com/doctrine/lexer/issues", "source": "https://github.com/doctrine/lexer/tree/1.2.1"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Flexer", "type": "tidelift"}], "time": "2020-05-25T17:44:05+00:00"}, {"name": "dompdf/dompdf", "version": "v0.8.6", "source": {"type": "git", "url": "https://github.com/dompdf/dompdf.git", "reference": "db91d81866c69a42dad1d2926f61515a1e3f42c5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dompdf/dompdf/zipball/db91d81866c69a42dad1d2926f61515a1e3f42c5", "reference": "db91d81866c69a42dad1d2926f61515a1e3f42c5", "shasum": ""}, "require": {"ext-dom": "*", "ext-mbstring": "*", "phenx/php-font-lib": "^0.5.2", "phenx/php-svg-lib": "^0.3.3", "php": "^7.1"}, "require-dev": {"mockery/mockery": "^1.3", "phpunit/phpunit": "^7.5", "squizlabs/php_codesniffer": "^3.5"}, "suggest": {"ext-gd": "Needed to process images", "ext-gmagick": "Improves image processing performance", "ext-imagick": "Improves image processing performance", "ext-zlib": "Needed for pdf stream compression"}, "type": "library", "extra": {"branch-alias": {"dev-develop": "0.7-dev"}}, "autoload": {"psr-4": {"Dompdf\\": "src/"}, "classmap": ["lib/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "DOMPDF is a CSS 2.1 compliant HTML to PDF converter", "homepage": "https://github.com/dompdf/dompdf", "support": {"issues": "https://github.com/dompdf/dompdf/issues", "source": "https://github.com/dompdf/dompdf/tree/master"}, "time": "2020-08-30T22:54:22+00:00"}, {"name": "dragonmantank/cron-expression", "version": "v2.3.1", "source": {"type": "git", "url": "https://github.com/dragonmantank/cron-expression.git", "reference": "65b2d8ee1f10915efb3b55597da3404f096acba2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/dragonmantank/cron-expression/zipball/65b2d8ee1f10915efb3b55597da3404f096acba2", "reference": "65b2d8ee1f10915efb3b55597da3404f096acba2", "shasum": ""}, "require": {"php": "^7.0|^8.0"}, "require-dev": {"phpunit/phpunit": "^6.4|^7.0|^8.0|^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.3-dev"}}, "autoload": {"psr-4": {"Cron\\": "src/Cron/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/dragonmantank"}], "description": "CRON for PHP: Calculate the next or previous run date and determine if a CRON expression is due", "keywords": ["cron", "schedule"], "support": {"issues": "https://github.com/dragonmantank/cron-expression/issues", "source": "https://github.com/dragonmantank/cron-expression/tree/v2.3.1"}, "funding": [{"url": "https://github.com/dragonmantank", "type": "github"}], "time": "2020-10-13T00:52:37+00:00"}, {"name": "egulias/email-validator", "version": "2.1.25", "source": {"type": "git", "url": "https://github.com/egulias/EmailValidator.git", "reference": "0dbf5d78455d4d6a41d186da50adc1122ec066f4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/egulias/EmailValidator/zipball/0dbf5d78455d4d6a41d186da50adc1122ec066f4", "reference": "0dbf5d78455d4d6a41d186da50adc1122ec066f4", "shasum": ""}, "require": {"doctrine/lexer": "^1.0.1", "php": ">=5.5", "symfony/polyfill-intl-idn": "^1.10"}, "require-dev": {"dominicsayers/isemail": "^3.0.7", "phpunit/phpunit": "^4.8.36|^7.5.15", "satooshi/php-coveralls": "^1.0.1"}, "suggest": {"ext-intl": "PHP Internationalization Libraries are required to use the SpoofChecking validation"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1.x-dev"}}, "autoload": {"psr-4": {"Egulias\\EmailValidator\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "A library for validating emails against several RFCs", "homepage": "https://github.com/egulias/EmailValidator", "keywords": ["email", "emailvalidation", "emailvalidator", "validation", "validator"], "support": {"issues": "https://github.com/egulias/EmailValidator/issues", "source": "https://github.com/egulias/EmailValidator/tree/2.1.25"}, "funding": [{"url": "https://github.com/egulias", "type": "github"}], "time": "2020-12-29T14:50:06+00:00"}, {"name": "elasticsearch/elasticsearch", "version": "v6.8.0", "source": {"type": "git", "url": "https://github.com/elastic/elasticsearch-php.git", "reference": "a524a7d8e5f58fbed0aa87fabc460c48d18108aa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/elastic/elasticsearch-php/zipball/a524a7d8e5f58fbed0aa87fabc460c48d18108aa", "reference": "a524a7d8e5f58fbed0aa87fabc460c48d18108aa", "shasum": ""}, "require": {"ext-json": ">=1.3.7", "ezimuel/ringphp": "^1.1.2", "php": "^7.3 || ^8.0", "psr/log": "~1.0"}, "require-dev": {"doctrine/inflector": "^1.3", "mockery/mockery": "^1.2", "phpstan/phpstan": "^0.12", "phpunit/phpunit": "^9.3", "squizlabs/php_codesniffer": "^3.4", "symfony/finder": "~4.0", "symfony/yaml": "~4.0", "symplify/git-wrapper": "~9.0"}, "suggest": {"ext-curl": "*", "monolog/monolog": "Allows for client-level logging and tracing"}, "type": "library", "autoload": {"files": ["src/autoload.php"], "psr-4": {"Elasticsearch\\": "src/Elasticsearch/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}], "description": "PHP Client for Elasticsearch", "keywords": ["client", "elasticsearch", "search"], "support": {"issues": "https://github.com/elastic/elasticsearch-php/issues", "source": "https://github.com/elastic/elasticsearch-php/tree/v6.8.0"}, "time": "2021-03-01T18:54:48+00:00"}, {"name": "ezimuel/guzzlestreams", "version": "3.0.1", "source": {"type": "git", "url": "https://github.com/ezimuel/guzzlestreams.git", "reference": "abe3791d231167f14eb80d413420d1eab91163a8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ezimuel/guzzlestreams/zipball/abe3791d231167f14eb80d413420d1eab91163a8", "reference": "abe3791d231167f14eb80d413420d1eab91163a8", "shasum": ""}, "require": {"php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "~4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Stream\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Fork of guzzle/streams (abandoned) to be used with elasticsearch-php", "homepage": "http://guzzlephp.org/", "keywords": ["Guzzle", "stream"], "support": {"source": "https://github.com/ezimuel/guzzlestreams/tree/3.0.1"}, "time": "2020-02-14T23:11:50+00:00"}, {"name": "ezimuel/ringphp", "version": "1.1.2", "source": {"type": "git", "url": "https://github.com/ezimuel/ringphp.git", "reference": "0b78f89d8e0bb9e380046c31adfa40347e9f663b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ezimuel/ringphp/zipball/0b78f89d8e0bb9e380046c31adfa40347e9f663b", "reference": "0b78f89d8e0bb9e380046c31adfa40347e9f663b", "shasum": ""}, "require": {"ezimuel/guzzlestreams": "^3.0.1", "php": ">=5.4.0", "react/promise": "~2.0"}, "require-dev": {"ext-curl": "*", "phpunit/phpunit": "~4.0"}, "suggest": {"ext-curl": "Guzzle will use specific adapters if cURL is present"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Ring\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Fork of guzzle/RingPHP (abandoned) to be used with elasticsearch-php", "support": {"source": "https://github.com/ezimuel/ringphp/tree/1.1.2"}, "time": "2020-02-14T23:51:21+00:00"}, {"name": "ezyang/htmlpurifier", "version": "v4.13.0", "source": {"type": "git", "url": "https://github.com/ezyang/htmlpurifier.git", "reference": "08e27c97e4c6ed02f37c5b2b20488046c8d90d75"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ezyang/htmlpurifier/zipball/08e27c97e4c6ed02f37c5b2b20488046c8d90d75", "reference": "08e27c97e4c6ed02f37c5b2b20488046c8d90d75", "shasum": ""}, "require": {"php": ">=5.2"}, "require-dev": {"simpletest/simpletest": "dev-master#72de02a7b80c6bb8864ef9bf66d41d2f58f826bd"}, "type": "library", "autoload": {"psr-0": {"HTMLPurifier": "library/"}, "files": ["library/HTMLPurifier.composer.php"], "exclude-from-classmap": ["/library/HTMLPurifier/Language/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://ezyang.com"}], "description": "Standards compliant HTML filter written in PHP", "homepage": "http://htmlpurifier.org/", "keywords": ["html"], "support": {"issues": "https://github.com/ezyang/htmlpurifier/issues", "source": "https://github.com/ezyang/htmlpurifier/tree/master"}, "time": "2020-06-29T00:56:53+00:00"}, {"name": "facade/flare-client-php", "version": "1.4.0", "source": {"type": "git", "url": "https://github.com/facade/flare-client-php.git", "reference": "ef0f5bce23b30b32d98fd9bb49c6fa37b40eb546"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/facade/flare-client-php/zipball/ef0f5bce23b30b32d98fd9bb49c6fa37b40eb546", "reference": "ef0f5bce23b30b32d98fd9bb49c6fa37b40eb546", "shasum": ""}, "require": {"facade/ignition-contracts": "~1.0", "illuminate/pipeline": "^5.5|^6.0|^7.0|^8.0", "php": "^7.1|^8.0", "symfony/http-foundation": "^3.3|^4.1|^5.0", "symfony/mime": "^3.4|^4.0|^5.1", "symfony/var-dumper": "^3.4|^4.0|^5.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.14", "phpunit/phpunit": "^7.5.16", "spatie/phpunit-snapshot-assertions": "^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"psr-4": {"Facade\\FlareClient\\": "src"}, "files": ["src/helpers.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Send PHP errors to <PERSON><PERSON><PERSON>", "homepage": "https://github.com/facade/flare-client-php", "keywords": ["exception", "facade", "flare", "reporting"], "support": {"issues": "https://github.com/facade/flare-client-php/issues", "source": "https://github.com/facade/flare-client-php/tree/1.4.0"}, "funding": [{"url": "https://github.com/spatie", "type": "github"}], "time": "2021-02-16T12:42:06+00:00"}, {"name": "facade/ignition", "version": "1.16.16", "source": {"type": "git", "url": "https://github.com/facade/ignition.git", "reference": "b6aea4a99303d9d32afd486a285162a89af8a8a3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/facade/ignition/zipball/b6aea4a99303d9d32afd486a285162a89af8a8a3", "reference": "b6aea4a99303d9d32afd486a285162a89af8a8a3", "shasum": ""}, "require": {"ext-json": "*", "ext-mbstring": "*", "facade/flare-client-php": "^1.3", "facade/ignition-contracts": "^1.0", "filp/whoops": "^2.4", "illuminate/support": "~5.5.0 || ~5.6.0 || ~5.7.0 || ~5.8.0 || ^6.0", "monolog/monolog": "^1.12 || ^2.0", "php": "^7.1|^8.0", "scrivo/highlight.php": "^9.15", "symfony/console": "^3.4 || ^4.0", "symfony/var-dumper": "^3.4 || ^4.0"}, "require-dev": {"mockery/mockery": "~1.3.3|^1.4.2", "orchestra/testbench": "^3.5 || ^3.6 || ^3.7 || ^3.8 || ^4.0"}, "suggest": {"laravel/telescope": "^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}, "laravel": {"providers": ["Facade\\Ignition\\IgnitionServiceProvider"], "aliases": {"Flare": "Facade\\Ignition\\Facades\\Flare"}}}, "autoload": {"psr-4": {"Facade\\Ignition\\": "src"}, "files": ["src/helpers.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A beautiful error page for Laravel applications.", "homepage": "https://github.com/facade/ignition", "keywords": ["error", "flare", "laravel", "page"], "support": {"docs": "https://flareapp.io/docs/ignition-for-laravel/introduction", "forum": "https://twitter.com/flareappio", "issues": "https://github.com/facade/ignition/issues", "source": "https://github.com/facade/ignition"}, "time": "2021-02-15T10:21:49+00:00"}, {"name": "facade/ignition-contracts", "version": "1.0.2", "source": {"type": "git", "url": "https://github.com/facade/ignition-contracts.git", "reference": "3c921a1cdba35b68a7f0ccffc6dffc1995b18267"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/facade/ignition-contracts/zipball/3c921a1cdba35b68a7f0ccffc6dffc1995b18267", "reference": "3c921a1cdba35b68a7f0ccffc6dffc1995b18267", "shasum": ""}, "require": {"php": "^7.3|^8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^v2.15.8", "phpunit/phpunit": "^9.3.11", "vimeo/psalm": "^3.17.1"}, "type": "library", "autoload": {"psr-4": {"Facade\\IgnitionContracts\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://flareapp.io", "role": "Developer"}], "description": "Solution contracts for Ignition", "homepage": "https://github.com/facade/ignition-contracts", "keywords": ["contracts", "flare", "ignition"], "support": {"issues": "https://github.com/facade/ignition-contracts/issues", "source": "https://github.com/facade/ignition-contracts/tree/1.0.2"}, "time": "2020-10-16T08:27:54+00:00"}, {"name": "fideloper/proxy", "version": "4.4.1", "source": {"type": "git", "url": "https://github.com/fideloper/TrustedProxy.git", "reference": "c073b2bd04d1c90e04dc1b787662b558dd65ade0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/fideloper/TrustedProxy/zipball/c073b2bd04d1c90e04dc1b787662b558dd65ade0", "reference": "c073b2bd04d1c90e04dc1b787662b558dd65ade0", "shasum": ""}, "require": {"illuminate/contracts": "^5.0|^6.0|^7.0|^8.0|^9.0", "php": ">=5.4.0"}, "require-dev": {"illuminate/http": "^5.0|^6.0|^7.0|^8.0|^9.0", "mockery/mockery": "^1.0", "phpunit/phpunit": "^6.0"}, "type": "library", "extra": {"laravel": {"providers": ["Fideloper\\Proxy\\TrustedProxyServiceProvider"]}}, "autoload": {"psr-4": {"Fideloper\\Proxy\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Set trusted proxies for <PERSON><PERSON>", "keywords": ["load balancing", "proxy", "trusted proxy"], "support": {"issues": "https://github.com/fideloper/TrustedProxy/issues", "source": "https://github.com/fideloper/TrustedProxy/tree/4.4.1"}, "time": "2020-10-22T13:48:01+00:00"}, {"name": "filp/whoops", "version": "2.9.2", "source": {"type": "git", "url": "https://github.com/filp/whoops.git", "reference": "df7933820090489623ce0be5e85c7e693638e536"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/filp/whoops/zipball/df7933820090489623ce0be5e85c7e693638e536", "reference": "df7933820090489623ce0be5e85c7e693638e536", "shasum": ""}, "require": {"php": "^5.5.9 || ^7.0 || ^8.0", "psr/log": "^1.0.1"}, "require-dev": {"mockery/mockery": "^0.9 || ^1.0", "phpunit/phpunit": "^4.8.36 || ^5.7.27 || ^6.5.14 || ^7.5.20 || ^8.5.8 || ^9.3.3", "symfony/var-dumper": "^2.6 || ^3.0 || ^4.0 || ^5.0"}, "suggest": {"symfony/var-dumper": "Pretty print complex values better with var-dumper available", "whoops/soap": "Formats errors as SOAP responses"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.7-dev"}}, "autoload": {"psr-4": {"Whoops\\": "src/Whoops/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://github.com/filp", "role": "Developer"}], "description": "php error handling for cool kids", "homepage": "https://filp.github.io/whoops/", "keywords": ["error", "exception", "handling", "library", "throwable", "whoops"], "support": {"issues": "https://github.com/filp/whoops/issues", "source": "https://github.com/filp/whoops/tree/2.9.2"}, "funding": [{"url": "https://github.com/denis-so<PERSON><PERSON>", "type": "github"}], "time": "2021-01-24T12:00:00+00:00"}, {"name": "firebase/php-jwt", "version": "v5.2.1", "source": {"type": "git", "url": "https://github.com/firebase/php-jwt.git", "reference": "f42c9110abe98dd6cfe9053c49bc86acc70b2d23"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/firebase/php-jwt/zipball/f42c9110abe98dd6cfe9053c49bc86acc70b2d23", "reference": "f42c9110abe98dd6cfe9053c49bc86acc70b2d23", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": ">=4.8 <=9"}, "type": "library", "autoload": {"psr-4": {"Firebase\\JWT\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A simple library to encode and decode JSON Web Tokens (JWT) in PHP. Should conform to the current spec.", "homepage": "https://github.com/firebase/php-jwt", "keywords": ["jwt", "php"], "support": {"issues": "https://github.com/firebase/php-jwt/issues", "source": "https://github.com/firebase/php-jwt/tree/v5.2.1"}, "time": "2021-02-12T00:02:00+00:00"}, {"name": "google/apiclient", "version": "v2.9.1", "source": {"type": "git", "url": "https://github.com/googleapis/google-api-php-client.git", "reference": "2fb6e702aca5d68203fa737f89f6f774022494c6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/googleapis/google-api-php-client/zipball/2fb6e702aca5d68203fa737f89f6f774022494c6", "reference": "2fb6e702aca5d68203fa737f89f6f774022494c6", "shasum": ""}, "require": {"firebase/php-jwt": "~2.0||~3.0||~4.0||~5.0", "google/apiclient-services": "~0.13", "google/auth": "^1.10", "guzzlehttp/guzzle": "~5.3.3||~6.0||~7.0", "guzzlehttp/psr7": "^1.2", "monolog/monolog": "^1.17|^2.0", "php": "^5.6|^7.0|^8.0", "phpseclib/phpseclib": "~2.0||^3.0.2"}, "require-dev": {"cache/filesystem-adapter": "^0.3.2|^1.1", "composer/composer": "^1.10", "dealerdirect/phpcodesniffer-composer-installer": "^0.7", "phpcompatibility/php-compatibility": "^9.2", "phpunit/phpunit": "^5.7||^8.5.13", "squizlabs/php_codesniffer": "~2.3", "symfony/css-selector": "~2.1", "symfony/dom-crawler": "~2.1"}, "suggest": {"cache/filesystem-adapter": "For caching certs and tokens (using Google\\Client::setCache)"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.x-dev"}}, "autoload": {"psr-4": {"Google\\": "src/"}, "files": ["src/aliases.php"], "classmap": ["src/aliases.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "Client library for Google APIs", "homepage": "http://developers.google.com/api-client-library/php", "keywords": ["google"], "support": {"issues": "https://github.com/googleapis/google-api-php-client/issues", "source": "https://github.com/googleapis/google-api-php-client/tree/v2.9.1"}, "time": "2021-01-19T17:48:59+00:00"}, {"name": "google/apiclient-services", "version": "v0.162.0", "source": {"type": "git", "url": "https://github.com/googleapis/google-api-php-client-services.git", "reference": "9085ff13f32f5baf8b3d2917ea63e2e029ecedb6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/googleapis/google-api-php-client-services/zipball/9085ff13f32f5baf8b3d2917ea63e2e029ecedb6", "reference": "9085ff13f32f5baf8b3d2917ea63e2e029ecedb6", "shasum": ""}, "require": {"php": ">=5.4"}, "require-dev": {"phpunit/phpunit": "^4.8|^5"}, "type": "library", "autoload": {"psr-0": {"Google_Service_": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "Client library for Google APIs", "homepage": "http://developers.google.com/api-client-library/php", "keywords": ["google"], "support": {"issues": "https://github.com/googleapis/google-api-php-client-services/issues", "source": "https://github.com/googleapis/google-api-php-client-services/tree/v0.162.0"}, "time": "2021-02-28T12:20:02+00:00"}, {"name": "google/auth", "version": "v1.15.0", "source": {"type": "git", "url": "https://github.com/googleapis/google-auth-library-php.git", "reference": "b346c07de6613e26443d7b4830e5e1933b830dc4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/googleapis/google-auth-library-php/zipball/b346c07de6613e26443d7b4830e5e1933b830dc4", "reference": "b346c07de6613e26443d7b4830e5e1933b830dc4", "shasum": ""}, "require": {"firebase/php-jwt": "~2.0|~3.0|~4.0|~5.0", "guzzlehttp/guzzle": "^5.3.1|^6.2.1|^7.0", "guzzlehttp/psr7": "^1.2", "php": ">=5.4", "psr/cache": "^1.0", "psr/http-message": "^1.0"}, "require-dev": {"guzzlehttp/promises": "0.1.1|^1.3", "kelvinmo/simplejwt": "^0.2.5|^0.5.1", "phpseclib/phpseclib": "^2", "phpunit/phpunit": "^4.8.36|^5.7", "sebastian/comparator": ">=1.2.3", "squizlabs/php_codesniffer": "^3.5"}, "suggest": {"phpseclib/phpseclib": "May be used in place of OpenSSL for signing strings or for token management. Please require version ^2."}, "type": "library", "autoload": {"psr-4": {"Google\\Auth\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "Google Auth Library for PHP", "homepage": "http://github.com/google/google-auth-library-php", "keywords": ["Authentication", "google", "oauth2"], "support": {"docs": "https://googleapis.github.io/google-auth-library-php/master/", "issues": "https://github.com/googleapis/google-auth-library-php/issues", "source": "https://github.com/googleapis/google-auth-library-php/tree/v1.15.0"}, "time": "2021-02-05T20:50:04+00:00"}, {"name": "graham-campbell/exceptions", "version": "v12.1.1", "source": {"type": "git", "url": "https://github.com/GrahamCampbell/Laravel-Exceptions.git", "reference": "45c2d8a99ccc926b17eb713a459659061d6f7ce1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/GrahamCampbell/Laravel-Exceptions/zipball/45c2d8a99ccc926b17eb713a459659061d6f7ce1", "reference": "45c2d8a99ccc926b17eb713a459659061d6f7ce1", "shasum": ""}, "require": {"illuminate/contracts": "^5.5|^6.0", "illuminate/support": "^5.5|^6.0", "php": "^7.1.3", "psr/log": "^1.0", "symfony/console": "^3.3|^4.0", "symfony/debug": "^3.3|^4.0", "symfony/http-foundation": "^3.3|^4.0"}, "require-dev": {"filp/whoops": "^2.4", "graham-campbell/analyzer": "^2.4", "graham-campbell/testbench": "^5.4", "mockery/mockery": "^1.3.1", "phpunit/phpunit": "^6.5|^7.0|^8.0"}, "suggest": {"filp/whoops": "Enables use of the debug displayer."}, "type": "library", "extra": {"branch-alias": {"dev-master": "12.1-dev"}, "laravel": {"providers": ["GrahamCampbell\\Exceptions\\ExceptionsServiceProvider"]}}, "autoload": {"psr-4": {"GrahamCampbell\\Exceptions\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides A Powerful Error Response System For Both Development And Production", "keywords": ["<PERSON>", "Graham<PERSON><PERSON><PERSON>", "Laravel Exceptions", "Laravel-Exceptions", "error", "errors", "exception", "exceptions", "framework", "laravel", "whoops"], "support": {"issues": "https://github.com/GrahamCampbell/Laravel-Exceptions/issues", "source": "https://github.com/GrahamCampbell/Laravel-Exceptions/tree/v12.1.1"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/graham-campbell/exceptions", "type": "tidelift"}], "time": "2020-04-14T16:12:13+00:00"}, {"name": "guzzlehttp/guzzle", "version": "6.5.5", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "9d4290de1cfd701f38099ef7e183b64b4b7b0c5e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/9d4290de1cfd701f38099ef7e183b64b4b7b0c5e", "reference": "9d4290de1cfd701f38099ef7e183b64b4b7b0c5e", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.0", "guzzlehttp/psr7": "^1.6.1", "php": ">=5.5", "symfony/polyfill-intl-idn": "^1.17.0"}, "require-dev": {"ext-curl": "*", "phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.4 || ^7.0", "psr/log": "^1.1"}, "suggest": {"psr/log": "Required for using the Log middleware"}, "type": "library", "extra": {"branch-alias": {"dev-master": "6.5-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Guzzle is a PHP HTTP client library", "homepage": "http://guzzlephp.org/", "keywords": ["client", "curl", "framework", "http", "http client", "rest", "web service"], "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/6.5"}, "time": "2020-06-16T21:01:06+00:00"}, {"name": "guzzlehttp/promises", "version": "1.4.0", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "60d379c243457e073cff02bc323a2a86cb355631"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/60d379c243457e073cff02bc323a2a86cb355631", "reference": "60d379c243457e073cff02bc323a2a86cb355631", "shasum": ""}, "require": {"php": ">=5.5"}, "require-dev": {"symfony/phpunit-bridge": "^4.4 || ^5.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.4-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Guzzle promises library", "keywords": ["promise"], "support": {"issues": "https://github.com/guzzle/promises/issues", "source": "https://github.com/guzzle/promises/tree/1.4.0"}, "time": "2020-09-30T07:37:28+00:00"}, {"name": "guzzlehttp/psr7", "version": "1.7.0", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "53330f47520498c0ae1f61f7e2c90f55690c06a3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/53330f47520498c0ae1f61f7e2c90f55690c06a3", "reference": "53330f47520498c0ae1f61f7e2c90f55690c06a3", "shasum": ""}, "require": {"php": ">=5.4.0", "psr/http-message": "~1.0", "ralouphie/getallheaders": "^2.0.5 || ^3.0.0"}, "provide": {"psr/http-message-implementation": "1.0"}, "require-dev": {"ext-zlib": "*", "phpunit/phpunit": "~4.8.36 || ^5.7.27 || ^6.5.14 || ^7.5.20 || ^8.5.8 || ^9.3.10"}, "suggest": {"laminas/laminas-httphandlerrunner": "Emit PSR-7 responses"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.7-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "homepage": "https://github.com/Tobion"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "support": {"issues": "https://github.com/guzzle/psr7/issues", "source": "https://github.com/guzzle/psr7/tree/1.7.0"}, "time": "2020-09-30T07:37:11+00:00"}, {"name": "intervention/image", "version": "2.5.1", "source": {"type": "git", "url": "https://github.com/Intervention/image.git", "reference": "abbf18d5ab8367f96b3205ca3c89fb2fa598c69e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Intervention/image/zipball/abbf18d5ab8367f96b3205ca3c89fb2fa598c69e", "reference": "abbf18d5ab8367f96b3205ca3c89fb2fa598c69e", "shasum": ""}, "require": {"ext-fileinfo": "*", "guzzlehttp/psr7": "~1.1", "php": ">=5.4.0"}, "require-dev": {"mockery/mockery": "~0.9.2", "phpunit/phpunit": "^4.8 || ^5.7"}, "suggest": {"ext-gd": "to use GD library based image processing.", "ext-imagick": "to use Imagick based image processing.", "intervention/imagecache": "Caching extension for the Intervention Image library"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.4-dev"}, "laravel": {"providers": ["Intervention\\Image\\ImageServiceProvider"], "aliases": {"Image": "Intervention\\Image\\Facades\\Image"}}}, "autoload": {"psr-4": {"Intervention\\Image\\": "src/Intervention/Image"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://olivervogel.com/"}], "description": "Image handling and manipulation library with support for Laravel integration", "homepage": "http://image.intervention.io/", "keywords": ["gd", "image", "imagick", "laravel", "thumbnail", "watermark"], "support": {"issues": "https://github.com/Intervention/image/issues", "source": "https://github.com/Intervention/image/tree/master"}, "time": "2019-11-02T09:15:47+00:00"}, {"name": "jakub-onderka/php-console-color", "version": "v0.2", "source": {"type": "git", "url": "https://github.com/JakubOnderka/PHP-Console-Color.git", "reference": "d5deaecff52a0d61ccb613bb3804088da0307191"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/JakubOnderka/PHP-Console-Color/zipball/d5deaecff52a0d61ccb613bb3804088da0307191", "reference": "d5deaecff52a0d61ccb613bb3804088da0307191", "shasum": ""}, "require": {"php": ">=5.4.0"}, "require-dev": {"jakub-onderka/php-code-style": "1.0", "jakub-onderka/php-parallel-lint": "1.0", "jakub-onderka/php-var-dump-check": "0.*", "phpunit/phpunit": "~4.3", "squizlabs/php_codesniffer": "1.*"}, "type": "library", "autoload": {"psr-4": {"JakubOnderka\\PhpConsoleColor\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-2-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "support": {"issues": "https://github.com/JakubOnderka/PHP-Console-Color/issues", "source": "https://github.com/JakubOnderka/PHP-Console-Color/tree/master"}, "abandoned": "php-parallel-lint/php-console-color", "time": "2018-09-29T17:23:10+00:00"}, {"name": "jakub-onderka/php-console-highlighter", "version": "v0.4", "source": {"type": "git", "url": "https://github.com/JakubOnderka/PHP-Console-Highlighter.git", "reference": "9f7a229a69d52506914b4bc61bfdb199d90c5547"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/JakubOnderka/PHP-Console-Highlighter/zipball/9f7a229a69d52506914b4bc61bfdb199d90c5547", "reference": "9f7a229a69d52506914b4bc61bfdb199d90c5547", "shasum": ""}, "require": {"ext-tokenizer": "*", "jakub-onderka/php-console-color": "~0.2", "php": ">=5.4.0"}, "require-dev": {"jakub-onderka/php-code-style": "~1.0", "jakub-onderka/php-parallel-lint": "~1.0", "jakub-onderka/php-var-dump-check": "~0.1", "phpunit/phpunit": "~4.0", "squizlabs/php_codesniffer": "~1.5"}, "type": "library", "autoload": {"psr-4": {"JakubOnderka\\PhpConsoleHighlighter\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.acci.cz/"}], "description": "Highlight PHP code in terminal", "support": {"issues": "https://github.com/JakubOnderka/PHP-Console-Highlighter/issues", "source": "https://github.com/JakubOnderka/PHP-Console-Highlighter/tree/master"}, "abandoned": "php-parallel-lint/php-console-highlighter", "time": "2018-09-29T18:48:56+00:00"}, {"name": "krabica/krabica-core", "version": "6.0.5", "source": {"type": "git", "url": "******************:buxus/krabica-core-module.git", "reference": "4208c13bd6aa3a99d68434b297bf4aefca91b2db"}, "require": {"buxus-libs/analytics": "^1.1.0", "buxus-libs/authentication": "^3.2.0", "buxus-libs/contact-form": "^2.2.0", "buxus-libs/dynamic-categories": "^2.1.0", "buxus-libs/email": "^2.1.0", "buxus-libs/fat-footer": "^1.0.0", "buxus-libs/fs": "^5.1.0", "buxus-libs/fulltext-search": "^3.1.0", "buxus-libs/google-sitemap": "^2.0.0", "buxus-libs/layouts": "^2.1.0", "buxus-libs/layouts-dynamic-list": "^1.5.0", "buxus-libs/mailinglist": "^3.2.0", "buxus-libs/mega-menu": "^2.2.0", "buxus-libs/photo-gallery": "^2.1.0", "buxus-libs/product-catalog": "^1.3.0", "buxus-libs/shopping-cart": "^2.2.0", "buxus-libs/tree-property": "^2.1.0", "buxus/core": "~7.1.8 || ^7.2.1", "buxus/legacy-base": "^2.1.15", "buxus/modules": "^1.5.0", "buxus/pages": "~3.3 || ^4.0.0", "buxus/scheduler": "^3.1.0", "buxus/substrate": "^1.1.0", "buxus/translate": "^2.1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "6.0.x-dev"}, "laravel": {"providers": ["Krabica\\KrabicaCoreModule"]}}, "autoload": {"psr-4": {"Krabica\\": "src/"}}, "notification-url": "https://packagist.ui42.sk/downloads/", "license": ["proprietary"], "description": "BUXUS CMS base content package", "time": "2020-04-26T21:34:20+02:00"}, {"name": "laravel/framework", "version": "v6.20.16", "source": {"type": "git", "url": "https://github.com/laravel/framework.git", "reference": "806082fb559fe595cb17cd6aa8571f03ed287814"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laravel/framework/zipball/806082fb559fe595cb17cd6aa8571f03ed287814", "reference": "806082fb559fe595cb17cd6aa8571f03ed287814", "shasum": ""}, "require": {"doctrine/inflector": "^1.4|^2.0", "dragonmantank/cron-expression": "^2.3.1", "egulias/email-validator": "^2.1.10", "ext-json": "*", "ext-mbstring": "*", "ext-openssl": "*", "league/commonmark": "^1.3", "league/flysystem": "^1.1", "monolog/monolog": "^1.12|^2.0", "nesbot/carbon": "^2.31", "opis/closure": "^3.6", "php": "^7.2.5|^8.0", "psr/container": "^1.0", "psr/simple-cache": "^1.0", "ramsey/uuid": "^3.7", "swiftmailer/swiftmailer": "^6.0", "symfony/console": "^4.3.4", "symfony/debug": "^4.3.4", "symfony/finder": "^4.3.4", "symfony/http-foundation": "^4.3.4", "symfony/http-kernel": "^4.3.4", "symfony/polyfill-php73": "^1.17", "symfony/process": "^4.3.4", "symfony/routing": "^4.3.4", "symfony/var-dumper": "^4.3.4", "tijsverkoyen/css-to-inline-styles": "^2.2.1", "vlucas/phpdotenv": "^3.3"}, "conflict": {"tightenco/collect": "<5.5.33"}, "replace": {"illuminate/auth": "self.version", "illuminate/broadcasting": "self.version", "illuminate/bus": "self.version", "illuminate/cache": "self.version", "illuminate/config": "self.version", "illuminate/console": "self.version", "illuminate/container": "self.version", "illuminate/contracts": "self.version", "illuminate/cookie": "self.version", "illuminate/database": "self.version", "illuminate/encryption": "self.version", "illuminate/events": "self.version", "illuminate/filesystem": "self.version", "illuminate/hashing": "self.version", "illuminate/http": "self.version", "illuminate/log": "self.version", "illuminate/mail": "self.version", "illuminate/notifications": "self.version", "illuminate/pagination": "self.version", "illuminate/pipeline": "self.version", "illuminate/queue": "self.version", "illuminate/redis": "self.version", "illuminate/routing": "self.version", "illuminate/session": "self.version", "illuminate/support": "self.version", "illuminate/translation": "self.version", "illuminate/validation": "self.version", "illuminate/view": "self.version"}, "require-dev": {"aws/aws-sdk-php": "^3.155", "doctrine/dbal": "^2.6", "filp/whoops": "^2.8", "guzzlehttp/guzzle": "^6.3.1|^7.0.1", "league/flysystem-cached-adapter": "^1.0", "mockery/mockery": "~1.3.3|^1.4.2", "moontoast/math": "^1.1", "orchestra/testbench-core": "^4.8", "pda/pheanstalk": "^4.0", "phpunit/phpunit": "^7.5.15|^8.4|^9.3.3", "predis/predis": "^1.1.1", "symfony/cache": "^4.3.4"}, "suggest": {"aws/aws-sdk-php": "Required to use the SQS queue driver, DynamoDb failed job storage and SES mail driver (^3.155).", "doctrine/dbal": "Required to rename columns and drop SQLite columns (^2.6).", "ext-ftp": "Required to use the Flysystem FTP driver.", "ext-gd": "Required to use Illuminate\\Http\\Testing\\FileFactory::image().", "ext-memcached": "Required to use the memcache cache driver.", "ext-pcntl": "Required to use all features of the queue worker.", "ext-posix": "Required to use all features of the queue worker.", "ext-redis": "Required to use the Redis cache and queue drivers (^4.0|^5.0).", "fakerphp/faker": "Required to use the eloquent factory builder (^1.9.1).", "filp/whoops": "Required for friendly error pages in development (^2.8).", "guzzlehttp/guzzle": "Required to use the Mailgun mail driver and the ping methods on schedules (^6.3.1|^7.0.1).", "laravel/tinker": "Required to use the tinker console command (^2.0).", "league/flysystem-aws-s3-v3": "Required to use the Flysystem S3 driver (^1.0).", "league/flysystem-cached-adapter": "Required to use the Flysystem cache (^1.0).", "league/flysystem-sftp": "Required to use the Flysystem SFTP driver (^1.0).", "moontoast/math": "Required to use ordered UUIDs (^1.1).", "nyholm/psr7": "Required to use PSR-7 bridging features (^1.2).", "pda/pheanstalk": "Required to use the beanstalk queue driver (^4.0).", "predis/predis": "Required to use the predis connector (^1.1.2).", "psr/http-message": "Required to allow Storage::put to accept a StreamInterface (^1.0).", "pusher/pusher-php-server": "Required to use the <PERSON><PERSON><PERSON> broadcast driver (^4.0).", "symfony/cache": "Required to PSR-6 cache bridge (^4.3.4).", "symfony/psr-http-message-bridge": "Required to use PSR-7 bridging features (^1.2).", "wildbit/swiftmailer-postmark": "Required to use Postmark mail driver (^3.0)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "6.x-dev"}}, "autoload": {"files": ["src/Illuminate/Foundation/helpers.php", "src/Illuminate/Support/helpers.php"], "psr-4": {"Illuminate\\": "src/Illuminate/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Laravel Framework.", "homepage": "https://laravel.com", "keywords": ["framework", "laravel"], "support": {"issues": "https://github.com/laravel/framework/issues", "source": "https://github.com/laravel/framework"}, "time": "2021-02-02T13:50:12+00:00"}, {"name": "laravel/tinker", "version": "v1.0.10", "source": {"type": "git", "url": "https://github.com/laravel/tinker.git", "reference": "ad571aacbac1539c30d480908f9d0c9614eaf1a7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/laravel/tinker/zipball/ad571aacbac1539c30d480908f9d0c9614eaf1a7", "reference": "ad571aacbac1539c30d480908f9d0c9614eaf1a7", "shasum": ""}, "require": {"illuminate/console": "~5.1|^6.0", "illuminate/contracts": "~5.1|^6.0", "illuminate/support": "~5.1|^6.0", "php": ">=5.5.9", "psy/psysh": "0.7.*|0.8.*|0.9.*", "symfony/var-dumper": "~3.0|~4.0"}, "require-dev": {"phpunit/phpunit": "~4.0|~5.0"}, "suggest": {"illuminate/database": "The Illuminate Database package (~5.1)."}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}, "laravel": {"providers": ["Laravel\\Tinker\\TinkerServiceProvider"]}}, "autoload": {"psr-4": {"Laravel\\Tinker\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Powerful REPL for the Laravel framework.", "keywords": ["REPL", "Tinker", "laravel", "psysh"], "support": {"issues": "https://github.com/laravel/tinker/issues", "source": "https://github.com/laravel/tinker/tree/v1.0.10"}, "time": "2019-08-07T15:10:45+00:00"}, {"name": "laravelcollective/html", "version": "v6.2.1", "source": {"type": "git", "url": "https://github.com/LaravelCollective/html.git", "reference": "ae15b9c4bf918ec3a78f092b8555551dd693fde3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/LaravelCollective/html/zipball/ae15b9c4bf918ec3a78f092b8555551dd693fde3", "reference": "ae15b9c4bf918ec3a78f092b8555551dd693fde3", "shasum": ""}, "require": {"illuminate/http": "^6.0|^7.0|^8.0", "illuminate/routing": "^6.0|^7.0|^8.0", "illuminate/session": "^6.0|^7.0|^8.0", "illuminate/support": "^6.0|^7.0|^8.0", "illuminate/view": "^6.0|^7.0|^8.0", "php": ">=7.2.5"}, "require-dev": {"illuminate/database": "^6.0|^7.0|^8.0", "mockery/mockery": "~1.0", "phpunit/phpunit": "~8.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "6.x-dev"}, "laravel": {"providers": ["Collective\\Html\\HtmlServiceProvider"], "aliases": {"Form": "Collective\\Html\\FormFacade", "Html": "Collective\\Html\\HtmlFacade"}}}, "autoload": {"psr-4": {"Collective\\Html\\": "src/"}, "files": ["src/helpers.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "HTML and Form Builders for the Laravel Framework", "homepage": "https://laravelcollective.com", "support": {"issues": "https://github.com/LaravelCollective/html/issues", "source": "https://github.com/LaravelCollective/html"}, "time": "2020-12-15T20:20:05+00:00"}, {"name": "lcobucci/jwt", "version": "3.4.5", "source": {"type": "git", "url": "https://github.com/lcobucci/jwt.git", "reference": "511629a54465e89a31d3d7e4cf0935feab8b14c1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/lcobucci/jwt/zipball/511629a54465e89a31d3d7e4cf0935feab8b14c1", "reference": "511629a54465e89a31d3d7e4cf0935feab8b14c1", "shasum": ""}, "require": {"ext-mbstring": "*", "ext-openssl": "*", "php": "^5.6 || ^7.0"}, "require-dev": {"mikey179/vfsstream": "~1.5", "phpmd/phpmd": "~2.2", "phpunit/php-invoker": "~1.1", "phpunit/phpunit": "^5.7 || ^7.3", "squizlabs/php_codesniffer": "~2.3"}, "suggest": {"lcobucci/clock": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"psr-4": {"Lcobucci\\JWT\\": "src"}, "files": ["compat/class-aliases.php", "compat/json-exception-polyfill.php", "compat/lcobucci-clock-polyfill.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A simple library to work with JSON Web Token and JSON Web Signature", "keywords": ["JWS", "jwt"], "support": {"issues": "https://github.com/lcobucci/jwt/issues", "source": "https://github.com/lcobucci/jwt/tree/3.4.5"}, "funding": [{"url": "https://github.com/lcobucci", "type": "github"}, {"url": "https://www.patreon.com/lcobucci", "type": "patreon"}], "time": "2021-02-16T09:40:01+00:00"}, {"name": "league/commonmark", "version": "1.5.7", "source": {"type": "git", "url": "https://github.com/thephpleague/commonmark.git", "reference": "11df9b36fd4f1d2b727a73bf14931d81373b9a54"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/commonmark/zipball/11df9b36fd4f1d2b727a73bf14931d81373b9a54", "reference": "11df9b36fd4f1d2b727a73bf14931d81373b9a54", "shasum": ""}, "require": {"ext-mbstring": "*", "php": "^7.1 || ^8.0"}, "conflict": {"scrutinizer/ocular": "1.7.*"}, "require-dev": {"cebe/markdown": "~1.0", "commonmark/commonmark.js": "0.29.2", "erusev/parsedown": "~1.0", "ext-json": "*", "github/gfm": "0.29.0", "michelf/php-markdown": "~1.4", "mikehaertl/php-shellcommand": "^1.4", "phpstan/phpstan": "^0.12", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.2", "scrutinizer/ocular": "^1.5", "symfony/finder": "^4.2"}, "bin": ["bin/commonmark"], "type": "library", "autoload": {"psr-4": {"League\\CommonMark\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.colinodell.com", "role": "Lead Developer"}], "description": "Highly-extensible PHP Markdown parser which fully supports the CommonMark spec and Github-Flavored Markdown (GFM)", "homepage": "https://commonmark.thephpleague.com", "keywords": ["commonmark", "flavored", "gfm", "github", "github-flavored", "markdown", "md", "parser"], "support": {"docs": "https://commonmark.thephpleague.com/", "issues": "https://github.com/thephpleague/commonmark/issues", "rss": "https://github.com/thephpleague/commonmark/releases.atom", "source": "https://github.com/thephpleague/commonmark"}, "funding": [{"url": "https://enjoy.gitstore.app/repositories/thephpleague/commonmark", "type": "custom"}, {"url": "https://www.colinodell.com/sponsor", "type": "custom"}, {"url": "https://www.paypal.me/colinpodell/10.00", "type": "custom"}, {"url": "https://github.com/colinodell", "type": "github"}, {"url": "https://www.patreon.com/colinodell", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/league/commonmark", "type": "tidelift"}], "time": "2020-10-31T13:49:32+00:00"}, {"name": "league/flysystem", "version": "1.1.3", "source": {"type": "git", "url": "https://github.com/thephpleague/flysystem.git", "reference": "9be3b16c877d477357c015cec057548cf9b2a14a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/flysystem/zipball/9be3b16c877d477357c015cec057548cf9b2a14a", "reference": "9be3b16c877d477357c015cec057548cf9b2a14a", "shasum": ""}, "require": {"ext-fileinfo": "*", "league/mime-type-detection": "^1.3", "php": "^7.2.5 || ^8.0"}, "conflict": {"league/flysystem-sftp": "<1.0.6"}, "require-dev": {"phpspec/prophecy": "^1.11.1", "phpunit/phpunit": "^8.5.8"}, "suggest": {"ext-fileinfo": "Required for MimeType", "ext-ftp": "Allows you to use FTP server storage", "ext-openssl": "Allows you to use FTPS server storage", "league/flysystem-aws-s3-v2": "Allows you to use S3 storage with AWS SDK v2", "league/flysystem-aws-s3-v3": "Allows you to use S3 storage with AWS SDK v3", "league/flysystem-azure": "Allows you to use Windows Azure Blob storage", "league/flysystem-cached-adapter": "Flysystem adapter decorator for metadata caching", "league/flysystem-eventable-filesystem": "Allows you to use EventableFilesystem", "league/flysystem-rackspace": "Allows you to use Rackspace Cloud Files", "league/flysystem-sftp": "Allows you to use SFTP server storage via phpseclib", "league/flysystem-webdav": "Allows you to use WebDAV storage", "league/flysystem-ziparchive": "Allows you to use ZipArchive adapter", "spatie/flysystem-dropbox": "Allows you to use Dropbox storage", "srmklive/flysystem-dropbox-v2": "Allows you to use Dropbox storage for PHP 5 applications"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"psr-4": {"League\\Flysystem\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Filesystem abstraction: Many filesystems, one API.", "keywords": ["Cloud Files", "WebDAV", "abstraction", "aws", "cloud", "copy.com", "dropbox", "file systems", "files", "filesystem", "filesystems", "ftp", "rackspace", "remote", "s3", "sftp", "storage"], "support": {"issues": "https://github.com/thephpleague/flysystem/issues", "source": "https://github.com/thephpleague/flysystem/tree/1.x"}, "funding": [{"url": "https://offset.earth/frankdejonge", "type": "other"}], "time": "2020-08-23T07:39:11+00:00"}, {"name": "league/fractal", "version": "0.17.0", "source": {"type": "git", "url": "https://github.com/thephpleague/fractal.git", "reference": "a0b350824f22fc2fdde2500ce9d6851a3f275b0e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/fractal/zipball/a0b350824f22fc2fdde2500ce9d6851a3f275b0e", "reference": "a0b350824f22fc2fdde2500ce9d6851a3f275b0e", "shasum": ""}, "require": {"php": ">=5.4"}, "require-dev": {"doctrine/orm": "^2.5", "illuminate/contracts": "~5.0", "mockery/mockery": "~0.9", "pagerfanta/pagerfanta": "~1.0.0", "phpunit/phpunit": "~4.0", "squizlabs/php_codesniffer": "~1.5", "zendframework/zend-paginator": "~2.3"}, "suggest": {"illuminate/pagination": "The Illuminate Pagination component.", "pagerfanta/pagerfanta": "Pagerfant<PERSON>", "zendframework/zend-paginator": "Zend Framework Paginator"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.13-dev"}}, "autoload": {"psr-4": {"League\\Fractal\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://philsturgeon.uk/", "role": "Developer"}], "description": "Handle the output of complex data structures ready for API output.", "homepage": "http://fractal.thephpleague.com/", "keywords": ["api", "json", "league", "rest"], "support": {"issues": "https://github.com/thephpleague/fractal/issues", "source": "https://github.com/thephpleague/fractal/tree/master"}, "time": "2017-06-12T11:04:56+00:00"}, {"name": "league/glide", "version": "1.7.0", "source": {"type": "git", "url": "https://github.com/thephpleague/glide.git", "reference": "ae5e26700573cb678919d28e425a8b87bc71c546"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/glide/zipball/ae5e26700573cb678919d28e425a8b87bc71c546", "reference": "ae5e26700573cb678919d28e425a8b87bc71c546", "shasum": ""}, "require": {"intervention/image": "^2.4", "league/flysystem": "^1.0", "php": "^7.2|^8.0", "psr/http-message": "^1.0"}, "require-dev": {"mockery/mockery": "^1.3.3", "phpunit/php-token-stream": "^3.1|^4.0", "phpunit/phpunit": "^8.5|^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"psr-4": {"League\\Glide\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://reinink.ca"}], "description": "Wonderfully easy on-demand image manipulation library with an HTTP based API.", "homepage": "http://glide.thephpleague.com", "keywords": ["ImageMagick", "editing", "gd", "image", "imagick", "league", "manipulation", "processing"], "support": {"issues": "https://github.com/thephpleague/glide/issues", "source": "https://github.com/thephpleague/glide/tree/1.7.0"}, "time": "2020-11-05T17:34:03+00:00"}, {"name": "league/mime-type-detection", "version": "1.7.0", "source": {"type": "git", "url": "https://github.com/thephpleague/mime-type-detection.git", "reference": "3b9dff8aaf7323590c1d2e443db701eb1f9aa0d3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/thephpleague/mime-type-detection/zipball/3b9dff8aaf7323590c1d2e443db701eb1f9aa0d3", "reference": "3b9dff8aaf7323590c1d2e443db701eb1f9aa0d3", "shasum": ""}, "require": {"ext-fileinfo": "*", "php": "^7.2 || ^8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.18", "phpstan/phpstan": "^0.12.68", "phpunit/phpunit": "^8.5.8 || ^9.3"}, "type": "library", "autoload": {"psr-4": {"League\\MimeTypeDetection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Mime-type detection for Flysystem", "support": {"issues": "https://github.com/thephpleague/mime-type-detection/issues", "source": "https://github.com/thephpleague/mime-type-detection/tree/1.7.0"}, "funding": [{"url": "https://github.com/frankdejonge", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/league/flysystem", "type": "tidelift"}], "time": "2021-01-18T20:58:21+00:00"}, {"name": "maennchen/zipstream-php", "version": "2.1.0", "source": {"type": "git", "url": "https://github.com/maennchen/ZipStream-PHP.git", "reference": "c4c5803cc1f93df3d2448478ef79394a5981cc58"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/maennchen/ZipStream-PHP/zipball/c4c5803cc1f93df3d2448478ef79394a5981cc58", "reference": "c4c5803cc1f93df3d2448478ef79394a5981cc58", "shasum": ""}, "require": {"myclabs/php-enum": "^1.5", "php": ">= 7.1", "psr/http-message": "^1.0", "symfony/polyfill-mbstring": "^1.0"}, "require-dev": {"ext-zip": "*", "guzzlehttp/guzzle": ">= 6.3", "mikey179/vfsstream": "^1.6", "phpunit/phpunit": ">= 7.5"}, "type": "library", "autoload": {"psr-4": {"ZipStream\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Jonatan Männchen", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "ZipStream is a library for dynamically streaming dynamic zip files from PHP without writing to the disk at all on the server.", "keywords": ["stream", "zip"], "support": {"issues": "https://github.com/maennchen/ZipStream-PHP/issues", "source": "https://github.com/maennchen/ZipStream-PHP/tree/master"}, "funding": [{"url": "https://opencollective.com/zipstream", "type": "open_collective"}], "time": "2020-05-30T13:11:16+00:00"}, {"name": "malkusch/lock", "version": "v2.1", "source": {"type": "git", "url": "https://github.com/php-lock/lock.git", "reference": "093f389ec2f38fc8686d2f70e23378182fce7714"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-lock/lock/zipball/093f389ec2f38fc8686d2f70e23378182fce7714", "reference": "093f389ec2f38fc8686d2f70e23378182fce7714", "shasum": ""}, "require": {"php": ">=7.1", "psr/log": "^1"}, "require-dev": {"eloquent/liberator": "^2.0", "ext-memcached": "*", "ext-pcntl": "*", "ext-pdo_mysql": "*", "ext-pdo_sqlite": "*", "ext-redis": "*", "ext-sysvsem": "*", "johnkary/phpunit-speedtrap": "^3.0", "kriswallsmith/spork": "^0.3", "mikey179/vfsstream": "^1.6", "php-mock/php-mock-phpunit": "^2.1", "phpunit/phpunit": "^7.4", "predis/predis": "^1.1", "squizlabs/php_codesniffer": "^3.3"}, "suggest": {"ext-pnctl": "Enables locking with flock without busy waiting in CLI scripts.", "ext-redis": "To use this library with the PHP Redis extension.", "ext-sysvsem": "Enables locking using semaphores.", "predis/predis": "To use this library with predis."}, "type": "library", "autoload": {"psr-4": {"malkusch\\lock\\": "classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["WTFPL"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://markus.malkusch.de", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Mutex library for exclusive code execution.", "homepage": "https://github.com/malkusch/lock", "keywords": ["advisory-locks", "cas", "flock", "lock", "locking", "memcache", "mutex", "mysql", "postgresql", "redis", "redlock", "semaphore"], "support": {"issues": "https://github.com/php-lock/lock/issues", "source": "https://github.com/php-lock/lock/tree/v2.1"}, "time": "2018-12-12T19:53:29+00:00"}, {"name": "markbaker/complex", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/MarkBaker/PHPComplex.git", "reference": "9999f1432fae467bc93c53f357105b4c31bb994c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MarkBaker/PHPComplex/zipball/9999f1432fae467bc93c53f357105b4c31bb994c", "reference": "9999f1432fae467bc93c53f357105b4c31bb994c", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^0.7.0", "phpcompatibility/php-compatibility": "^9.0", "phpdocumentor/phpdocumentor": "2.*", "phploc/phploc": "^4.0", "phpmd/phpmd": "2.*", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.3", "sebastian/phpcpd": "^4.0", "squizlabs/php_codesniffer": "^3.4"}, "type": "library", "autoload": {"psr-4": {"Complex\\": "classes/src/"}, "files": ["classes/src/functions/abs.php", "classes/src/functions/acos.php", "classes/src/functions/acosh.php", "classes/src/functions/acot.php", "classes/src/functions/acoth.php", "classes/src/functions/acsc.php", "classes/src/functions/acsch.php", "classes/src/functions/argument.php", "classes/src/functions/asec.php", "classes/src/functions/asech.php", "classes/src/functions/asin.php", "classes/src/functions/asinh.php", "classes/src/functions/atan.php", "classes/src/functions/atanh.php", "classes/src/functions/conjugate.php", "classes/src/functions/cos.php", "classes/src/functions/cosh.php", "classes/src/functions/cot.php", "classes/src/functions/coth.php", "classes/src/functions/csc.php", "classes/src/functions/csch.php", "classes/src/functions/exp.php", "classes/src/functions/inverse.php", "classes/src/functions/ln.php", "classes/src/functions/log2.php", "classes/src/functions/log10.php", "classes/src/functions/negative.php", "classes/src/functions/pow.php", "classes/src/functions/rho.php", "classes/src/functions/sec.php", "classes/src/functions/sech.php", "classes/src/functions/sin.php", "classes/src/functions/sinh.php", "classes/src/functions/sqrt.php", "classes/src/functions/tan.php", "classes/src/functions/tanh.php", "classes/src/functions/theta.php", "classes/src/operations/add.php", "classes/src/operations/subtract.php", "classes/src/operations/multiply.php", "classes/src/operations/divideby.php", "classes/src/operations/divideinto.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Class for working with complex numbers", "homepage": "https://github.com/MarkBaker/PHPComplex", "keywords": ["complex", "mathematics"], "support": {"issues": "https://github.com/MarkBaker/PHPComplex/issues", "source": "https://github.com/MarkBaker/PHPComplex/tree/PHP8"}, "time": "2020-08-26T10:42:07+00:00"}, {"name": "markbaker/matrix", "version": "2.1.2", "source": {"type": "git", "url": "https://github.com/MarkBaker/PHPMatrix.git", "reference": "361c0f545c3172ee26c3d596a0aa03f0cef65e6a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MarkBaker/PHPMatrix/zipball/361c0f545c3172ee26c3d596a0aa03f0cef65e6a", "reference": "361c0f545c3172ee26c3d596a0aa03f0cef65e6a", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^0.7.0", "phpcompatibility/php-compatibility": "^9.0", "phpdocumentor/phpdocumentor": "2.*", "phploc/phploc": "^4.0", "phpmd/phpmd": "2.*", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.3", "sebastian/phpcpd": "^4.0", "squizlabs/php_codesniffer": "^3.4"}, "type": "library", "autoload": {"psr-4": {"Matrix\\": "classes/src/"}, "files": ["classes/src/Functions/adjoint.php", "classes/src/Functions/antidiagonal.php", "classes/src/Functions/cofactors.php", "classes/src/Functions/determinant.php", "classes/src/Functions/diagonal.php", "classes/src/Functions/identity.php", "classes/src/Functions/inverse.php", "classes/src/Functions/minors.php", "classes/src/Functions/trace.php", "classes/src/Functions/transpose.php", "classes/src/Operations/add.php", "classes/src/Operations/directsum.php", "classes/src/Operations/subtract.php", "classes/src/Operations/multiply.php", "classes/src/Operations/divideby.php", "classes/src/Operations/divideinto.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Class for working with matrices", "homepage": "https://github.com/MarkBaker/PHPMatrix", "keywords": ["mathematics", "matrix", "vector"], "support": {"issues": "https://github.com/MarkBaker/PHPMatrix/issues", "source": "https://github.com/MarkBaker/PHPMatrix/tree/2.1.2"}, "time": "2021-01-23T16:37:31+00:00"}, {"name": "monolog/monolog", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/Seldaek/monolog.git", "reference": "1cb1cde8e8dd0f70cc0fe51354a59acad9302084"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/monolog/zipball/1cb1cde8e8dd0f70cc0fe51354a59acad9302084", "reference": "1cb1cde8e8dd0f70cc0fe51354a59acad9302084", "shasum": ""}, "require": {"php": ">=7.2", "psr/log": "^1.0.1"}, "provide": {"psr/log-implementation": "1.0.0"}, "require-dev": {"aws/aws-sdk-php": "^2.4.9 || ^3.0", "doctrine/couchdb": "~1.0@dev", "elasticsearch/elasticsearch": "^7", "graylog2/gelf-php": "^1.4.2", "mongodb/mongodb": "^1.8", "php-amqplib/php-amqplib": "~2.4", "php-console/php-console": "^3.1.3", "phpspec/prophecy": "^1.6.1", "phpstan/phpstan": "^0.12.59", "phpunit/phpunit": "^8.5", "predis/predis": "^1.1", "rollbar/rollbar": "^1.3", "ruflin/elastica": ">=0.90 <7.0.1", "swiftmailer/swiftmailer": "^5.3|^6.0"}, "suggest": {"aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "elasticsearch/elasticsearch": "Allow sending log messages to an Elasticsearch server via official client", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-mbstring": "Allow to work properly with unicode symbols", "ext-mongodb": "Allow sending log messages to a MongoDB server (via driver)", "graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "mongodb/mongodb": "Allow sending log messages to a MongoDB server (via library)", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "php-console/php-console": "Allow sending log messages to Google Chrome", "rollbar/rollbar": "Allow sending log messages to Rollbar", "ruflin/elastica": "Allow sending log messages to an Elastic Search server"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.x-dev"}}, "autoload": {"psr-4": {"Monolog\\": "src/Monolog"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "https://seld.be"}], "description": "Sends your logs to files, sockets, inboxes, databases and various web services", "homepage": "https://github.com/Seldaek/monolog", "keywords": ["log", "logging", "psr-3"], "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/2.2.0"}, "funding": [{"url": "https://github.com/Seldaek", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/monolog/monolog", "type": "tidelift"}], "time": "2020-12-14T13:15:25+00:00"}, {"name": "mpdf/mpdf", "version": "v8.0.10", "source": {"type": "git", "url": "https://github.com/mpdf/mpdf.git", "reference": "1333a962cd2f7ae1a127b7534b7734b58179186f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mpdf/mpdf/zipball/1333a962cd2f7ae1a127b7534b7734b58179186f", "reference": "1333a962cd2f7ae1a127b7534b7734b58179186f", "shasum": ""}, "require": {"ext-gd": "*", "ext-mbstring": "*", "myclabs/deep-copy": "^1.7", "paragonie/random_compat": "^1.4|^2.0|^9.99.99", "php": "^5.6 || ^7.0 || ~8.0.0", "psr/log": "^1.0", "setasign/fpdi": "^2.1"}, "require-dev": {"mockery/mockery": "^1.3.0", "mpdf/qrcode": "^1.1.0", "phpunit/phpunit": "^5.7", "squizlabs/php_codesniffer": "^3.5.0", "tracy/tracy": "^2.4"}, "suggest": {"ext-bcmath": "Needed for generation of some types of barcodes", "ext-xml": "Needed mainly for SVG manipulation", "ext-zlib": "Needed for compression of embedded resources, such as fonts"}, "type": "library", "autoload": {"psr-4": {"Mpdf\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-only"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "role": "<PERSON><PERSON><PERSON>, maintainer"}, {"name": "<PERSON>", "role": "<PERSON><PERSON><PERSON> (retired)"}], "description": "PHP library generating PDF files from UTF-8 encoded HTML", "homepage": "https://mpdf.github.io", "keywords": ["pdf", "php", "utf-8"], "support": {"docs": "http://mpdf.github.io", "issues": "https://github.com/mpdf/mpdf/issues", "source": "https://github.com/mpdf/mpdf"}, "funding": [{"url": "https://www.paypal.me/mpdf", "type": "custom"}], "time": "2021-01-08T14:59:28+00:00"}, {"name": "mustache/mustache", "version": "v2.10.0", "source": {"type": "git", "url": "https://github.com/bobthecow/mustache.php.git", "reference": "0bb2f76e2f34a8864a32be34c4ec66274d76c05e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/bobthecow/mustache.php/zipball/0bb2f76e2f34a8864a32be34c4ec66274d76c05e", "reference": "0bb2f76e2f34a8864a32be34c4ec66274d76c05e", "shasum": ""}, "require": {"php": ">=5.2.4"}, "require-dev": {"fabpot/php-cs-fixer": "~1.6", "phpunit/phpunit": "~3.7|~4.0|~5.0"}, "type": "library", "autoload": {"psr-0": {"Mustache": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://justinhileman.com"}], "description": "A Mustache implementation in PHP.", "homepage": "https://github.com/bobthecow/mustache.php", "keywords": ["mustache", "templating"], "support": {"issues": "https://github.com/bobthecow/mustache.php/issues", "source": "https://github.com/bobthecow/mustache.php/tree/v2.10.0"}, "time": "2016-02-27T19:22:46+00:00"}, {"name": "myclabs/deep-copy", "version": "1.10.2", "source": {"type": "git", "url": "https://github.com/myclabs/DeepCopy.git", "reference": "776f831124e9c62e1a2c601ecc52e776d8bb7220"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/myclabs/DeepCopy/zipball/776f831124e9c62e1a2c601ecc52e776d8bb7220", "reference": "776f831124e9c62e1a2c601ecc52e776d8bb7220", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "replace": {"myclabs/deep-copy": "self.version"}, "require-dev": {"doctrine/collections": "^1.0", "doctrine/common": "^2.6", "phpunit/phpunit": "^7.1"}, "type": "library", "autoload": {"psr-4": {"DeepCopy\\": "src/DeepCopy/"}, "files": ["src/DeepCopy/deep_copy.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "Create deep copies (clones) of your objects", "keywords": ["clone", "copy", "duplicate", "object", "object graph"], "support": {"issues": "https://github.com/myclabs/DeepCopy/issues", "source": "https://github.com/myclabs/DeepCopy/tree/1.10.2"}, "funding": [{"url": "https://tidelift.com/funding/github/packagist/myclabs/deep-copy", "type": "tidelift"}], "time": "2020-11-13T09:40:50+00:00"}, {"name": "myclabs/php-enum", "version": "1.8.0", "source": {"type": "git", "url": "https://github.com/myclabs/php-enum.git", "reference": "46cf3d8498b095bd33727b13fd5707263af99421"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/myclabs/php-enum/zipball/46cf3d8498b095bd33727b13fd5707263af99421", "reference": "46cf3d8498b095bd33727b13fd5707263af99421", "shasum": ""}, "require": {"ext-json": "*", "php": "^7.3 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^9.5", "squizlabs/php_codesniffer": "1.*", "vimeo/psalm": "^4.5.1"}, "type": "library", "autoload": {"psr-4": {"MyCLabs\\Enum\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP Enum contributors", "homepage": "https://github.com/myclabs/php-enum/graphs/contributors"}], "description": "PHP Enum implementation", "homepage": "http://github.com/myclabs/php-enum", "keywords": ["enum"], "support": {"issues": "https://github.com/myclabs/php-enum/issues", "source": "https://github.com/myclabs/php-enum/tree/1.8.0"}, "funding": [{"url": "https://github.com/mnapoli", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/myclabs/php-enum", "type": "tidelift"}], "time": "2021-02-15T16:11:48+00:00"}, {"name": "namshi/jose", "version": "7.2.3", "source": {"type": "git", "url": "https://github.com/namshi/jose.git", "reference": "89a24d7eb3040e285dd5925fcad992378b82bcff"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/namshi/jose/zipball/89a24d7eb3040e285dd5925fcad992378b82bcff", "reference": "89a24d7eb3040e285dd5925fcad992378b82bcff", "shasum": ""}, "require": {"ext-date": "*", "ext-hash": "*", "ext-json": "*", "ext-pcre": "*", "ext-spl": "*", "php": ">=5.5", "symfony/polyfill-php56": "^1.0"}, "require-dev": {"phpseclib/phpseclib": "^2.0", "phpunit/phpunit": "^4.5|^5.0", "satooshi/php-coveralls": "^1.0"}, "suggest": {"ext-openssl": "Allows to use OpenSSL as crypto engine.", "phpseclib/phpseclib": "Allows to use Phpseclib as crypto engine, use version ^2.0."}, "type": "library", "autoload": {"psr-4": {"Namshi\\JOSE\\": "src/Namshi/JOSE/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON> (cirpo)", "email": "<EMAIL>"}], "description": "JSON Object Signing and Encryption library for PHP.", "keywords": ["JSON Web Signature", "JSON Web Token", "JWS", "json", "jwt", "token"], "support": {"issues": "https://github.com/namshi/jose/issues", "source": "https://github.com/namshi/jose/tree/master"}, "time": "2016-12-05T07:27:31+00:00"}, {"name": "nesbot/carbon", "version": "2.45.1", "source": {"type": "git", "url": "https://github.com/briannesbitt/Carbon.git", "reference": "528783b188bdb853eb21239b1722831e0f000a8d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/briannesbitt/Carbon/zipball/528783b188bdb853eb21239b1722831e0f000a8d", "reference": "528783b188bdb853eb21239b1722831e0f000a8d", "shasum": ""}, "require": {"ext-json": "*", "php": "^7.1.8 || ^8.0", "symfony/polyfill-mbstring": "^1.0", "symfony/translation": "^3.4 || ^4.0 || ^5.0"}, "require-dev": {"doctrine/orm": "^2.7", "friendsofphp/php-cs-fixer": "^2.14 || ^3.0", "kylekatarnls/multi-tester": "^2.0", "phpmd/phpmd": "^2.9", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^0.12.54", "phpunit/phpunit": "^7.5.20 || ^8.5.14", "squizlabs/php_codesniffer": "^3.4"}, "bin": ["bin/carbon"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.x-dev", "dev-3.x": "3.x-dev"}, "laravel": {"providers": ["Carbon\\Laravel\\ServiceProvider"]}, "phpstan": {"includes": ["extension.neon"]}}, "autoload": {"psr-4": {"Carbon\\": "src/Carbon/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://nesbot.com"}, {"name": "kylekatarnls", "homepage": "http://github.com/kylekatarnls"}], "description": "An API extension for DateTime that supports 281 different languages.", "homepage": "http://carbon.nesbot.com", "keywords": ["date", "datetime", "time"], "support": {"issues": "https://github.com/briannesbitt/Carbon/issues", "source": "https://github.com/briannesbitt/Carbon"}, "funding": [{"url": "https://opencollective.com/Carbon", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/nesbot/carbon", "type": "tidelift"}], "time": "2021-02-11T18:30:17+00:00"}, {"name": "nicmart/tree", "version": "0.3.1", "source": {"type": "git", "url": "https://github.com/nicmart/Tree.git", "reference": "c55ba47c64a3cb7454c22e6d630729fc2aab23ff"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nicmart/Tree/zipball/c55ba47c64a3cb7454c22e6d630729fc2aab23ff", "reference": "c55ba47c64a3cb7454c22e6d630729fc2aab23ff", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"ergebnis/composer-normalize": "^2.8.0", "ergebnis/license": "^1.0.0", "ergebnis/php-cs-fixer-config": "^2.2.1", "phpunit/phpunit": "^7.5.20"}, "type": "library", "autoload": {"psr-4": {"Tree\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A basic but flexible php tree data structure and a fluent tree builder implementation.", "support": {"issues": "https://github.com/nicmart/Tree/issues", "source": "https://github.com/nicmart/Tree/tree/0.3.1"}, "time": "2020-11-27T09:02:17+00:00"}, {"name": "nikic/php-parser", "version": "v4.10.4", "source": {"type": "git", "url": "https://github.com/nikic/PHP-Parser.git", "reference": "c6d052fc58cb876152f89f532b95a8d7907e7f0e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nikic/PHP-Parser/zipball/c6d052fc58cb876152f89f532b95a8d7907e7f0e", "reference": "c6d052fc58cb876152f89f532b95a8d7907e7f0e", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": ">=7.0"}, "require-dev": {"ircmaxell/php-yacc": "^0.0.7", "phpunit/phpunit": "^6.5 || ^7.0 || ^8.0 || ^9.0"}, "bin": ["bin/php-parse"], "type": "library", "extra": {"branch-alias": {"dev-master": "4.9-dev"}}, "autoload": {"psr-4": {"PhpParser\\": "lib/Php<PERSON><PERSON>er"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>"}], "description": "A PHP parser written in PHP", "keywords": ["parser", "php"], "support": {"issues": "https://github.com/nikic/PHP-Parser/issues", "source": "https://github.com/nikic/PHP-Parser/tree/v4.10.4"}, "time": "2020-12-20T10:01:03+00:00"}, {"name": "opis/closure", "version": "3.6.1", "source": {"type": "git", "url": "https://github.com/opis/closure.git", "reference": "943b5d70cc5ae7483f6aff6ff43d7e34592ca0f5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/opis/closure/zipball/943b5d70cc5ae7483f6aff6ff43d7e34592ca0f5", "reference": "943b5d70cc5ae7483f6aff6ff43d7e34592ca0f5", "shasum": ""}, "require": {"php": "^5.4 || ^7.0 || ^8.0"}, "require-dev": {"jeremeamia/superclosure": "^2.0", "phpunit/phpunit": "^4.0 || ^5.0 || ^6.0 || ^7.0 || ^8.0 || ^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.6.x-dev"}}, "autoload": {"psr-4": {"Opis\\Closure\\": "src/"}, "files": ["functions.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Sorin Sarca", "email": "<EMAIL>"}], "description": "A library that can be used to serialize closures (anonymous functions) and arbitrary objects.", "homepage": "https://opis.io/closure", "keywords": ["anonymous functions", "closure", "function", "serializable", "serialization", "serialize"], "support": {"issues": "https://github.com/opis/closure/issues", "source": "https://github.com/opis/closure/tree/3.6.1"}, "time": "2020-11-07T02:01:34+00:00"}, {"name": "paragonie/constant_time_encoding", "version": "v2.4.0", "source": {"type": "git", "url": "https://github.com/paragonie/constant_time_encoding.git", "reference": "f34c2b11eb9d2c9318e13540a1dbc2a3afbd939c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/constant_time_encoding/zipball/f34c2b11eb9d2c9318e13540a1dbc2a3afbd939c", "reference": "f34c2b11eb9d2c9318e13540a1dbc2a3afbd939c", "shasum": ""}, "require": {"php": "^7|^8"}, "require-dev": {"phpunit/phpunit": "^6|^7|^8|^9", "vimeo/psalm": "^1|^2|^3|^4"}, "type": "library", "autoload": {"psr-4": {"ParagonIE\\ConstantTime\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com", "role": "Maintainer"}, {"name": "<PERSON> 'Sc00bz' <PERSON>", "email": "<EMAIL>", "homepage": "https://www.tobtu.com", "role": "Original Developer"}], "description": "Constant-time Implementations of RFC 4648 Encoding (Base-64, Base-32, Base-16)", "keywords": ["base16", "base32", "base32_decode", "base32_encode", "base64", "base64_decode", "base64_encode", "bin2hex", "encoding", "hex", "hex2bin", "rfc4648"], "support": {"email": "<EMAIL>", "issues": "https://github.com/paragonie/constant_time_encoding/issues", "source": "https://github.com/paragonie/constant_time_encoding"}, "time": "2020-12-06T15:14:20+00:00"}, {"name": "paragonie/random_compat", "version": "v9.99.99", "source": {"type": "git", "url": "https://github.com/paragonie/random_compat.git", "reference": "84b4dfb120c6f9b4ff7b3685f9b8f1aa365a0c95"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/random_compat/zipball/84b4dfb120c6f9b4ff7b3685f9b8f1aa365a0c95", "reference": "84b4dfb120c6f9b4ff7b3685f9b8f1aa365a0c95", "shasum": ""}, "require": {"php": "^7"}, "require-dev": {"phpunit/phpunit": "4.*|5.*", "vimeo/psalm": "^1"}, "suggest": {"ext-libsodium": "Provides a modern crypto API that can be used to generate random bytes."}, "type": "library", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com"}], "description": "PHP 5.x polyfill for random_bytes() and random_int() from PHP 7", "keywords": ["csprng", "polyfill", "pseudorandom", "random"], "support": {"email": "<EMAIL>", "issues": "https://github.com/paragonie/random_compat/issues", "source": "https://github.com/paragonie/random_compat"}, "time": "2018-07-02T15:55:56+00:00"}, {"name": "phenx/php-font-lib", "version": "0.5.2", "source": {"type": "git", "url": "https://github.com/PhenX/php-font-lib.git", "reference": "ca6ad461f032145fff5971b5985e5af9e7fa88d8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PhenX/php-font-lib/zipball/ca6ad461f032145fff5971b5985e5af9e7fa88d8", "reference": "ca6ad461f032145fff5971b5985e5af9e7fa88d8", "shasum": ""}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5 || ^6 || ^7"}, "type": "library", "autoload": {"psr-4": {"FontLib\\": "src/FontLib"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A library to read, parse, export and make subsets of different types of font files.", "homepage": "https://github.com/PhenX/php-font-lib", "support": {"issues": "https://github.com/PhenX/php-font-lib/issues", "source": "https://github.com/PhenX/php-font-lib/tree/0.5.2"}, "time": "2020-03-08T15:31:32+00:00"}, {"name": "phenx/php-svg-lib", "version": "v0.3.3", "source": {"type": "git", "url": "https://github.com/PhenX/php-svg-lib.git", "reference": "5fa61b65e612ce1ae15f69b3d223cb14ecc60e32"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PhenX/php-svg-lib/zipball/5fa61b65e612ce1ae15f69b3d223cb14ecc60e32", "reference": "5fa61b65e612ce1ae15f69b3d223cb14ecc60e32", "shasum": ""}, "require": {"sabberworm/php-css-parser": "^8.3"}, "require-dev": {"phpunit/phpunit": "^5.5|^6.5"}, "type": "library", "autoload": {"psr-4": {"Svg\\": "src/Svg"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A library to read, parse and export to PDF SVG files.", "homepage": "https://github.com/PhenX/php-svg-lib", "support": {"issues": "https://github.com/PhenX/php-svg-lib/issues", "source": "https://github.com/PhenX/php-svg-lib/tree/master"}, "time": "2019-09-11T20:02:13+00:00"}, {"name": "phpdocumentor/reflection-common", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionCommon.git", "reference": "1d01c49d4ed62f25aa84a747ad35d5a16924662b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionCommon/zipball/1d01c49d4ed62f25aa84a747ad35d5a16924662b", "reference": "1d01c49d4ed62f25aa84a747ad35d5a16924662b", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-2.x": "2.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Common reflection classes used by phpdocumentor to reflect the code structure", "homepage": "http://www.phpdoc.org", "keywords": ["FQSEN", "phpDocumentor", "phpdoc", "reflection", "static analysis"], "support": {"issues": "https://github.com/phpDocumentor/ReflectionCommon/issues", "source": "https://github.com/phpDocumentor/ReflectionCommon/tree/2.x"}, "time": "2020-06-27T09:03:43+00:00"}, {"name": "phpdocumentor/reflection-docblock", "version": "4.3.4", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "reference": "da3fd972d6bafd628114f7e7e036f45944b62e9c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/da3fd972d6bafd628114f7e7e036f45944b62e9c", "reference": "da3fd972d6bafd628114f7e7e036f45944b62e9c", "shasum": ""}, "require": {"php": "^7.0", "phpdocumentor/reflection-common": "^1.0.0 || ^2.0.0", "phpdocumentor/type-resolver": "~0.4 || ^1.0.0", "webmozart/assert": "^1.0"}, "require-dev": {"doctrine/instantiator": "^1.0.5", "mockery/mockery": "^1.0", "phpdocumentor/type-resolver": "0.4.*", "phpunit/phpunit": "^6.4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": ["src/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "With this component, a library can provide support for annotations via DocBlocks or otherwise retrieve information that is embedded in a DocBlock.", "support": {"issues": "https://github.com/phpDocumentor/ReflectionDocBlock/issues", "source": "https://github.com/phpDocumentor/ReflectionDocBlock/tree/release/4.x"}, "time": "2019-12-28T18:55:12+00:00"}, {"name": "phpdocumentor/type-resolver", "version": "1.4.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/TypeResolver.git", "reference": "6a467b8989322d92aa1c8bf2bebcc6e5c2ba55c0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/6a467b8989322d92aa1c8bf2bebcc6e5c2ba55c0", "reference": "6a467b8989322d92aa1c8bf2bebcc6e5c2ba55c0", "shasum": ""}, "require": {"php": "^7.2 || ^8.0", "phpdocumentor/reflection-common": "^2.0"}, "require-dev": {"ext-tokenizer": "*"}, "type": "library", "extra": {"branch-alias": {"dev-1.x": "1.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A PSR-5 based resolver of Class names, Types and Structural Element Names", "support": {"issues": "https://github.com/phpDocumentor/TypeResolver/issues", "source": "https://github.com/phpDocumentor/TypeResolver/tree/1.4.0"}, "time": "2020-09-17T18:55:26+00:00"}, {"name": "phpmailer/phpmailer", "version": "v5.2.28", "source": {"type": "git", "url": "https://github.com/PHPMailer/PHPMailer.git", "reference": "acba50393dd03da69a50226c139722af8b153b11"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPMailer/PHPMailer/zipball/acba50393dd03da69a50226c139722af8b153b11", "reference": "acba50393dd03da69a50226c139722af8b153b11", "shasum": ""}, "require": {"ext-ctype": "*", "php": ">=5.0.0"}, "require-dev": {"doctrine/annotations": "1.2.*", "jms/serializer": "0.16.*", "phpdocumentor/phpdocumentor": "2.*", "phpunit/phpunit": "4.8.*", "symfony/debug": "2.8.*", "symfony/filesystem": "2.8.*", "symfony/translation": "2.8.*", "symfony/yaml": "2.8.*", "zendframework/zend-cache": "2.5.1", "zendframework/zend-config": "2.5.1", "zendframework/zend-eventmanager": "2.5.1", "zendframework/zend-filter": "2.5.1", "zendframework/zend-i18n": "2.5.1", "zendframework/zend-json": "2.5.1", "zendframework/zend-math": "2.5.1", "zendframework/zend-serializer": "2.5.*", "zendframework/zend-servicemanager": "2.5.*", "zendframework/zend-stdlib": "2.5.1"}, "suggest": {"league/oauth2-google": "Needed for Google XOAUTH2 authentication"}, "type": "library", "autoload": {"classmap": ["class.phpmailer.php", "class.phpmaileroauth.php", "class.phpmaileroauthgoogle.php", "class.smtp.php", "class.pop3.php", "extras/EasyPeasyICS.php", "extras/ntlm_sasl_client.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}], "description": "PHPMailer is a full-featured email creation and transfer class for PHP", "support": {"issues": "https://github.com/PHPMailer/PHPMailer/issues", "source": "https://github.com/PHPMailer/PHPMailer/tree/v5.2.28"}, "funding": [{"url": "https://marcus.bointon.com/donations/", "type": "custom"}, {"url": "https://github.com/Synchro", "type": "github"}, {"url": "https://www.patreon.com/marcusbointon", "type": "patreon"}], "time": "2020-03-19T14:29:37+00:00"}, {"name": "phpoffice/phpspreadsheet", "version": "1.16.0", "source": {"type": "git", "url": "https://github.com/PHPOffice/PhpSpreadsheet.git", "reference": "76d4323b85129d0c368149c831a07a3e258b2b50"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPOffice/PhpSpreadsheet/zipball/76d4323b85129d0c368149c831a07a3e258b2b50", "reference": "76d4323b85129d0c368149c831a07a3e258b2b50", "shasum": ""}, "require": {"ext-ctype": "*", "ext-dom": "*", "ext-fileinfo": "*", "ext-gd": "*", "ext-iconv": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-simplexml": "*", "ext-xml": "*", "ext-xmlreader": "*", "ext-xmlwriter": "*", "ext-zip": "*", "ext-zlib": "*", "ezyang/htmlpurifier": "^4.13", "maennchen/zipstream-php": "^2.1", "markbaker/complex": "^1.5||^2.0", "markbaker/matrix": "^1.2||^2.0", "php": "^7.2||^8.0", "psr/http-client": "^1.0", "psr/http-factory": "^1.0", "psr/simple-cache": "^1.0"}, "require-dev": {"dompdf/dompdf": "^0.8.5", "friendsofphp/php-cs-fixer": "^2.16", "jpgraph/jpgraph": "^4.0", "mpdf/mpdf": "^8.0", "phpcompatibility/php-compatibility": "^9.3", "phpunit/phpunit": "^8.5||^9.3", "squizlabs/php_codesniffer": "^3.5", "tecnickcom/tcpdf": "^6.3"}, "suggest": {"dompdf/dompdf": "Option for rendering PDF with PDF Writer (doesn't yet support PHP8)", "jpgraph/jpgraph": "Option for rendering charts, or including charts with PDF or HTML Writers", "mpdf/mpdf": "Option for rendering PDF with PDF Writer", "tecnickcom/tcpdf": "Option for rendering PDF with PDF Writer (doesn't yet support PHP8)"}, "type": "library", "autoload": {"psr-4": {"PhpOffice\\PhpSpreadsheet\\": "src/PhpSpreadsheet"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://blog.maartenballiauw.be"}, {"name": "<PERSON>", "homepage": "https://markbakeruk.net"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://rootslabs.net"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}], "description": "PHPSpreadsheet - Read, Create and Write Spreadsheet documents in PHP - Spreadsheet engine", "homepage": "https://github.com/PHPOffice/PhpSpreadsheet", "keywords": ["OpenXML", "excel", "gnumeric", "ods", "php", "spreadsheet", "xls", "xlsx"], "support": {"issues": "https://github.com/PHPOffice/PhpSpreadsheet/issues", "source": "https://github.com/PHPOffice/PhpSpreadsheet/tree/1.16.0"}, "time": "2020-12-31T18:03:49+00:00"}, {"name": "phpoption/phpoption", "version": "1.7.5", "source": {"type": "git", "url": "https://github.com/schmittjoh/php-option.git", "reference": "994ecccd8f3283ecf5ac33254543eb0ac946d525"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/schmittjoh/php-option/zipball/994ecccd8f3283ecf5ac33254543eb0ac946d525", "reference": "994ecccd8f3283ecf5ac33254543eb0ac946d525", "shasum": ""}, "require": {"php": "^5.5.9 || ^7.0 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.4.1", "phpunit/phpunit": "^4.8.35 || ^5.7.27 || ^6.5.6 || ^7.0 || ^8.0 || ^9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.7-dev"}}, "autoload": {"psr-4": {"PhpOption\\": "src/PhpOption/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Option Type for PHP", "keywords": ["language", "option", "php", "type"], "support": {"issues": "https://github.com/schmittjoh/php-option/issues", "source": "https://github.com/schmittjoh/php-option/tree/1.7.5"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/phpoption/phpoption", "type": "tidelift"}], "time": "2020-07-20T17:29:33+00:00"}, {"name": "phpseclib/phpseclib", "version": "3.0.5", "source": {"type": "git", "url": "https://github.com/phpseclib/phpseclib.git", "reference": "7c751ea006577e4c2e83326d90c8b1e8c11b8ede"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpseclib/phpseclib/zipball/7c751ea006577e4c2e83326d90c8b1e8c11b8ede", "reference": "7c751ea006577e4c2e83326d90c8b1e8c11b8ede", "shasum": ""}, "require": {"paragonie/constant_time_encoding": "^1|^2", "paragonie/random_compat": "^1.4|^2.0|^9.99.99", "php": ">=5.6.1"}, "require-dev": {"phing/phing": "~2.7", "phpunit/phpunit": "^5.7|^6.0|^9.4", "squizlabs/php_codesniffer": "~2.0"}, "suggest": {"ext-gmp": "Install the GMP (GNU Multiple Precision) extension in order to speed up arbitrary precision integer arithmetic operations.", "ext-libsodium": "SSH2/SFTP can make use of some algorithms provided by the libsodium-php extension.", "ext-mcrypt": "Install the Mcrypt extension in order to speed up a few other cryptographic operations.", "ext-openssl": "Install the OpenSSL extension in order to speed up a wide variety of cryptographic operations."}, "type": "library", "autoload": {"files": ["phpseclib/bootstrap.php"], "psr-4": {"phpseclib3\\": "phpseclib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Lead Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "PHP Secure Communications Library - Pure-PHP implementations of RSA, AES, SSH2, SFTP, X.509 etc.", "homepage": "http://phpseclib.sourceforge.net", "keywords": ["BigInteger", "aes", "asn.1", "asn1", "blowfish", "crypto", "cryptography", "encryption", "rsa", "security", "sftp", "signature", "signing", "ssh", "twofish", "x.509", "x509"], "support": {"issues": "https://github.com/phpseclib/phpseclib/issues", "source": "https://github.com/phpseclib/phpseclib/tree/3.0.5"}, "funding": [{"url": "https://github.com/terrafrost", "type": "github"}, {"url": "https://www.patreon.com/phpseclib", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/phpseclib/phpseclib", "type": "tidelift"}], "time": "2021-02-12T16:18:16+00:00"}, {"name": "psr/cache", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/cache.git", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/cache/zipball/d11b50ad223250cf17b86e38383413f5a6764bf8", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Cache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for caching libraries", "keywords": ["cache", "psr", "psr-6"], "support": {"source": "https://github.com/php-fig/cache/tree/master"}, "time": "2016-08-06T20:24:11+00:00"}, {"name": "psr/container", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "b7ce3b176482dbbc1245ebf52b181af44c2cf55f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/b7ce3b176482dbbc1245ebf52b181af44c2cf55f", "reference": "b7ce3b176482dbbc1245ebf52b181af44c2cf55f", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/master"}, "time": "2017-02-14T16:28:37+00:00"}, {"name": "psr/http-client", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/http-client.git", "reference": "2dfb5f6c5eff0e91e20e913f8c5452ed95b86621"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-client/zipball/2dfb5f6c5eff0e91e20e913f8c5452ed95b86621", "reference": "2dfb5f6c5eff0e91e20e913f8c5452ed95b86621", "shasum": ""}, "require": {"php": "^7.0 || ^8.0", "psr/http-message": "^1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP clients", "homepage": "https://github.com/php-fig/http-client", "keywords": ["http", "http-client", "psr", "psr-18"], "support": {"source": "https://github.com/php-fig/http-client/tree/master"}, "time": "2020-06-29T06:28:15+00:00"}, {"name": "psr/http-factory", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/http-factory.git", "reference": "12ac7fcd07e5b077433f5f2bee95b3a771bf61be"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-factory/zipball/12ac7fcd07e5b077433f5f2bee95b3a771bf61be", "reference": "12ac7fcd07e5b077433f5f2bee95b3a771bf61be", "shasum": ""}, "require": {"php": ">=7.0.0", "psr/http-message": "^1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interfaces for PSR-7 HTTP message factories", "keywords": ["factory", "http", "message", "psr", "psr-17", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-factory/tree/master"}, "time": "2019-04-30T12:38:16+00:00"}, {"name": "psr/http-message", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/f6561bf28d520154e4b0ec72be95418abe6d9363", "reference": "f6561bf28d520154e4b0ec72be95418abe6d9363", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-message/tree/master"}, "time": "2016-08-06T14:39:51+00:00"}, {"name": "psr/log", "version": "1.1.3", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "0f73288fd15629204f9d42b7055f72dacbe811fc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/0f73288fd15629204f9d42b7055f72dacbe811fc", "reference": "0f73288fd15629204f9d42b7055f72dacbe811fc", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/1.1.3"}, "time": "2020-03-23T09:12:05+00:00"}, {"name": "psr/simple-cache", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/simple-cache.git", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/simple-cache/zipball/408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\SimpleCache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interfaces for simple caching", "keywords": ["cache", "caching", "psr", "psr-16", "simple-cache"], "support": {"source": "https://github.com/php-fig/simple-cache/tree/master"}, "time": "2017-10-23T01:57:42+00:00"}, {"name": "psy/psysh", "version": "v0.9.12", "source": {"type": "git", "url": "https://github.com/bobthecow/psysh.git", "reference": "90da7f37568aee36b116a030c5f99c915267edd4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/bobthecow/psysh/zipball/90da7f37568aee36b116a030c5f99c915267edd4", "reference": "90da7f37568aee36b116a030c5f99c915267edd4", "shasum": ""}, "require": {"dnoegel/php-xdg-base-dir": "0.1.*", "ext-json": "*", "ext-tokenizer": "*", "jakub-onderka/php-console-highlighter": "0.3.*|0.4.*", "nikic/php-parser": "~1.3|~2.0|~3.0|~4.0", "php": ">=5.4.0", "symfony/console": "~2.3.10|^2.4.2|~3.0|~4.0|~5.0", "symfony/var-dumper": "~2.7|~3.0|~4.0|~5.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.2", "hoa/console": "~2.15|~3.16", "phpunit/phpunit": "~4.8.35|~5.0|~6.0|~7.0"}, "suggest": {"ext-pcntl": "Enabling the PCNTL extension makes PsySH a lot happier :)", "ext-pdo-sqlite": "The doc command requires SQLite to work.", "ext-posix": "If you have PCNTL, you'll want the POSIX extension as well.", "ext-readline": "Enables support for arrow-key history navigation, and showing and manipulating command history.", "hoa/console": "A pure PHP readline implementation. You'll want this if your PHP install doesn't already support readline or libedit."}, "bin": ["bin/psysh"], "type": "library", "extra": {"branch-alias": {"dev-develop": "0.9.x-dev"}}, "autoload": {"files": ["src/functions.php"], "psr-4": {"Psy\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://justinhileman.com"}], "description": "An interactive shell for modern PHP.", "homepage": "http://psysh.org", "keywords": ["REPL", "console", "interactive", "shell"], "support": {"issues": "https://github.com/bobthecow/psysh/issues", "source": "https://github.com/bobthecow/psysh/tree/v0.9.12"}, "time": "2019-12-06T14:19:43+00:00"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "120b605dfeb996808c31b6477290a714d356e822"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "type": "library", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "support": {"issues": "https://github.com/ralouphie/getallheaders/issues", "source": "https://github.com/ralouphie/getallheaders/tree/develop"}, "time": "2019-03-08T08:55:37+00:00"}, {"name": "ralouphie/mimey", "version": "1.0.8", "source": {"type": "git", "url": "https://github.com/ralouphie/mimey.git", "reference": "d69688d4856daa13a9124c819d148ccc2235dea2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/mimey/zipball/d69688d4856daa13a9124c819d148ccc2235dea2", "reference": "d69688d4856daa13a9124c819d148ccc2235dea2", "shasum": ""}, "require": {"php": "^5.3|^7.0"}, "require-dev": {"phpunit/phpunit": "~3.7.0", "satooshi/php-coveralls": ">=1.0"}, "type": "library", "autoload": {"psr-4": {"Mimey\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP package for converting file extensions to MIME types and vice versa.", "support": {"issues": "https://github.com/ralouphie/mimey/issues", "source": "https://github.com/ralouphie/mimey/tree/master"}, "time": "2017-01-27T20:57:22+00:00"}, {"name": "ramsey/uuid", "version": "3.9.3", "source": {"type": "git", "url": "https://github.com/ramsey/uuid.git", "reference": "7e1633a6964b48589b142d60542f9ed31bd37a92"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ramsey/uuid/zipball/7e1633a6964b48589b142d60542f9ed31bd37a92", "reference": "7e1633a6964b48589b142d60542f9ed31bd37a92", "shasum": ""}, "require": {"ext-json": "*", "paragonie/random_compat": "^1 | ^2 | 9.99.99", "php": "^5.4 | ^7 | ^8", "symfony/polyfill-ctype": "^1.8"}, "replace": {"rhumsaa/uuid": "self.version"}, "require-dev": {"codeception/aspect-mock": "^1 | ^2", "doctrine/annotations": "^1.2", "goaop/framework": "1.0.0-alpha.2 | ^1 | ^2.1", "jakub-onderka/php-parallel-lint": "^1", "mockery/mockery": "^0.9.11 | ^1", "moontoast/math": "^1.1", "paragonie/random-lib": "^2", "php-mock/php-mock-phpunit": "^0.3 | ^1.1", "phpunit/phpunit": "^4.8 | ^5.4 | ^6.5", "squizlabs/php_codesniffer": "^3.5"}, "suggest": {"ext-ctype": "Provides support for PHP Ctype functions", "ext-libsodium": "Provides the PECL libsodium extension for use with the SodiumRandomGenerator", "ext-openssl": "Provides the OpenSSL extension for use with the OpenSslGenerator", "ext-uuid": "Provides the PECL UUID extension for use with the PeclUuidTimeGenerator and PeclUuidRandomGenerator", "moontoast/math": "Provides support for converting UUID to 128-bit integer (in string form).", "paragonie/random-lib": "Provides RandomLib for use with the RandomLibAdapter", "ramsey/uuid-console": "A console application for generating UUIDs with ramsey/uuid", "ramsey/uuid-doctrine": "Allows the use of Ramsey\\Uuid\\Uuid as Doctrine field type."}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Ramsey\\Uuid\\": "src/"}, "files": ["src/functions.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://benramsey.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Formerly rhumsaa/uuid. A PHP 5.4+ library for generating RFC 4122 version 1, 3, 4, and 5 universally unique identifiers (UUID).", "homepage": "https://github.com/ramsey/uuid", "keywords": ["guid", "identifier", "uuid"], "support": {"issues": "https://github.com/ramsey/uuid/issues", "rss": "https://github.com/ramsey/uuid/releases.atom", "source": "https://github.com/ramsey/uuid", "wiki": "https://github.com/ramsey/uuid/wiki"}, "time": "2020-02-21T04:36:14+00:00"}, {"name": "react/promise", "version": "v2.8.0", "source": {"type": "git", "url": "https://github.com/reactphp/promise.git", "reference": "f3cff96a19736714524ca0dd1d4130de73dbbbc4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/reactphp/promise/zipball/f3cff96a19736714524ca0dd1d4130de73dbbbc4", "reference": "f3cff96a19736714524ca0dd1d4130de73dbbbc4", "shasum": ""}, "require": {"php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "^7.0 || ^6.5 || ^5.7 || ^4.8.36"}, "type": "library", "autoload": {"psr-4": {"React\\Promise\\": "src/"}, "files": ["src/functions_include.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A lightweight implementation of CommonJS Promises/A for PHP", "keywords": ["promise", "promises"], "support": {"issues": "https://github.com/reactphp/promise/issues", "source": "https://github.com/reactphp/promise/tree/v2.8.0"}, "time": "2020-05-12T15:16:56+00:00"}, {"name": "sabberworm/php-css-parser", "version": "8.3.1", "source": {"type": "git", "url": "https://github.com/sabberworm/PHP-CSS-Parser.git", "reference": "d217848e1396ef962fb1997cf3e2421acba7f796"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sabberworm/PHP-CSS-Parser/zipball/d217848e1396ef962fb1997cf3e2421acba7f796", "reference": "d217848e1396ef962fb1997cf3e2421acba7f796", "shasum": ""}, "require": {"php": ">=5.3.2"}, "require-dev": {"codacy/coverage": "^1.4", "phpunit/phpunit": "~4.8"}, "type": "library", "autoload": {"psr-0": {"Sabberworm\\CSS": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "Parser for CSS Files written in PHP", "homepage": "http://www.sabberworm.com/blog/2010/6/10/php-css-parser", "keywords": ["css", "parser", "stylesheet"], "support": {"issues": "https://github.com/sabberworm/PHP-CSS-Parser/issues", "source": "https://github.com/sabberworm/PHP-CSS-Parser/tree/8.3.1"}, "time": "2020-06-01T09:10:00+00:00"}, {"name": "scrivo/highlight.php", "version": "v9.18.1.6", "source": {"type": "git", "url": "https://github.com/scrivo/highlight.php.git", "reference": "44a3d4136edb5ad8551590bf90f437db80b2d466"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/scrivo/highlight.php/zipball/44a3d4136edb5ad8551590bf90f437db80b2d466", "reference": "44a3d4136edb5ad8551590bf90f437db80b2d466", "shasum": ""}, "require": {"ext-json": "*", "ext-mbstring": "*", "php": ">=5.4"}, "require-dev": {"phpunit/phpunit": "^4.8|^5.7", "sabberworm/php-css-parser": "^8.3", "symfony/finder": "^2.8|^3.4", "symfony/var-dumper": "^2.8|^3.4"}, "type": "library", "autoload": {"psr-0": {"Highlight\\": "", "HighlightUtilities\\": ""}, "files": ["HighlightUtilities/functions.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "homepage": "http://www.scrivo.org/", "role": "Project Author"}, {"name": "<PERSON>", "homepage": "https://allejo.io", "role": "Maintainer"}, {"name": "<PERSON>", "homepage": "https://twobrain.io", "role": "Contributor"}], "description": "Server side syntax highlighter that supports 185 languages. It's a PHP port of highlight.js", "keywords": ["code", "highlight", "highlight.js", "highlight.php", "syntax"], "support": {"issues": "https://github.com/scrivo/highlight.php/issues", "source": "https://github.com/scrivo/highlight.php"}, "funding": [{"url": "https://github.com/allejo", "type": "github"}], "time": "2020-12-22T19:20:29+00:00"}, {"name": "sentry/sentry", "version": "1.11.0", "source": {"type": "git", "url": "https://github.com/getsentry/sentry-php.git", "reference": "159eeaa02bb2ef8a8ec669f3c88e4bff7e6a7ffe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/getsentry/sentry-php/zipball/159eeaa02bb2ef8a8ec669f3c88e4bff7e6a7ffe", "reference": "159eeaa02bb2ef8a8ec669f3c88e4bff7e6a7ffe", "shasum": ""}, "require": {"ext-curl": "*", "php": "^5.3|^7.0"}, "conflict": {"raven/raven": "*"}, "require-dev": {"friendsofphp/php-cs-fixer": "^1.8.0", "monolog/monolog": "^1.0", "phpunit/phpunit": "^4.8.35 || ^5.7"}, "suggest": {"ext-hash": "*", "ext-json": "*", "ext-mbstring": "*", "monolog/monolog": "Automatically capture Monolog events as breadcrumbs"}, "bin": ["bin/sentry"], "type": "library", "extra": {"branch-alias": {"dev-master": "1.11.x-dev"}}, "autoload": {"psr-0": {"Raven_": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A PHP client for Sentry (http://getsentry.com)", "homepage": "http://getsentry.com", "keywords": ["log", "logging"], "support": {"issues": "https://github.com/getsentry/sentry-php/issues", "source": "https://github.com/getsentry/sentry-php/tree/1.11.0"}, "time": "2020-02-12T18:38:11+00:00"}, {"name": "setasign/fpdi", "version": "v2.3.6", "source": {"type": "git", "url": "https://github.com/Setasign/FPDI.git", "reference": "6231e315f73e4f62d72b73f3d6d78ff0eed93c31"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Setasign/FPDI/zipball/6231e315f73e4f62d72b73f3d6d78ff0eed93c31", "reference": "6231e315f73e4f62d72b73f3d6d78ff0eed93c31", "shasum": ""}, "require": {"ext-zlib": "*", "php": "^5.6 || ^7.0 || ^8.0"}, "conflict": {"setasign/tfpdf": "<1.31"}, "require-dev": {"phpunit/phpunit": "~5.7", "setasign/fpdf": "~1.8", "setasign/tfpdf": "1.31", "squizlabs/php_codesniffer": "^3.5", "tecnickcom/tcpdf": "~6.2"}, "suggest": {"setasign/fpdf": "FPDI will extend this class but as it is also possible to use TCPDF or tFPDF as an alternative. There's no fixed dependency configured."}, "type": "library", "autoload": {"psr-4": {"setasign\\Fpdi\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.setasign.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.setasign.com"}], "description": "FPDI is a collection of PHP classes facilitating developers to read pages from existing PDF documents and use them as templates in FPDF. Because it is also possible to use FPDI with TCPDF, there are no fixed dependencies defined. Please see suggestions for packages which evaluates the dependencies automatically.", "homepage": "https://www.setasign.com/fpdi", "keywords": ["fpdf", "fpdi", "pdf"], "support": {"issues": "https://github.com/Setasign/FPDI/issues", "source": "https://github.com/Setasign/FPDI/tree/v2.3.6"}, "funding": [{"url": "https://tidelift.com/funding/github/packagist/setasign/fpdi", "type": "tidelift"}], "time": "2021-02-11T11:37:01+00:00"}, {"name": "solarium/solarium", "version": "5.2.0", "source": {"type": "git", "url": "https://github.com/solariumphp/solarium.git", "reference": "9208b615cb2ed6f306be6e696431b6b71e4d42db"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/solariumphp/solarium/zipball/9208b615cb2ed6f306be6e696431b6b71e4d42db", "reference": "9208b615cb2ed6f306be6e696431b6b71e4d42db", "shasum": ""}, "require": {"ext-json": "*", "php": "^7.1.3", "psr/http-client": "^1.0", "psr/http-factory": "^1.0", "symfony/event-dispatcher": "^4.3 || ^5.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.16", "guzzlehttp/guzzle": "^3.8 || ^6.2", "nyholm/psr7": "^1.2", "php-coveralls/php-coveralls": "^2.1", "php-http/guzzle6-adapter": "^2.0", "phpunit/phpunit": "^8.0", "squizlabs/php_codesniffer": "^3.4", "symfony/phpunit-bridge": "^5.0", "zendframework/zend-http": "^2.8"}, "suggest": {"minimalcode/search": "Query builder compatible with Solarium, allows simplified solr-query handling"}, "type": "library", "autoload": {"psr-4": {"Solarium\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "See GitHub contributors", "homepage": "https://github.com/basdenooijer/solarium/contributors"}], "description": "PHP Solr client", "homepage": "http://www.solarium-project.org", "keywords": ["php", "search", "solr"], "support": {"issues": "https://github.com/solariumphp/solarium/issues", "source": "https://github.com/solariumphp/solarium/tree/5.x"}, "time": "2020-04-03T22:16:30+00:00"}, {"name": "spatie/browsershot", "version": "3.40.2", "source": {"type": "git", "url": "https://github.com/spatie/browsershot.git", "reference": "3e55eaf5ab8cee65d1661a567e89b3374afb9116"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/spatie/browsershot/zipball/3e55eaf5ab8cee65d1661a567e89b3374afb9116", "reference": "3e55eaf5ab8cee65d1661a567e89b3374afb9116", "shasum": ""}, "require": {"php": "^7.1", "spatie/image": "^1.5.3", "spatie/temporary-directory": "^1.1", "symfony/process": "^4.2|^5.0"}, "require-dev": {"phpunit/phpunit": "^6.1|^7.5", "spatie/phpunit-snapshot-assertions": "^1.0"}, "type": "library", "autoload": {"psr-4": {"Spatie\\Browsershot\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/freekmurze", "role": "Developer"}], "description": "Convert a webpage to an image or pdf using headless Chrome", "homepage": "https://github.com/spatie/browsershot", "keywords": ["chrome", "convert", "headless", "image", "pdf", "puppeteer", "screenshot", "webpage"], "support": {"issues": "https://github.com/spatie/browsershot/issues", "source": "https://github.com/spatie/browsershot/tree/3.40.2"}, "funding": [{"url": "https://github.com/spatie", "type": "github"}], "time": "2020-11-11T22:18:15+00:00"}, {"name": "spatie/crawler", "version": "4.7.6", "source": {"type": "git", "url": "https://github.com/spatie/crawler.git", "reference": "fe6e428b8ceaec561b9819bb948242ad0cfd40bb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/spatie/crawler/zipball/fe6e428b8ceaec561b9819bb948242ad0cfd40bb", "reference": "fe6e428b8ceaec561b9819bb948242ad0cfd40bb", "shasum": ""}, "require": {"guzzlehttp/guzzle": "^6.3 || ^7.0", "guzzlehttp/psr7": "^1.4", "nicmart/tree": "^0.3.0", "php": "^7.3|^8.0", "spatie/browsershot": "^3.14", "spatie/robots-txt": "^1.0.1", "symfony/dom-crawler": "^4.0 || ^5.0", "tightenco/collect": "^5.6 || ^6.0 || ^7.0"}, "require-dev": {"larapack/dd": "^1.1", "phpunit/phpunit": "^9.0"}, "type": "library", "autoload": {"psr-4": {"Spatie\\Crawler\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Crawl all internal links found on a website", "homepage": "https://github.com/spatie/crawler", "keywords": ["crawler", "link", "spatie", "website"], "support": {"issues": "https://github.com/spatie/crawler/issues", "source": "https://github.com/spatie/crawler/tree/4.7.6"}, "funding": [{"url": "https://github.com/spatie", "type": "github"}], "time": "2020-12-01T20:55:10+00:00"}, {"name": "spatie/image", "version": "1.10.2", "source": {"type": "git", "url": "https://github.com/spatie/image.git", "reference": "12662673fbe649bffcd3a24188a404dc31fa118c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/spatie/image/zipball/12662673fbe649bffcd3a24188a404dc31fa118c", "reference": "12662673fbe649bffcd3a24188a404dc31fa118c", "shasum": ""}, "require": {"ext-exif": "*", "ext-mbstring": "*", "league/glide": "^1.6", "php": "^7.2|^8.0", "spatie/image-optimizer": "^1.1", "spatie/temporary-directory": "^1.0", "symfony/process": "^3.0|^4.0|^5.0"}, "require-dev": {"phpunit/phpunit": "^8.0|^9.0", "symfony/var-dumper": "^4.0|^5.0"}, "type": "library", "autoload": {"psr-4": {"Spatie\\Image\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://spatie.be", "role": "Developer"}], "description": "Manipulate images with an expressive API", "homepage": "https://github.com/spatie/image", "keywords": ["image", "spatie"], "support": {"issues": "https://github.com/spatie/image/issues", "source": "https://github.com/spatie/image/tree/1.10.2"}, "funding": [{"url": "https://spatie.be/open-source/support-us", "type": "custom"}, {"url": "https://github.com/spatie", "type": "github"}], "time": "2021-01-26T07:53:19+00:00"}, {"name": "spatie/image-optimizer", "version": "1.3.2", "source": {"type": "git", "url": "https://github.com/spatie/image-optimizer.git", "reference": "6aa170eb292758553d332efee5e0c3977341080c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/spatie/image-optimizer/zipball/6aa170eb292758553d332efee5e0c3977341080c", "reference": "6aa170eb292758553d332efee5e0c3977341080c", "shasum": ""}, "require": {"ext-fileinfo": "*", "php": "^7.2|^8.0", "psr/log": "^1.0", "symfony/process": "^4.2|^5.0"}, "require-dev": {"phpunit/phpunit": "^8.0|^9.0", "symfony/var-dumper": "^4.2|^5.0"}, "type": "library", "autoload": {"psr-4": {"Spatie\\ImageOptimizer\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://spatie.be", "role": "Developer"}], "description": "Easily optimize images using PHP", "homepage": "https://github.com/spatie/image-optimizer", "keywords": ["image-optimizer", "spatie"], "support": {"issues": "https://github.com/spatie/image-optimizer/issues", "source": "https://github.com/spatie/image-optimizer/tree/1.3.2"}, "time": "2020-11-28T12:37:58+00:00"}, {"name": "spatie/laravel-permission", "version": "3.18.0", "source": {"type": "git", "url": "https://github.com/spatie/laravel-permission.git", "reference": "1c51a5fa12131565fe3860705163e53d7a26258a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/spatie/laravel-permission/zipball/1c51a5fa12131565fe3860705163e53d7a26258a", "reference": "1c51a5fa12131565fe3860705163e53d7a26258a", "shasum": ""}, "require": {"illuminate/auth": "^5.8|^6.0|^7.0|^8.0", "illuminate/container": "^5.8|^6.0|^7.0|^8.0", "illuminate/contracts": "^5.8|^6.0|^7.0|^8.0", "illuminate/database": "^5.8|^6.0|^7.0|^8.0", "php": "^7.2.5|^8.0"}, "require-dev": {"orchestra/testbench": "^3.8|^4.0|^5.0|^6.0", "phpunit/phpunit": "^8.0|^9.0", "predis/predis": "^1.1"}, "type": "library", "extra": {"laravel": {"providers": ["Spatie\\Permission\\PermissionServiceProvider"]}}, "autoload": {"psr-4": {"Spatie\\Permission\\": "src"}, "files": ["src/helpers.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://spatie.be", "role": "Developer"}], "description": "Permission handling for Laravel 5.8 and up", "homepage": "https://github.com/spatie/laravel-permission", "keywords": ["acl", "laravel", "permission", "permissions", "rbac", "roles", "security", "spatie"], "support": {"issues": "https://github.com/spatie/laravel-permission/issues", "source": "https://github.com/spatie/laravel-permission/tree/3.18.0"}, "funding": [{"url": "https://github.com/spatie", "type": "github"}], "time": "2020-11-09T14:08:36+00:00"}, {"name": "spatie/laravel-sitemap", "version": "5.8.0", "source": {"type": "git", "url": "https://github.com/spatie/laravel-sitemap.git", "reference": "90c4dd061ba251c2bff9edf83d5e1d38d17f1529"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/spatie/laravel-sitemap/zipball/90c4dd061ba251c2bff9edf83d5e1d38d17f1529", "reference": "90c4dd061ba251c2bff9edf83d5e1d38d17f1529", "shasum": ""}, "require": {"illuminate/support": "~5.8.0|^6.0|^7.0|^8.0", "nesbot/carbon": "^1.21|^2.0", "php": "^7.2", "spatie/crawler": "^4.1.0"}, "require-dev": {"mockery/mockery": "^1.3.1", "orchestra/testbench": "~3.8.8|^4.0|^5.0|^6.0", "phpunit/phpunit": "^8.3|^9.0|^9.3", "spatie/phpunit-snapshot-assertions": "^3.0.0", "spatie/temporary-directory": "^1.1"}, "type": "library", "extra": {"laravel": {"providers": ["Spatie\\Sitemap\\SitemapServiceProvider"]}}, "autoload": {"psr-4": {"Spatie\\Sitemap\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://spatie.be", "role": "Developer"}], "description": "Create and generate sitemaps with ease", "homepage": "https://github.com/spatie/laravel-sitemap", "keywords": ["laravel-sitemap", "spatie"], "support": {"issues": "https://github.com/spatie/laravel-sitemap/issues", "source": "https://github.com/spatie/laravel-sitemap/tree/5.8.0"}, "funding": [{"url": "https://spatie.be/open-source/support-us", "type": "custom"}], "time": "2020-09-09T09:29:31+00:00"}, {"name": "spatie/robots-txt", "version": "1.0.10", "source": {"type": "git", "url": "https://github.com/spatie/robots-txt.git", "reference": "8802a2bee670b3c13cfd21ede0322f72b3efb711"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/spatie/robots-txt/zipball/8802a2bee670b3c13cfd21ede0322f72b3efb711", "reference": "8802a2bee670b3c13cfd21ede0322f72b3efb711", "shasum": ""}, "require": {"php": "^7.3 || ^8.0"}, "require-dev": {"larapack/dd": "^1.0", "phpunit/phpunit": "^8.0 || ^9.0"}, "type": "library", "autoload": {"psr-4": {"Spatie\\Robots\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://spatie.be", "role": "Developer"}], "description": "Determine if a page may be crawled from robots.txt and robots meta tags", "homepage": "https://github.com/spatie/robots-txt", "keywords": ["robots-txt", "spatie"], "support": {"issues": "https://github.com/spatie/robots-txt/issues", "source": "https://github.com/spatie/robots-txt/tree/1.0.10"}, "time": "2020-12-07T11:10:49+00:00"}, {"name": "spatie/schema-org", "version": "2.16.0", "source": {"type": "git", "url": "https://github.com/spatie/schema-org.git", "reference": "31f1fd54584d98b50026ecb17fe7067384ec209a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/spatie/schema-org/zipball/31f1fd54584d98b50026ecb17fe7067384ec209a", "reference": "31f1fd54584d98b50026ecb17fe7067384ec209a", "shasum": ""}, "require": {"ext-json": "*", "php": "^7.3"}, "require-dev": {"graham-campbell/analyzer": "^2.3", "league/flysystem": "^1.0", "phpunit/phpunit": "^8.0", "symfony/console": "^3.2", "tightenco/collect": "^7.25", "twig/twig": "^1.28"}, "type": "library", "autoload": {"psr-4": {"Spatie\\SchemaOrg\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://spatie.be", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://gummibeer.de", "role": "Developer"}], "description": "A fluent builder Schema.org types and ld+json generator", "homepage": "https://github.com/spatie/schema-org", "keywords": ["schema-org", "spatie"], "support": {"issues": "https://github.com/spatie/schema-org/issues", "source": "https://github.com/spatie/schema-org/tree/master"}, "funding": [{"url": "https://spatie.be/open-source/support-us", "type": "custom"}, {"url": "https://github.com/spatie", "type": "github"}], "time": "2020-08-27T16:55:28+00:00"}, {"name": "spatie/temporary-directory", "version": "1.3.0", "source": {"type": "git", "url": "https://github.com/spatie/temporary-directory.git", "reference": "f517729b3793bca58f847c5fd383ec16f03ffec6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/spatie/temporary-directory/zipball/f517729b3793bca58f847c5fd383ec16f03ffec6", "reference": "f517729b3793bca58f847c5fd383ec16f03ffec6", "shasum": ""}, "require": {"php": "^7.2|^8.0"}, "require-dev": {"phpunit/phpunit": "^8.0|^9.0"}, "type": "library", "autoload": {"psr-4": {"Spatie\\TemporaryDirectory\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://spatie.be", "role": "Developer"}], "description": "Easily create, use and destroy temporary directories", "homepage": "https://github.com/spatie/temporary-directory", "keywords": ["php", "spatie", "temporary-directory"], "support": {"issues": "https://github.com/spatie/temporary-directory/issues", "source": "https://github.com/spatie/temporary-directory/tree/1.3.0"}, "time": "2020-11-09T15:54:21+00:00"}, {"name": "swiftmailer/swiftmailer", "version": "v6.2.5", "source": {"type": "git", "url": "https://github.com/swiftmailer/swiftmailer.git", "reference": "698a6a9f54d7eb321274de3ad19863802c879fb7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/swiftmailer/swiftmailer/zipball/698a6a9f54d7eb321274de3ad19863802c879fb7", "reference": "698a6a9f54d7eb321274de3ad19863802c879fb7", "shasum": ""}, "require": {"egulias/email-validator": "^2.0", "php": ">=7.0.0", "symfony/polyfill-iconv": "^1.0", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0"}, "require-dev": {"mockery/mockery": "^1.0", "symfony/phpunit-bridge": "^4.4|^5.0"}, "suggest": {"ext-intl": "Needed to support internationalized email addresses"}, "type": "library", "extra": {"branch-alias": {"dev-master": "6.2-dev"}}, "autoload": {"files": ["lib/swift_required.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Swiftmailer, free feature-rich PHP mailer", "homepage": "https://swiftmailer.symfony.com", "keywords": ["email", "mail", "mailer"], "support": {"issues": "https://github.com/swiftmailer/swiftmailer/issues", "source": "https://github.com/swiftmailer/swiftmailer/tree/v6.2.5"}, "funding": [{"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/swiftmailer/swiftmailer", "type": "tidelift"}], "time": "2021-01-12T09:35:59+00:00"}, {"name": "symfony/console", "version": "v4.4.19", "source": {"type": "git", "url": "https://github.com/symfony/console.git", "reference": "24026c44fc37099fa145707fecd43672831b837a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/console/zipball/24026c44fc37099fa145707fecd43672831b837a", "reference": "24026c44fc37099fa145707fecd43672831b837a", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php73": "^1.8", "symfony/polyfill-php80": "^1.15", "symfony/service-contracts": "^1.1|^2"}, "conflict": {"symfony/dependency-injection": "<3.4", "symfony/event-dispatcher": "<4.3|>=5", "symfony/lock": "<4.4", "symfony/process": "<3.3"}, "provide": {"psr/log-implementation": "1.0"}, "require-dev": {"psr/log": "~1.0", "symfony/config": "^3.4|^4.0|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/event-dispatcher": "^4.3", "symfony/lock": "^4.4|^5.0", "symfony/process": "^3.4|^4.0|^5.0", "symfony/var-dumper": "^4.3|^5.0"}, "suggest": {"psr/log": "For using the console logger", "symfony/event-dispatcher": "", "symfony/lock": "", "symfony/process": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Eases the creation of beautiful and testable command line interfaces", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/console/tree/v4.4.19"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-01-27T09:09:26+00:00"}, {"name": "symfony/css-selector", "version": "v5.2.3", "source": {"type": "git", "url": "https://github.com/symfony/css-selector.git", "reference": "f65f217b3314504a1ec99c2d6ef69016bb13490f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/css-selector/zipball/f65f217b3314504a1ec99c2d6ef69016bb13490f", "reference": "f65f217b3314504a1ec99c2d6ef69016bb13490f", "shasum": ""}, "require": {"php": ">=7.2.5"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\CssSelector\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Converts CSS selectors to XPath expressions", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/css-selector/tree/v5.2.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-01-27T10:01:46+00:00"}, {"name": "symfony/debug", "version": "v4.4.19", "source": {"type": "git", "url": "https://github.com/symfony/debug.git", "reference": "af4987aa4a5630e9615be9d9c3ed1b0f24ca449c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/debug/zipball/af4987aa4a5630e9615be9d9c3ed1b0f24ca449c", "reference": "af4987aa4a5630e9615be9d9c3ed1b0f24ca449c", "shasum": ""}, "require": {"php": ">=7.1.3", "psr/log": "~1.0", "symfony/polyfill-php80": "^1.15"}, "conflict": {"symfony/http-kernel": "<3.4"}, "require-dev": {"symfony/http-kernel": "^3.4|^4.0|^5.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Debug\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to ease debugging PHP code", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/debug/tree/v4.4.19"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-01-27T09:09:26+00:00"}, {"name": "symfony/deprecation-contracts", "version": "v2.2.0", "source": {"type": "git", "url": "https://github.com/symfony/deprecation-contracts.git", "reference": "5fa56b4074d1ae755beb55617ddafe6f5d78f665"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/5fa56b4074d1ae755beb55617ddafe6f5d78f665", "reference": "5fa56b4074d1ae755beb55617ddafe6f5d78f665", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"files": ["function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/master"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-09-07T11:33:47+00:00"}, {"name": "symfony/dom-crawler", "version": "v5.2.3", "source": {"type": "git", "url": "https://github.com/symfony/dom-crawler.git", "reference": "5d89ceb53ec65e1973a555072fac8ed5ecad3384"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/dom-crawler/zipball/5d89ceb53ec65e1973a555072fac8ed5ecad3384", "reference": "5d89ceb53ec65e1973a555072fac8ed5ecad3384", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php80": "^1.15"}, "conflict": {"masterminds/html5": "<2.6"}, "require-dev": {"masterminds/html5": "^2.6", "symfony/css-selector": "^4.4|^5.0"}, "suggest": {"symfony/css-selector": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\DomCrawler\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Eases DOM navigation for HTML and XML documents", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/dom-crawler/tree/v5.2.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-01-27T10:01:46+00:00"}, {"name": "symfony/error-handler", "version": "v4.4.19", "source": {"type": "git", "url": "https://github.com/symfony/error-handler.git", "reference": "d603654eaeb713503bba3e308b9e748e5a6d3f2e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/error-handler/zipball/d603654eaeb713503bba3e308b9e748e5a6d3f2e", "reference": "d603654eaeb713503bba3e308b9e748e5a6d3f2e", "shasum": ""}, "require": {"php": ">=7.1.3", "psr/log": "~1.0", "symfony/debug": "^4.4.5", "symfony/polyfill-php80": "^1.15", "symfony/var-dumper": "^4.4|^5.0"}, "require-dev": {"symfony/http-kernel": "^4.4|^5.0", "symfony/serializer": "^4.4|^5.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\ErrorHandler\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to manage errors and ease debugging PHP code", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/error-handler/tree/v4.4.19"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-01-27T09:09:26+00:00"}, {"name": "symfony/event-dispatcher", "version": "v4.4.19", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher.git", "reference": "c352647244bd376bf7d31efbd5401f13f50dad0c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/c352647244bd376bf7d31efbd5401f13f50dad0c", "reference": "c352647244bd376bf7d31efbd5401f13f50dad0c", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/event-dispatcher-contracts": "^1.1"}, "conflict": {"symfony/dependency-injection": "<3.4"}, "provide": {"psr/event-dispatcher-implementation": "1.0", "symfony/event-dispatcher-implementation": "1.1"}, "require-dev": {"psr/log": "~1.0", "symfony/config": "^3.4|^4.0|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/error-handler": "~3.4|~4.4", "symfony/expression-language": "^3.4|^4.0|^5.0", "symfony/http-foundation": "^3.4|^4.0|^5.0", "symfony/service-contracts": "^1.1|^2", "symfony/stopwatch": "^3.4|^4.0|^5.0"}, "suggest": {"symfony/dependency-injection": "", "symfony/http-kernel": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\EventDispatcher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools that allow your application components to communicate with each other by dispatching events and listening to them", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v4.4.19"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-01-27T09:09:26+00:00"}, {"name": "symfony/event-dispatcher-contracts", "version": "v1.1.9", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher-contracts.git", "reference": "84e23fdcd2517bf37aecbd16967e83f0caee25a7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/84e23fdcd2517bf37aecbd16967e83f0caee25a7", "reference": "84e23fdcd2517bf37aecbd16967e83f0caee25a7", "shasum": ""}, "require": {"php": ">=7.1.3"}, "suggest": {"psr/event-dispatcher": "", "symfony/event-dispatcher-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\EventDispatcher\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to dispatching event", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v1.1.9"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-07-06T13:19:58+00:00"}, {"name": "symfony/finder", "version": "v4.4.19", "source": {"type": "git", "url": "https://github.com/symfony/finder.git", "reference": "25d79cfccfc12e84e7a63a248c3f0720fdd92db6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/finder/zipball/25d79cfccfc12e84e7a63a248c3f0720fdd92db6", "reference": "25d79cfccfc12e84e7a63a248c3f0720fdd92db6", "shasum": ""}, "require": {"php": ">=7.1.3"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Finds files and directories via an intuitive fluent interface", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/finder/tree/v4.4.19"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-01-27T09:09:26+00:00"}, {"name": "symfony/http-client-contracts", "version": "v2.3.1", "source": {"type": "git", "url": "https://github.com/symfony/http-client-contracts.git", "reference": "41db680a15018f9c1d4b23516059633ce280ca33"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-client-contracts/zipball/41db680a15018f9c1d4b23516059633ce280ca33", "reference": "41db680a15018f9c1d4b23516059633ce280ca33", "shasum": ""}, "require": {"php": ">=7.2.5"}, "suggest": {"symfony/http-client-implementation": ""}, "type": "library", "extra": {"branch-version": "2.3", "branch-alias": {"dev-main": "2.3-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\HttpClient\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to HTTP clients", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/http-client-contracts/tree/v2.3.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-14T17:08:19+00:00"}, {"name": "symfony/http-foundation", "version": "v4.4.19", "source": {"type": "git", "url": "https://github.com/symfony/http-foundation.git", "reference": "8888741b633f6c3d1e572b7735ad2cae3e03f9c5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-foundation/zipball/8888741b633f6c3d1e572b7735ad2cae3e03f9c5", "reference": "8888741b633f6c3d1e572b7735ad2cae3e03f9c5", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/mime": "^4.3|^5.0", "symfony/polyfill-mbstring": "~1.1", "symfony/polyfill-php80": "^1.15"}, "require-dev": {"predis/predis": "~1.0", "symfony/expression-language": "^3.4|^4.0|^5.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpFoundation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Defines an object-oriented layer for the HTTP specification", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/http-foundation/tree/v4.4.19"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-01-27T09:09:26+00:00"}, {"name": "symfony/http-kernel", "version": "v4.4.19", "source": {"type": "git", "url": "https://github.com/symfony/http-kernel.git", "reference": "07ea794a327d7c8c5d76e3058fde9fec6a711cb4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-kernel/zipball/07ea794a327d7c8c5d76e3058fde9fec6a711cb4", "reference": "07ea794a327d7c8c5d76e3058fde9fec6a711cb4", "shasum": ""}, "require": {"php": ">=7.1.3", "psr/log": "~1.0", "symfony/error-handler": "^4.4", "symfony/event-dispatcher": "^4.4", "symfony/http-client-contracts": "^1.1|^2", "symfony/http-foundation": "^4.4|^5.0", "symfony/polyfill-ctype": "^1.8", "symfony/polyfill-php73": "^1.9", "symfony/polyfill-php80": "^1.15"}, "conflict": {"symfony/browser-kit": "<4.3", "symfony/config": "<3.4", "symfony/console": ">=5", "symfony/dependency-injection": "<4.3", "symfony/translation": "<4.2", "twig/twig": "<1.43|<2.13,>=2"}, "provide": {"psr/log-implementation": "1.0"}, "require-dev": {"psr/cache": "~1.0", "symfony/browser-kit": "^4.3|^5.0", "symfony/config": "^3.4|^4.0|^5.0", "symfony/console": "^3.4|^4.0", "symfony/css-selector": "^3.4|^4.0|^5.0", "symfony/dependency-injection": "^4.3|^5.0", "symfony/dom-crawler": "^3.4|^4.0|^5.0", "symfony/expression-language": "^3.4|^4.0|^5.0", "symfony/finder": "^3.4|^4.0|^5.0", "symfony/process": "^3.4|^4.0|^5.0", "symfony/routing": "^3.4|^4.0|^5.0", "symfony/stopwatch": "^3.4|^4.0|^5.0", "symfony/templating": "^3.4|^4.0|^5.0", "symfony/translation": "^4.2|^5.0", "symfony/translation-contracts": "^1.1|^2", "twig/twig": "^1.43|^2.13|^3.0.4"}, "suggest": {"symfony/browser-kit": "", "symfony/config": "", "symfony/console": "", "symfony/dependency-injection": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpKernel\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a structured process for converting a Request into a Response", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/http-kernel/tree/v4.4.19"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-01-27T13:50:53+00:00"}, {"name": "symfony/mime", "version": "v5.2.3", "source": {"type": "git", "url": "https://github.com/symfony/mime.git", "reference": "7dee6a43493f39b51ff6c5bb2bd576fe40a76c86"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/mime/zipball/7dee6a43493f39b51ff6c5bb2bd576fe40a76c86", "reference": "7dee6a43493f39b51ff6c5bb2bd576fe40a76c86", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/deprecation-contracts": "^2.1", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0", "symfony/polyfill-php80": "^1.15"}, "conflict": {"phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/mailer": "<4.4"}, "require-dev": {"egulias/email-validator": "^2.1.10", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/dependency-injection": "^4.4|^5.0", "symfony/property-access": "^4.4|^5.1", "symfony/property-info": "^4.4|^5.1", "symfony/serializer": "^5.2"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Mime\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows manipulating MIME messages", "homepage": "https://symfony.com", "keywords": ["mime", "mime-type"], "support": {"source": "https://github.com/symfony/mime/tree/v5.2.3"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-02-02T06:10:15+00:00"}, {"name": "symfony/polyfill-ctype", "version": "v1.22.1", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-ctype.git", "reference": "c6c942b1ac76c82448322025e084cadc56048b4e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/c6c942b1ac76c82448322025e084cadc56048b4e", "reference": "c6c942b1ac76c82448322025e084cadc56048b4e", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-ctype": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.22-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.22.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-01-07T16:49:33+00:00"}, {"name": "symfony/polyfill-iconv", "version": "v1.22.1", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-iconv.git", "reference": "06fb361659649bcfd6a208a0f1fcaf4e827ad342"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-iconv/zipball/06fb361659649bcfd6a208a0f1fcaf4e827ad342", "reference": "06fb361659649bcfd6a208a0f1fcaf4e827ad342", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-iconv": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.22-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Iconv\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Iconv extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "iconv", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-iconv/tree/v1.22.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-01-22T09:19:47+00:00"}, {"name": "symfony/polyfill-intl-idn", "version": "v1.22.1", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-idn.git", "reference": "2d63434d922daf7da8dd863e7907e67ee3031483"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/2d63434d922daf7da8dd863e7907e67ee3031483", "reference": "2d63434d922daf7da8dd863e7907e67ee3031483", "shasum": ""}, "require": {"php": ">=7.1", "symfony/polyfill-intl-normalizer": "^1.10", "symfony/polyfill-php72": "^1.10"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.22-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Intl\\Idn\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's idn_to_ascii and idn_to_utf8 functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "idn", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-idn/tree/v1.22.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-01-22T09:19:47+00:00"}, {"name": "symfony/polyfill-intl-normalizer", "version": "v1.22.1", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-normalizer.git", "reference": "43a0283138253ed1d48d352ab6d0bdb3f809f248"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/43a0283138253ed1d48d352ab6d0bdb3f809f248", "reference": "43a0283138253ed1d48d352ab6d0bdb3f809f248", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.22-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Intl\\Normalizer\\": ""}, "files": ["bootstrap.php"], "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's Normalizer class and related functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "intl", "normalizer", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.22.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-01-22T09:19:47+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.22.1", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "5232de97ee3b75b0360528dae24e73db49566ab1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/5232de97ee3b75b0360528dae24e73db49566ab1", "reference": "5232de97ee3b75b0360528dae24e73db49566ab1", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.22-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.22.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-01-22T09:19:47+00:00"}, {"name": "symfony/polyfill-php56", "version": "v1.20.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php56.git", "reference": "54b8cd7e6c1643d78d011f3be89f3ef1f9f4c675"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php56/zipball/54b8cd7e6c1643d78d011f3be89f3ef1f9f4c675", "reference": "54b8cd7e6c1643d78d011f3be89f3ef1f9f4c675", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "metapackage", "extra": {"branch-alias": {"dev-main": "1.20-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 5.6+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php56/tree/v1.20.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-10-23T14:02:19+00:00"}, {"name": "symfony/polyfill-php72", "version": "v1.22.1", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php72.git", "reference": "cc6e6f9b39fe8075b3dabfbaf5b5f645ae1340c9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php72/zipball/cc6e6f9b39fe8075b3dabfbaf5b5f645ae1340c9", "reference": "cc6e6f9b39fe8075b3dabfbaf5b5f645ae1340c9", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.22-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Php72\\": ""}, "files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.2+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php72/tree/v1.22.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-01-07T16:49:33+00:00"}, {"name": "symfony/polyfill-php73", "version": "v1.22.1", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php73.git", "reference": "a678b42e92f86eca04b7fa4c0f6f19d097fb69e2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php73/zipball/a678b42e92f86eca04b7fa4c0f6f19d097fb69e2", "reference": "a678b42e92f86eca04b7fa4c0f6f19d097fb69e2", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.22-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Php73\\": ""}, "files": ["bootstrap.php"], "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.3+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php73/tree/v1.22.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-01-07T16:49:33+00:00"}, {"name": "symfony/polyfill-php80", "version": "v1.22.1", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php80.git", "reference": "dc3063ba22c2a1fd2f45ed856374d79114998f91"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/dc3063ba22c2a1fd2f45ed856374d79114998f91", "reference": "dc3063ba22c2a1fd2f45ed856374d79114998f91", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.22-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Php80\\": ""}, "files": ["bootstrap.php"], "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php80/tree/v1.22.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-01-07T16:49:33+00:00"}, {"name": "symfony/process", "version": "v4.4.19", "source": {"type": "git", "url": "https://github.com/symfony/process.git", "reference": "7e950b6366d4da90292c2e7fa820b3c1842b965a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/process/zipball/7e950b6366d4da90292c2e7fa820b3c1842b965a", "reference": "7e950b6366d4da90292c2e7fa820b3c1842b965a", "shasum": ""}, "require": {"php": ">=7.1.3"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Process\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Executes commands in sub-processes", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/process/tree/v4.4.19"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-01-27T09:09:26+00:00"}, {"name": "symfony/routing", "version": "v4.4.19", "source": {"type": "git", "url": "https://github.com/symfony/routing.git", "reference": "87529f6e305c7acb162840d1ea57922038072425"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/routing/zipball/87529f6e305c7acb162840d1ea57922038072425", "reference": "87529f6e305c7acb162840d1ea57922038072425", "shasum": ""}, "require": {"php": ">=7.1.3"}, "conflict": {"symfony/config": "<4.2", "symfony/dependency-injection": "<3.4", "symfony/yaml": "<3.4"}, "require-dev": {"doctrine/annotations": "^1.10.4", "psr/log": "~1.0", "symfony/config": "^4.2|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/expression-language": "^3.4|^4.0|^5.0", "symfony/http-foundation": "^3.4|^4.0|^5.0", "symfony/yaml": "^3.4|^4.0|^5.0"}, "suggest": {"doctrine/annotations": "For using the annotation loader", "symfony/config": "For using the all-in-one router or any loader", "symfony/expression-language": "For using expression matching", "symfony/http-foundation": "For using a Symfony Request object", "symfony/yaml": "For using the YAML loader"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Routing\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Maps an HTTP request to a set of configuration variables", "homepage": "https://symfony.com", "keywords": ["router", "routing", "uri", "url"], "support": {"source": "https://github.com/symfony/routing/tree/v4.4.19"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-01-27T09:09:26+00:00"}, {"name": "symfony/service-contracts", "version": "v2.2.0", "source": {"type": "git", "url": "https://github.com/symfony/service-contracts.git", "reference": "d15da7ba4957ffb8f1747218be9e1a121fd298a1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/service-contracts/zipball/d15da7ba4957ffb8f1747218be9e1a121fd298a1", "reference": "d15da7ba4957ffb8f1747218be9e1a121fd298a1", "shasum": ""}, "require": {"php": ">=7.2.5", "psr/container": "^1.0"}, "suggest": {"symfony/service-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Service\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to writing services", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/service-contracts/tree/master"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-09-07T11:33:47+00:00"}, {"name": "symfony/translation", "version": "v4.4.19", "source": {"type": "git", "url": "https://github.com/symfony/translation.git", "reference": "e1d0c67167a553556d9f974b5fa79c2448df317a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation/zipball/e1d0c67167a553556d9f974b5fa79c2448df317a", "reference": "e1d0c67167a553556d9f974b5fa79c2448df317a", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/polyfill-mbstring": "~1.0", "symfony/translation-contracts": "^1.1.6|^2"}, "conflict": {"symfony/config": "<3.4", "symfony/dependency-injection": "<3.4", "symfony/http-kernel": "<4.4", "symfony/yaml": "<3.4"}, "provide": {"symfony/translation-implementation": "1.0"}, "require-dev": {"psr/log": "~1.0", "symfony/config": "^3.4|^4.0|^5.0", "symfony/console": "^3.4|^4.0|^5.0", "symfony/dependency-injection": "^3.4|^4.0|^5.0", "symfony/finder": "~2.8|~3.0|~4.0|^5.0", "symfony/http-kernel": "^4.4", "symfony/intl": "^3.4|^4.0|^5.0", "symfony/service-contracts": "^1.1.2|^2", "symfony/yaml": "^3.4|^4.0|^5.0"}, "suggest": {"psr/log-implementation": "To use logging capability in translator", "symfony/config": "", "symfony/yaml": ""}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Translation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools to internationalize your application", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/translation/tree/v4.4.19"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-01-27T09:09:26+00:00"}, {"name": "symfony/translation-contracts", "version": "v2.3.0", "source": {"type": "git", "url": "https://github.com/symfony/translation-contracts.git", "reference": "e2eaa60b558f26a4b0354e1bbb25636efaaad105"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/translation-contracts/zipball/e2eaa60b558f26a4b0354e1bbb25636efaaad105", "reference": "e2eaa60b558f26a4b0354e1bbb25636efaaad105", "shasum": ""}, "require": {"php": ">=7.2.5"}, "suggest": {"symfony/translation-implementation": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.3-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Translation\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to translation", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/translation-contracts/tree/v2.3.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2020-09-28T13:05:58+00:00"}, {"name": "symfony/var-dumper", "version": "v4.4.19", "source": {"type": "git", "url": "https://github.com/symfony/var-dumper.git", "reference": "a1eab2f69906dc83c5ddba4632180260d0ab4f7f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-dumper/zipball/a1eab2f69906dc83c5ddba4632180260d0ab4f7f", "reference": "a1eab2f69906dc83c5ddba4632180260d0ab4f7f", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/polyfill-mbstring": "~1.0", "symfony/polyfill-php72": "~1.5", "symfony/polyfill-php80": "^1.15"}, "conflict": {"phpunit/phpunit": "<4.8.35|<5.4.3,>=5.0", "symfony/console": "<3.4"}, "require-dev": {"ext-iconv": "*", "symfony/console": "^3.4|^4.0|^5.0", "symfony/process": "^4.4|^5.0", "twig/twig": "^1.43|^2.13|^3.0.4"}, "suggest": {"ext-iconv": "To convert non-UTF-8 strings to UTF-8 (or symfony/polyfill-iconv in case ext-iconv cannot be used).", "ext-intl": "To show region name in time zone dump", "symfony/console": "To use the ServerDumpCommand and/or the bin/var-dump-server script"}, "bin": ["Resources/bin/var-dump-server"], "type": "library", "autoload": {"files": ["Resources/functions/dump.php"], "psr-4": {"Symfony\\Component\\VarDumper\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides mechanisms for walking through any arbitrary PHP variable", "homepage": "https://symfony.com", "keywords": ["debug", "dump"], "support": {"source": "https://github.com/symfony/var-dumper/tree/v4.4.19"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2021-01-27T09:09:26+00:00"}, {"name": "tightenco/collect", "version": "v7.26.1", "source": {"type": "git", "url": "https://github.com/tighten/collect.git", "reference": "5e460929279ad806e59fc731e649e9b25fc8774a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/tighten/collect/zipball/5e460929279ad806e59fc731e649e9b25fc8774a", "reference": "5e460929279ad806e59fc731e649e9b25fc8774a", "shasum": ""}, "require": {"php": "^7.1.3", "symfony/var-dumper": "^3.4 || ^4.0 || ^5.0"}, "require-dev": {"mockery/mockery": "^1.0", "nesbot/carbon": "^2.23.0", "phpunit/phpunit": "^7.0"}, "type": "library", "autoload": {"files": ["src/Collect/Support/helpers.php", "src/Collect/Support/alias.php"], "psr-4": {"Tightenco\\Collect\\": "src/Collect"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Collect - Illuminate Collections as a separate package.", "keywords": ["collection", "laravel"], "support": {"issues": "https://github.com/tighten/collect/issues", "source": "https://github.com/tighten/collect/tree/v7.26.1"}, "time": "2020-09-05T00:05:48+00:00"}, {"name": "tijsverkoyen/css-to-inline-styles", "version": "2.2.3", "source": {"type": "git", "url": "https://github.com/tijsverkoyen/CssToInlineStyles.git", "reference": "b43b05cf43c1b6d849478965062b6ef73e223bb5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/tijsverkoyen/CssToInlineStyles/zipball/b43b05cf43c1b6d849478965062b6ef73e223bb5", "reference": "b43b05cf43c1b6d849478965062b6ef73e223bb5", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "php": "^5.5 || ^7.0 || ^8.0", "symfony/css-selector": "^2.7 || ^3.0 || ^4.0 || ^5.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.0 || ^7.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.2.x-dev"}}, "autoload": {"psr-4": {"TijsVerkoyen\\CssToInlineStyles\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "CssToInlineStyles is a class that enables you to convert HTML-pages/files into HTML-pages/files with inline styles. This is very useful when you're sending emails.", "homepage": "https://github.com/tijsverkoyen/CssToInlineStyles", "support": {"issues": "https://github.com/tijsverkoyen/CssToInlineStyles/issues", "source": "https://github.com/tijsverkoyen/CssToInlineStyles/tree/2.2.3"}, "time": "2020-07-13T06:12:54+00:00"}, {"name": "tymon/jwt-auth", "version": "1.0.0", "source": {"type": "git", "url": "******************:buxus-support/jwt-auth.git", "reference": "103739700dc0358039a33b5bc91247570bb83529"}, "require": {"illuminate/auth": "^5.1|^6", "illuminate/contracts": "^5.1|^6", "illuminate/http": "^5.1|^6", "illuminate/support": "^5.1|^6", "lcobucci/jwt": "^3.2", "namshi/jose": "^7.0", "nesbot/carbon": "^1.0|^2.0", "php": "^5.5.9|^7.0"}, "require-dev": {"cartalyst/sentinel": "^2|^3", "illuminate/console": "^5.1|^6", "illuminate/database": "^5.1|^6", "illuminate/routing": "^5.1|^6", "mockery/mockery": ">=0.9.9", "phpunit/phpunit": "~4.8|~6.0"}, "type": "library", "extra": {"branch-alias": {"dev-develop": "1.0-dev"}, "laravel": {"aliases": {"JWTAuth": "Tymon\\JWTAuth\\Facades\\JWTAuth", "JWTFactory": "Tymon\\JWTAuth\\Facades\\JWTFactory"}, "providers": ["Tymon\\JWTAuth\\Providers\\LaravelServiceProvider"]}}, "autoload": {"psr-4": {"Tymon\\JWTAuth\\": "src/"}}, "notification-url": "https://packagist.ui42.sk/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://tymon.xyz", "role": "Developer"}], "description": "JSON Web Token Authentication for <PERSON><PERSON> and <PERSON><PERSON>", "homepage": "https://github.com/tymondesigns/jwt-auth", "keywords": ["auth", "authentication", "json web token", "jwt", "laravel"], "support": {"issues": "https://github.com/tymondesigns/jwt-auth/issues", "source": "https://github.com/tymondesigns/jwt-auth"}, "time": "2019-09-09T03:33:39+02:00"}, {"name": "vlucas/phpdotenv", "version": "v3.6.8", "source": {"type": "git", "url": "https://github.com/vlucas/phpdotenv.git", "reference": "5e679f7616db829358341e2d5cccbd18773bdab8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/vlucas/phpdotenv/zipball/5e679f7616db829358341e2d5cccbd18773bdab8", "reference": "5e679f7616db829358341e2d5cccbd18773bdab8", "shasum": ""}, "require": {"php": "^5.4 || ^7.0 || ^8.0", "phpoption/phpoption": "^1.5.2", "symfony/polyfill-ctype": "^1.17"}, "require-dev": {"ext-filter": "*", "ext-pcre": "*", "phpunit/phpunit": "^4.8.36 || ^5.7.27 || ^6.5.14 || ^7.5.20"}, "suggest": {"ext-filter": "Required to use the boolean validator.", "ext-pcre": "Required to use most of the library."}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.6-dev"}}, "autoload": {"psr-4": {"Dotenv\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://gjcampbell.co.uk/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://vancelucas.com/"}], "description": "Loads environment variables from `.env` to `getenv()`, `$_ENV` and `$_SERVER` automagically.", "keywords": ["dotenv", "env", "environment"], "support": {"issues": "https://github.com/vlucas/phpdotenv/issues", "source": "https://github.com/vlucas/phpdotenv/tree/v3.6.8"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/vlucas/phpdotenv", "type": "tidelift"}], "time": "2021-01-20T14:39:46+00:00"}, {"name": "webmozart/assert", "version": "1.9.1", "source": {"type": "git", "url": "https://github.com/webmozarts/assert.git", "reference": "bafc69caeb4d49c39fd0779086c03a3738cbb389"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webmozarts/assert/zipball/bafc69caeb4d49c39fd0779086c03a3738cbb389", "reference": "bafc69caeb4d49c39fd0779086c03a3738cbb389", "shasum": ""}, "require": {"php": "^5.3.3 || ^7.0 || ^8.0", "symfony/polyfill-ctype": "^1.8"}, "conflict": {"phpstan/phpstan": "<0.12.20", "vimeo/psalm": "<3.9.1"}, "require-dev": {"phpunit/phpunit": "^4.8.36 || ^7.5.13"}, "type": "library", "autoload": {"psr-4": {"Webmozart\\Assert\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Assertions to validate method input/output with nice error messages.", "keywords": ["assert", "check", "validate"], "support": {"issues": "https://github.com/webmozarts/assert/issues", "source": "https://github.com/webmozarts/assert/tree/1.9.1"}, "time": "2020-07-08T17:02:28+00:00"}, {"name": "zendframework/zendframework1", "version": "1.14.4", "source": {"type": "git", "url": "******************:buxus-support/zf1.git", "reference": "ead9d4863ec11fc00ccf00977ff8f7c2ffbbd510"}, "dist": {"type": "zip", "url": "https://packages.ui42.sk/api/v4/projects/buxus-support%2Fzf1/repository/archive.zip?sha=ead9d4863ec11fc00ccf00977ff8f7c2ffbbd510", "reference": "ead9d4863ec11fc00ccf00977ff8f7c2ffbbd510", "shasum": ""}, "require": {"php": ">=5.2.11"}, "require-dev": {"phpunit/dbunit": "1.3.*", "phpunit/phpunit": "3.7.*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.14.x-dev"}}, "autoload": {"psr-0": {"Zend_": "library/"}}, "notification-url": "https://packagist.ui42.sk/downloads/", "include-path": ["library/"], "license": ["BSD-3-<PERSON><PERSON>"], "description": "Zend Framework 1 - BUXUS fork", "homepage": "http://framework.zend.com/", "keywords": ["framework", "zf1"], "time": "2023-09-14T12:25:44+00:00"}], "packages-dev": [{"name": "beyondcode/laravel-dump-server", "version": "1.3.0", "source": {"type": "git", "url": "https://github.com/beyondcode/laravel-dump-server.git", "reference": "fcc88fa66895f8c1ff83f6145a5eff5fa2a0739a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/beyondcode/laravel-dump-server/zipball/fcc88fa66895f8c1ff83f6145a5eff5fa2a0739a", "reference": "fcc88fa66895f8c1ff83f6145a5eff5fa2a0739a", "shasum": ""}, "require": {"illuminate/console": "5.6.*|5.7.*|5.8.*|^6.0", "illuminate/http": "5.6.*|5.7.*|5.8.*|^6.0", "illuminate/support": "5.6.*|5.7.*|5.8.*|^6.0", "php": "^7.1", "symfony/var-dumper": "^4.1.1"}, "require-dev": {"larapack/dd": "^1.0", "phpunit/phpunit": "^7.0"}, "type": "library", "extra": {"laravel": {"providers": ["BeyondCode\\DumpServer\\DumpServerServiceProvider"]}}, "autoload": {"psr-4": {"BeyondCode\\DumpServer\\": "src"}, "files": ["helpers.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://beyondco.de", "role": "Developer"}], "description": "Symfony Var-Dump Server for Laravel", "homepage": "https://github.com/beyondcode/laravel-dump-server", "keywords": ["beyondcode", "laravel-dump-server"], "support": {"issues": "https://github.com/beyondcode/laravel-dump-server/issues", "source": "https://github.com/beyondcode/laravel-dump-server/tree/1.3.0"}, "time": "2019-08-11T13:17:40+00:00"}, {"name": "buxus/buxusstan", "version": "1.1.1", "source": {"type": "git", "url": "******************:buxus/buxusstan.git", "reference": "bafb8f7f25d688d8b097ddebbfddcbe03f01d1ad"}, "dist": {"type": "zip", "url": "https://packages.ui42.sk/api/v4/projects/buxus%2Fbuxusstan/repository/archive.zip?sha=bafb8f7f25d688d8b097ddebbfddcbe03f01d1ad", "reference": "bafb8f7f25d688d8b097ddebbfddcbe03f01d1ad", "shasum": ""}, "require": {"buxus/core": "^7.2", "buxus/legacy": "^1.0", "buxus/util": "^2.0", "illuminate/support": "5.8.* || ^6.0 || ^7.0 || ^8.0", "nunomaduro/larastan": "^0.7.6 || ^1.0 || ^2.0", "php": "^7.4 || ^8.0"}, "suggest": {"buxus-eshop/vouchers": "^2.3.31 - Coding standards fixes", "buxus-libs/fulltext-search": "^4.0.3 || ^5.0 - Coding standards fixes", "buxus/eshop": "^1.6.53 || ^2.0 - Coding standards fixes", "buxus/fs": "^5.2.11 || ^6.0 - Coding standards fixes", "buxus/legacy-base": "^2.1.76 - Coding standards fixes", "buxus/pages": "^4.0.18 - Coding standards fixes", "buxus/seourl-legacy": "^2.1.28 - Coding standards fixes", "buxus/webuser": "^2.3.16 || ^3.0 - Coding standards fixes"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Buxus\\BuxusStan\\": "src/"}}, "notification-url": "https://packagist.ui42.sk/downloads/", "license": ["proprietary"], "description": "A LaraStan extension for BUXUS CMS", "homepage": "http://www.buxus.sk/", "keywords": ["<PERSON><PERSON><PERSON>", "phpstan", "testing"], "time": "2022-11-08T17:20:31+00:00"}, {"name": "buxus/update-log-plugin", "version": "1.0.1", "source": {"type": "git", "url": "******************:buxus-libs/update-log-plugin.git", "reference": "89e203c8fd97a81aeb0337517b7cb558e1ac32b7"}, "require": {"composer-plugin-api": "^1.0 || ^2.0", "ext-json": "*"}, "type": "composer-plugin", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}, "class": "\\Buxus\\Composer\\UpdateLog\\UpdateLogPlugin"}, "autoload": {"psr-4": {"Buxus\\Composer\\UpdateLog\\": "src/"}}, "notification-url": "https://packagist.ui42.sk/downloads/", "license": ["proprietary"], "description": "Plugin to display update log after composer update", "time": "2020-05-12T09:30:25+02:00"}, {"name": "composer/ca-bundle", "version": "1.3.7", "source": {"type": "git", "url": "https://github.com/composer/ca-bundle.git", "reference": "76e46335014860eec1aa5a724799a00a2e47cc85"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/ca-bundle/zipball/76e46335014860eec1aa5a724799a00a2e47cc85", "reference": "76e46335014860eec1aa5a724799a00a2e47cc85", "shasum": ""}, "require": {"ext-openssl": "*", "ext-pcre": "*", "php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^0.12.55", "psr/log": "^1.0", "symfony/phpunit-bridge": "^4.2 || ^5", "symfony/process": "^2.5 || ^3.0 || ^4.0 || ^5.0 || ^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.x-dev"}}, "autoload": {"psr-4": {"Composer\\CaBundle\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Lets you find a path to the system CA bundle, and includes a fallback to the Mozilla CA bundle.", "keywords": ["cabundle", "cacert", "certificate", "ssl", "tls"], "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/ca-bundle/issues", "source": "https://github.com/composer/ca-bundle/tree/1.3.7"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2023-08-30T09:31:38+00:00"}, {"name": "composer/composer", "version": "2.2.22", "source": {"type": "git", "url": "https://github.com/composer/composer.git", "reference": "fedc76ee3f3e3d57d20993b9f4c5fcfb2f8596aa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/composer/zipball/fedc76ee3f3e3d57d20993b9f4c5fcfb2f8596aa", "reference": "fedc76ee3f3e3d57d20993b9f4c5fcfb2f8596aa", "shasum": ""}, "require": {"composer/ca-bundle": "^1.0", "composer/metadata-minifier": "^1.0", "composer/pcre": "^1.0", "composer/semver": "^3.0", "composer/spdx-licenses": "^1.2", "composer/xdebug-handler": "^2.0 || ^3.0", "justinrainbow/json-schema": "^5.2.11", "php": "^5.3.2 || ^7.0 || ^8.0", "psr/log": "^1.0 || ^2.0", "react/promise": "^1.2 || ^2.7", "seld/jsonlint": "^1.4", "seld/phar-utils": "^1.0", "symfony/console": "^2.8.52 || ^3.4.35 || ^4.4 || ^5.0", "symfony/filesystem": "^2.8.52 || ^3.4.35 || ^4.4 || ^5.0 || ^6.0", "symfony/finder": "^2.8.52 || ^3.4.35 || ^4.4 || ^5.0 || ^6.0", "symfony/process": "^2.8.52 || ^3.4.35 || ^4.4 || ^5.0 || ^6.0"}, "require-dev": {"phpspec/prophecy": "^1.10", "symfony/phpunit-bridge": "^4.2 || ^5.0 || ^6.0"}, "suggest": {"ext-openssl": "Enabling the openssl extension allows you to access https URLs for repositories and packages", "ext-zip": "Enabling the zip extension allows you to unzip archives", "ext-zlib": "Allow gzip compression of HTTP requests"}, "bin": ["bin/composer"], "type": "library", "extra": {"branch-alias": {"dev-main": "2.2-dev"}}, "autoload": {"psr-4": {"Composer\\": "src/Composer"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "https://seld.be"}], "description": "Composer helps you declare, manage and install dependencies of PHP projects. It ensures you have the right stack everywhere.", "homepage": "https://getcomposer.org/", "keywords": ["autoload", "dependency", "package"], "support": {"irc": "ircs://irc.libera.chat:6697/composer", "issues": "https://github.com/composer/composer/issues", "source": "https://github.com/composer/composer/tree/2.2.22"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2023-09-29T08:53:46+00:00"}, {"name": "composer/metadata-minifier", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/composer/metadata-minifier.git", "reference": "c549d23829536f0d0e984aaabbf02af91f443207"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/metadata-minifier/zipball/c549d23829536f0d0e984aaabbf02af91f443207", "reference": "c549d23829536f0d0e984aaabbf02af91f443207", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"composer/composer": "^2", "phpstan/phpstan": "^0.12.55", "symfony/phpunit-bridge": "^4.2 || ^5"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.x-dev"}}, "autoload": {"psr-4": {"Composer\\MetadataMinifier\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Small utility library that handles metadata minification and expansion.", "keywords": ["composer", "compression"], "support": {"issues": "https://github.com/composer/metadata-minifier/issues", "source": "https://github.com/composer/metadata-minifier/tree/1.0.0"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2021-04-07T13:37:33+00:00"}, {"name": "composer/pcre", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/composer/pcre.git", "reference": "67a32d7d6f9f560b726ab25a061b38ff3a80c560"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/pcre/zipball/67a32d7d6f9f560b726ab25a061b38ff3a80c560", "reference": "67a32d7d6f9f560b726ab25a061b38ff3a80c560", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^1.3", "phpstan/phpstan-strict-rules": "^1.1", "symfony/phpunit-bridge": "^4.2 || ^5"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.x-dev"}}, "autoload": {"psr-4": {"Composer\\Pcre\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "PCRE wrapping library that offers type-safe preg_* replacements.", "keywords": ["PCRE", "preg", "regex", "regular expression"], "support": {"issues": "https://github.com/composer/pcre/issues", "source": "https://github.com/composer/pcre/tree/1.0.1"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2022-01-21T20:24:37+00:00"}, {"name": "composer/semver", "version": "3.4.0", "source": {"type": "git", "url": "https://github.com/composer/semver.git", "reference": "35e8d0af4486141bc745f23a29cc2091eb624a32"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/semver/zipball/35e8d0af4486141bc745f23a29cc2091eb624a32", "reference": "35e8d0af4486141bc745f23a29cc2091eb624a32", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^1.4", "symfony/phpunit-bridge": "^4.2 || ^5"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "autoload": {"psr-4": {"Composer\\Semver\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://robbast.nl"}], "description": "Semver library that offers utilities, version constraint parsing and validation.", "keywords": ["semantic", "semver", "validation", "versioning"], "support": {"irc": "ircs://irc.libera.chat:6697/composer", "issues": "https://github.com/composer/semver/issues", "source": "https://github.com/composer/semver/tree/3.4.0"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2023-08-31T09:50:34+00:00"}, {"name": "composer/spdx-licenses", "version": "1.5.7", "source": {"type": "git", "url": "https://github.com/composer/spdx-licenses.git", "reference": "c848241796da2abf65837d51dce1fae55a960149"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/spdx-licenses/zipball/c848241796da2abf65837d51dce1fae55a960149", "reference": "c848241796da2abf65837d51dce1fae55a960149", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^0.12.55", "symfony/phpunit-bridge": "^4.2 || ^5"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.x-dev"}}, "autoload": {"psr-4": {"Composer\\Spdx\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://robbast.nl"}], "description": "SPDX licenses list and validation library.", "keywords": ["license", "spdx", "validator"], "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/spdx-licenses/issues", "source": "https://github.com/composer/spdx-licenses/tree/1.5.7"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2022-05-23T07:37:50+00:00"}, {"name": "composer/xdebug-handler", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/composer/xdebug-handler.git", "reference": "ced299686f41dce890debac69273b47ffe98a40c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/xdebug-handler/zipball/ced299686f41dce890debac69273b47ffe98a40c", "reference": "ced299686f41dce890debac69273b47ffe98a40c", "shasum": ""}, "require": {"composer/pcre": "^1 || ^2 || ^3", "php": "^7.2.5 || ^8.0", "psr/log": "^1 || ^2 || ^3"}, "require-dev": {"phpstan/phpstan": "^1.0", "phpstan/phpstan-strict-rules": "^1.1", "symfony/phpunit-bridge": "^6.0"}, "type": "library", "autoload": {"psr-4": {"Composer\\XdebugHandler\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "john-s<PERSON><PERSON><PERSON>@blueyonder.co.uk"}], "description": "Restarts a process without Xdebug.", "keywords": ["Xdebug", "performance"], "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/xdebug-handler/issues", "source": "https://github.com/composer/xdebug-handler/tree/3.0.3"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2022-02-25T21:32:43+00:00"}, {"name": "doctrine/instantiator", "version": "1.4.0", "source": {"type": "git", "url": "https://github.com/doctrine/instantiator.git", "reference": "d56bf6102915de5702778fe20f2de3b2fe570b5b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/instantiator/zipball/d56bf6102915de5702778fe20f2de3b2fe570b5b", "reference": "d56bf6102915de5702778fe20f2de3b2fe570b5b", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^8.0", "ext-pdo": "*", "ext-phar": "*", "phpbench/phpbench": "^0.13 || 1.0.0-alpha2", "phpstan/phpstan": "^0.12", "phpstan/phpstan-phpunit": "^0.12", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Instantiator\\": "src/Doctrine/Instantiator/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://ocramius.github.io/"}], "description": "A small, lightweight utility to instantiate objects in PHP without invoking their constructors", "homepage": "https://www.doctrine-project.org/projects/instantiator.html", "keywords": ["constructor", "instantiate"], "support": {"issues": "https://github.com/doctrine/instantiator/issues", "source": "https://github.com/doctrine/instantiator/tree/1.4.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Finstantiator", "type": "tidelift"}], "time": "2020-11-10T18:47:58+00:00"}, {"name": "fzaninotto/faker", "version": "v1.9.2", "source": {"type": "git", "url": "https://github.com/fzaninotto/Faker.git", "reference": "848d8125239d7dbf8ab25cb7f054f1a630e68c2e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/fzaninotto/Faker/zipball/848d8125239d7dbf8ab25cb7f054f1a630e68c2e", "reference": "848d8125239d7dbf8ab25cb7f054f1a630e68c2e", "shasum": ""}, "require": {"php": "^5.3.3 || ^7.0"}, "require-dev": {"ext-intl": "*", "phpunit/phpunit": "^4.8.35 || ^5.7", "squizlabs/php_codesniffer": "^2.9.2"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.9-dev"}}, "autoload": {"psr-4": {"Faker\\": "src/Faker/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "Faker is a PHP library that generates fake data for you.", "keywords": ["data", "faker", "fixtures"], "support": {"issues": "https://github.com/fzaninotto/Faker/issues", "source": "https://github.com/fzaninotto/Faker/tree/v1.9.2"}, "abandoned": true, "time": "2020-12-11T09:56:16+00:00"}, {"name": "hamcrest/hamcrest-php", "version": "v2.0.1", "source": {"type": "git", "url": "https://github.com/hamcrest/hamcrest-php.git", "reference": "8c3d0a3f6af734494ad8f6fbbee0ba92422859f3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/hamcrest/hamcrest-php/zipball/8c3d0a3f6af734494ad8f6fbbee0ba92422859f3", "reference": "8c3d0a3f6af734494ad8f6fbbee0ba92422859f3", "shasum": ""}, "require": {"php": "^5.3|^7.0|^8.0"}, "replace": {"cordoval/hamcrest-php": "*", "davedevelopment/hamcrest-php": "*", "kodova/hamcrest-php": "*"}, "require-dev": {"phpunit/php-file-iterator": "^1.4 || ^2.0", "phpunit/phpunit": "^4.8.36 || ^5.7 || ^6.5 || ^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1-dev"}}, "autoload": {"classmap": ["hamcrest"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "This is the PHP port of Hamcrest Matchers", "keywords": ["test"], "support": {"issues": "https://github.com/hamcrest/hamcrest-php/issues", "source": "https://github.com/hamcrest/hamcrest-php/tree/v2.0.1"}, "time": "2020-07-09T08:09:16+00:00"}, {"name": "justin<PERSON><PERSON>/json-schema", "version": "v5.2.13", "source": {"type": "git", "url": "https://github.com/justinrainbow/json-schema.git", "reference": "fbbe7e5d79f618997bc3332a6f49246036c45793"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/justinrainbow/json-schema/zipball/fbbe7e5d79f618997bc3332a6f49246036c45793", "reference": "fbbe7e5d79f618997bc3332a6f49246036c45793", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"friendsofphp/php-cs-fixer": "~2.2.20||~2.15.1", "json-schema/json-schema-test-suite": "1.2.0", "phpunit/phpunit": "^4.8.35"}, "bin": ["bin/validate-json"], "type": "library", "extra": {"branch-alias": {"dev-master": "5.0.x-dev"}}, "autoload": {"psr-4": {"JsonSchema\\": "src/JsonSchema/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "i<PERSON>@wiedler.ch"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A library to validate a json schema.", "homepage": "https://github.com/justinrainbow/json-schema", "keywords": ["json", "schema"], "support": {"issues": "https://github.com/justinrainbow/json-schema/issues", "source": "https://github.com/justinrainbow/json-schema/tree/v5.2.13"}, "time": "2023-09-26T02:20:38+00:00"}, {"name": "mockery/mockery", "version": "1.3.4", "source": {"type": "git", "url": "https://github.com/mockery/mockery.git", "reference": "31467aeb3ca3188158613322d66df81cedd86626"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mockery/mockery/zipball/31467aeb3ca3188158613322d66df81cedd86626", "reference": "31467aeb3ca3188158613322d66df81cedd86626", "shasum": ""}, "require": {"hamcrest/hamcrest-php": "^2.0.1", "lib-pcre": ">=7.0", "php": ">=5.6.0"}, "require-dev": {"phpunit/phpunit": "^5.7.10|^6.5|^7.5|^8.5|^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"psr-0": {"Mockery": "library/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://blog.astrumfutura.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://davedevelopment.co.uk"}], "description": "Mockery is a simple yet flexible PHP mock object framework", "homepage": "https://github.com/mockery/mockery", "keywords": ["BDD", "TDD", "library", "mock", "mock objects", "mockery", "stub", "test", "test double", "testing"], "support": {"issues": "https://github.com/mockery/mockery/issues", "source": "https://github.com/mockery/mockery/tree/1.3.4"}, "time": "2021-02-24T09:51:00+00:00"}, {"name": "nunomaduro/collision", "version": "v3.1.0", "source": {"type": "git", "url": "https://github.com/nunomaduro/collision.git", "reference": "88b58b5bd9bdcc54756480fb3ce87234696544ee"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nunomaduro/collision/zipball/88b58b5bd9bdcc54756480fb3ce87234696544ee", "reference": "88b58b5bd9bdcc54756480fb3ce87234696544ee", "shasum": ""}, "require": {"filp/whoops": "^2.1.4", "jakub-onderka/php-console-highlighter": "0.3.*|0.4.*", "php": "^7.1 || ^8.0", "symfony/console": "~2.8|~3.3|~4.0"}, "require-dev": {"laravel/framework": "^6.0", "phpunit/phpunit": "^8.0 || ^9.0"}, "type": "library", "extra": {"laravel": {"providers": ["NunoMaduro\\Collision\\Adapters\\Laravel\\CollisionServiceProvider"]}}, "autoload": {"psr-4": {"NunoMaduro\\Collision\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Cli error handling for console/command-line PHP applications.", "keywords": ["artisan", "cli", "command-line", "console", "error", "handling", "laravel", "laravel-zero", "php", "symfony"], "support": {"issues": "https://github.com/nunomaduro/collision/issues", "source": "https://github.com/nunomaduro/collision"}, "funding": [{"url": "https://www.paypal.com/cgi-bin/webscr?cmd=_s-xclick&hosted_button_id=66BYDWAT92N6L", "type": "custom"}, {"url": "https://github.com/nunomaduro", "type": "github"}, {"url": "https://www.patreon.com/nunomaduro", "type": "patreon"}], "time": "2020-10-29T16:05:21+00:00"}, {"name": "nunomaduro/larastan", "version": "1.0.4", "source": {"type": "git", "url": "https://github.com/nunomaduro/larastan.git", "reference": "769bc6346a6cce3b823c30eaace33d9c3a0dd40e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nunomaduro/larastan/zipball/769bc6346a6cce3b823c30eaace33d9c3a0dd40e", "reference": "769bc6346a6cce3b823c30eaace33d9c3a0dd40e", "shasum": ""}, "require": {"composer/composer": "^1.0 || ^2.0", "ext-json": "*", "illuminate/console": "^6.0 || ^7.0 || ^8.0 || ^9.0", "illuminate/container": "^6.0 || ^7.0 || ^8.0 || ^9.0", "illuminate/contracts": "^6.0 || ^7.0 || ^8.0 || ^9.0", "illuminate/database": "^6.0 || ^7.0 || ^8.0 || ^9.0", "illuminate/http": "^6.0 || ^7.0 || ^8.0 || ^9.0", "illuminate/pipeline": "^6.0 || ^7.0 || ^8.0 || ^9.0", "illuminate/support": "^6.0 || ^7.0 || ^8.0 || ^9.0", "mockery/mockery": "^0.9 || ^1.0", "php": "^7.2 || ^8.0", "phpstan/phpstan": "^1.0 <1.9", "symfony/process": "^4.3 || ^5.0 || ^6.0"}, "require-dev": {"nikic/php-parser": "^4.13.0", "orchestra/testbench": "^4.0 || ^5.0 || ^6.0 || ^7.0", "phpunit/phpunit": "^7.3 || ^8.2 || ^9.3"}, "suggest": {"orchestra/testbench": "Using Larastan for analysing a package needs Testbench"}, "type": "phpstan-extension", "extra": {"branch-alias": {"dev-master": "1.0-dev"}, "phpstan": {"includes": ["extension.neon"]}}, "autoload": {"psr-4": {"NunoMaduro\\Larastan\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Larastan - Discover bugs in your code without running it. A phpstan/phpstan wrapper for Lara<PERSON>", "keywords": ["PHPStan", "code analyse", "code analysis", "<PERSON><PERSON><PERSON>", "laravel", "package", "php", "static analysis"], "support": {"issues": "https://github.com/nunomaduro/larastan/issues", "source": "https://github.com/nunomaduro/larastan/tree/1.0.4"}, "funding": [{"url": "https://www.paypal.com/paypalme/enunomaduro", "type": "custom"}, {"url": "https://github.com/canvural", "type": "github"}, {"url": "https://github.com/nunomaduro", "type": "github"}, {"url": "https://www.patreon.com/nunomaduro", "type": "patreon"}], "time": "2022-11-09T09:09:31+00:00"}, {"name": "phar-io/manifest", "version": "1.0.3", "source": {"type": "git", "url": "https://github.com/phar-io/manifest.git", "reference": "7761fcacf03b4d4f16e7ccb606d4879ca431fcf4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phar-io/manifest/zipball/7761fcacf03b4d4f16e7ccb606d4879ca431fcf4", "reference": "7761fcacf03b4d4f16e7ccb606d4879ca431fcf4", "shasum": ""}, "require": {"ext-dom": "*", "ext-phar": "*", "phar-io/version": "^2.0", "php": "^5.6 || ^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Component for reading phar.io manifest information from a PHP Archive (PHAR)", "support": {"issues": "https://github.com/phar-io/manifest/issues", "source": "https://github.com/phar-io/manifest/tree/master"}, "time": "2018-07-08T19:23:20+00:00"}, {"name": "phar-io/version", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/phar-io/version.git", "reference": "45a2ec53a73c70ce41d55cedef9063630abaf1b6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phar-io/version/zipball/45a2ec53a73c70ce41d55cedef9063630abaf1b6", "reference": "45a2ec53a73c70ce41d55cedef9063630abaf1b6", "shasum": ""}, "require": {"php": "^5.6 || ^7.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Library for handling version information and constraints", "support": {"issues": "https://github.com/phar-io/version/issues", "source": "https://github.com/phar-io/version/tree/master"}, "time": "2018-07-08T19:19:57+00:00"}, {"name": "phpspec/prophecy", "version": "v1.10.3", "source": {"type": "git", "url": "https://github.com/phpspec/prophecy.git", "reference": "451c3cd1418cf640de218914901e51b064abb093"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpspec/prophecy/zipball/451c3cd1418cf640de218914901e51b064abb093", "reference": "451c3cd1418cf640de218914901e51b064abb093", "shasum": ""}, "require": {"doctrine/instantiator": "^1.0.2", "php": "^5.3|^7.0", "phpdocumentor/reflection-docblock": "^2.0|^3.0.2|^4.0|^5.0", "sebastian/comparator": "^1.2.3|^2.0|^3.0|^4.0", "sebastian/recursion-context": "^1.0|^2.0|^3.0|^4.0"}, "require-dev": {"phpspec/phpspec": "^2.5 || ^3.2", "phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.5 || ^7.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.10.x-dev"}}, "autoload": {"psr-4": {"Prophecy\\": "src/Prophecy"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://everzet.com"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Highly opinionated mocking framework for PHP 5.3+", "homepage": "https://github.com/phpspec/prophecy", "keywords": ["Double", "Dummy", "fake", "mock", "spy", "stub"], "support": {"issues": "https://github.com/phpspec/prophecy/issues", "source": "https://github.com/phpspec/prophecy/tree/v1.10.3"}, "time": "2020-03-05T15:02:03+00:00"}, {"name": "phpstan/phpstan", "version": "1.8.11", "source": {"type": "git", "url": "https://github.com/phpstan/phpstan.git", "reference": "46e223dd68a620da18855c23046ddb00940b4014"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpstan/phpstan/zipball/46e223dd68a620da18855c23046ddb00940b4014", "reference": "46e223dd68a620da18855c23046ddb00940b4014", "shasum": ""}, "require": {"php": "^7.2|^8.0"}, "conflict": {"phpstan/phpstan-shim": "*"}, "bin": ["phpstan", "phpstan.phar"], "type": "library", "autoload": {"files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "PHPStan - PHP Static Analysis Tool", "keywords": ["dev", "static analysis"], "support": {"issues": "https://github.com/phpstan/phpstan/issues", "source": "https://github.com/phpstan/phpstan/tree/1.8.11"}, "funding": [{"url": "https://github.com/ondrejmirtes", "type": "github"}, {"url": "https://github.com/phpstan", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/phpstan/phpstan", "type": "tidelift"}], "time": "2022-10-24T15:45:13+00:00"}, {"name": "phpunit/php-code-coverage", "version": "6.1.4", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage.git", "reference": "807e6013b00af69b6c5d9ceb4282d0393dbb9d8d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-code-coverage/zipball/807e6013b00af69b6c5d9ceb4282d0393dbb9d8d", "reference": "807e6013b00af69b6c5d9ceb4282d0393dbb9d8d", "shasum": ""}, "require": {"ext-dom": "*", "ext-xmlwriter": "*", "php": "^7.1", "phpunit/php-file-iterator": "^2.0", "phpunit/php-text-template": "^1.2.1", "phpunit/php-token-stream": "^3.0", "sebastian/code-unit-reverse-lookup": "^1.0.1", "sebastian/environment": "^3.1 || ^4.0", "sebastian/version": "^2.0.1", "theseer/tokenizer": "^1.1"}, "require-dev": {"phpunit/phpunit": "^7.0"}, "suggest": {"ext-xdebug": "^2.6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "6.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that provides collection, processing, and rendering functionality for PHP code coverage information.", "homepage": "https://github.com/sebastian<PERSON>mann/php-code-coverage", "keywords": ["coverage", "testing", "xunit"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-code-coverage/tree/master"}, "time": "2018-10-31T16:06:48+00:00"}, {"name": "phpunit/php-file-iterator", "version": "2.0.3", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator.git", "reference": "4b49fb70f067272b659ef0174ff9ca40fdaa6357"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-file-iterator/zipball/4b49fb70f067272b659ef0174ff9ca40fdaa6357", "reference": "4b49fb70f067272b659ef0174ff9ca40fdaa6357", "shasum": ""}, "require": {"php": ">=7.1"}, "require-dev": {"phpunit/phpunit": "^8.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "FilterIterator implementation that filters files based on a list of suffixes.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/", "keywords": ["filesystem", "iterator"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-file-iterator/tree/2.0.3"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-11-30T08:25:21+00:00"}, {"name": "phpunit/php-text-template", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template.git", "reference": "31f8b717e51d9a2afca6c9f046f5d69fc27c8686"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/php-text-template/zipball/31f8b717e51d9a2afca6c9f046f5d69fc27c8686", "reference": "31f8b717e51d9a2afca6c9f046f5d69fc27c8686", "shasum": ""}, "require": {"php": ">=5.3.3"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Simple template engine.", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/", "keywords": ["template"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-text-template/tree/1.2.1"}, "time": "2015-06-21T13:50:34+00:00"}, {"name": "phpunit/php-timer", "version": "2.1.3", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-timer.git", "reference": "2454ae1765516d20c4ffe103d85a58a9a3bd5662"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/php-timer/zipball/2454ae1765516d20c4ffe103d85a58a9a3bd5662", "reference": "2454ae1765516d20c4ffe103d85a58a9a3bd5662", "shasum": ""}, "require": {"php": ">=7.1"}, "require-dev": {"phpunit/phpunit": "^8.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Utility class for timing", "homepage": "https://github.com/sebastian<PERSON>mann/php-timer/", "keywords": ["timer"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/php-timer/issues", "source": "https://github.com/sebastian<PERSON>mann/php-timer/tree/2.1.3"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-11-30T08:20:02+00:00"}, {"name": "phpunit/php-token-stream", "version": "3.1.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/php-token-stream.git", "reference": "472b687829041c24b25f475e14c2f38a09edf1c2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/php-token-stream/zipball/472b687829041c24b25f475e14c2f38a09edf1c2", "reference": "472b687829041c24b25f475e14c2f38a09edf1c2", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": ">=7.1"}, "require-dev": {"phpunit/phpunit": "^7.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Wrapper around PHP's tokenizer extension.", "homepage": "https://github.com/sebastian<PERSON>mann/php-token-stream/", "keywords": ["tokenizer"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/php-token-stream/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/php-token-stream/tree/3.1.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "abandoned": true, "time": "2020-11-30T08:38:46+00:00"}, {"name": "phpunit/phpunit", "version": "7.5.20", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/phpunit.git", "reference": "9467db479d1b0487c99733bb1e7944d32deded2c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/phpunit/zipball/9467db479d1b0487c99733bb1e7944d32deded2c", "reference": "9467db479d1b0487c99733bb1e7944d32deded2c", "shasum": ""}, "require": {"doctrine/instantiator": "^1.1", "ext-dom": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-xml": "*", "myclabs/deep-copy": "^1.7", "phar-io/manifest": "^1.0.2", "phar-io/version": "^2.0", "php": "^7.1", "phpspec/prophecy": "^1.7", "phpunit/php-code-coverage": "^6.0.7", "phpunit/php-file-iterator": "^2.0.1", "phpunit/php-text-template": "^1.2.1", "phpunit/php-timer": "^2.1", "sebastian/comparator": "^3.0", "sebastian/diff": "^3.0", "sebastian/environment": "^4.0", "sebastian/exporter": "^3.1", "sebastian/global-state": "^2.0", "sebastian/object-enumerator": "^3.0.3", "sebastian/resource-operations": "^2.0", "sebastian/version": "^2.0.1"}, "conflict": {"phpunit/phpunit-mock-objects": "*"}, "require-dev": {"ext-pdo": "*"}, "suggest": {"ext-soap": "*", "ext-xdebug": "*", "phpunit/php-invoker": "^2.0"}, "bin": ["phpunit"], "type": "library", "extra": {"branch-alias": {"dev-master": "7.5-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "The PHP Unit Testing framework.", "homepage": "https://phpunit.de/", "keywords": ["phpunit", "testing", "xunit"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/phpunit/issues", "source": "https://github.com/sebastian<PERSON>mann/phpunit/tree/7.5.20"}, "time": "2020-01-08T08:45:45+00:00"}, {"name": "sebastian/code-unit-reverse-lookup", "version": "1.0.2", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/code-unit-reverse-lookup.git", "reference": "1de8cd5c010cb153fcd68b8d0f64606f523f7619"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/code-unit-reverse-lookup/zipball/1de8cd5c010cb153fcd68b8d0f64606f523f7619", "reference": "1de8cd5c010cb153fcd68b8d0f64606f523f7619", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"phpunit/phpunit": "^8.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Looks up which function or method a line of code belongs to", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/code-unit-reverse-lookup/tree/1.0.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-11-30T08:15:22+00:00"}, {"name": "sebastian/comparator", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/comparator.git", "reference": "1071dfcef776a57013124ff35e1fc41ccd294758"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/comparator/zipball/1071dfcef776a57013124ff35e1fc41ccd294758", "reference": "1071dfcef776a57013124ff35e1fc41ccd294758", "shasum": ""}, "require": {"php": ">=7.1", "sebastian/diff": "^3.0", "sebastian/exporter": "^3.1"}, "require-dev": {"phpunit/phpunit": "^8.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides the functionality to compare PHP values for equality", "homepage": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator", "keywords": ["comparator", "compare", "equality"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/comparator/issues", "source": "https://github.com/sebastian<PERSON>mann/comparator/tree/3.0.3"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-11-30T08:04:30+00:00"}, {"name": "sebastian/diff", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/diff.git", "reference": "14f72dd46eaf2f2293cbe79c93cc0bc43161a211"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/diff/zipball/14f72dd46eaf2f2293cbe79c93cc0bc43161a211", "reference": "14f72dd46eaf2f2293cbe79c93cc0bc43161a211", "shasum": ""}, "require": {"php": ">=7.1"}, "require-dev": {"phpunit/phpunit": "^7.5 || ^8.0", "symfony/process": "^2 || ^3.3 || ^4"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Diff implementation", "homepage": "https://github.com/sebastian<PERSON>mann/diff", "keywords": ["diff", "udiff", "unidiff", "unified diff"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/diff/issues", "source": "https://github.com/sebastian<PERSON>mann/diff/tree/3.0.3"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-11-30T07:59:04+00:00"}, {"name": "sebastian/environment", "version": "4.2.4", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/environment.git", "reference": "d47bbbad83711771f167c72d4e3f25f7fcc1f8b0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastianbergmann/environment/zipball/d47bbbad83711771f167c72d4e3f25f7fcc1f8b0", "reference": "d47bbbad83711771f167c72d4e3f25f7fcc1f8b0", "shasum": ""}, "require": {"php": ">=7.1"}, "require-dev": {"phpunit/phpunit": "^7.5"}, "suggest": {"ext-posix": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.2-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to handle HHVM/PHP environments", "homepage": "http://www.github.com/sebastianbergmann/environment", "keywords": ["Xdebug", "environment", "hhvm"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/environment/issues", "source": "https://github.com/sebastian<PERSON>mann/environment/tree/4.2.4"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-11-30T07:53:42+00:00"}, {"name": "sebastian/exporter", "version": "3.1.3", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/exporter.git", "reference": "6b853149eab67d4da22291d36f5b0631c0fd856e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/exporter/zipball/6b853149eab67d4da22291d36f5b0631c0fd856e", "reference": "6b853149eab67d4da22291d36f5b0631c0fd856e", "shasum": ""}, "require": {"php": ">=7.0", "sebastian/recursion-context": "^3.0"}, "require-dev": {"ext-mbstring": "*", "phpunit/phpunit": "^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.1.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Provides the functionality to export PHP variables for visualization", "homepage": "http://www.github.com/sebastianbergmann/exporter", "keywords": ["export", "exporter"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/exporter/issues", "source": "https://github.com/sebastian<PERSON>mann/exporter/tree/3.1.3"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-11-30T07:47:53+00:00"}, {"name": "sebastian/global-state", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/global-state.git", "reference": "e8ba02eed7bbbb9e59e43dedd3dddeff4a56b0c4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebastian<PERSON>mann/global-state/zipball/e8ba02eed7bbbb9e59e43dedd3dddeff4a56b0c4", "reference": "e8ba02eed7bbbb9e59e43dedd3dddeff4a56b0c4", "shasum": ""}, "require": {"php": "^7.0"}, "require-dev": {"phpunit/phpunit": "^6.0"}, "suggest": {"ext-uopz": "*"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Snapshotting of global state", "homepage": "http://www.github.com/sebastian<PERSON>mann/global-state", "keywords": ["global state"], "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/global-state/issues", "source": "https://github.com/sebastian<PERSON>mann/global-state/tree/2.0.0"}, "time": "2017-04-27T15:39:26+00:00"}, {"name": "sebastian/object-enumerator", "version": "3.0.4", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON><PERSON>/object-enumerator.git", "reference": "e67f6d32ebd0c749cf9d1dbd9f226c727043cdf2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/se<PERSON><PERSON><PERSON><PERSON>/object-enumerator/zipball/e67f6d32ebd0c749cf9d1dbd9f226c727043cdf2", "reference": "e67f6d32ebd0c749cf9d1dbd9f226c727043cdf2", "shasum": ""}, "require": {"php": ">=7.0", "sebastian/object-reflector": "^1.1.1", "sebastian/recursion-context": "^3.0"}, "require-dev": {"phpunit/phpunit": "^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Traverses array structures and object graphs to enumerate all referenced objects", "homepage": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-enumerator/tree/3.0.4"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-11-30T07:40:27+00:00"}, {"name": "sebastian/object-reflector", "version": "1.1.2", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector.git", "reference": "9b8772b9cbd456ab45d4a598d2dd1a1bced6363d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/object-reflector/zipball/9b8772b9cbd456ab45d4a598d2dd1a1bced6363d", "reference": "9b8772b9cbd456ab45d4a598d2dd1a1bced6363d", "shasum": ""}, "require": {"php": ">=7.0"}, "require-dev": {"phpunit/phpunit": "^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Allows reflection of object attributes, including inherited and non-public ones", "homepage": "https://github.com/sebastian<PERSON>mann/object-reflector/", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/object-reflector/tree/1.1.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-11-30T07:37:18+00:00"}, {"name": "sebastian/recursion-context", "version": "3.0.1", "source": {"type": "git", "url": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context.git", "reference": "367dcba38d6e1977be014dc4b22f47a484dac7fb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/recursion-context/zipball/367dcba38d6e1977be014dc4b22f47a484dac7fb", "reference": "367dcba38d6e1977be014dc4b22f47a484dac7fb", "shasum": ""}, "require": {"php": ">=7.0"}, "require-dev": {"phpunit/phpunit": "^6.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides functionality to recursively process PHP variables", "homepage": "http://www.github.com/sebastian<PERSON>mann/recursion-context", "support": {"issues": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/recursion-context/tree/3.0.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-11-30T07:34:24+00:00"}, {"name": "sebastian/resource-operations", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/resource-operations.git", "reference": "31d35ca87926450c44eae7e2611d45a7a65ea8b3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/resource-operations/zipball/31d35ca87926450c44eae7e2611d45a7a65ea8b3", "reference": "31d35ca87926450c44eae7e2611d45a7a65ea8b3", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides a list of PHP built-in functions that operate on resources", "homepage": "https://www.github.com/sebastianbergmann/resource-operations", "support": {"issues": "https://github.com/sebastian<PERSON>mann/resource-operations/issues", "source": "https://github.com/sebastian<PERSON>mann/resource-operations/tree/2.0.2"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2020-11-30T07:30:19+00:00"}, {"name": "sebastian/version", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/version.git", "reference": "99732be0ddb3361e16ad77b68ba41efc8e979019"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON><PERSON>/version/zipball/99732be0ddb3361e16ad77b68ba41efc8e979019", "reference": "99732be0ddb3361e16ad77b68ba41efc8e979019", "shasum": ""}, "require": {"php": ">=5.6"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "description": "Library that helps with managing the version number of Git-hosted PHP projects", "homepage": "https://github.com/sebastian<PERSON>mann/version", "support": {"issues": "https://github.com/sebastian<PERSON>mann/version/issues", "source": "https://github.com/sebas<PERSON><PERSON>mann/version/tree/master"}, "time": "2016-10-03T07:35:21+00:00"}, {"name": "seld/jsonlint", "version": "1.10.0", "source": {"type": "git", "url": "https://github.com/Seldaek/jsonlint.git", "reference": "594fd6462aad8ecee0b45ca5045acea4776667f1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/jsonlint/zipball/594fd6462aad8ecee0b45ca5045acea4776667f1", "reference": "594fd6462aad8ecee0b45ca5045acea4776667f1", "shasum": ""}, "require": {"php": "^5.3 || ^7.0 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^1.5", "phpunit/phpunit": "^4.8.35 || ^5.7 || ^6.0 || ^8.5.13"}, "bin": ["bin/jsonlint"], "type": "library", "autoload": {"psr-4": {"Seld\\JsonLint\\": "src/Seld/JsonLint/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "JSON Linter", "keywords": ["json", "linter", "parser", "validator"], "support": {"issues": "https://github.com/Seldaek/jsonlint/issues", "source": "https://github.com/Seldaek/jsonlint/tree/1.10.0"}, "funding": [{"url": "https://github.com/Seldaek", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/seld/jsonlint", "type": "tidelift"}], "time": "2023-05-11T13:16:46+00:00"}, {"name": "seld/phar-utils", "version": "1.2.1", "source": {"type": "git", "url": "https://github.com/Seldaek/phar-utils.git", "reference": "ea2f4014f163c1be4c601b9b7bd6af81ba8d701c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/phar-utils/zipball/ea2f4014f163c1be4c601b9b7bd6af81ba8d701c", "reference": "ea2f4014f163c1be4c601b9b7bd6af81ba8d701c", "shasum": ""}, "require": {"php": ">=5.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Seld\\PharUtils\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be"}], "description": "PHAR file format utilities, for when PHP phars you up", "keywords": ["phar"], "support": {"issues": "https://github.com/Seldaek/phar-utils/issues", "source": "https://github.com/Seldaek/phar-utils/tree/1.2.1"}, "time": "2022-08-31T10:31:18+00:00"}, {"name": "symfony/filesystem", "version": "v5.4.25", "source": {"type": "git", "url": "https://github.com/symfony/filesystem.git", "reference": "0ce3a62c9579a53358d3a7eb6b3dfb79789a6364"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/filesystem/zipball/0ce3a62c9579a53358d3a7eb6b3dfb79789a6364", "reference": "0ce3a62c9579a53358d3a7eb6b3dfb79789a6364", "shasum": ""}, "require": {"php": ">=7.2.5", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.8", "symfony/polyfill-php80": "^1.16"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Filesystem\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides basic utilities for the filesystem", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/filesystem/tree/v5.4.25"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2023-05-31T13:04:02+00:00"}, {"name": "theseer/tokenizer", "version": "1.2.0", "source": {"type": "git", "url": "https://github.com/theseer/tokenizer.git", "reference": "75a63c33a8577608444246075ea0af0d052e452a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/theseer/tokenizer/zipball/75a63c33a8577608444246075ea0af0d052e452a", "reference": "75a63c33a8577608444246075ea0af0d052e452a", "shasum": ""}, "require": {"ext-dom": "*", "ext-tokenizer": "*", "ext-xmlwriter": "*", "php": "^7.2 || ^8.0"}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A small library for converting tokenized PHP source code into XML and potentially other formats", "support": {"issues": "https://github.com/theseer/tokenizer/issues", "source": "https://github.com/theseer/tokenizer/tree/master"}, "funding": [{"url": "https://github.com/theseer", "type": "github"}], "time": "2020-07-12T23:59:07+00:00"}], "aliases": [], "minimum-stability": "stable", "stability-flags": [], "prefer-stable": false, "prefer-lowest": false, "platform": {"php": "^7.4.0"}, "platform-dev": [], "plugin-api-version": "2.0.0"}