<?php
/**
 * Zend Framework
 *
 * LICENSE
 *
 * This source file is subject to the new BSD license that is bundled
 * with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://framework.zend.com/license/new-bsd
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category   Zend
 * @package    Zend_Translate
 * @subpackage Ressource
 * @copyright  Copyright (c) 2005-2015 Zend Technologies USA Inc. (http://www.zend.com)
 * @license    http://framework.zend.com/license/new-bsd     New BSD License
 * @version    $Id:$
 */

/**
 * SK-Revision:
 */
return array(
    // Zend_Captcha_ReCaptcha
    "Missing captcha fields" => "Chýba captcha pole",
    "Failed to validate captcha" => "Captchu sa nepodarilo overiť",
    "Captcha value is wrong: %value%" => "Hodnota captchy neni správna: %value%",

    // Zend_Captcha_Word
    "Empty captcha value" => "Hodnota captchy nebola zadaná",
    "Captcha ID field is missing" => "Chýba pole captcha ID",
    "Captcha value is wrong" => "Chybná hodnota catpchy",
);
