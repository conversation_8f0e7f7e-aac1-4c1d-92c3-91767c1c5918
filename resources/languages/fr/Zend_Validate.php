<?php
/**
 * Zend Framework
 *
 * LICENSE
 *
 * This source file is subject to the new BSD license that is bundled
 * with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://framework.zend.com/license/new-bsd
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category   Zend
 * @package    Zend_Translate
 * @subpackage Ressource
 * @copyright  Copyright (c) 2005-2015 Zend Technologies USA Inc. (http://www.zend.com)
 * @license    http://framework.zend.com/license/new-bsd     New BSD License
 * @version    $Id:$
 */

/**
 * EN-Revision: 22668
 */
return array (
    "'%hostname%' does not appear to have a valid MX record for the email address '%value%'" => "'%hostname%' ne semble pas avoir d'enregistrement MX valide pour l'adresse email '%value%'",
    "'%hostname%' is not a valid hostname for email address '%value%'" => "'%hostname%' n'est pas un nom d'hôte valide pour l'adresse email '%value%'",
    "'%hostname%' is not in a routable network segment. The email address '%value%' should not be resolved from public network" => "'%hostname%' n'est pas dans un segment réseau routable. L'adresse email '%value%' ne devrait pas être résolue depuis un réseau public.",
    "'%localPart%' can not be matched against dot-atom format" => "'%localPart%' ne correspond pas au format dot-atom",
    "'%localPart%' can not be matched against quoted-string format" => "'%localPart%' ne correspond pas au format quoted-string",
    "'%localPart%' is not a valid local part for email address '%value%'" => "'%localPart%' n'est pas une partie locale valide pour l'adresse email '%value%'",
    "'%value%' appears to be a DNS hostname but cannot extract TLD part" => "'%value%' semble être un nom d'hôte DNS mais l'extension TLD ne peut être extraite",
    "'%value%' appears to be a DNS hostname but cannot match TLD against known list" => "'%value%' semble être un nom d'hôte DNS mais son extension TLD semble inconnue",
    "'%value%' appears to be a DNS hostname but cannot match against hostname schema for TLD '%tld%'" => "'%value%' semble être un nom d'hôte DNS valide mais ne correspond pas au schéma de l'extension TLD '%tld%'",
    "'%value%' appears to be a DNS hostname but contains a dash in an invalid position" => "'%value%' semble être un nom d'hôte DNS mais il contient un tiret à une position invalide",
    "'%value%' appears to be a DNS hostname but the given punycode notation cannot be decoded" => "'%value%' semble être un DNS valide mais le code n'a pu être décodé",
    "'%value%' appears to be a local network name but local network names are not allowed" => "'%value%' semble être un nom réseau local mais les noms locaux sont interdits",
    "'%value%' appears to be an IP address, but IP addresses are not allowed" => "'%value%' semble être une IP valide mais celles-ci ne sont pas autorisées",
    "'%value%' contains an invalid amount of digits" => "'%value%' contient un nombre incorrect de chiffres",
    "'%value%' contains characters which are non alphabetic and no digits" => "'%value%' contient des caractères non alphabétiques et non numériques",
    "'%value%' contains invalid characters" => "'%value%' contient des caractères invalides",
    "'%value%' contains non alphabetic characters" => "'%value%' contient des caractères non alphabétiques",
    "'%value%' does not appear to be a float" => "'%value%' ne semble pas être de type flottant",
    "'%value%' does not appear to be a postal code" => "'%value%' ne semble pas être un code postal valide",
    "'%value%' does not appear to be a valid IP address" => "'%value%' n'est pas une IP valide",
    "'%value%' does not appear to be a valid date" => "'%value%' ne semble pas être une date valide",
    "'%value%' does not appear to be a valid local network name" => "'%value%' ne semble pas être une adresse réseau local valide",
    "'%value%' does not appear to be an integer" => "'%value%' n'est pas un entier",
    "'%value%' does not fit the date format '%format%'" => "'%value%' ne correspond pas au format de date '%format%'",
    "'%value%' does not match against pattern '%pattern%'" => "'%value%' n'a pas de correspondance avec le motif '%pattern%'",
    "'%value%' does not match the expected structure for a DNS hostname" => "'%value%' ne correspond pas à la structure d'un nom d'hôte DNS valide",
    "'%value%' exceeds the allowed length" => "'%value%' excède la taille autorisée",
    "'%value%' failed checksum validation" => "'%value%' ne passe pas la validation de somme de contrôle",
    "'%value%' has a false IBAN format" => "'%value%' n'a pas un format IBAN valide",
    "'%value%' has failed the IBAN check" => "'%value%' n'a pas passé la validation IBAN",
    "'%value%' has not only hexadecimal digit characters" => "'%value%' ne contient pas uniquement des caractères héxadécimaux",
    "'%value%' is an empty string" => "'%value%' est une chaîne vide",
    "'%value%' is less than %min% characters long" => "La taille de '%value%' est inférieure à %min% caractères",
    "'%value%' is more than %max% characters long" => "La taille de '%value%' est supérieure à %max% caractères",
    "'%value%' is not a valid ISBN number" => "'%value%' n'est pas un ISBN valide",
    "'%value%' is not a valid email address in the basic format local-part@hostname" => "'%value%' n'est pas un email valide dans le format local-part@hostname",
    "'%value%' is not a valid sitemap changefreq" => "'%value%' n'est pas une valeur de fréquence de sitemap valide",
    "'%value%' is not a valid sitemap lastmod" => "'%value%' n'est pas une date de modification de sitemap valide",
    "'%value%' is not a valid sitemap location" => "'%value%' n'est pas un emplacement valide pour une sitemap",
    "'%value%' is not a valid sitemap priority" => "'%value%' n'est pas une priorité sitemap valide",
    "'%value%' is not between '%min%' and '%max%', inclusively" => "'%value%' n'est pas comprise entre '%min%' et '%max%', inclusivement",
    "'%value%' is not from an allowed institute" => "'%value%' ne provient pas d'une institution autorisée",
    "'%value%' is not greater than '%min%'" => "'%value%' n'est pas plus grand que '%min%'",
    "'%value%' is not less than '%max%'" => "'%value%' n'est pas plus petit que '%max%'",
    "'%value%' is not strictly between '%min%' and '%max%'" => "'%value%' n'est pas strictement comprise entre '%min%' et '%max%'",
    "'%value%' is not valid" => "'%value%' n'est pas valide",
    "'%value%' must contain between 13 and 19 digits" => "'%value%' doit contenir entre 13 et 19 chiffres",
    "'%value%' must contain only digits" => "'%value%' ne doit contenir que des chiffres",
    "'%value%' seems to be an invalid creditcard number" => "'%value%' ne semble pas être une carte de crédit valide",
    "'%value%' seems to contain an invalid checksum" => "'%value%' semble contenir un somme de vérification invalide",
    "'%value%' should have a length of %length% characters" => "'%value%' devrait avoir une taille de %length% caractères",
    "'%value%' was not found in the haystack" => "'%value%' ne fait pas partie des valeurs attendues",
    "A PHP extension returned an error while uploading the file '%value%'" => "Une extension PHP a retourné une erreur lors de l'envoi du fichier '%value%'",
    "A crc32 hash could not be evaluated for the given file" => "La somme de contrôle crc32 n'a pas pu être évaluée pour le fichier",
    "A hash could not be evaluated for the given file" => "Une somme de contrôle n'a pas pu être calculée pour le fichier",
    "A md5 hash could not be evaluated for the given file" => "Une somme de contrôle MD5 n'a pas pu être calculée pour le fichier",
    "A record matching '%value%' was found" => "Un enregistrement a été trouvé pour '%value%'",
    "A sha1 hash could not be evaluated for the given file" => "La valeur de somme de contrôle SHA-1 n'a pas pu être calculée pour le fichier",
    "An exception has been raised within the callback" => "Une exception a été levée par la fonction de rappel",
    "An exception has been raised while validating '%value%'" => "Une exception a été levée lors de la validation de '%value%'",
    "All files in sum should have a maximum size of '%max%' but '%size%' were detected" => "Tous les fichiers devraient avoir une taille maximale de '%max%' mais une taille de '%size%' a été détectée",
    "All files in sum should have a minimum size of '%min%' but '%size%' were detected" => "Tous les fichiers devraient avoir une taille minimale de '%min%' mais une taille de '%size%' a été détectée",
    "File '%value%' can't be written" => "Le fichier '%value%' ne peut être écrit",
    "File '%value%' does not exist" => "Le fichier '%value%' n'existe pas",
    "File '%value%' does not match the given crc32 hashes" => "Le fichier '%value%' ne correspond pas à la somme de contrôle crc32",
    "File '%value%' does not match the given hashes" => "Le fichier '%value%' ne correspond pas à la somme de contrôle",
    "File '%value%' does not match the given md5 hashes" => "Le fichier '%value%' ne correspond pas à la somme de contrôle MD5",
    "File '%value%' does not match the given sha1 hashes" => "Le fichier '%value%' ne correspond pas à la somme de contrôle SHA-1",
    "File '%value%' exceeds the defined form size" => "Le fichier '%value%' excède la taille requise par le formulaire",
    "File '%value%' exceeds the defined ini size" => "Le fichier '%value%' excède la taille requise par le fichier ini",
    "File '%value%' exists" => "Le fichier '%value%' existe déja",
    "File '%value%' has a false extension" => "Le fichier '%value%' n'a pas la bonne extension",
    "File '%value%' has a false mimetype of '%type%'" => "Le fichier '%value%' a un mauvais type MIME : '%type%'",
    "File '%value%' is no image, '%type%' detected" => "Le fichier '%value%' n'est pas une image : '%type%' détecté",
    "File '%value%' is not compressed, '%type%' detected" => "Le fichier '%value%' n'est pas compressé : '%type%' détecté",
    "File '%value%' is not readable or does not exist" => "Le fichier '%value%' n'est pas lisible ou n'existe pas",
    "File '%value%' was illegally uploaded. This could be a possible attack" => "Fichier '%value%' mal envoyé. Ceci peut être possiblement une attaque",
    "File '%value%' was not found" => "Fichier '%value%' introuvable",
    "File '%value%' was not uploaded" => "Le fichier '%value%' n'a pas été envoyé",
    "File '%value%' was only partially uploaded" => "Le fichier '%value%' n'a été que partiellement envoyé",
    "Invalid type given. Numeric string, integer or float expected" => "Type invalide. Chaîne numérique, entier ou flottant attendu",
    "Invalid type given. String expected" => "Type invalide. Chaîne attendue",
    "Invalid type given. String or integer expected" => "Type invalide. Chaîne ou entier attendu",
    "Invalid type given. String, integer, array or Zend_Date expected" => "Type invalide. Chaîne, entier, tableau ou Zend_Date attendu",
    "Invalid type given. String, integer, float, boolean or array expected" => "Type invalide. Chaîne, entier, flottant, booléen ou tableau attendu",
    "Invalid type given. String, integer or float expected" => "Type invalide. Chaîne, entier ou flottant attendu",
    "Luhn algorithm (mod-10 checksum) failed on '%value%'" => "L'algorithme Luhn (somme de contrôle mod-10) a échoué pour '%value%'",
    "Maximum allowed height for image '%value%' should be '%maxheight%' but '%height%' detected" => "La hauteur maximale de l'image '%value%' devrait être '%maxheight%' mais '%height%' a été détectée",
    "Maximum allowed size for file '%value%' is '%max%' but '%size%' detected" => "La taille maximale requise pour le fichier '%value%' est de '%max%' mais '%size%' a été détecté",
    "Maximum allowed width for image '%value%' should be '%maxwidth%' but '%width%' detected" => "La largeur maximale de l'image '%value%' devrait être '%maxwidth%' mais '%width%' a été détectée",
    "Minimum expected height for image '%value%' should be '%minheight%' but '%height%' detected" => "La hauteur minimale de l'image '%value%' devrait être '%minheight%' mais '%height%' a été détectée",
    "Minimum expected size for file '%value%' is '%min%' but '%size%' detected" => "La taille minimale requise pour le fichier '%value%' est de '%min%' mais '%size%' a été détecté",
    "Minimum expected width for image '%value%' should be '%minwidth%' but '%width%' detected" => "La largeur minimale de l'image '%value%' devrait être '%minwidth%' mais '%width%' a été détectée",
    "No record matching '%value%' was found" => "Aucun enregistrement trouvé pour '%value%'",
    "No temporary directory was found for file '%value%'" => "Pas de dossier temporaire trouvé pour le fichier '%value%'",
    "No token was provided to match against" => "Aucun jeton de correspondance n'a été donné",
    "One or more files can not be read" => "Un ou plusieurs fichiers n'est pas lisible",
    "The mimetype of file '%value%' could not be detected" => "Le type MIME du fichier '%value%' n'a pu être détecté",
    "The size of image '%value%' could not be detected" => "La taille de l'image '%value%' n'a pas pu être détectée",
    "The two given tokens do not match" => "Les deux jetons passés ne correspondent pas",
    "There was an internal error while using the pattern '%pattern%'" => "Il y a eu une erreur interne lors de l'utilisation du motif '%pattern%'",
    "Too few files, minimum '%min%' are expected but '%count%' are given" => "Trop peu de fichiers : un minimum de '%min%' est autorisé mais '%count%' ont été fournis",
    "Too less words, minimum '%min%' are expected but '%count%' were counted" => "Trop peu de mots, un minimum de '%min%' est requis, '%count%' ont été fournis",
    "Too many files, maximum '%max%' are allowed but '%count%' are given" => "Trop de fichiers : un maximum de'%max%' est autorisé mais '%count%' ont été fournis",
    "Too much words, maximum '%max%' are allowed but '%count%' were counted" => "Trop de mots, un maximum de '%max%' est requis, '%count%' ont été fournis",
    "Unknown country within the IBAN '%value%'" => "Pays inconnu pour l'IBAN '%value%'",
    "Unknown error while uploading file '%value%'" => "Erreur inconnue lors de l'envoi du fichier '%value%'",
    "Value is required and can't be empty" => "Cette valeur est obligatoire et ne peut être vide",
);
