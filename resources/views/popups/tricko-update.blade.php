<div id="app-popup-modal-tricko-update-{{ $tag }}">
    <div>
        <b-modal
            id="modal-popup"
            ref="modal-popup"
            title="{{ $title }}"
            @@show="resetModal"
            @@hidden="resetModal"
            @@ok="handleOk"
            @@cancel="handleCancel"
        >
            {!! $content !!}
            <form ref="form" @submit.stop.prevent="handleSubmit">
                <input type="hidden" name="_token" value="<?= csrf_token() ?>" />

                <b-form-group
                    label="Veľkosť trička"
                    label-for="tricko-input"
                    invalid-feedback="Musíte vyplniť veľkosť trička."
                    :state="trickoState"
                >
                    <b-form-select
                        id="tricko-input"
                        ref="tricko-input"
                        v-model="tricko"
                        :state="trickoState"
                        :options="tricka"
                    ></b-form-select>
                </b-form-group>

                <br />
                <b-form-checkbox id="dontshow-input" v-model="dontshow" :state="dontshowState">
                    Tento popup už nezobrazovať (Veľkosť trička vždy môžete zadať vo <a href="{!! \Buxus\Util\Url::page(\Buxus\Util\PageIds::getZmenaProfiluPouzivatela()) !!}">svojom profile</a> )
                </b-form-checkbox>

                <div class="wait-icon"><img src="{!! asset('buxus/assets/images/wait-38px.gif') !!}" /></div>
            </form>

            <template #modal-ok>Uložiť</template>
            <template #modal-cancel>Zrušiť</template>
        </b-modal>
    </div>
</div>

<script type="text/javascript">
    $(function() {
        window.allPopups.push(new Vue({
            el: '#app-popup-modal-tricko-update-{{ $tag }}',
            mounted: function() {
            },
            data() {
                return {
                    tricko: '',
                    trickoState: null,

                    dontshow: false,
                    dontshowState: null,

                    tricka: [
                        { value: 'Pánske S' , text: 'Pánske S' },
                        { value: 'Pánske M' , text: 'Pánske M' },
                        { value: 'Pánske L' , text: 'Pánske L' },
                        { value: 'Pánske XL' , text: 'Pánske XL' },
                        { value: 'Pánske XXL' , text: 'Pánske XXL' },

                        { value: 'Dámske XS' , text: 'Dámske XS' },
                        { value: 'Dámske S' , text: 'Dámske S' },
                        { value: 'Dámske M' , text: 'Dámske M' },
                        { value: 'Dámske L' , text: 'Dámske L' },
                        { value: 'Dámske XL' , text: 'Dámske XL' },
                        { value: 'Dámske XXL' , text: 'Dámske XXL' },
                    ],
                    isProcessed: false
                }
            },
            methods: {
                init() {
                    this.$refs['modal-popup'].show()
                    this.isProcessed = true;
                },
                isPopupProcessed() {
                    return this.isProcessed;
                },
                checkFormValidity() {
                    const valid = this.$refs.form.checkValidity();

                    if(!valid) {
                        return false;
                    }
                    return true;
                },
                resetModal() {
                    this.tricko = ''
                    this.trickoState = null
                },
                handleOk(bvModalEvt) {
                    // Prevent modal from closing
                    bvModalEvt.preventDefault()
                    // Trigger submit handler
                    this.handleSubmit()
                },
                handleCancel(bvModalEvt) {
                    // Prevent modal from closing
                    bvModalEvt.preventDefault()
                    this.saveShowCheckbox();
                    window.nextPopup();
                },
                handleSubmit() {
                    // Exit when the form isn't valid
                    if (!this.checkFormValidity()) {
                        return
                    }
                    this.saveForm();
                },

                saveForm() {
                    $('.wait-icon img').show();
                    axios.post('{!! \Buxus\Util\Url::page($popup_id) !!}', {
                        page_id: {!! $popup_id !!},
                        action:'update-tricko',
                        popup_tag: '{{ $tag }}',
                        tricko: this.tricko,
                        _token:'<?= csrf_token() ?>',
                        dontshow: this.dontshow
                    })
                        .then(response => {
                            if(!response.data.response) {
                                if(response.data.msg) {
                                    alert(response.data.msg);
                                }
                                else {
                                    alert('Nepodarilo sa uložiť.')
                                }
                                $('.wait-icon img').hide();
                            }
                            else {
                                $('.wait-icon img').hide();
                                // Hide the modal manually
                                this.$nextTick(() => {
                                    this.$bvModal.hide('modal-popup')
                                    window.nextPopup();
                                })
                            }
                        })
                        .catch(error => {
                            $('.wait-icon img').hide();
                            alert('Nepodarilo sa uložiť. Neznáma chyba.')
                        });
                },

                saveShowCheckbox() {
                    $('.wait-icon img').show();
                    axios.post('{!! \Buxus\Util\Url::page($popup_id) !!}', {
                        page_id: {!! $popup_id !!},
                        popup_tag: '{{ $tag }}',
                        action:'update-showcheckbox',
                        dontshow: this.dontshow
                    })
                        .then(response => {
                            if(!response.data.response) {
                                if(response.data.msg) {
                                    alert(response.data.msg);
                                }
                                else {
                                    alert('Nepodarilo sa uložiť.')
                                }
                                $('.wait-icon img').hide();
                                // Hide the modal manually
                                this.$nextTick(() => {
                                    this.$bvModal.hide('modal-popup')
                                })
                            }
                            else {
                                $('.wait-icon img').hide();
                                // Hide the modal manually
                                this.$nextTick(() => {
                                    this.$bvModal.hide('modal-popup')
                                })
                            }
                        })
                        .catch(error => {
                            $('.wait-icon img').hide();
                            alert('Nepodarilo sa uložiť. Neznáma chyba.')
                            // Hide the modal manually
                            this.$nextTick(() => {
                                this.$bvModal.hide('modal-popup')
                            })
                        });
                }
            }
        }));
    });

</script>
