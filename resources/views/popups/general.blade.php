<div id="app-popup-modal-{{ $popup_tag }}">
    <div>
        <b-modal
            id="modal-popup"
            ref="modal-popup"
            title="{{ $title }}"
            @@show="resetModal"
            @@hidden="resetModal"
            @@ok="handleOk"
            @@cancel="handleCancel"
        >
            {!! $content !!}

            Lekáreň: {{ $pharmacy_name }}
            <form ref="form" @submit.stop.prevent="handleSubmit">
                <input type="hidden" name="_token" value="<?= csrf_token() ?>" />

            @foreach($form_config as $form_row)
                    <b-form-group
                        label="{{ $form_row->label }}"
                        label-for="{{ $form_row->name }}-input"
                        invalid-feedback="Musíte vyplniť {{ $form_row->label }}"
                        :state="{{ $form_row->name }}State"
                    >
                        <b-form-select
                            id="{{ $form_row->name }}-input"
                            ref="{{ $form_row->name }}-input"
                            v-model="{{ $form_row->name }}"
                            :state="{{ $form_row->name }}State"
                            :options="{{ $form_row->name }}Options"
                        ></b-form-select>
                    </b-form-group>
                @endforeach

                <br />
                <b-form-checkbox id="dontshow-input" v-model="dontshow" :state="dontshowState">
                    Tento popup už nezobrazovať
                </b-form-checkbox>

                <div class="wait-icon"><img src="{!! asset('buxus/assets/images/wait-38px.gif') !!}" /></div>
            </form>

            <template #modal-ok>Uložiť</template>
            <template #modal-cancel>Zrušiť</template>
        </b-modal>
    </div>
</div>

<script type="text/javascript">
    $(function() {
        window.allPopups.push(new Vue({
            el: '#app-popup-modal-{{ $popup_tag }}',
            mounted: function() {
            },
            data() {
                return {
                    @foreach($form_config as $form_row)
                        {{ $form_row->name }}: '',
                        {{ $form_row->name }}State: null,
                        {{ $form_row->name }}Options: @json($form_row->values),
                    @endforeach
                    dontshow: true,
                    dontshowState: null,
                    isProcessed: false
                }
            },
            methods: {
                init() {
                    this.$refs['modal-popup'].show()
                    this.isProcessed = true;
                },
                isPopupProcessed() {
                    return this.isProcessed;
                },
                checkFormValidity() {
                    const valid = this.$refs.form.checkValidity();

                    @foreach($form_config as $form_row)
                        this.{{ $form_row->name }}State = this.{{ $form_row->name }}.length > 0;
                    @endforeach

                    if(!valid @foreach($form_config as $form_row) || !this.{{ $form_row->name }}State @endforeach) {
                        return false;
                    }
                    return true;
                },
                resetModal() {
                    @foreach($form_config as $form_row)
                        this.{{ $form_row->name }} = ''
                        this.{{ $form_row->name }}State = null
                    @endforeach
                },
                handleOk(bvModalEvt) {
                    // Prevent modal from closing
                    bvModalEvt.preventDefault()
                    // Trigger submit handler
                    this.handleSubmit()
                },
                handleCancel(bvModalEvt) {
                    // Prevent modal from closing
                    bvModalEvt.preventDefault()
                    this.saveShowCheckbox();
                    window.nextPopup()
                },
                handleSubmit() {
                    // Exit when the form isn't valid
                    if (!this.checkFormValidity()) {
                        return
                    }
                    this.saveForm();
                },

                saveForm() {
                    $('.wait-icon img').show();
                    axios.post('{!! \Buxus\Util\Url::page($popup_id) !!}', {
                        page_id: {!! $popup_id !!},
                        action:'update-popup-general',
                        popup_id: {!! $popup_id !!},
                        popup_tag: '{!! $popup_tag !!}',
                        _token:'<?= csrf_token() ?>',
                        @foreach($form_config as $form_row)
                            {{ $form_row->name }}: this.{{ $form_row->name }},
                        @endforeach
                        dontshow: this.dontshow
                    })
                        .then(response => {
                            if(!response.data.response) {
                                if(response.data.msg) {
                                    alert(response.data.msg);
                                }
                                else {
                                    alert('Nepodarilo sa uložiť.')
                                }
                                $('.wait-icon img').hide();
                            }
                            else {
                                $('.wait-icon img').hide();
                                // Hide the modal manually
                                this.$nextTick(() => {
                                    this.$bvModal.hide('modal-popup')
                                    window.nextPopup()
                                })
                            }
                        })
                        .catch(error => {
                            $('.wait-icon img').hide();
                            alert('Nepodarilo sa uložiť. Neznáma chyba.')
                        });
                },

                saveShowCheckbox() {
                    $('.wait-icon img').show();
                    axios.post('{!! \Buxus\Util\Url::page($popup_id) !!}', {
                        page_id: {!! $popup_id !!},
                        action:'update-showcheckbox',
                        popup_id: {{ $popup_id }},
                        popup_tag: '{!! $popup_tag !!}',
                        dontshow: this.dontshow
                    })
                        .then(response => {
                            if(!response.data.response) {
                                if(response.data.msg) {
                                    alert(response.data.msg);
                                }
                                else {
                                    alert('Nepodarilo sa uložiť.')
                                }
                                $('.wait-icon img').hide();
                                // Hide the modal manually
                                this.$nextTick(() => {
                                    this.$bvModal.hide('modal-popup')
                                })
                            }
                            else {
                                $('.wait-icon img').hide();
                                // Hide the modal manually
                                this.$nextTick(() => {
                                    this.$bvModal.hide('modal-popup')
                                })
                            }
                        })
                        .catch(error => {
                            $('.wait-icon img').hide();
                            alert('Nepodarilo sa uložiť. Neznáma chyba.')
                            // Hide the modal manually
                            this.$nextTick(() => {
                                this.$bvModal.hide('modal-popup')
                            })
                        });
                }
            }
        }));
    });

</script>
