<div id="app-popup-modal-phone-number-update-{{ $tag }}">
    <div>
        <b-modal
            id="modal-popup"
            ref="modal-popup"
            title="{{ $title }}"
            @@show="resetModal"
            @@hidden="resetModal"
            @@ok="handleOk"
            @@cancel="handleCancel"
        >
            {!! $content !!}
            <form ref="form" @submit.stop.prevent="handleSubmit">
                <input type="hidden" name="_token" value="<?= csrf_token() ?>" />
                <b-form-group
                    label="Telefónne číslo"
                    label-for="phone-input"
                    invalid-feedback="Musíte vyplniť telefónne číslo."
                    :state="phoneState"
                >
                    <b-form-input
                        id="phone-input"
                        ref="phone-input"
                        v-model="phone"
                        :state="phoneState"
                        required
                    ></b-form-input>
                </b-form-group>

                <b-form-group
                    label="Veľkosť trička"
                    label-for="tricko-input"
                    invalid-feedback="Musíte vyplniť veľkosť trička."
                    :state="trickoState"
                >
                    <b-form-select
                        id="tricko-input"
                        ref="tricko-input"
                        v-model="tricko"
                        :state="trickoState"
                        :options="tricka"
                    ></b-form-select>
                </b-form-group>

                <b-form-checkbox id="suhlas-input" ref="suhlas-input" v-model="suhlas" :state="suhlasState" required>
                    súhlas so zasielaním marketingových informácii *
                </b-form-checkbox>
                <br />
                <b-form-checkbox id="dontshow-input" v-model="dontshow" :state="dontshowState">
                    Tento popup už nezobrazovať (Telefónne číslo vždy môžete zadať vo <a href="{!! \Buxus\Util\Url::page(\Buxus\Util\PageIds::getZmenaProfiluPouzivatela()) !!}">svojom profile</a> )
                </b-form-checkbox>

                <div class="wait-icon"><img src="{!! asset('buxus/assets/images/wait-38px.gif') !!}" /></div>
            </form>

            <template #modal-ok>Uložiť</template>
            <template #modal-cancel>Zrušiť</template>
        </b-modal>
    </div>
</div>

<script type="text/javascript">
    $(function() {
        window.allPopups.push(new Vue({
            el: '#app-popup-modal-phone-number-update-{{ $tag }}',
            mounted: function() {
            },
            data() {
                return {
                    phone: '',
                    phoneState: null,

                    tricko: '',
                    trickoState: null,


                    suhlas: false,
                    suhlasState: null,

                    dontshow: false,
                    dontshowState: null,

                    submittedNames: [],

                    tricka: [
                        { value: 'Pánske S' , text: 'Pánske S' },
                        { value: 'Pánske M' , text: 'Pánske M' },
                        { value: 'Pánske L' , text: 'Pánske L' },
                        { value: 'Pánske XL' , text: 'Pánske XL' },
                        { value: 'Pánske XXL' , text: 'Pánske XXL' },

                        { value: 'Dámske XS' , text: 'Dámske XS' },
                        { value: 'Dámske S' , text: 'Dámske S' },
                        { value: 'Dámske M' , text: 'Dámske M' },
                        { value: 'Dámske L' , text: 'Dámske L' },
                        { value: 'Dámske XL' , text: 'Dámske XL' },
                        { value: 'Dámske XXL' , text: 'Dámske XXL' },
                    ],
                    isProcessed: false

                }
            },
            methods: {
                init() {
                    this.$refs['modal-popup'].show()
                    this.isProcessed = true;
                },
                isPopupProcessed() {
                    return this.isProcessed;
                },
                checkFormValidity() {
                    const valid = this.$refs.form.checkValidity();

                    const phoneRegexp = /^((\+[0-9]{3})|0)? ?[1-9][0-9]{2} ?[0-9]{3} ?[0-9]{3}$/;
                    this.phoneState = this.phone.length > 0 && phoneRegexp.test(this.phone);
                    this.suhlasState = this.suhlas;

                    if(!valid || !this.phoneState || !this.suhlasState) {
                        return false;
                    }
                    return true;
                },
                resetModal() {
                    this.phone = ''
                    this.phoneState = null
                    this.tricko = ''
                    this.trickoState = null
                },
                handleOk(bvModalEvt) {
                    // Prevent modal from closing
                    bvModalEvt.preventDefault()
                    // Trigger submit handler
                    this.handleSubmit()
                },
                handleCancel(bvModalEvt) {
                    // Prevent modal from closing
                    bvModalEvt.preventDefault()
                    this.saveShowCheckbox();
                    window.nextPopup();
                },
                handleSubmit() {
                    // Exit when the form isn't valid
                    if (!this.checkFormValidity()) {
                        return
                    }
                    this.saveForm();
                },

                saveForm() {
                    $('.wait-icon img').show();
                    axios.post('{!! \Buxus\Util\Url::page($popup_id) !!}', {
                        page_id: {!! $popup_id !!},
                        action:'update-phone',
                        phone: this.phone,
                        tricko: this.tricko,
                        suhlas: this.suhlas,
                        _token:'<?= csrf_token() ?>',
                        dontshow: this.dontshow
                    })
                        .then(response => {
                            if(!response.data.response) {
                                if(response.data.msg) {
                                    alert(response.data.msg);
                                }
                                else {
                                    alert('Nepodarilo sa uložiť.')
                                }
                                $('.wait-icon img').hide();
                            }
                            else {
                                $('.wait-icon img').hide();
                                // Hide the modal manually
                                this.$nextTick(() => {
                                    this.$bvModal.hide('modal-popup')
                                    window.nextPopup();
                                })
                            }
                        })
                        .catch(error => {
                            $('.wait-icon img').hide();
                            alert('Nepodarilo sa uložiť. Neznáma chyba.')
                        });
                },

                saveShowCheckbox() {
                    $('.wait-icon img').show();
                    axios.post('{!! \Buxus\Util\Url::page($popup_id) !!}', {
                        page_id: {!! $popup_id !!},
                        action:'update-showcheckbox',
                        dontshow: this.dontshow
                    })
                        .then(response => {
                            if(!response.data.response) {
                                if(response.data.msg) {
                                    alert(response.data.msg);
                                }
                                else {
                                    alert('Nepodarilo sa uložiť.')
                                }
                                $('.wait-icon img').hide();
                                // Hide the modal manually
                                this.$nextTick(() => {
                                    this.$bvModal.hide('modal-popup')
                                })
                            }
                            else {
                                $('.wait-icon img').hide();
                                // Hide the modal manually
                                this.$nextTick(() => {
                                    this.$bvModal.hide('modal-popup')
                                })
                            }
                        })
                        .catch(error => {
                            $('.wait-icon img').hide();
                            alert('Nepodarilo sa uložiť. Neznáma chyba.')
                            // Hide the modal manually
                            this.$nextTick(() => {
                                this.$bvModal.hide('modal-popup')
                            })
                        });
                }
            }
        }));
    });

</script>
