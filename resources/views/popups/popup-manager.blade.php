<div id="all-popups">
    <script>
        window.allPopups = new Array();
        document.addEventListener('DOMContentLoaded', function(event) {
            setTimeout( function() {
                window.allPopups[0].init();
                }, 1000)
        });

        window.nextPopup = function () {
            window.allPopups.every(function (popup) {
                if(!popup.isPopupProcessed()) {
                    popup.init();
                    return false;
                }
                return true;
            })
        }
    </script>
    {!! $popups_html !!}
</div>
