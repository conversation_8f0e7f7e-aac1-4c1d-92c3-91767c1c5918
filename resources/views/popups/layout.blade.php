<div id="app-popup-modal-{{ $tag }}">
    <div>
        <b-modal
            id="modal-popup"
            ref="modal-popup"
            title="{{ $title }}"
        @@show="resetModal"
        @@hidden="resetModal"
        @@ok="handleOk"
        >{!! $content !!}</b-modal>
    </div>
</div>

<script type="text/javascript">
    $(function() {
        window.allPopups.push(new Vue({
        el: '#app-popup-modal-{{ $tag }}',
        mounted: function() {
        },
        data() {
            return {
                isProcessed: false
            }
        },
        methods: {
            init() {
                this.$refs['modal-popup'].show()
                this.isProcessed = true;
            },
            isPopupProcessed() {
                return this.isProcessed;
            },
            resetModal() {
            },
            handleOk(bvModalEvt) {
                // Prevent modal from closing
                bvModalEvt.preventDefault()
                // Trigger submit handler
                this.handleSubmit()
            },
            handleSubmit() {


                // Hide the modal manually
                this.$nextTick(() => {
                    this.$bvModal.hide('modal-popup')
                    window.nextPopup();
                })
            }
        }
    }));
    });

</script>
