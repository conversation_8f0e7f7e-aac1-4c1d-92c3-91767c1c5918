@extends('buxus-legacy::layout.blade_in_zf')

@section('content')

    @include('moje-body.vue-components.moje-body-tab-moje-body')
    @include('moje-body.vue-components.moje-body-tab-ziadost-o-vyplatenie')
    @include('moje-body.vue-components.moje-body-tab-historia-vyplacania')

    <div class="container">
        <section  id="content">
            <div class="row">
                <div class="col-md-12" style="@if($hidden_title) display:none; @endif;">
                    <h1 class="page-title">{{ $page->getValue('title')  }}</h1>
                </div>
                <div class="col-md-12">

                    <div id="moje-body-app">

                        <b-alert v-model="showAlertActivePharmacyNotSet" variant="danger">Musíte vybrať lekáreň!</b-alert>

                        <b-tabs content-class="mt-3" v-if="activePharmacyId && hasRightsToMojeBody" @activate-tab="onActivateTab">
                            <b-tab title="Vyplácanie bodov"  active v-if="hasRightsToMojeBody">
                                <moje-body-tab-moje-body></moje-body-tab-moje-body>
                            </b-tab>
                            <b-tab title="Žiadosti o vyplatenie" v-if="hasRightsToZiadosti">
                                <moje-body-tab-ziadost-o-vyplatenie></moje-body-tab-ziadost-o-vyplatenie>
                            </b-tab>
                            <b-tab title="História vyplácania">
                                <moje-body-tab-historia-vyplacania></moje-body-tab-historia-vyplacania>
                            </b-tab>
                        </b-tabs>

                    </div>

                    <script type="text/javascript">
                        var mojeBodyApp = new Vue({
                            el: '#moje-body-app',
                            components: {
                                'moje-body-tab-moje-body': vueComponentMojeBodyTabMojeBody,
                                'moje-body-tab-ziadost-o-vyplatenie': vueComponentMojeBodyTabZiadostOVyplatenie,
                                'moje-body-tab-historia-vyplacania': vueComponentMojeBodyTabHistoriaVyplacania,
                            },
                            data: function () {
                                return @json($app_data);
                            },
                            computed: {
                                showAlertActivePharmacyNotSet: function() {
                                    return(!this.activePharmacyId);
                                }
                            },
                            methods: {
                                onActivateTab: function (newTabIndex, prevTabIndex, bvEvt) {
                                    this.$root.$emit('activate-tab', newTabIndex, prevTabIndex);
                                }
                            }
                        });
                    </script>
                </div>
            </div>
        </section>
    </div>
@endsection


@section('zf_layout_name')
    main
@endsection
