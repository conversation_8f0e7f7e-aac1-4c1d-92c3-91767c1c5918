<template id="vue-template-moje-body-tab-historia-vyplacania">
    <div>
        <b-row>
            <b-col lg="3">
                <b-input-group size="sm">
                    <b-form-input v-model="filterText" type="search" id="filterText" placeholder="Vyhľadať"></b-form-input>
                    <b-input-group-append>
                        <b-button :disabled="!filterText" @click="filterText = ''">X</b-button>
                    </b-input-group-append>
                </b-input-group>
            </b-col>

            <b-col lg="3">
                <div style="padding-bottom: 10px;">
                    <b-input-group size="sm">
                        <b-form-input v-model="filterBodyOd" type="search" id="filterBodyOd" placeholder="body od"></b-form-input>
                        <b-input-group-append>
                            <b-button :disabled="!filterBodyOd" @click="filterBodyOd = ''">X</b-button>
                        </b-input-group-append>
                    </b-input-group>
                </div>
                <div>
                    <b-input-group size="sm">
                        <b-form-input v-model="filterBodyDo" type="search" id="filterBodyDo" placeholder="body do"></b-form-input>
                        <b-input-group-append>
                            <b-button :disabled="!filterBodyDo" @click="filterBodyDo = ''">X</b-button>
                        </b-input-group-append>
                    </b-input-group>
                </div>
            </b-col>


            <b-col lg="3">
                <b-form-datepicker id="filterFrom1" v-model="filterFrom" placeholder="od" locale="sk" reset-button></b-form-datepicker>
                <b-form-datepicker id="filterTo1" v-model="filterTo" placeholder="do" locale="sk" reset-button></b-form-datepicker>
            </b-col>

            <b-col lg="3">

                <b-form-tags v-model="filterStates" size="lg" add-on-change no-outer-focus>
                    <template v-slot="{ tags, inputAttrs, inputHandlers, disabled, removeTag }">
                        <ul v-if="tags.length > 0" class="list-inline d-inline-block mb-2">
                            <li v-for="tag in tags" :key="tag" class="list-inline-item">
                                <b-form-tag
                                    @remove="removeTag(tag)"
                                    :title="tag"
                                    :disabled="disabled"
                                    variant="info"
                                >@{{ tag }}</b-form-tag>
                            </li>
                        </ul>
                        <b-form-select
                            v-bind="inputAttrs"
                            v-on="inputHandlers"
                            :disabled="disabled || filterStatesArray.length === 0"
                            :options="filterStatesArray"
                        >
                            <template v-slot:first>
                                <!-- This is required to prevent bugs with Safari -->
                                <option disabled value="">Vyberte akcie...</option>
                            </template>
                        </b-form-select>
                    </template>
                </b-form-tags>

                <a v-if="isVbcAdmin" class="btn btn-secondary" href="{{ $url }}&action=xlsx">XLSX export</a>
            </b-col>


        </b-row>

        <b-row>
            <b-col lg="12">
                <br />
            </b-col>
        </b-row>

        <b-row v-if="requests.length">
            <b-col>
                <b-table
                    ref="table"
                    id="table-ziadosti"
                    striped hover
                    :items="items"
                    responsive
                    :fields="fields"

                    selectable
                    :select-mode="'single'"
                    @row-clicked="onRowClicked"

                    :filter="filter" :filter-function="filterData"
                    :busy="isBusy"
                    @filtered="onFiltered"
                >
                    <template v-slot:head(check)="data">
                        <b-form-checkbox v-model="checkAll" @change="onChangeCheckAll"></b-form-checkbox>
                    </template>

                    <template v-slot:cell(check)="data">
                        <b-form-checkbox v-model="data.item.check" @change="onChangeCheck(data.item)"></b-form-checkbox>
                    </template>

                    <template v-slot:cell(user)="data">
                        @{{ data.value != ' ' ? data.value : data.item.pzs }}
                    </template>

                    <template v-slot:cell()="data">
                        @{{ data.value }}
                    </template>
                    <template v-slot:row-details="row">
                        <b-card>
                            <b-table :items="details" :fields="detailsFields">
                                <template v-slot:cell(stav)="data">
                                    <strong :class="{ 'text-success': data.value == 'Hotovo', 'text-warning': data.value == 'Čaká sa' }">@{{ data.value }}</strong>
                                </template>
                            </b-table>
                        </b-card>
                    </template>

                    <template v-slot:cell(pdf)="data">
                        <a v-if="data.item.pharmacy_id" v-bind:href="'{{ $url }}&action=pdf&id=' + data.item.request_id + '&pharmacy_id=' + data.item.pharmacy_id">pdf</a>
                    </template>

                </b-table>
            </b-col>
        </b-row>

        <div class="row" v-if="!requests.length">
            <div class="col-md-3"></div>
            <div class="col-md-6">
                <div class="alert alert-warning">
                    Nie sú žiadne záznamy.
                </div>
            </div>
            <div class="col-md-3"></div>
        </div>


    </div>
</template>

<script type="text/javascript">
    var vueComponentMojeBodyTabHistoriaVyplacania = {
        template: '#vue-template-moje-body-tab-historia-vyplacania',
        mounted() {
            let that = this;
            this.$root.$on('activate-tab', function (newTabIndex, prevTabIndex, bvEvt) {
                if(newTabIndex === 2) {
                    console.log('!!!EVENT activate-tab ' + newTabIndex);
                    that.updateData();
                }
            })
        },
        data: function () {
            return {
                aaa:1,
                requests: @json($log_vyplacania['log-vyplacania']),
                isVbcAdmin: {{ $app_data['isVbcAdmin'] }},
                selected:[],
                selectedRow:null,
                checkAll:false,
                states: {
                    'Žiadosť o vyplatenie bodov.': 'Žiadosť o vyplatenie',
                    'Žiadosť odsúhlasená majiteľom': 'Odsúhlasená majiteľom',
                    'Schválenie vyplatenia':'Schválenie vyplatenia'

                },
                fields: [
                    {
                        key: 'user',
                        label: 'Žiadateľ',
                        sortable: true
                    },
                    {
                        key: 'pzs',
                        label: 'PZS',
                        sortable: true
                    },
                    {
                        key: 'pharmacy',
                        label: 'Lekáreň',
                        sortable: true
                    },
                    {
                        key: 'last_update',
                        label: 'Dátum poslednej zmeny',
                        class: 'text-left',
                        formatter: 'dateFormat',
                        sortable: true
                    },
                    {
                        key: 'request_timestamp',
                        label: 'Dátum založenia',
                        class: 'text-left',
                        formatter: 'dateFormat',
                        sortable: true
                    },
                    {
                        key: 'requested_value',
                        label: 'Body',
                        sortable: true
                    },
                    {
                        key: 'current_state',
                        label: 'Aktuálny stav',
                        sortable: true
                    },
                    {
                        key: 'pdf',
                        label: 'Pdf'
                    }
                ],
                filter:'',
                filterText: null,
                filterFrom: '{{ $from }}',
                filterTo: '{{ $to }}',
                filterBodyOd: null,
                filterBodyDo: null,
                isBusy: false,
                filterUsed: false,
                filteredItemIds: [],
                filterStates: [],
            };
        },
        computed: {
            items: function() {
                return(this.requests);
            },
            detailsFields: function() { return [
                {
                    key:'pohyb',
                    label:'Pohyb',
                    class:'text-left'
                },
                {
                    key:'body',
                    label:'Body',
                },
                {
                    key:'datum',
                    label:'Dátum',
                },
                {
                    key:'stav',
                    label:'Stav',
                },
                {
                    key:'zodpovedny',
                    label:'Zodpovedná osoba',
                }
            ]
            },
            details: function() {
                if(this.selectedRow === null) {
                    return [];
                }
                return [
                    {
                        pohyb:"Žiadosť o vyplatenie bodov",
                        body:this.selectedRow.requested_value,
                        datum: this.dateFormat(this.selectedRow.request_timestamp),
                        stav:'Hotovo',
                        zodpovedny:this.selectedRow.user
                    },
                    {
                        pohyb: "Žiadosť odsúhlasená majiteľom",
                        body: this.selectedRow.requested_value,
                        datum: this.selectedRow.accept_pharmacy_owner_id ? this.dateFormat(this.selectedRow.accept_pharmacy_owner_timestamp):'',
                        stav: this.selectedRow.accept_pharmacy_owner_id? 'Hotovo':(this.selectedRow.accept_admin_id?'':'Čaká sa'),
                        zodpovedny: this.selectedRow.accept_pharmacy_owner_id ? this.selectedRow.owner :'Majiteľ lekárne'
                    },
                    {
                        pohyb:"Schválenie vyplatenia",
                        body: this.selectedRow.requested_value,
                        datum: this.selectedRow.accept_admin_id ? this.dateFormat(this.selectedRow.accept_admin_timestamp):'',
                        stav: this.selectedRow.accept_pharmacy_owner_id ? (this.selectedRow.accept_admin_id?'Hotovo':'Čaká sa'):(this.selectedRow.accept_admin_id?'Hotovo':''),
                        zodpovedny: 'Vaša Lekáraň'
                    }
                ];
            },
            filterStatesArray() {
                return Object.values(this.states).filter(function (opt) { return (this.filterStates.indexOf(opt) === -1) }, this);
            }
        },
        methods: {
            newDateObject(mysqlDateTime) {
                if(mysqlDateTime === null) {
                    return(null);
                }
                var dateTimeParts= mysqlDateTime.split(/[- :T.Z]/); // regular expression split that creates array with: year, month, day, hour, minutes, seconds values
                dateTimeParts[1]--; // monthIndex begins with 0 for January and ends with 11 for December so we need to decrement by one
                var dateObject = new Date(dateTimeParts[0], dateTimeParts[1], dateTimeParts[2], dateTimeParts[3]?dateTimeParts[3]:0, dateTimeParts[4]?dateTimeParts[4]:0, dateTimeParts[5]?dateTimeParts[5]:0);
                return(dateObject);
            },
            dateFormat(value) {
                let d = this.newDateObject(value);
                if(d === null) {
                    return('');
                }
                return(d.getDate() + '.' + (d.getMonth()+1) + '.' + d.getFullYear());
            },

            onRowClicked(row)
            {
                var show = true;
                if(row._showDetails) {
                    show = false;
                }

                this.selectedRow = null;
                this.$refs.table.items.forEach(function(item) {
                    item._showDetails = false;
                });


                if(show) {
                    row._showDetails = true;
                    this.selectedRow = row;
                }
            },

            updateData()
            {
                let that = this;
                let params = {
                    action: 'update-log-vyplacania',
                    from: this.filterFrom,
                    to: this.filterTo
                };

                this.isBusy = true;
                axios.post( '{{ $url }}', {
                    page_id: {{ $page_id }},
                    params: params
                }).then(function(response) {
                    that.isBusy = false;
                    //console.log(response)
                    that.requests = response.data.log_vyplacania['log-vyplacania'];
                    //that.key++;
                });
            },

            filterData(row, filter) {

                if(this.filterBodyOd !== null && this.filterBodyOd !== '') {
                    if(Math.round(parseFloat(row.requested_value)*100) < Math.round(parseFloat(this.filterBodyOd)*100)) {
                        return(false);
                    }
                }

                if(this.filterBodyDo !== null && this.filterBodyDo !== '') {
                    console.log(this.filterBodyDo);
                    console.log(row);
                    if(Math.round(parseFloat(row.requested_value)*100) > Math.round(parseFloat(this.filterBodyDo)*100)) {
                        return(false);
                    }
                }

                if(this.filterFrom !== null && this.filterFrom !== '') {
                    if(row.last_update < this.filterFrom) {
                        return(false);
                    }
                }

                if(this.filterTo !== null && this.filterTo !== '') {
                    let d1 = this.newDateObject(row.last_update);
                    let d2 = this.newDateObject(this.filterTo);
                    if(d1.toISOString().substring(0,10) > (d2.toISOString().substring(0,10) + ' 23:59:59')) {
                        return(false);
                    }
                }

                if(this.filterText !== null && this.filterText.length) {
                    var filterTextResult = false;
                    if (row.user && this.fulltextCompare(row.user, this.filterText)) {
                        filterTextResult = true;
                    }
                    if (row.pharmacy && this.fulltextCompare(row.pharmacy, this.filterText)) {
                        filterTextResult = true;
                    }
                    if (row.pzs && this.fulltextCompare(row.pzs, this.filterText)) {
                        filterTextResult = true;
                    }

                    if(!filterTextResult) {
                        return (false);
                    }
                }

                if(this.filterStates.length) {
                    let state = false;
                    if(this.filterStates.indexOf(this.states[row.current_state]) !== -1) {
                        state = true;
                    }
                    if(!state) {
                        return(false);
                    }
                }

                return(true);
            },
            fulltextCompare(string, filter) {
                string = string.toLowerCase();
                filter = filter.toLowerCase();

                string = this.removeDiacritic(string);
                filter = this.removeDiacritic(filter);

                if(string.indexOf(filter) !== -1) {
                    return(true);
                }
                return(false);
            },

            removeDiacritic(text) {
                var dia =   "áäčďéíľĺňóôŕšťúýÁČĎÉÍĽĹŇÓŠŤÚÝŽ";
                var nodia = "aacdeillnoorstuyACDEILLNOSTUYZ";

                var convertText = "";
                for(i=0; i<text.length; i++) {
                    if(dia.indexOf(text.charAt(i))!=-1) {
                        convertText += nodia.charAt(dia.indexOf(text.charAt(i)));
                    }
                    else {
                        convertText += text.charAt(i);
                    }
                }
                return convertText;
            },

            onFiltered(filteredItems) {
                this.filteredItemIds = [];
                this.filterUsed = true;

                if(filteredItems.length) {
                    filteredItems.forEach(function(item) {
                        this.filteredItemIds.push(item.id);
                    }, this);
                }
            }
        },
        watch: {
            filterText: function(val, oldVal) {
                this.filter += '.';
            },
            filterFrom: function(val, oldVal) {
                this.filter += '.';
                this.updateData();
            },
            filterTo: function(val, oldVal) {
                this.filter += '.';
                this.updateData();
            },
            filterBodyOd: function(val, oldVal) {
                this.filter += '.';
            },
            filterBodyDo: function(val, oldVal) {
                this.filter += '.';
            },
            filterStates: function(val, oldVal) {
                this.filter += '.';
            },
        }
    };
</script>

