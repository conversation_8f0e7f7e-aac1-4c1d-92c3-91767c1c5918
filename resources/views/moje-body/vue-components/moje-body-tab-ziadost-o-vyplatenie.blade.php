<template id="vue-template-moje-body-tab-ziadost-o-vyplatenie">
    <div>

        <b-row>
            <b-col lg="3">
                <b-input-group size="sm">
                    <b-form-input v-model="filterText" type="search" id="filterText" placeholder="Vyhľadať"></b-form-input>
                    <b-input-group-append>
                        <b-button :disabled="!filterText" @click="filterText = ''">X</b-button>
                    </b-input-group-append>
                </b-input-group>
            </b-col>

            <b-col lg="3">
                <div style="padding-bottom: 10px;">
                    <b-input-group size="sm">
                        <b-form-input v-model="filterBodyOd" type="search" id="filterBodyOd" placeholder="body od"></b-form-input>
                        <b-input-group-append>
                            <b-button :disabled="!filterBodyOd" @click="filterBodyOd = ''">X</b-button>
                        </b-input-group-append>
                    </b-input-group>
                </div>
                <div>
                    <b-input-group size="sm">
                        <b-form-input v-model="filterBodyDo" type="search" id="filterBodyDo" placeholder="body do"></b-form-input>
                        <b-input-group-append>
                            <b-button :disabled="!filterBodyDo" @click="filterBodyDo = ''">X</b-button>
                        </b-input-group-append>
                    </b-input-group>
                </div>
            </b-col>


            <b-col lg="3">
                <b-form-datepicker id="filterFrom3" v-model="filterFrom" placeholder="od" locale="sk" reset-button></b-form-datepicker>
                <b-form-datepicker id="filterTo3" v-model="filterTo" placeholder="do" locale="sk" reset-button></b-form-datepicker>
            </b-col>

            <b-col lg="3">
                Združiť podľa lekárne <b-form-checkbox switch v-model="groupByPharmacy" @change="onChangeGroupByPharmacy"></b-form-checkbox>
            </b-col>


        </b-row>

        <b-row>
            <b-col lg="12">
                <br />
            </b-col>
        </b-row>

        <b-row v-if="!groupByPharmacy && requests.length">
            <b-col>
                <b-table
                    ref="table"
                    id="table-ziadosti"
                    striped hover
                    :items="items"
                    responsive
                    :fields="fields"

                    selectable
                    :select-mode="'single'"
                    @row-clicked="onRowClicked"

                    :filter="filter" :filter-function="filterData"
                    :busy="isBusy"
                >
                    <!--template v-slot:head(check)="data">
                        <b-form-checkbox v-model="checkAll" @change="onChangeCheckAll"></b-form-checkbox>
                    </template -->

                    <template v-slot:cell(user)="data">
                        @{{ data.value != ' ' ? data.value : data.item.pzs }}
                    </template>

                    <template v-slot:cell(check)="data">
                        <!-- b-form-checkbox v-model="data.item.check" @change="onChangeCheck(data.item)"></b-form-checkbox -->
                    </template>
                    <template v-slot:cell()="data">
                        @{{ data.value }}
                    </template>
                    <template v-slot:row-details="row">
                        <b-card>
                            <b-table :items="details" :fields="detailsFields">
                                <template v-slot:cell(stav)="data">
                                    <strong :class="{ 'text-success': data.value == 'Hotovo', 'text-warning': data.value == 'Čaká sa' }">@{{ data.value }}</strong>
                                </template>
                            </b-table>
                        </b-card>
                    </template>
                </b-table>
            </b-col>
        </b-row>

        <b-row v-if="groupByPharmacy && requests_by_pharmacy.length">
            <b-col>
                <b-table
                    ref="table_by_pharmacy"
                    id="table-ziadosti-pharmacy"
                    striped hover
                    :items="items_pharmacy"
                    responsive
                    :fields="fields_pharmacy"

                    :filter="filter" :filter-function="filterData"
                    :busy="isBusy"
                    @filtered="onFilteredByPharmacy"
                >
                    <template v-slot:head(check)="data">
                        <b-form-checkbox v-model="checkAll" @change="onChangeCheckAll"></b-form-checkbox>
                    </template>

                    <template v-slot:cell(check)="data">
                        <b-form-checkbox v-model="data.item.check" @change="onChangeCheck(data.item)"></b-form-checkbox>
                    </template>

                    <template v-slot:cell(pdf)="data">
                        <a v-if="data.item.pharmacy_id" v-bind:href="'{{ $url }}&action=pdf&id=' + data.item.request_id + '&pharmacy_id=' + data.item.pharmacy_id">pdf</a>
                    </template>

                    <template v-slot:cell()="data">
                        @{{ data.value }}
                    </template>
            </b-col>
        </b-row>

        <b-row v-if="groupByPharmacy && requests_by_pharmacy.length">
            <b-col lg="12">
                <div :class="{'state-box':true }">
                    <div class="state-box-content">
                        <!-- b-form-checkbox v-if="acceptEnabled" v-model="agreement">Súhlasím s <a href="#" target="_blank">podmienkami vyplácania</a></b-form-checkbox -->

                        <a href="#" :class="{btn:true, disabled:(!acceptEnabled || !acceptButtonEnabled)}" v-if="acceptEnabled" @click.prevent.stop="onClickAccept">Schváliť</a>


                        <b-modal id="modal-eticky-kodex" size="xl" scrollable title="{{ $kodex->getValue(Buxus\Util\PropertyTag::TITLE_TAG) }}" ok-only ok-disabled @ok="onClickOkEtickyKodex" @shown="onShowModal">
                            <h1>{{ $kodex->getValue(Buxus\Util\PropertyTag::TITLE_TAG) }}</h1>
                            {!! $kodex->getValue(Buxus\Util\PropertyTag::TEXT_TAG) !!}
                            <span class="end">-----</span>
                            <div id="agreementKodex" v-if="!isVbcAdmin">
                                <b-form-checkbox v-model="agreementKodex" @change="onChangeAgreementKodex">Súhlasím s etickým kódexom</b-form-checkbox>
                            </div>
                        </b-modal>

                        <b-modal id="modal-vop" size="xl" scrollable title="{{ $vop->getValue(Buxus\Util\PropertyTag::TITLE_TAG) }}" ok-only ok-disabled @ok="onClickAcceptDone" @shown="onShowModal">
                            <h1>{{ $vop->getValue(Buxus\Util\PropertyTag::TITLE_TAG) }}</h1>
                            {!! $vop->getValue(Buxus\Util\PropertyTag::TEXT_TAG) !!}
                            <span class="end">-----</span>
                            <div id="agreementVop" v-if="!isVbcAdmin">
                                <b-form-checkbox v-model="agreementVop" @change="onChangeAgreementVop">Súhlasím s VOP</b-form-checkbox>
                            </div>
                        </b-modal>

                    </div>
                </div>
            </b-col>
        </b-row>

        <div class="row" v-if="!requests.length">
            <div class="col-md-3"></div>
            <div class="col-md-6">
                <div class="alert alert-warning">
                    Nie sú žiadne žiadosti k vyplateniu.
                </div>
            </div>
            <div class="col-md-3"></div>
        </div>

    </div>
</template>

<script type="text/javascript">
    var vueComponentMojeBodyTabZiadostOVyplatenie = {
        template: '#vue-template-moje-body-tab-ziadost-o-vyplatenie',

        mounted() {
            let that = this;
            this.$root.$on('activate-tab', function (newTabIndex, prevTabIndex, bvEvt) {
                if(newTabIndex === 1) {
                    console.log('!!!EVENT activate-tab ' + newTabIndex);
                    that.updateData();
                }
            });

            this.$root.$on('scroll', function (newTabIndex, prevTabIndex, bvEvt) {
                console.log('scrolling');
                // modal-body
            });

        },
        data: function () {
            return {
                requests: @json($ziadosti['ziadosti']),
                requests_by_pharmacy: @json($ziadosti_by_pharmacy['ziadosti_by_pharmacy']),
                isVbcAdmin: {{ $app_data['isVbcAdmin'] }},
                selected:[],
                selectedRow:null,
                groupByPharmacy:true,
                checkAll:false,
                fields: [
                    {
                        key: 'check',
                        label: '',
                    },
                    {
                        key: 'user',
                        label: 'Žiadateľ',
                        sortable: true
                    },
                    {
                        key: 'pzs',
                        label: 'PZS',
                        sortable: true
                    },
                    {
                        key: 'pharmacy',
                        label: 'Lekáreň',
                        sortable: true
                    },
                    {
                        key: 'last_update',
                        label: 'Dátum poslednej akcie',
                        class: 'text-left',
                        formatter: 'dateFormat',
                        sortable: true
                    },
                    {
                        key: 'requested_value',
                        label: 'Body',
                        sortable: true
                    },
                ],

                fields_pharmacy: [
                    {
                        key: 'check',
                        label: '',
                    },
                    {
                        key: 'pzs',
                        label: 'PZS',
                        sortable: true
                    },
                    {
                        key: 'pharmacy',
                        label: 'Lekáreň',
                        sortable: true
                    },
                    {
                        key: 'last_update',
                        label: 'Dátum poslednej akcie',
                        class: 'text-left',
                        formatter: 'dateFormat',
                        sortable: true
                    },
                    {
                        key: 'requested_value',
                        label: 'Body',
                        sortable: true
                    },
                    {
                        key: 'pdf',
                        label: 'Pdf'
                    },
                ],

                filter:'',
                filterText: null,
                filterFrom: '{{ $from }}',
                filterTo: '{{ $to }}',
                filterBodyOd: null,
                filterBodyDo: null,
                isBusy: false,
                filterUsed: false,
                filteredItemIds: [],
                acceptEnabled: true,
                agreement: false,
                agreementKodex: false,
                agreementVop: false,
                acceptButtonEnabled: false
            };
        },
        computed: {
            items: function() {
                return(this.requests);
            },
            items_pharmacy: function() {
                return(this.requests_by_pharmacy);
            },
            detailsFields: function() { return [
                {
                    key:'pohyb',
                    label:'Pohyb',
                    class:'text-left'
                },
                {
                    key:'body',
                    label:'Body',
                },
                {
                    key:'datum',
                    label:'Dátum',
                },
                {
                    key:'stav',
                    label:'Stav',
                },
                {
                    key:'zodpovedny',
                    label:'Zodpovedná osoba',
                }
            ]
            },
            details: function() {
                if(this.selectedRow === null) {
                    return [];
                }
                return [
                    {
                        pohyb:"Žiadosť o vyplatenie bodov",
                        body:this.selectedRow.requested_value,
                        datum: this.dateFormat(this.selectedRow.request_timestamp),
                        stav:'Hotovo',
                        zodpovedny:this.selectedRow.user
                    },
                    {
                        pohyb: "Žiadosť odsúhlasená majiteľom",
                        body: this.selectedRow.requested_value,
                        datum: this.selectedRow.accept_pharmacy_owner_id ? this.dateFormat(this.selectedRow.accept_pharmacy_owner_timestamp):'',
                        stav: this.selectedRow.accept_pharmacy_owner_id? 'Hotovo':(this.selectedRow.accept_admin_id?'':'Čaká sa'),
                        zodpovedny: this.selectedRow.accept_pharmacy_owner_id ? this.selectedRow.owner :'Majiteľ lekárne'
                    },
                    {
                        pohyb:"Schválenie vyplatenia",
                        body: this.selectedRow.requested_value,
                        datum: this.selectedRow.accept_admin_id ? this.dateFormat(this.selectedRow.accept_admin_timestamp):'',
                        stav: this.selectedRow.accept_pharmacy_owner_id ? (this.selectedRow.accept_admin_id?'Hotovo':'Čaká sa'):(this.selectedRow.accept_admin_id?'Hotovo':''),
                        zodpovedny: this.selectedRow.accept_admin_id ? this.selectedRow.admin :'Vaša Lekáreň'
                    }
                ];
            }
        },
        methods: {
            // handleScroll (event) {
            //     var $el = $('.modal-body:visible');
            //     var pos = (parseInt($el.offset().top) + parseInt($el.scrollTop()) + parseInt($el.height()));
            //     var endPos = parseInt($el.find('.end').offset().top) - 15;
            //
            //     console.log('pos' + pos + ' ' + endPos);
            //
            //     if(pos >= endPos) {
            //         $('.modal-footer:visible .btn-primary').removeAttr('disabled').removeClass('disabled');
            //     }
            //
            //
            // },
            //
            // initScroll(event) {
            //     console.log('show modal');
            //     $('.modal-body:visible').on('scroll', this.handleScroll); //addEventListener('scroll', this.handleScroll);
            //     this.handleScroll();
            // },
            // destroyScroll(event) {
            //     console.log('hide modal');
            // },

            onShowModal() {
                if(this.isVbcAdmin) {
                    $('.modal-footer:visible .btn-primary').prop( "disabled", false ).removeClass('disabled');
                }
            },

            onChangeAgreementKodex()
            {
                if(this.agreementKodex) {
                    $('.modal-footer:visible .btn-primary').prop( "disabled", false ).removeClass('disabled');
                }
                else {
                    $('.modal-footer:visible .btn-primary').prop( "disabled", true ).addClass('disabled');
                }
            },

            onChangeAgreementVop()
            {
                if(this.agreementVop) {
                    $('.modal-footer:visible .btn-primary').removeAttr('disabled').removeClass('disabled');
                }
                else {
                    $('.modal-footer:visible .btn-primary').setAttr('disabled').addClass('disabled');
                }
            },

            onClickAccept() {
                if(this.isVbcAdmin) {
                    if(confirm('Naozaj chcete schváliť označené žiadosti?')) {
                        this.onClickAcceptDone();
                    }
                }
                else {
                    this.$bvModal.show('modal-eticky-kodex');
                }
            },
            onClickOkEtickyKodex() {
                this.$bvModal.show('modal-vop');
            },

            onClickAcceptDone() {
                let itemIds = [];
                let that = this;

                this.checkAll = false;
                this.acceptButtonEnabled = false;
                this.agreement = false;
                this.$refs.table_by_pharmacy.items.forEach(function(item) {
                    //console.log('foreach ' + item.id);
                    if(this.filterUsed === false || this.filteredItemIds.indexOf(item.id) != -1) {
                        //console.log('foreach filter ' + item.id);
                        if(item.check) {
                            //console.log('foreach filter checked ' + item.id);
                            itemIds.push(item.id);
                        }
                    }
                    item.check = false;
                }, this);

                let params = {
                    action: 'accept-ziadosti',
                    from: this.filterFrom,
                    to: this.filterTo,
                    acceptIds: itemIds
                };

                this.isBusy = true;
                axios.post( '{{ $url }}', {
                    page_id: {{ $page_id }},
                    params: params
                }).then(function(response) {
                    that.isBusy = false;
                    //console.log(response)
                    that.requests = response.data.ziadosti.ziadosti;
                    that.requests_by_pharmacy = response.data.ziadosti_by_pharmacy.ziadosti_by_pharmacy;
                    //that.key++;
                    alert('Počet úspešne schválených žiadostí: ' + (response.data.num) + '.');
                });
            },
            newDateObject(mysqlDateTime) {
                if(mysqlDateTime === null) {
                    return(null);
                }
                var dateTimeParts= mysqlDateTime.split(/[- :T.Z]/); // regular expression split that creates array with: year, month, day, hour, minutes, seconds values
                dateTimeParts[1]--; // monthIndex begins with 0 for January and ends with 11 for December so we need to decrement by one
                var dateObject = new Date(dateTimeParts[0], dateTimeParts[1], dateTimeParts[2], dateTimeParts[3]?dateTimeParts[3]:0, dateTimeParts[4]?dateTimeParts[4]:0, dateTimeParts[5]?dateTimeParts[5]:0);
                return(dateObject);
            },
            dateFormat(value) {
                let d = this.newDateObject(value);
                if(d === null) {
                    return('');
                }
                return(d.getDate() + '.' + (d.getMonth()+1) + '.' + d.getFullYear());
            },

            onChangeGroupByPharmacy()
            {
                this.updateData();
            },

            onChangeCheck(clickedItem)
            {
                this.acceptButtonEnabled = false;

                this.$refs.table_by_pharmacy.items.forEach(function(item) {
                    if (clickedItem.id === item.id) {
                        if(item.check) {
                            this.acceptButtonEnabled = true;
                        }
                    }
                    else {
                        if(item.check) {
                            this.acceptButtonEnabled = true;
                        }
                    }
                }, this);
            },

            onChangeCheckAll()
            {
                this.$refs.table_by_pharmacy.items.forEach(function(item) {
                    item.check = false;
                    if(this.filterUsed === false || this.filteredItemIds.indexOf(item.id) != -1) {
                        item.check = this.checkAll;
                        this.acceptButtonEnabled = this.checkAll;
                    }
                }, this);
            },

            onRowClicked(row)
            {
                var show = true;
                if(row._showDetails) {
                    show = false;
                }

                this.selectedRow = null;
                this.$refs.table.items.forEach(function(item) {
                    item._showDetails = false;
                });


                if(show) {
                    row._showDetails = true;
                    this.selectedRow = row;
                }
            },

            updateData()
            {
                let that = this;
                let params = {
                    action: 'update-ziadosti',
                    from: this.filterFrom,
                    to: this.filterTo,
                    groupByPharmacy: this.groupByPharmacy
                };

                this.isBusy = true;
                axios.post( '{{ $url }}', {
                    page_id: {{ $page_id }},
                    params: params
                }).then(function(response) {
                    that.isBusy = false;
                    //console.log(response)
                    that.requests = response.data.ziadosti.ziadosti;
                    that.requests_by_pharmacy = response.data.ziadosti_by_pharmacy.ziadosti_by_pharmacy;
                    //that.key++;
                });
            },

            filterData(row, filter) {

                if(this.filterBodyOd !== null && this.filterBodyOd !== '') {
                    if(Math.round(parseFloat(row.requested_value)*100) < Math.round(parseFloat(this.filterBodyOd)*100)) {
                        return(false);
                    }
                }

                if(this.filterBodyDo !== null && this.filterBodyDo !== '') {
                    console.log(this.filterBodyDo);
                    console.log(row);
                    if(Math.round(parseFloat(row.requested_value)*100) > Math.round(parseFloat(this.filterBodyDo)*100)) {
                        return(false);
                    }
                }

                if(this.filterFrom !== null && this.filterFrom !== '') {
                    if(row.last_update < this.filterFrom) {
                        return(false);
                    }
                }

                if(this.filterTo !== null && this.filterTo !== '') {
                    let d1 = this.newDateObject(row.last_update);
                    let d2 = this.newDateObject(this.filterTo);
                    if(d1.toISOString().substring(0,10) > (d2.toISOString().substring(0,10) + ' 23:59:59')) {
                        return(false);
                    }
                }

                if(this.filterText !== null && this.filterText.length) {
                    if (row.user && this.fulltextCompare(row.user, this.filterText)) {
                        return (true);
                    }
                    if (row.pharmacy && this.fulltextCompare(row.pharmacy, this.filterText)) {
                        return (true);
                    }
                    if (row.pzs && this.fulltextCompare(row.pzs, this.filterText)) {
                        return (true);
                    }

                    return(false);
                }
                return(true);
            },
            fulltextCompare(string, filter) {
                string = string.toLowerCase();
                filter = filter.toLowerCase();

                string = this.removeDiacritic(string);
                filter = this.removeDiacritic(filter);

                if(string.indexOf(filter) !== -1) {
                    return(true);
                }
                return(false);
            },

            removeDiacritic(text) {
                var dia =   "áäčďéíľĺňóôŕšťúýÁČĎÉÍĽĹŇÓŠŤÚÝŽ";
                var nodia = "aacdeillnoorstuyACDEILLNOSTUYZ";

                var convertText = "";
                for(i=0; i<text.length; i++) {
                    if(dia.indexOf(text.charAt(i))!=-1) {
                        convertText += nodia.charAt(dia.indexOf(text.charAt(i)));
                    }
                    else {
                        convertText += text.charAt(i);
                    }
                }
                return convertText;
            },

            onFilteredByPharmacy(filteredItems) {
                this.filteredItemIds = [];
                this.filterUsed = true;

                if(filteredItems.length) {
                    filteredItems.forEach(function(item) {
                        this.filteredItemIds.push(item.id);
                    }, this);
                }
            }


        },
        watch: {
            filterText: function(val, oldVal) {
                this.filter += '.';
            },
            filterFrom: function(val, oldVal) {
                this.filter += '.';
                this.updateData();
            },
            filterTo: function(val, oldVal) {
                this.filter += '.';
                this.updateData();
            },
            filterBodyOd: function(val, oldVal) {
                this.filter += '.';
            },
            filterBodyDo: function(val, oldVal) {
                this.filter += '.';
            },
        }
    };
</script>


