<template id="vue-template-moje-body-tab-moje-body">
    <div class="tab-moje-body">
        <b-row>
            <b-col lg="4">
                <div :class="{'state-box':true, 'big-font':true, admin:isVbcAdmin }">
                    <div class="state-box-content">
                        Aktuálny stav bodov:<br />
                        <span class="big">@{{ currentCount }}</span>
                        <div v-if="isVbcAdmin" class="admin-setup-count text-center">
                            <br />
                            Minimálny počet bodov na vyplatenie:
                            <input type="text" v-model="minCount" class="form-control text-right" />
                            <a href="#" @click.prevent.stop="onUpdateMinCount" :class="{btn:true, disabled:setupUpdateDisabled}">Nastaviť</a>
                        </div>
                    </div>
                </div>
            </b-col>
            <b-col lg="4">
                <div :class="{'state-box':true, 'big-font':true, admin:isVbcAdmin }">
                    <div class="state-box-content" title="Cutoff, do ktoreho sa spracuvavaju importovane body">
                        Najbližší termín spracovania žiadostí:<br />
                        <span class="big">@{{ nextDateProcessFormated }}</span>
                        <div v-if="isVbcAdmin" class="admin-setup-count text-center">
                            <b-form-datepicker id="nextDateProcessAdmin" v-model="nextDateProcessAdmin" placeholder="zadajte dátum" locale="sk"></b-form-datepicker>
                            <a href="#" @click.prevent.stop="onUpdateNextProcessDate" :class="{btn:true, disabled:setupUpdateDisabled}">Nastaviť</a>
                        </div>
                    </div>
                </div>
            </b-col>
            <b-col lg="4">
                <div :class="{'state-box':true, 'big-font':true, admin:isVbcAdmin }">
                    <div class="state-box-content" title="podla tohoto datumu sa spajaju .pdf podklady k vyplacaniu">
                        Najbližší termín vyplácania:<br />
                        <span class="big">@{{ nextDateFormated }}</span>
                        <div v-if="isVbcAdmin" class="admin-setup-count text-center">
                            <b-form-datepicker id="nextDateAdmin" v-model="nextDateAdmin" placeholder="zadajte dátum" locale="sk"></b-form-datepicker>
                            <a href="#" @click.prevent.stop="onUpdateNextDate" :class="{btn:true, disabled:setupUpdateDisabled}">Nastaviť</a>
                        </div>
                    </div>
                </div>
            </b-col>
            <b-col lg="12">
                <hr />
                &nbsp;
            </b-col>
            <b-col lg="12" class="text-left">
                {!! $page->getValue(\Buxus\Util\PropertyTag::TEXT_TAG()) !!}
            </b-col>
            <!-- b-col lg="4">
                <div :class="{'state-box':true, admin:isVbcAdmin }">
                    <div class="state-box-content">
                        <b-form-checkbox v-if="requestEnabled" v-model="agreement">Súhlasím s <a href="#" target="_blank">podmienkami vyplácania</a></b-form-checkbox>

                        <a href="#" :class="{btn:true, disabled:(!requestEnabled || !agreement || !requestButtonEnabled)}" v-if="requestEnabled" @click.prevent.stop="onClickRequest">Požiadať o vyplatenie</a>
                        <a href="#" class="btn no-margin" v-if="!requestEnabled" @click.prevent.stop>Pre vyplatenie potrebujete ešte<br /><strong>@{{ Math.ceil(minCount - currentCount) }} bodov</strong></a>
                    </div>
                </div>
            </b-col -->
        </b-row>

        <b-row>
            <b-col lg="12">
                <br />
            </b-col>
        </b-row>

        <b-row>
            <b-col>
                <!-- b-table
                    ref="table"
                    id="table-moje-body"
                    striped hover
                    :items="requests"
                    responsive
                    :fields="fields"


                    selectable
                    :select-mode="'single'"
                    @row-clicked="onRowClicked"
                >
                    <template v-slot:cell()="data">
                        @{{ data.value }}
                    </template>
                    <template v-slot:row-details="row">
                        <b-card>
                            <b-table :items="details" :fields="detailsFields">
                                <template v-slot:cell(stav)="data">
                                    <strong :class="{ 'text-success': data.value == 'Hotovo', 'text-warning': data.value == 'Čaká sa' }">@{{ data.value }}</strong>
                                </template>
                            </b-table>
                        </b-card>
                    </template>
                </b-table -->
            </b-col>
        </b-row>

    </div>
</template>

<script type="text/javascript">
    var vueComponentMojeBodyTabMojeBody = {
        template: '#vue-template-moje-body-tab-moje-body',
        mounted() {
            let that = this;
            this.$root.$on('activate-tab', function (newTabIndex, prevTabIndex, bvEvt) {
                if(newTabIndex === 0) {
                    console.log('!!!EVENT activate-tab ' + newTabIndex);
                    that.updateData();
                }
            })
        },
        data: function () {
            return {
                requests: @json($mojebody['requests']),
                cartState: @json($mojebody['cart_state']),
                isVbcAdmin: {{ $app_data['isVbcAdmin'] }},
                currentCount: {{ $mojebody['currentCount'] }},
                minCount: {{ $mojebody['minCount'] }},
                nextDate: '{{ $mojebody['nextDate'] }}',
                nextDateProcess: '{{ $mojebody['nextDateProcess'] }}',
                nextDateAdmin: '{{ $mojebody['nextDate'] }}',
                nextDateProcessAdmin: '{{ $mojebody['nextDateProcess'] }}',
                agreement:false,
                requestButtonEnabled:true,
                setupUpdateDisabled:false,
                selectedRow:null,
                fields: [
                    {
                        key: 'request_timestamp',
                        label: 'Dátum žiadosti',
                        class: 'text-left',
                        formatter: 'dateFormat',
                    },
                    {
                        key: 'requested_value',
                        label: 'Body',
                    },
                    {
                        key: 'current_state',
                        label: 'Stav',
                    },
                ],
            };
        },
        computed: {
            requestEnabled: function() {
                return(this.currentCount >= this.minCount);
            },
            nextDateFormated: function() {
                var d = this.newDateObject(this.nextDate);
                if(d === null) {
                    return('');
                }
                return(d.getDate() + '.' + (d.getMonth()+1) + '.' + d.getFullYear());
            },
            nextDateProcessFormated: function() {
                var d = this.newDateObject(this.nextDateProcess);
                if(d === null) {
                    return('');
                }
                return(d.getDate() + '.' + (d.getMonth()+1) + '.' + d.getFullYear());
            },
            detailsFields: function() { return [
                {
                    key:'pohyb',
                    label:'Pohyb',
                    class:'text-left'
                },
                {
                    key:'body',
                    label:'Body',
                },
                {
                    key:'datum',
                    label:'Dátum',
                },
                {
                    key:'stav',
                    label:'Stav',
                },
                {
                    key:'zodpovedny',
                    label:'Zodpovedná osoba',
                }
            ] },
            details: function() {
                if(this.selectedRow === null) {
                    return [];
                }
                return [
                    {
                        pohyb:"Žiadosť o vyplatenie bodov",
                        body:this.selectedRow.requested_value,
                        datum: this.dateFormat(this.selectedRow.request_timestamp),
                        stav:'Hotovo',
                        zodpovedny:this.selectedRow.user
                    },
                    {
                        pohyb: "Žiadosť odsúhlasená majiteľom",
                        body: this.selectedRow.requested_value,
                        datum: this.selectedRow.accept_pharmacy_owner_id ? this.dateFormat(this.selectedRow.accept_pharmacy_owner_timestamp):'',
                        stav: this.selectedRow.accept_pharmacy_owner_id? 'Hotovo':(this.selectedRow.accept_admin_id?'':'Čaká sa'),
                        zodpovedny: this.selectedRow.accept_pharmacy_owner_id ? this.selectedRow.owner :'Majiteľ lekárne'
                    },
                    {
                        pohyb:"Schválenie vyplatenia",
                        body: this.selectedRow.requested_value,
                        datum: this.selectedRow.accept_admin_id ? this.dateFormat(this.selectedRow.accept_admin_timestamp):'',
                        stav: this.selectedRow.accept_pharmacy_owner_id ? (this.selectedRow.accept_admin_id?'Hotovo':'Čaká sa'):(this.selectedRow.accept_admin_id?'Hotovo':''),
                        zodpovedny: this.selectedRow.accept_admin_id ? this.selectedRow.admin :'Vaša lekáreň'
                    }
                ];
            }
        },
        methods: {
            newDateObject(mysqlDateTime) {
                if(mysqlDateTime === null) {
                    return(null);
                }
                var dateTimeParts= mysqlDateTime.split(/[- :T.Z]/); // regular expression split that creates array with: year, month, day, hour, minutes, seconds values
                dateTimeParts[1]--; // monthIndex begins with 0 for January and ends with 11 for December so we need to decrement by one
                var dateObject = new Date(dateTimeParts[0], dateTimeParts[1], dateTimeParts[2], dateTimeParts[3]?dateTimeParts[3]:0, dateTimeParts[4]?dateTimeParts[4]:0, dateTimeParts[5]?dateTimeParts[5]:0);
                return(dateObject);
            },
            dateFormat(value) {
                let d = this.newDateObject(value);
                if(d === null) {
                    return('');
                }
                return(d.getDate() + '.' + (d.getMonth()+1) + '.' + d.getFullYear());
            },
            onRowClicked(row)
            {
                var show = true;
                if(row._showDetails) {
                    show = false;
                }

                this.selectedRow = null;
                this.$refs.table.items.forEach(function(item) {
                    item._showDetails = false;
                });


                if(show) {
                    row._showDetails = true;
                    this.selectedRow = row;
                }
            },
            updateData()
            {
                var params = {
                    action: 'update-moje-body',
                };
                var that = this;

                axios.post( '{{ $url }}', {
                    page_id: {{ $page_id }},
                    params: params
                }).then(function(response) {
                    if(response.data.result === true) {
                        cart_vbc_pocet_bodov = response.data.mojebody.cartState.cart_vbc_pocet_bodov;
                        vbc_points_total = response.data.mojebody.cartState.vbc_points_total;
                        vbc_points_available = response.data.mojebody.cartState.vbc_points_available;
                        vbc_used_in_cart = response.data.mojebody.cartState.vbc_used_in_cart;
                        $('.points-available').html(vbc_points_available);
                        that.currentCount = vbc_points_total;
                        that.cartState = response.data.mojebody.cartState;
                        that.requests = response.data.mojebody.requests;
                        that.nextDate = response.data.mojebody.nextDate;
                        that.nextDateAdmin = response.data.mojebody.nextDate;
                        that.nextDateProcess = response.data.mojebody.nextDateProcess;
                        that.nextDateProcessAdmin = response.data.mojebody.nextDateProcess;
                        that.minCount = parseInt(response.data.mojebody.minCount);
                    }
                });

            },

            onUpdateNextDate()
            {
                var params = {
                    action: 'update-next-date',
                    value: this.nextDateAdmin
                };
                var that = this;
                this.setupUpdateDisabled = true;
                axios.post( '{{ $url }}', {
                    page_id: {{ $page_id }},
                    params: params
                }).then(function(response) {
                    if(response.data.result === true) {
                        that.nextDate = response.data.mojebody.nextDate;
                        that.nextDateAdmin = response.data.mojebody.nextDate;
                        that.setupUpdateDisabled = false;
                        alert('Najbližší termín vyplácania bol úspešne nastavený.');
                    }
                });
            },

            onUpdateNextProcessDate()
            {
                var params = {
                    action: 'update-next-date-process',
                    value: this.nextDateProcessAdmin
                };
                var that = this;
                this.setupUpdateDisabled = true;
                axios.post( '{{ $url }}', {
                    page_id: {{ $page_id }},
                    params: params
                }).then(function(response) {
                    if(response.data.result === true) {
                        that.nextDateProcess = response.data.mojebody.nextDateProcess;
                        that.nextDateProcessAdmin = response.data.mojebody.nextDateProcess;
                        that.setupUpdateDisabled = false;
                        alert('Najbližší termín spracovania bol úspešne nastavený.');
                    }
                });
            },

            onUpdateMinCount()
            {
                var params = {
                    action: 'update-min-count',
                    value: this.minCount
                };
                var that = this;
                this.setupUpdateDisabled = true;
                axios.post( '{{ $url }}', {
                    page_id: {{ $page_id }},
                    params: params
                }).then(function(response) {
                    if(response.data.result === true) {
                        that.minCount = parseInt(response.data.mojebody.minCount);
                        that.setupUpdateDisabled = false;
                        alert('Minimálny počet bodov na vyplatenie bol úspešne nastavený.');
                    }
                });
            }

        },
        watch: {

        }
    };
</script>

