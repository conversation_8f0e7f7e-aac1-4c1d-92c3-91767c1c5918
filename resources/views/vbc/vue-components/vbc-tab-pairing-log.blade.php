<template id="vue-template-vbc-tab-pairing-log">
    <div>
        <b-row>
            <b-col lg="3">
                <b-input-group size="sm">
                    <b-form-input v-model="filterText" type="search" id="filterText" placeholder="Vyhľadať"></b-form-input>
                    <b-input-group-append>
                        <b-button :disabled="!filterText" @click="filterText = ''">X</b-button>
                    </b-input-group-append>
                </b-input-group>
            </b-col>

            <b-col lg="3">

                    <b-form-tags v-model="filterAction" size="lg" add-on-change no-outer-focus>
                        <template v-slot="{ tags, inputAttrs, inputHandlers, disabled, removeTag }">
                            <ul v-if="tags.length > 0" class="list-inline d-inline-block mb-2">
                                <li v-for="tag in tags" :key="tag" class="list-inline-item">
                                    <b-form-tag
                                        @remove="removeTag(tag)"
                                        :title="tag"
                                        :disabled="disabled"
                                        variant="info"
                                    >@{{ tag }}</b-form-tag>
                                </li>
                            </ul>
                            <b-form-select
                                v-bind="inputAttrs"
                                v-on="inputHandlers"
                                :disabled="disabled || filterActionsArray.length === 0"
                                :options="filterActionsArray"
                            >
                                <template v-slot:first>
                                    <!-- This is required to prevent bugs with Safari -->
                                    <option disabled value="">Vyberte akcie...</option>
                                </template>
                            </b-form-select>
                        </template>
                    </b-form-tags>
            </b-col>


            <b-col lg="3">

                <b-form-tags v-model="filterRole" size="lg" add-on-change no-outer-focus>
                    <template v-slot="{ tags, inputAttrs, inputHandlers, disabled, removeTag }">
                        <ul v-if="tags.length > 0" class="list-inline d-inline-block mb-2">
                            <li v-for="tag in tags" :key="tag" class="list-inline-item">
                                <b-form-tag
                                    @remove="removeTag(tag)"
                                    :title="tag"
                                    :disabled="disabled"
                                    variant="info"
                                >@{{ tag }}</b-form-tag>
                            </li>
                        </ul>
                        <b-form-select
                            v-bind="inputAttrs"
                            v-on="inputHandlers"
                            :disabled="disabled || filterRolesArray.length === 0"
                            :options="filterRolesArray"
                        >
                            <template v-slot:first>
                                <!-- This is required to prevent bugs with Safari -->
                                <option disabled value="">Vyberte roly...</option>
                            </template>
                        </b-form-select>
                    </template>
                </b-form-tags>
            </b-col>

            <b-col lg="3">
                <b-form-datepicker id="filterFrom" v-model="filterFrom" placeholder="od" locale="sk" reset-button></b-form-datepicker>
                <b-form-datepicker id="filterTo" v-model="filterTo" placeholder="do" locale="sk" reset-button></b-form-datepicker>
            </b-col>



        </b-row>

        <br />
        <b-table striped hover :items="log" :fields="fields" :filter="filter" :filter-function="filterData" @filtered="onFiltered" id="pairing-table-log" responsive :busy="isBusy">
            <template v-slot:cell(action)="data">
                <div :class="[data.item.action]">@{{ data.value }}</div>
            </template>

            <template v-slot:cell(user)="data">
                <span v-if="data.item.user_id">@{{ data.value }}</span>
                <span v-if="!data.item.user_id">Lekáreň @{{ data.item.pzs }}</span>
            </template>
        </b-table>
    </div>
</template>

<script type="text/javascript">
    var vueComponentVbcTabPairingLog = {
        template: '#vue-template-vbc-tab-pairing-log',
        mounted() {
            let that = this;
            this.$root.$on('activate-tab', function (newTabIndex, prevTabIndex, bvEvt) {
                if(newTabIndex === 2 + (this.hasRightsToRozdelovanie?1:0)) {
                    console.log('!!!EVENT activate-tab PAIRING ' + newTabIndex);
                    that.updateData();
                }
            })
        },
        data: function () {
            return {
                log: @json($pairingLog),
                fields: [
                    {
                        key: 'action_user',
                        label: 'Aktér',
                        class: 'td-akter'
                    },
                    {
                        key: 'role',
                        label: 'Rola',
                        class: 'td-role'
                    },
                    {
                        key: 'user',
                        label: 'Prihlasovacie meno',
                        class: 'td-user'
                    },
                    {
                        key: 'lis_id',
                        label: 'LIS ID',
                        class: 'td-lis-id'
                    },
                    {
                        key: 'action',
                        label: 'Akcia',
                        class: 'td-action',
                        formatter: 'actionFormat',
                        filterByFormatted: true
                    },
                    {
                        key: 'created_at',
                        label: 'Dátum',
                        class: 'td-date',
                        formatter: 'dateFormat',
                        filterByFormatted: true
                    },
                ],
                actions: {
                    add: 'Priradený',
                    delete: 'Rozpojený',
                    accept: 'Schválený',
                    cancel: 'Vrátený',
                    activate: 'Potvrdený'
                },
                roles: @json($roles),
                filter:'',
                filterText: null,
                filterRole: [],
                filterAction: [],
                filterFrom: '{{ $from }}',
                filterTo: '{{ $to }}',

                isBusy: false
            };
        },
        computed: {
            filterActionsArray() {
                return Object.values(this.actions).filter(function (opt) { return (this.filterAction.indexOf(opt) === -1) }, this);
            },
            filterRolesArray() {
                return this.roles.filter(function (opt) { return (this.filterRole.indexOf(opt) === -1) }, this);
            }
        },
        methods: {
            newDateObject(mysqlDateTime) {
                if(mysqlDateTime === null) {
                    return(null);
                }
                var dateTimeParts= mysqlDateTime.split(/[- :T.Z]/); // regular expression split that creates array with: year, month, day, hour, minutes, seconds values
                dateTimeParts[1]--; // monthIndex begins with 0 for January and ends with 11 for December so we need to decrement by one
                var dateObject = new Date(dateTimeParts[0], dateTimeParts[1], dateTimeParts[2], dateTimeParts[3]?dateTimeParts[3]:0, dateTimeParts[4]?dateTimeParts[4]:0, dateTimeParts[5]?dateTimeParts[5]:0);
                return(dateObject);
            },
            dateFormat(value) {
                let d = this.newDateObject(value);
                if(d === null) {
                    return('');
                }
                return(d.getDate() + '.' + (d.getMonth()+1) + '.' + d.getFullYear() + ' ' + d.getHours() + ':' + d.getMinutes());
            },
            actionFormat(value)
            {
                if(this.actions[value]) {
                    return(this.actions[value]);
                }
                return(value);
            },

            onFiltered(filteredItems) {

            },
            filterData(row, filter) {
                if(this.filterFrom !== null && this.filterFrom !== '') {
                    if(row.created_at < this.filterFrom) {
                        return(false);
                    }
                }

                if(this.filterTo !== null && this.filterTo !== '') {
                    let d1 = this.newDateObject(row.created_at);
                    let d2 = this.newDateObject(this.filterTo);
                    if(d1.toISOString().substring(0,10) > (d2.toISOString().substring(0,10) + ' 23:59:59')) {
                        return(false);
                    }
                }


                if(this.filterRole.length) {
                    let role = false;
                    if(this.filterRole.indexOf(row.role) !== -1) {
                        role = true;
                    }
                    if(!role) {
                        return(false);
                    }
                }

                if(this.filterAction.length) {
                    let action = false;
                    if(this.filterAction.indexOf(this.actions[row.action]) !== -1) {
                        action = true;
                    }
                    if(!action) {
                        return(false);
                    }
                }

                if(this.filterText !== null && this.filterText.length) {
                    if (row.action_user && this.fulltextCompare(row.action_user, this.filterText)) {
                        return (true);
                    }
                    if (row.user && this.fulltextCompare(row.user, this.filterText)) {
                        return (true);
                    }
                    if (row.lis_id  && this.fulltextCompare(row.lis_id, this.filterText)) {
                        return (true);
                    }
                    return(false);
                }
                return(true);
            },
            fulltextCompare(string, filter) {
                string = string.toLowerCase();
                filter = filter.toLowerCase();

                string = this.removeDiacritic(string);
                filter = this.removeDiacritic(filter);

                if(string.indexOf(filter) !== -1) {
                    return(true);
                }
                return(false);
            },

            removeDiacritic(text) {
                var dia =   "áäčďéíľĺňóôŕšťúýÁČĎÉÍĽĹŇÓŠŤÚÝŽ";
                var nodia = "aacdeillnoorstuyACDEILLNOSTUYZ";

                var convertText = "";
                for(i=0; i<text.length; i++) {
                    if(dia.indexOf(text.charAt(i))!=-1) {
                        convertText += nodia.charAt(dia.indexOf(text.charAt(i)));
                    }
                    else {
                        convertText += text.charAt(i);
                    }
                }
                return convertText;
            },


            updateData()
            {
                let that = this;
                let params = {
                    action: 'reload-pairing-log',
                    from: this.filterFrom,
                    to: this.filterTo
                };

                this.isBusy = true;
                axios.post( '{{ $url }}', {
                    page_id: {{ $page_id }},
                    params: params
                }).then(function(response) {
                    that.isBusy = false;
                    //console.log(response)
                    that.log = response.data.pairingLog;
                    //that.key++;
                    //that.$root.$emit('bv::refresh::table', 'pairing-table');
                });
            }

        },
        watch: {
            filterText: function(val, oldVal) {
                this.filter += '.';
            },
            filterRole: function(val, oldVal) {
                this.filter += '.';
            },
            filterAction: function(val, oldVal) {
                this.filter += '.';
            },
            filterFrom: function(val, oldVal) {
                this.filter += '.';
                this.updateData();
            },
            filterTo: function(val, oldVal) {
                this.filter += '.';
                this.updateData();
            },
        }
    };
</script>
