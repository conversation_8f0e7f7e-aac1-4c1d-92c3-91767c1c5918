<template id="vue-template-vbc-tab-rozdelenie-bodov">
    <div class="tab-rozdelenie-bodov">
        <div class="row">
            <div class="col-md-3"></div>
            <div class="col-md-6">
                <b-form-select v-if="config.free.length" v-model="sutazeSelected" :options="sutazeOptions"></b-form-select>
                <div v-if="!config.free.length" class="alert alert-warning">
                    Nie sú žiadne body k rozdeleniu.
                </div>
            </div>
            <div class="col-md-3"></div>
        </div>

        <div class="row"></div>

        <div class="row" v-if="sutazeSelected !== null">
            <div class="col-md-2"></div>
            <div class="col-md-4">
                Body na rozdelenie: <b-form-input v-model="bodyNaRozdelenie" readonly class="inputBody"></b-form-input>
            </div>
            <div class="col-md-4">
                Rozdelené body: <b-form-input v-model="bodyRozdelene" readonly class="inputBody"></b-form-input>
            </div>
            <div class="col-md-2"></div>
        </div>
        <div class="row" v-if="sutazeSelected !== null">
            <div class="col-md-12">
                <b-button variant="danger" @click="onCancel">Zrušiť</b-button>
                <b-button variant="success" @click="onSave" :disabled="submitDisabled">Prideliť</b-button>
            </div>
        </div>

        <div class="row" v-if="sutazeSelected !== null">
            <div class="col-md-12">
                <b-table id="table-rozdelenie-bodov" striped hover :items="items" :fields="fields">
                    <template v-slot:cell(add)="data">
                        <b-form-input class="add-input text-right" v-model="forUsers['s' + sutazeSelected]['u' + data.item.user_id]"
                                      @focus="$event.target.setSelectionRange(0,$event.target.value.length)"
                                      @change="onChangeAdd(sutazeSelected, data.item.user_id)"
                        ></b-form-input>
                    </template>
                </b-table>
            </div>
        </div>
    </div>
</template>

<script type="text/javascript">
    var vueComponentVbcTabRozdelenieBodov = {
        template: '#vue-template-vbc-tab-rozdelenie-bodov',
        mounted() {
            let that = this;
            this.$root.$on('activate-tab', function (newTabIndex, prevTabIndex, bvEvt) {
                if(newTabIndex === 1) {
                    console.log('!!!EVENT activate-tab ' + newTabIndex);
                    that.updateData();
                }
            })

            this.config.free.forEach(function (item, index) {

                this.$set(this.forUsers, 's' + index, {});

                this.config.employees.forEach(function(row) {
                    this.$set(this.forUsers['s' + index], 'u' + row.user_id, 0);
                }, this);
            }, this);

            if(this.config.free.length == 1) {
                this.sutazeSelected = 0;
            }
        },
        data: function () {
            return {
                config: @json($rozdelovanie),
                fields: [
                    {
                        key: 'name',
                        label: 'Meno a priezvisko',
                        formatter: 'nameFormat',
                        class: 'text-left'
                    },
                    {
                        key: 'total',
                        label: 'Aktuálny stav bodov'
                    },
                    {
                        key: 'add',
                        label: 'Prideliť body'
                    },
                    {
                        key: 'result',
                        label: 'Výsledný stav bodov'
                    }
                ],

                sutazeSelected: null,
                forUsers: {},
                key:0,
                submitDisabled: false

            };
        },
        computed: {
            sutazeOptions: function() {
                var ret = [ { value: null, text: '-- Zvoľte súťaž, ktorej body chcete rozdeliť --' }];
                this.config.free.forEach(function (item, index) {
                    ret.push({ value: index, text: item.nazov + ' (' + item.pocet_bodov + ' bodov)' });
                });
                return ret;
            },
            items: function() {
                var rows = [];

                this.config.employees.forEach(function(row) {
                    var total = row.total?parseFloat(row.total):0;
                    var add = parseFloat(this.forUsers['s' + this.sutazeSelected]['u' + row.user_id]);

                    if(!add) {
                        add = 0;
                    }
                    rows.push({
                        'user_id': row.user_id,
                        'username': row.username,
                        'total': total,
                        'add': 0,
                        'result': add + total
                    });
                }, this);

                return(rows);
            },
            bodyNaRozdelenie: function() {
                if(this.sutazeSelected === null) {
                    return(0);
                }
                var pocet = parseFloat(this.config.free[this.sutazeSelected].pocet_bodov);
                pocet = pocet - this.bodyRozdelene;

                return(pocet);
            },

            bodyRozdelene: function() {
                if(this.sutazeSelected === null) {
                    return(0);
                }
                var rozdelene = 0;
                for(var propertyName in this.forUsers['s' + this.sutazeSelected]) {
                    var add = parseFloat(this.forUsers['s' + this.sutazeSelected][propertyName]);
                    if(!add) {
                        add = 0;
                    }
                    rozdelene += add;
                };

                return(rozdelene);
            }
        },
        methods: {
            nameFormat(value, key, item)
            {
                var user = this.config.employees.find(function(i) {
                    return (i.user_id == item.user_id);
                });

                if(user) {
                    return(user.first_name + ' ' + user.surname);
                }
                return(item.username);
            },
            updateData()
            {
                var params = {
                    action: 'rozdelenie-reload',
                };
                var that = this;
                axios.post( '{{ $url }}', {
                    page_id: {{ $page_id }},
                    params: params
                }).then(function(response) {
                    if(response.data.result === true) {
                        that.config = response.data.rozdelovanie;
                        if(that.sutazeSelected !== null && !that.config.free[that.sutazeSelected]) {
                            that.sutazeSelected = null;
                        }
                    }
                });
            },
            onChangeAdd(sutazeSelected, user_id)
            {
                var value = this.forUsers['s' + sutazeSelected]['u' + user_id];

                value = parseFloat(value);
                if(!value) {
                    value = 0;
                }
                if(this.bodyNaRozdelenie < 0) {
                    value = value + this.bodyNaRozdelenie;
                }
                this.forUsers['s' + sutazeSelected]['u' + user_id] = value;
            },
            onCancel()
            {
                for(var propertyName in this.forUsers['s' + this.sutazeSelected]) {
                    this.forUsers['s' + this.sutazeSelected][propertyName] = 0;
                }
            },
            onSave()
            {
                if(!confirm('Rozdelenie bodov nie je možné vziať späť. Naozaj chcete uložiť body?')) {
                    return;
                }

                this.submitDisabled = true;
                var that = this;
                var params = {
                    action: 'rozdelenie',
                    sutaz: this.config.free[this.sutazeSelected],
                    forUsers: this.forUsers['s' + this.sutazeSelected]
                };
                console.log('save');
                axios.post( '{{ $url }}', {
                    page_id: {{ $page_id }},
                    params: params
                }).then(function(response) {
                    //console.log(response)
                    that.submitDisabled = false;
                    if(response.data.result === true) {
                        if(!that.bodyNaRozdelenie) {
                            that.sutazeSelected = null;
                        }

                        that.config = response.data.rozdelovanie;

                        if(that.sutazeSelected !== null) {
                            that.config.employees.forEach(function (row) {
                                this.forUsers['s' + this.sutazeSelected]['u' + row.user_id] = 0;
                            }, that);
                        }


                    }
                });
            }

        },
        watch: {

        }
    };
</script>

