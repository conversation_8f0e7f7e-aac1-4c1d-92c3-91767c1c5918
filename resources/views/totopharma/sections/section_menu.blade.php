<div class="section-menu">
    <div class="container">
        <nav class="navbar navbar-expand-lg navbar-light" id="section-<?= $section->page->getPageId() ?>">
            <a class="navbar-brand" href="#section-<?= $section->page->getPageId() ?>"><img src="<?= asset('/buxus/assets/totopharma/images/toto.svg') ?>" alt="TOTO Pharma" /></a>
            <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ml-auto main-menu">
                    @foreach($section->page->getValue(\Buxus\Util\PropertyTag::MENU_TAG()) as $item)
                        <li class="nav-item">
                            <a class="nav-link" href="<?
                                if($item->getToPageId() && isset($anchors[$item->getToPageId()])) {
                                    echo '#'.$anchors[$item->getToPageId()];
                                }
                                else {
                                    if(intval($item->getToPageId())) {
                                        echo \Buxus::pageUrl($item->getToPageId());
                                    }
                                    else {
                                        if(strlen($item->getUrl())) {
                                            echo $item->getUrl();
                                        }
                                        else {
                                            echo '#';
                                        }
                                    }
                                }
                            ?>"><?= $item->getText() ?></a>
                        </li>
                    @endforeach
                </ul>

                <ul class="navbar-nav ml-auto social">
                    @if(strlen($section->page->getValue(\Buxus\Util\PropertyTag::FACEBOOK_TAG())))
                        <li class="nav-item facebook">
                            <a class="nav-link hvr-grow" href="<?= $section->page->getValue(\Buxus\Util\PropertyTag::FACEBOOK_TAG()) ?>">
                                <img src="/buxus/assets/totopharma/images/facebook.svg" alt="facebook" />
                            </a>
                        </li>
                    @endif
                    @if(strlen($section->page->getValue(\Buxus\Util\PropertyTag::INSTAGRAM_TAG())))
                        <li class="nav-item instagram">
                            <a class="nav-link hvr-grow" href="<?= $section->page->getValue(\Buxus\Util\PropertyTag::INSTAGRAM_TAG()) ?>">
                                <img src="/buxus/assets/totopharma/images/instagram.svg" alt="instagram" />
                            </a>
                        </li>
                    @endif
                </ul>
            </div>
        </nav>
    </div>
    <script>
        document.addEventListener("DOMContentLoaded", () => {
            function countAlpha(empty, full, empty_alpha = 0) {
                if(window.innerWidth < 992) {
                    return(1);
                }

                var alpha = empty_alpha;

                if($(window).scrollTop() <= empty) {
                    return(empty_alpha);
                }
                if($(window).scrollTop() >= full) {
                    return(empty_alpha?0:1);
                }

                if($(window).scrollTop() > empty && $(window).scrollTop() < full) {
                    if(empty_alpha) {
                        alpha = empty_alpha - ($(window).scrollTop() - empty) / (full - empty);
                    }
                    else {
                        alpha = ($(window).scrollTop() - empty) / (full - empty);
                    }
                }
                return(alpha);
            }

            $(window).scroll(function(){
                $('.section-menu').css("background-color", 'rgba(255,255,255,' + countAlpha(200, 300, 0) + ')');
                $('.logo').css("opacity", countAlpha(100, 200, 1));
                $('.navbar-brand').css("opacity", countAlpha(200, 300, 0));
            })

            $(document).on('click', 'a', function(e) {
                if($(this).attr('href').substr(0, 1) == '#') {
                    var target = $(this).attr('href').substr(1);

                    if(target.length > 0) {
                        var aTag = $("a[name='"+ target +"']");
                        if(aTag.length) {
                            scrollToAnchor(target);
                            e.preventDefault();
                            e.stopPropagation();

                            $('.navbar-toggler').addClass('collapsed');
                            $('.navbar-collapse.collapse.show').removeClass('show');
                        }
                    }
                }
            });
        });

        function scrollToAnchor(aid){
            var aTag = $("a[name='"+ aid +"']");
            $('html,body').animate({scrollTop: aTag.offset().top},'slow');
        }



    </script>
</div>
