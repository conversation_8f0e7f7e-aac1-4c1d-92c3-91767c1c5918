<div class="row">
    <div class="col">
        <div class="row section section-carousel">
            <div class="col-md-12 background">
                <div id="carousel-<?= $section->page->getPageId() ?>" class="carousel slide" data-ride="carousel">
                    <ol class="carousel-indicators">
                        <? $k=0 ?>
                        @foreach($section->items as $item)
                            <li data-target="#carousel-<?= $section->page->getPageId() ?>" data-slide-to="<?= $k ?>" class="<?= $k==0 ?'active':'' ?>"></li>
                            <? $k++; ?>
                        @endforeach
                    </ol>
                    <div class="carousel-inner">
                        <? $k=0 ?>
                        @foreach($section->items as $item)
                            <div class="carousel-item <?= $k==0 ?'active':'' ?>">
                                <div class="row">
                                    <div class="col-md-3 col-left">
                                        <img src="{{ \Buxus::imageUrl($item->getValue(\Buxus\Util\PropertyTag::ESHOP_PRODUCT_IMAGE_TAG())) }}" alt="{{ $item->getValue(\Buxus\Util\PropertyTag::TITLE_2_TAG()) }}" />
                                    </div>
                                    <div class="col-md-6" col-middle>
                                        <div class="item-content">
                                            <div>
                                                <h2>{{ $item->getValue(\Buxus\Util\PropertyTag::TITLE_TAG()) }} <span>{{ $item->getValue(\Buxus\Util\PropertyTag::TITLE_2_TAG()) }}</span></h2>
                                                @if(strlen(strip_tags($item->getValue(\Buxus\Util\PropertyTag::TEXT_TAG()))))
                                                    {!! $item->evalValue(\Buxus\Util\PropertyTag::TEXT_TAG()) !!}
                                                @endif
                                            </div>

                                            <div class="buttons">
                                                <a href="{{ $item->getValue(\Buxus\Util\PropertyTag::BUTTON_LINK_TAG()) }}" class="btn hvr-grow"><span>{{ $item->getValue(\Buxus\Util\PropertyTag::BUTTON_TEXT_TAG()) }}</span></a>
                                                <a href="{{ $item->getValue(\Buxus\Util\PropertyTag::BUTTON2_LINK_TAG()) }}" class="btn btn-outline-primary hvr-grow"><span>{{ $item->getValue(\Buxus\Util\PropertyTag::BUTTON2_TEXT_TAG()) }}</span></a>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-right">
                                        <img src="{{ \Buxus::imageUrl($item->getValue(\Buxus\Util\PropertyTag::IMAGE_TAG())) }}" alt="{{ $item->getValue(\Buxus\Util\PropertyTag::TITLE_TAG()) }}" />
                                    </div>
                                </div>
                            </div>
                            <? $k++; ?>
                        @endforeach

                    </div>
                    <a class="carousel-control-prev" href="#carousel-<?= $section->page->getPageId() ?>" role="button" data-slide="prev">
                        <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                        <span class="sr-only">Previous</span>
                    </a>
                    <a class="carousel-control-next" href="#carousel-<?= $section->page->getPageId() ?>" role="button" data-slide="next">
                        <span class="carousel-control-next-icon" aria-hidden="true"></span>
                        <span class="sr-only">Next</span>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
