
<div class="row">
    <div class="col">
        <div class="row section section-contact">
            <div class="col-md-12">
                <div class="row gutter background">
                    <div class="col-md-3">
                        @if(strlen($section->page->getValue(\Buxus\Util\PropertyTag::IMAGE_TAG())))
                            <div class="section-image">
                                <img src="{{ \Buxus::imageUrl($section->page->getValue(\Buxus\Util\PropertyTag::IMAGE_TAG())) }}" alt="contact" />
                            </div>
                        @endif

                        @if(strlen($section->page->getValue(\Buxus\Util\PropertyTag::TITLE_2_TAG())))
                            <h3 class="h2 colored">{{ $section->page->getValue(\Buxus\Util\PropertyTag::TITLE_2_TAG()) }}</h3>
                            <img class="wave" src="/buxus/assets/totopharma/images/contact-wave.png" />
                        @endif
                    </div>

                    <div class="col-md-9">
                        <div class="row">
                            <div class="col-md-12">
                                <h2>{{ $section->page->getValue(\Buxus\Util\PropertyTag::TITLE_TAG()) }}</h2>
                                @if(strlen(strip_tags($section->page->getValue(\Buxus\Util\PropertyTag::TEXT_TAG()))))
                                    {!! $section->page->evalValue(\Buxus\Util\PropertyTag::TEXT_TAG()) !!}
                                @endif
                            </div>
                        </div>
                        <div class="row contact-flex-end">
                            <div class="col-lg-4 col-sm-6">
                                @if(strlen(strip_tags($section->page->getValue(\Buxus\Util\PropertyTag::TEXT_BLOCK_1_TAG()))))
                                    {!! $section->page->evalValue(\Buxus\Util\PropertyTag::TEXT_BLOCK_1_TAG()) !!}
                                @endif
                            </div>
                            <div class="col-lg-4 col-sm-6">
                                @if(strlen(strip_tags($section->page->getValue(\Buxus\Util\PropertyTag::TEXT_BLOCK_2_TAG()))))
                                    {!! $section->page->evalValue(\Buxus\Util\PropertyTag::TEXT_BLOCK_2_TAG()) !!}
                                @endif
                            </div>
                            <div class="col-lg-4  col-sm-12 buttons">
                                <a class="btn btn-outline-primary  hvr-grow colored" href="{!! $section->page->getValue(\Buxus\Util\PropertyTag::BUTTON_LINK_TAG()) !!}">{{ $section->page->getValue(\Buxus\Util\PropertyTag::BUTTON_TEXT_TAG()) }}</a>

                                @if(strlen(strip_tags($section->page->getValue(\Buxus\Util\PropertyTag::BUTTON_COMMENT_TAG()))))
                                    <div class="button-comment">
                                        {!! $section->page->evalValue(\Buxus\Util\PropertyTag::BUTTON_COMMENT_TAG()) !!}
                                    </div>
                                @endif
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>

    </div>
</div>


