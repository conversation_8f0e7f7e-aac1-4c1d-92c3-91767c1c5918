
<div class="row" id="section-{{ $section->page->getPageId() }}">
    <div class="col">
        <div class="row section section-fields section-fields-{{ $section->page->getValue(\Buxus\Util\PropertyTag::LAYOUT_TYPE_OF_FIELDS_TAG) }} d-block">
            <div class="col-md-4 float-left">
                <div class="row">
                    <div class="col-md-9">
                        @if(strlen($section->page->getValue(\Buxus\Util\PropertyTag::IMAGE_TAG())))
                            <div class="section-image">
                                <img src="{{ \Buxus::imageUrl($section->page->getValue(\Buxus\Util\PropertyTag::IMAGE_TAG())) }}" alt="{{ $section->page->getValue(\Buxus\Util\PropertyTag::TITLE_TAG()) }}" />
                            </div>
                        @endif

                        <h2>{{ $section->page->getValue(\Buxus\Util\PropertyTag::TITLE_TAG()) }}</h2>
                        @if(strlen(strip_tags($section->page->getValue(\Buxus\Util\PropertyTag::TEXT_TAG()))))
                            {!! $section->page->evalValue(\Buxus\Util\PropertyTag::TEXT_TAG()) !!}
                        @endif
                    </div>
                </div>
            </div>
            @foreach($section->items as $item)
                @if(strlen($item->getValue(\Buxus\Util\PropertyTag::URL_TAG())))
                    <a href="{{ $item->getValue(\Buxus\Util\PropertyTag::URL_TAG()) }}" class="col-sm-4 float-left item">
                @else
                    <div class="col-sm-4 float-left item">
                @endif
                        <div class="item-image">
                            <img src="{{ \Buxus::imageUrl($item->getValue(\Buxus\Util\PropertyTag::IMAGE_TAG())) }}" alt="{{ $item->getValue(\Buxus\Util\PropertyTag::TITLE_TAG()) }}" />
                        </div>
                        <h3>{{ $item->getValue(\Buxus\Util\PropertyTag::TITLE_TAG()) }}</h3>
                        @if(strlen(strip_tags($item->getValue(\Buxus\Util\PropertyTag::TEXT_TAG()))))
                            {!! $item->evalValue(\Buxus\Util\PropertyTag::TEXT_TAG()) !!}
                        @endif
                @if(strlen($item->getValue(\Buxus\Util\PropertyTag::URL_TAG())))
                    </a>
                @else
                    </div>
                @endif
            @endforeach
        </div>

    </div>
    <script type="text/javascript">
        document.addEventListener("DOMContentLoaded", () => {
            setInterval(resizeFields{{ $section->page->getPageId() }}, 1000);
        });
        document.addEventListener("DOMContentLoaded", () => {
            resizeFields{{ $section->page->getPageId() }}();
        });
        function resizeFields{{ $section->page->getPageId() }}()
        {
            var items = $('#section-{{ $section->page->getPageId() }} .item');
            var max_height = 0;

            items.each(function() {
                $(this).css('height', 'auto');
            });

            items.each(function() {
                max_height = Math.max(max_height, $(this).height());
            });

            if(max_height > 0) {
                items.each(function() {
                    $(this).height(max_height);
                });
            }
        }
    </script>
</div>


