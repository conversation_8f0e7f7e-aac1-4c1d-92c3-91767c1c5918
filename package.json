{"name": "buxus", "version": "1.0.0", "description": "Node.js modules for BUXUS", "devDependencies": {"@babel/cli": "^7.8.4", "@babel/core": "^7.9.0", "@babel/polyfill": "^7.8.7", "@babel/preset-env": "^7.9.5", "bower": "^1.8.4", "gulp": "^4.0.0", "gulp-clean": "^0.3.1", "gulp-concat": "^2.4.3", "gulp-concat-css": "^2.2.0", "gulp-cssimport": "^1.3.1", "gulp-data": "^1.2.0", "gulp-import-css": "^0.1.2", "gulp-jshint": "^1.9.0", "gulp-less": "^3.0.0", "gulp-minify-css": "^0.3.12", "gulp-sourcemaps": "^1.3.0", "gulp-strip-debug": "^1.0.2", "gulp-uglify-es": "^1.0.4", "gulp-util": "^3.0.2", "natives": "^1.1.6", "requirejs": "^2.1.15", "rimraf": "^2.2.8", "run-sequence": "^1.1.2", "sync-exec": "^0.6.1", "through2": "^3.0.1"}}