version: '3.7'

secrets:
  host_ssh_key_rsa:
    file: ~/.ssh/id_rsa
  host_ssh_key_dsa:
    file: ~/.ssh/id_dsa

services:
  webserver:
    build:
      context: ./docker/dockerfiles/php/
      dockerfile: ${PHPVERSION}.Dockerfile
    container_name: '${PROJECT_NAME}-${PHPVERSION}'
    restart: 'always'
    ports:
      - "${HOST_HTTP_PORT:-80}:${HTTP_PORT:-80}"
      - "${HOST_HTTPS_PORT:-443}:${HTTPS_PORT:-443}"
    links:
      - database
      - memcached
    volumes:
      - ./:/var/www/
      - ./docker/${PHPVERSION}.ini:/usr/local/etc/php/php.ini
      - ./docker/vhosts:/etc/apache2/sites-enabled
      - ./docker/logs/apache2:/var/log/apache2

    environment:
      PMA_PORT: ${HOST_PHPMYADMIN_PORT:-8080}
    secrets:
      - host_ssh_key_rsa
      - host_ssh_key_dsa

  database:
    build:
      context: ./docker/dockerfiles/db/
      dockerfile: ${DATABASE}.Dockerfile
    container_name: '${PROJECT_NAME}-database'
    restart: 'always'
    ports:
      - "127.0.0.1:${HOST_DB_PORT:-3306}:${DB_PORT:-3306}"
    volumes:
      - ./docker/data/${DATABASE}:/var/lib/mysql
      - ./docker/logs/${DATABASE}:/var/log/mysql
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASSWORD:-rootpwd}
      MYSQL_DATABASE: ${DB_DATABASE}
      MYSQL_USER: ${DB_USERNAME}
      MYSQL_PASSWORD: ${DB_PASSWORD}
  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    container_name: '${PROJECT_NAME}-phpmyadmin'
    links:
      - database
    environment:
      PMA_HOST: database
      PMA_PORT: 3306
      PMA_USER: root
      PMA_PASSWORD: ${DB_ROOT_PASSWORD:-rootpwd}
      MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASSWORD:-rootpwd}
      MYSQL_USER: ${DB_USERNAME}
      MYSQL_PASSWORD: ${DB_PASSWORD}
    ports:
      - '${HOST_PHPMYADMIN_PORT:-8080}:${PHPMYADMIN_PORT:-80}'
    volumes:
      - /sessions
      - ./docker/${PHPVERSION}.ini:/usr/local/etc/php/conf.d/php-phpmyadmin.ini
  redis:
    container_name: '${PROJECT_NAME}-redis'
    image: redis:latest
    ports:
      - "127.0.0.1:${HOST_PORT_REDIS:-6379}:${REDIS_PORT:-6379}"
  memcached:
    container_name: '${PROJECT_NAME}-memcached'
    image: memcached:latest
    restart: 'always'
    ports:
      - "11211:11211"
