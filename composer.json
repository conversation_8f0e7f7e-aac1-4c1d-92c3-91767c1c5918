{"name": "buxus/buxus", "description": "BUXUS 7 CMS", "keywords": ["framework", "BUXUS", "CMS"], "license": "proprietary", "type": "project", "require": {"php": "^7.4.0", "buxus-libs/devel": "^1.1", "buxus/core": "^7.2.0", "buxus/eshop-legacy": "^1.2", "dompdf/dompdf": "^0.8.4", "fideloper/proxy": "^4.0", "krabica/krabica-core": "^6.0.2", "laravel/framework": "^6.0", "laravel/tinker": "^1.0", "spatie/laravel-permission": "^3.6"}, "require-dev": {"beyondcode/laravel-dump-server": "^1.0", "buxus/buxusstan": "^1.1", "buxus/update-log-plugin": "^1.0", "fzaninotto/faker": "^1.4", "mockery/mockery": "^1.0", "nunomaduro/collision": "^3.0", "phpunit/phpunit": "^7.5"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"buxus/buxus-registrator-plugin": true, "buxus/update-log-plugin": true}}, "autoload": {"classmap": ["database/seeds", "database/factories", "buxus/application/models", "buxus/project_includes"], "psr-4": {"App\\": ["app/"]}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php artisan buxus:post-install"]}, "repositories": [{"type": "composer", "url": "https://packagist.ui42.sk"}], "extra": {"branch-alias": {"dev-master": "7.2.x-dev"}, "laravel": {"dont-discover": []}}}