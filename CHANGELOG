##2.1.1
- [developer]removed deprecated named constructors

##2.1.0
- [developer] module now uses laravel package discovery
- [developer] usage of new `buxus-libs/fs` version 4.0

##2.0.1
- [bugfix] fix `DynamicCategory\DynamicQuery::addItem` method causing item nesting due to wrong refactoring
- [bugfix] fix selecting active items in `item\chosen-select.phtml` & fix html path to it in `DynamicCategory\Item\ChosenSelect`
- [bugfix] add `DynamicCategory\Item\ChosenSelect::setData` method, as the property didn't honor using checkboxes and thus didn't set any data when saving

##2.0.0
- [developer] added `ChosenSelect` abstract class - multi select box with search
- [developer] added `Checkboxes` abstract class, which is multi-checkbox dynamic category property with additional options - one of many, require all, exact match
- [developer] added `MinMax` abstract class to reuse for range filtering
- [developer] added all module translation strings to bx translations & added EN translation
- [developer] removed `DynamicCategory\Item\Producer`, which is now used from product-catalog module 1.2.0+
- [developer] moved `DynamicCategory\Item\Akcia` & `DynamicCategory\Item\Price` dynamic category properties to product-catalog module
- [developer] refactored all code to PSR2 coding standard

##1.4.7
- [developer][feature] added loading of additional configs from config directory `dynamic_categories_items`

##1.4.6
- [bugfix] fixed SOLR query generation for boolean operations when a single dynamic item returns an empty query

##1.4.5
- [deprecated] removed usage of deprecated `\Buxus\SeoUrl\SeoUrlNameProperty` class

##1.4.4
- [developer] changed module translations to use `buxus-translator` service

##1.4.3
- [bugfix] fixed requirejs setup to not throw errors for new BUXUS installations

##1.4.2
- [developer] updated composer.json documentation links

##1.4.1
- [bugfix] fixed ajax call for dynamic property edit in page detail

##1.4.0
- [developer] refactored classes to support `app()->make()` without parameters

##1.3.5
- [developer] changed migration to reflect change of product controller to ProductCatalogController

##1.3.4
- [developer] added support for fs_sort property for dynamic categories
- [bugfix] fixed \DynamicCategory\Manager\DynamicList to reset visibility and sort on deactivation

##1.3.3
- [config] added config option filter_mask_tags for setting the FS tags that can be visible in the facet filter

##1.3.2
- [developer] support for BUXUS module installer

##1.3.1
- [bugfix] opravy custom properties v migraciach
- [feature] podpora pre SOLR FS

##1.3.0
- [developer] uprava managerov aby fungovali cez service container
- [developer] fix short_open_tags
- [developer] opravy koli PHP7
- [developer] pridana metoda \DynamicCategory\DynamicQuery::generateQuery() pre lahsie pretazovanie

##1.2.3
- [feature] uprava \DynamicCategory\AbstractDynamicQueryFsSortProperty aby zobrazoval ASC aj DESC pri sortovacich property ktore su prepinacie

##1.2.2
- [feature] podpora pre sortovanie queries

##1.2.1
- [config] zmena formatu config suboru na array koli kompatibilite s laravelom
- [developer] upravy pre BUXUS7

##1.2.0

##1.1.2
- [developer] oprava dependency na buxus/util

##1.1.1
- [developer] uprava dependency na buxus-libs/fs aby bola fixovana len na major verziu

##1.1.0
- [developer] prerobenie migracii na publikovanie

##1.0.6
- [feature] Umožnenie preťaženia viewov vlastnými

##1.0.5
- [developer] uprava koli zmodulovaniu BUXUS 6.6.0

##1.0.4
- [developer] oprava koli novemu buxus-libs/fs v 1.2.0

##1.0.3
- [developer] fix pre pouzitie mimo Zend MVC

##1.0.2
- [developer] uprava views aby preberali view paths a helper paths z hlavneho MVC view

##1.0.1
- [developer] opravy dependencies

##1.0.0
- prvá verzia
