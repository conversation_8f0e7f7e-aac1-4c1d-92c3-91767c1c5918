{
    theme : "modern",
    skin: 'buxus',
    plugins : "fullpage,buxus_tags,advlist,autolink,lists,link,image,charmap,print,preview,hr,anchor,pagebreak,searchreplace,wordcount,visualblocks,visualchars,code,fullscreen,insertdatetime,media,nonbreaking,save,table,directionality,emoticons,template,paste,textcolor,buxus,autoresize,contextmenu,colorpicker,imagetools",
    toolbar1: "undo redo | newdocument fullpage | bold italic underline strikethrough | alignleft aligncenter alignright alignjustify | styleselect formatselect fontselect fontsizeselect",
    toolbar2: "buxus_link buxus_link_web buxus_link_img buxus_link_doc buxus_image buxus_media anchor unlink buxus_tags | cut copy paste | searchreplace | bullist numlist | outdent indent blockquote | undo redo | code | inserttime preview | forecolor backcolor",
    toolbar3: "table | hr removeformat | subscript superscript | charmap emoticons | print fullscreen | ltr rtl | visualchars visualblocks nonbreaking template pagebreak restoredraft",
    contextmenu: "buxus_link_web buxus_image inserttable | cell row column deletetable",
    menubar: false,
    toolbar_items_size: 'small',
    autoresize_min_height: 10,
    autoresize_bottom_margin: 10,
    paste_auto_cleanup_on_paste: true,

    browser_spellcheck: true,

    images_upload_url: '/buxus/dhtml/image_upload.php',
    images_upload_base_path: '',

    image_advtab: true,
    image_caption: true,

    convert_urls: false,

    buxus_tags: [
        {
            tag: "layout",
            header: "LAYOUT",
            color: "#ffffff",
            background: "#00aa00"
        }
    ]

}
