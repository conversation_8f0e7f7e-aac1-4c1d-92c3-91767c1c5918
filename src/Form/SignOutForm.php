<?php

namespace Mailinglist\Form;

use Buxus\Captcha\Form\Element\HoneypotFormElement;
use Buxus\Event\BuxusEvent;
use Buxus\Util\PageIds;
use Buxus\Util\Url;
use Buxus\WebUser\Event\WebUserMailinglistUnsubscribedEvent;
use Email\Facade\Email;
use ExceptionHandler;
use FormBase\Element\HiddenElement;
use FormBase\Element\SubmitElement;
use FormBase\Element\TextElement;
use Illuminate\Http\Request;
use Mailinglist\MailinglistPages;
use Mailinglist\MailinglistUtils;
use Mailinglist\Form\Validator\ValidateMailinglistAssigning;
use Zend_Controller_Request_Abstract;
use Zend_Filter_StringToLower;
use Zend_Validate_EmailAddress;
use Zend_Validate_StringLength;

class SignOutForm extends SignInForm
{
    public function init()
    {
        // Set form action
        $this->setAction(Url::taggedPage(MailinglistPages::SIGN_OUT));

        // Load field values
        $emailValue = $_REQUEST['e_mail'] ?? '';
        $mailinglistTagValue = $_REQUEST['mailinglist_tag'] ?? config('buxus_mailinglist.default_tag', 'newsletter');

        // Email
        $email = new TextElement('e_mail');
        $email->setTranslatedLabel(\Trans::str('mailinglist', 'Email'));
        $email->setValue($emailValue);
        $email->setRequired(true);
        $email->addValidator(new Zend_Validate_EmailAddress());
        $email->addValidator(new Zend_Validate_StringLength(0, 60));
        $email->addValidator(new ValidateMailinglistAssigning('mailinglist_tag'));
        $email->setAttrib('placeholder', \Trans::str('mailinglist', 'vaša e-mailová adresa'));
        $email->addFilter(new Zend_Filter_StringToLower());

        $this->addElement($email);

        // Mailinglist tag
        $mailinglist_tag = new HiddenElement('mailinglist_tag');
        $mailinglist_tag->setValue($mailinglistTagValue);
        $this->addElement($mailinglist_tag);

        // Fake
        $user_full_name = new HoneypotFormElement();
        $this->addElement($user_full_name);

        $csrfTokenElement = new HiddenElement('_token');
        $csrfTokenElement->setValue(csrf_token());
        $this->addElement($csrfTokenElement);

        // Submit
        $submit = new SubmitElement($this->getFormTag());
        $submit->setTranslatedLabel(\Trans::str('mailinglist', 'Odoslať'));
        $this->addElement($submit);

        // Set translation
        $this->setTranslation();
    }

    public function loadDefaultDecorators()
    {
        parent::loadDefaultDecorators();

        // Set special element decorators
        $mailinglist_tag = $this->getElement('mailinglist_tag');
        $mailinglist_tag->clearDecorators();
        $mailinglist_tag->addDecorator('ViewHelper');

        $user_full_name = $this->getElement('user_full_name');
        $user_full_name->clearDecorators();
        $user_full_name->addDecorator('ViewHelper');
    }

    /**
     * @param Request $request
     */
    public function actionHandler(Request $request)
    {
        if ($this->isValid($request->all())) { // The form is valid
            $e_mail = $this->getValue('e_mail');
            $mailinglist_tag = $this->getValue('mailinglist_tag');

            $user = \WebUserFactory::getByEmail($e_mail);

            if ($user) {
                // Load mailinglilst subject
                $mailinglist_name = MailinglistUtils::getMailinglistName($mailinglist_tag);

                // Set confirmation string
                $confirm_string = sha1(rand(0, 1000000));
                $sign_out_confirm_string_tag = mb_substr('sign_out_cs_' . $mailinglist_tag, 0, 50);

                $user->setCustomOption($sign_out_confirm_string_tag, $confirm_string);

                try {
                    $user->save();
                } catch (\Exception $e) {
                    // Undefined error
                    ExceptionHandler::logException($e);
                    ExceptionHandler::mailException($e);

                    $this->redirect(Url::taggedPage(MailinglistPages::SIGN_OUT_ERROR));
                }

                // Send email to user
                /**
                 * @var \Email\Contract\Email $buxus_email
                 */
                $buxus_email = Email::get(PageIds::getPageId(MailinglistPages::SIGN_OUT_EMAIL));

                // Add recipient
                $recipient_address = $user->getFirstName() . ' ' . $user->getSurname() . ' <' . $user->getEmail() . '>';
                $buxus_email->addRecipientAddress($recipient_address);

                $buxus_email->setDataTag('HOSTNAME', \BuxusSite::getHostname());
                $buxus_email->setDataTag('HOMEPAGE_URL', Url::staticUrl(''));
                $buxus_email->setDataTag(
                    'CONFIRM_LINK',
                    Url::staticUrl(
                        Url::taggedPage(
                            MailinglistPages::SIGN_OUT_CONFIRM,
                            '&e_mail=' . $user->getEmail() .
                            '&mailinglist_tag=' . $mailinglist_tag .
                            '&cs=' . $confirm_string
                        )
                    )
                );
                $buxus_email->setDataTag('MAILINGLIST_NAME', $mailinglist_name);

                //Send email
                $buxus_email->send();
            }

            $this->redirect(Url::taggedPage(MailinglistPages::SIGN_OUT_RECEIVED));
        }
    }

    /**
     * @param Request|Zend_Controller_Request_Abstract $request
     */
    public function actionHandlerConfirm(Request $request)
    {
        $e_mail = $request->get('e_mail');
        $mailinglist_tag = $request->get('mailinglist_tag');
        $confirm_string = (string)$request->get('cs');

        $user = \WebUserFactory::getByEmail($e_mail);

        if ((empty($user)) || (empty($mailinglist_tag)) || (mb_strlen($confirm_string) != 40)) { // Not correct user email, mailinglist tag or confirm string
            return;
        }

        if (!$user->getMailing($mailinglist_tag)) {
            return;
        }

        $sign_out_confirm_string_tag = mb_substr('sign_out_cs_' . $mailinglist_tag, 0, 50);

        $user_confirm_string = $user->getCustomOption($sign_out_confirm_string_tag);

        if ((empty($user_confirm_string)) || ($user_confirm_string != $confirm_string)) { // Incorrect confirm string
            $this->redirect(Url::taggedPage(MailinglistPages::SIGN_OUT_UNSUCCESFULL));
        }
        $user->setMailing($mailinglist_tag, false);
        $user->removeCustomOption($sign_out_confirm_string_tag);

        try {
            $user->save();
            BuxusEvent::pushContext('mailinglist-sign-out-form');
            BuxusEvent::fire(new WebUserMailinglistUnsubscribedEvent($user, [$mailinglist_tag]));
            BuxusEvent::popContext();
        } catch (\Exception $e) {
            // Undefined error
            ExceptionHandler::logException($e);
            ExceptionHandler::mailException($e);

            $this->redirect(Url::page(PageIds::getPageId(MailinglistPages::SIGN_OUT_ERROR)));
        }
    }
}
