<?php
/** @var int $id */
$id = $this->id;

/** @var string[]|integer[] $values */
$values = $this->values;

/** @var string[]|integer[] $active_values */
$active_values = $this->active_values;

/** @var string $name */
$name = $this->name;
?>

<?= htmlspecialchars($this->name) ?>
<div class="hidden-checkboxes" id="hidden-checkboxes__<?= $id ?>">
    <?php foreach ($values as $value_id => $name): ?>
        <div>
            <label>
                <input type="checkbox" name="val<?= $value_id ?>" value="<?= $value_id ?>"<?= in_array($value_id, $active_values) ? ' checked="checked"' : '' ?>/>
                <?= htmlspecialchars($name) ?>
            </label>
        </div>
    <?php endforeach; ?>
</div>
<select id="chosen-select__<?= $id ?>" multiple>
    <?php foreach ($values as $value_id => $name): ?>
        <option value="<?= $value_id ?>"<?= in_array($value_id, $active_values) ? ' selected="selected"' : '' ?>>
            <?= htmlspecialchars($name) ?>
        </option>
    <?php endforeach; ?>
</select>

<script type="text/javascript">
    require(['jquery', 'chosen'], function ($, chosen) {
        $('#chosen-select__<?= $id ?>').chosen({
            normalize_search_text: function(search_text) {
                return chosen.buxus_removeDiacritics(search_text);
            },
            allow_single_deselect: true,
            search_contains: true,
            width: "100%"
        }).on('change', function(e) {
            let val = $(this).val();
            let checkboxes = $('#hidden-checkboxes__<?= $id ?>').find('input[type=checkbox]');
            checkboxes.attr('checked', null);
            checkboxes.prop('checked', false);
            if (val !== null && val.length > 0 && checkboxes.length > 0) {
                checkboxes.each(function () {
                    if (val.includes($(this).attr('value').toString())) {
                        $(this).attr('checked', 'checked');
                        $(this).prop('checked', true);
                    }
                })

            }
        });
    });
</script>

<style>
    .hidden-checkboxes {
        display: none;
    }
</style>