<?php
/** @var int $id */
$id = $this->id;

/** @var string $name */
$name = $this->name;

/** @var int[]|string[] $values */
$values = $this->values;

/** @var string[]|integer[] $active_values */
$active_values = $this->active_values;

/** @var int[]|string[] $active */
$active = $this->active;

/** @var string[] $apply_modes */
$apply_modes = $this->apply_modes;

/** @var int $apply_mode */
$apply_mode = $this->apply_mode;
?>

<?= htmlspecialchars($name) ?>:&nbsp;
<div style="margin:5px 0">
    <?= __bx('dynamic-category::property.apply_type') ?>:&nbsp;
    <select name="apply_mode">
        <?php foreach ($apply_modes as $mode => $name): ?>
            <option value="<?= $mode ?>"<?= ($mode == $apply_mode) ? ' selected="selected"' : '' ?>><?= htmlspecialchars($name) ?></option>
        <?php endforeach; ?>
    </select>
</div>
<?php foreach ($values as $id => $name): ?>
    <div>
        <label>
            <input type="checkbox" name="val<?= $id ?>" value="<?= $id ?>"<?= in_array($id, $active_values) ? ' checked="checked"' : '' ?>/>
            <?= htmlspecialchars($name) ?>
        </label>
    </div>
<?php endforeach; ?>
