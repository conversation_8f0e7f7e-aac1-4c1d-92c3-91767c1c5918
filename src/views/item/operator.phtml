<?php
/** @var int $id */
$id = $this->id;

/** @var string $content */
$content = $this->content;

/** @var bool $inverted */
$inverted = $this->inverted;

/** @var \DynamicCategory\DynamicQuery $query */
$query = $this->query;
?>

<div style="border: 1px solid #aaaaaa;padding:10px;background:#ffffff">
    <div class="form" >
        <input type="hidden" name="parent_id" value="<?= $id ?>" />
        <?= __bx('dynamic-category::property.add_item') ?>:
        <select name="item_type">
            <?php foreach ($query->getAddableItems() as $type => $name): ?>
                <option value="<?= $type ?>"><?= htmlspecialchars($name) ?></option>
            <?php endforeach; ?>
        </select>
        <button class="cmd_add button"><?= __bx('dynamic-category::property.add') ?></button>
        </div>
        <div class="form">
            <input type="hidden" name="id" value="<?= $id ?>" />
            <?= $content ?>
            <label style="display:block;margin:5px 0;border-top:1px solid #aaaaaa">
                <input type="checkbox" name="inv" value="1"<?= $inverted ? ' checked="checked' : '' ?>/>
                <?= __bx('dynamic-category::property.invert') ?>
            </label>
        <div style="margin-top: 15px">
            <button class="cmd_set button"><?= __bx('dynamic-category::property.save') ?></button>
            <button class="cmd_remove button"><?= __bx('dynamic-category::property.remove') ?></button>
            <button class="cmd_close button"><?= __bx('dynamic-category::property.close') ?></button>
        </div>
    </div>
</div>