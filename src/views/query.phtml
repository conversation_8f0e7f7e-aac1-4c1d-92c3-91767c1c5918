<?php
/** @var string $assets_url */
$assets_url = $this->assets_url;

/** @var string $ajax_url */
$ajax_url = $this->ajax_url;

/** @var \DynamicCategory\DynamicQuery $query */
$query = $this->query;

/** @var string $destination_id */
$destination_id = $this->destination_id;

/** @var string $contents */
$contents = $this->contents;
?>

<script type="text/javascript">
    requirejs.config({
        paths: {
            'dynamic-query': '<?= $assets_url ?>js',
            'jquery-json': '<?= $assets_url ?>js/jquery.json-2.3.min'
        },
        shim: {
            'dynamic-query/jstree/jquery.jstree': [
                'jquery'
            ],
            'jquery-json': [
                'jquery'
            ]
        }
    });
    require(['jquery', 'dynamic-query/expression'], function () {
        $(function () {
            $('#dynamic_query_container_<?= $destination_id ?>').DynamicQueryManager({
                destination_id: '<?= $destination_id ?>',
                url: '<?= $ajax_url ?>',
                type: '<?= $query->getQueryType() ?>'
            });
        });
    });
</script>
<div id="dynamic_query_container_<?= $destination_id ?>">
    <?= $contents ?>
</div>