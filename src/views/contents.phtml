<?php
/** @var string $name */
$name = $this->name;

/** @var bool $include_add */
$include_add = $this->include_add;

/** @var string $tree */
$tree = $this->tree;

/** @var string $panes */
$panes = $this->panes;

/** @var \DynamicCategory\DynamicQuery $query */
$query = $this->query;
?>

<div style="margin-bottom: 5px">
    <button class="cmd_undo button"><?= __bx('dynamic-category::property.undo') ?></button>
</div>
<div class="tree" style="float:left;width:160px">
    <?= $tree ?>
</div>
<div class="panes" style="float:left">
    <?php if ($include_add): ?>
        <div class="form">
            <input type="hidden" name="parent_id" value="" />
            <?= __bx('dynamic-category::property.add_item') ?>:
            <select name="item_type">
                <?php foreach ($query->getAddableItems() as $type => $name): ?>
                    <option value="<?= $type ?>"><?= htmlspecialchars($name) ?></option>
                <?php endforeach; ?>
            </select>
            <button class="cmd_add button"><?= __bx('dynamic-category::property.add') ?></button>
        </div>
    <?php endif; ?>
    <?= $panes ?>
</div>
<div style="clear:both"></div>