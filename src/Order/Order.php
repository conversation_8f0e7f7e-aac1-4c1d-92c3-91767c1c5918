<?php

namespace Buxus\Eshop\Order;

use Buxus\Error\ErrorReporter;
use Buxus\Eshop\Contracts\DeliveryTypeFactory;
use Buxus\Eshop\Contracts\PaymentTypeFactory;
use Buxus\Eshop\Contracts\PriceViewer;
use Buxus\Eshop\Contracts\ShopOrderState;
use Buxus\Eshop\Event\OrderCreatedEvent;
use Buxus\Eshop\Event\OrderDeleteEvent;
use Buxus\Eshop\Event\OrderPaidEvent;
use Buxus\Eshop\Event\OrderPreCreateEvent;
use Buxus\Eshop\Event\OrderPreUpdateEvent;
use Buxus\Eshop\Event\OrderStateChangeEvent;
use Buxus\Eshop\Event\OrderUpdateEvent;
use Buxus\Eshop\Exceptions\InvalidOrderIdException;
use Buxus\Eshop\Facades\OrderNotifier;
use Buxus\Eshop\Facades\OrderStateManager;
use Buxus\Eshop\Item\Delivery\GenericDeliveryType;
use Buxus\Eshop\Item\Delivery\OrderEmailNoteProviderDeliveryItemInterface;
use Buxus\Eshop\Item\Payment\GenericPaymentType;
use Buxus\Testing\Acceptance\AcceptanceTestingOrderInterface;
use Buxus\Testing\Acceptance\AcceptanceTestingOrderTrait;
use Buxus\Eshop\Utils\OrderUtils;
use Buxus\Email\KeyValueDataProviderInterface;
use Buxus\Event\BuxusEvent;
use Buxus\Util\Url;
use Illuminate\Support\Str;

class Order implements OrderInterface, \ArrayAccess, KeyValueDataProviderInterface, AcceptanceTestingOrderInterface
{
    use AcceptanceTestingOrderTrait;

    /** @var array */
    protected static $db_attributes = [
        'variable_symbol',
        'order_datetime',
        'first_name',
        'surname',
        'email',
        'phone',
        'delivery_type',
        'payment_type',
        'order_web_user_id',
        'inv_company_name',
        'ico',
        'dic',
        'drc',
        'note',
        'currency',
        'city',
        'street',
        'zip',
        'company_name',
        'inv_street',
        'inv_city',
        'inv_zip',
        'inv_company_name',
        'paid_price',
        'payment_datetime',
        'order_state',
        'post_price',
        'post_price_vat',
        'site',
        'invoice_number',
        'internal_note',
        'is_active'
    ];

    /** @var string|int */
    protected $orderId;

    /** @var OrderItemInterface[] */
    protected $items = [];

    /** @var GenericDeliveryType|null */
    protected $transportType;

    /** @var GenericPaymentType */
    protected $paymentType;

    /** @var int|null */
    protected $originalOrderState;

    /** @var float|int|null */
    protected $totalPriceWithoutVat;

    /** @var array */
    protected $_data = [
        'is_active' => 'T',
    ];

    /** @var array */
    protected $_original_data = [];

    /**
     * @param mixed $orderId
     * @throws InvalidOrderIdException
     */
    public function setOrderId($orderId)
    {
        $this->orderId = $orderId;

        if (!is_null($this->orderId)) {
            $this->load();
        }
    }

    /**
     * @return string|int
     */
    public function getOrderId()
    {
        return $this->orderId;
    }

    /**
     * @throws InvalidOrderIdException
     */
    protected function load()
    {
        $orderData = (array)\DB::table('tblShopOrders')
            ->where('order_id', $this->getOrderId())
            ->first();

        if (empty($orderData)) {
            throw new InvalidOrderIdException($this->getOrderId());
        }

        // convert paid_price to float value
        if (isset($orderData['paid_price'])) {
            $orderData['paid_price'] = floatval($orderData['paid_price']);
        }

        $options = \DB::table('tblShopOrderOptions')
            ->where('order_id', $this->getOrderId())
            ->get()->toArray();

        foreach ($options as $option) {
            if (!in_array($option->order_tag, self::$db_attributes)) {
                $orderData[$option->order_tag] = $option->order_value;
            }
        }

        $this->_data = $orderData;

        $items = \DB::table('tblShopOrderItems')
            ->where('order_id', $this->getOrderId())
            ->get()
            ->keyBy('order_item_id')
            ->toArray();

        foreach ($items as $itemId => $item) {
            $itemOptions = \DB::table('tblShopOrderItemOptions')
                ->where('order_item_id', $itemId)
                ->pluck('option_value', 'option_tag')
                ->toArray();

            $itemData = (array)$item;
            $itemData['options'] = $itemOptions;

            $items[$itemId] = $itemData;
        }

        $orderItems = [];
        foreach ($items as $itemData) {
            /**
             * @var OrderItemInterface $orderItem
             */
            $orderItem = app('buxus.eshop.order-item-object');
            $orderItem->hydrateData($itemData);

            $orderItems[] = $orderItem;
        }

        $this->setItems($orderItems);
    }

    /**
     * @param int|string $key
     * @param mixed $value
     */
    public function setData($key, $value)
    {
        // keys will be normalized to lowercase
        $key = strtolower($key);

        if (!array_key_exists($key, $this->_original_data)) {
            $this->_original_data[$key] = (isset($this->_data[$key]) ? $this->_data[$key] : null);
        } else {
            if ($this->_original_data[$key] === $value) {
                unset($this->_original_data[$key]);
            }
        }
        $this->_data[$key] = $value;
    }

    /**
     * @param string|int $key
     */
    protected function unsetData($key)
    {
        // keys will be normalized to lowercase
        $key = strtolower($key);

        if (array_key_exists($key, $this->_data)) {
            if (!array_key_exists($key, $this->_original_data)) {
                $this->_original_data[$key] = $this->_data[$key];
            }
            unset($this->_data[$key]);
        }
    }

    /**
     * @param $key
     * @return string|int|float
     */
    public function getData($key)
    {
        if (isset(self::$db_attributes[$key], $this->_data[$key])) {
            return $this->_data[$key];
        }

        // treat other items as order options
        if (is_array($this->_data) && array_key_exists($key, $this->_data)) {
            return $this->_data[$key];
        }
    }

    /**
     * @return OrderItemInterface[]
     */
    public function getItems()
    {
        return $this->items;
    }

    /**
     * @param OrderItemInterface[] $items
     */
    public function setItems($items)
    {
        $this->items = $items;
    }

    /**
     * @param OrderItemInterface $item
     */
    public function addItem(OrderItemInterface $item)
    {
        $this->items[] = $item;
    }

    /**
     * Insert or update the order in database
     *
     * @param bool $triggers
     */
    public function save($triggers = true)
    {
        if (!is_null($this->orderId)) {
            $this->update($triggers);
        } else {
            $this->insert($triggers);
        }

        $this->setData('updated_at', date('Y-m-d H:i:s'));

        $fireStateChangeEvent = (array_key_exists('order_state', $this->_original_data));
        $fireOrderPaidEvent = (array_key_exists('payment_datetime',
                $this->_original_data) && !empty($this->getPaymentDatetime()) && empty($this->_original_data['payment_datetime']));

        // reset the changed status
        $this->_original_data = [];

        if ($triggers && $fireStateChangeEvent) {
            BuxusEvent::fire(new OrderStateChangeEvent($this, $this->_original_data['order_state'],
                $this->getData('order_state')));
        }

        if ($triggers && $fireOrderPaidEvent) {
            BuxusEvent::fire(new OrderPaidEvent($this));
        }
    }

    /**
     * Insert new order into the database
     *
     * @param bool $triggers
     */
    protected function insert($triggers = true)
    {
        if (!array_key_exists('order_datetime', $this->_data)) {
            $this->setData('order_datetime', date('Y-m-d H:i:s'));
        }

        $this->generateSecurityToken();

        if ($triggers) {
            BuxusEvent::fire(new OrderPreCreateEvent($this));
        }

        \DB::raw('LOCK TABLES tblShopOrders WRITE');
        try {
            if (empty($this->getVariableSymbol())) {
                $this->generateVariableSymbol();
            }

            // extract tblOrders attributes
            $data = [];
            foreach (self::$db_attributes as $attribute) {
                if (isset($this->_data[$attribute])) {
                    $data[$attribute] = $this->_data[$attribute];
                }
            }

            $this->orderId = \DB::table('tblShopOrders')->insertGetId($data);
        } finally {
            \DB::raw('UNLOCK TABLES');
        }

        $this->saveOptions();

        foreach ($this->items as $item) {
            $this->insertItem($item);
        }

        if ($triggers) {
            BuxusEvent::fire(new OrderCreatedEvent($this));
        }
    }

    protected function generateVariableSymbol(): void
    {
        // variable symbol should be generated
        if (\AcceptanceTesting::isTestActive()) {
            \AcceptanceTesting::modifyOrderIfAcceptanceTesting($this);
        } else {
            $this->setVariableSymbol(OrderUtils::getVs(config('buxus_eshop.vs_mask')));
        }
    }

    protected function saveOptions()
    {
        $unusedOptionTags = [];
        $modifiedOptions = [];

        // extract changed options
        foreach ($this->_original_data as $key => $value) {
            if (!in_array($key, self::$db_attributes)) {
                $modifiedOptions[$key] = $value;
            }
        }

        $index = 0;
        foreach ($modifiedOptions as $tag => $value) {
            if (array_key_exists($tag, $this->_data)) {
                // the option has been added or modified
                \DB::statement("INSERT INTO tblShopOrderOptions (order_id, order_tag, order_value, sort_number)
                    VALUES (:order_id, :order_tag, :order_value, :order)
                    ON DUPLICATE KEY UPDATE order_tag=VALUES(order_tag), order_value=VALUES(order_value)", [
                    ':order_tag' => $tag,
                    ':order_value' => $this->_data[$tag],
                    ':order_id' => $this->getOrderId(),
                    ':order' => $index++,
                ]);
            } else {
                $unusedOptionTags[] = $tag;
            }
        }

        if (!empty($unusedOptionTags)) {
            \DB::table('tblShopOrderOptions')
                ->where('order_id', '=', $this->getOrderId())
                ->whereIn('order_tag', $unusedOptionTags)
                ->delete();
        }
    }

    /**
     * @param bool $triggers
     */
    protected function update($triggers = true)
    {
        $originalOrder = \OrderFactory::getById($this->orderId);

        if ($triggers) {
            BuxusEvent::fire(new OrderPreUpdateEvent($this, $originalOrder));
        }

        // extract changed attributes
        $data = [];
        foreach (self::$db_attributes as $attribute) {
            if (array_key_exists($attribute, $this->_original_data)) {
                $data[$attribute] = $this->_data[$attribute];
            }
        }

        if (!empty($data)) {
            \DB::table('tblShopOrders')
                ->where('order_id', $this->getOrderId())
                ->update($data);
        }

        $this->saveOptions();

        $this->updateItems();

        $this->createJournalEntry($originalOrder);

        if ($triggers) {
            BuxusEvent::fire(new OrderUpdateEvent($this, $originalOrder));
        }
    }

    public function delete()
    {
        \DB::table('tblShopOrders')
            ->where('order_id', $this->getOrderId())
            ->update([
                'is_active' => 'F'
            ]);

        BuxusEvent::fire(new OrderDeleteEvent($this));
    }

    /**
     * @param OrderInterface $originalOrder
     */
    protected function createJournalEntry(OrderInterface $originalOrder)
    {
    }

    protected function updateItems()
    {
        $unusedOrderItems = \DB::table('tblShopOrderItems')
            ->where('order_id', $this->getOrderId())
            ->pluck('order_item_id');

        /** @var OrderItemInterface $item */
        foreach ($this->items as $item) {
            $itemInArray = $unusedOrderItems->contains($item->getOrderItemId());
            if ($itemInArray) {
                $unusedOrderItems->offsetUnset($unusedOrderItems->search($item->getOrderItemId()));
            }
            if (!$item->getOrderItemId() || !$itemInArray) {
                $this->insertItem($item);
            } else {
                $this->updateItem($item);
            }
        }

        /**
         * Delete unused order items & their options in bulk
         */
        if (!empty($unusedOrderItems)) {
            \DB::table('tblShopOrderItemOptions')
                ->whereIn('order_item_id', $unusedOrderItems->all())
                ->delete();

            \DB::table('tblShopOrderItems')
                ->whereIn('order_item_id', $unusedOrderItems->all())
                ->delete();
        }
    }

    /**
     * @param OrderItemInterface $item
     */
    protected function insertItem(OrderItemInterface $item)
    {
        $orderItemId = \DB::table('tblShopOrderItems')
            ->insertGetId([
                'order_item_name' => $item->getProductName(),
                'order_id' => $this->orderId,
                'page_id' => $item->getPageId(),
                'nr_of_items' => $item->getAmount(),
                'price_per_item' => $item->getItemPrice(),
                'price_name' => $item->getPriceName(),
                'price_type' => $item->getPriceType(),
                'vat_per_item' => $item->getVatRatePercent(),
                'nr_of_delivered_items' => $item->getDeliveredAmount() ?: 0,
            ]);

        $item->setOrderItemId($orderItemId);

        $itemOptions = collect($item->getOptions())
            ->transform(function ($option, $tag) use ($orderItemId) {
                return [
                    'option_tag' => $tag,
                    'option_value' => $option,
                    'order_item_id' => $orderItemId
                ];
            });

        \DB::table('tblShopOrderItemOptions')->insert($itemOptions->all());
    }

    protected function updateItem(OrderItemInterface $item)
    {
        \DB::table('tblShopOrderItems')
            ->where('order_item_id', $item->getOrderItemId())
            ->update([
                'order_item_name' => $item->getProductName(),
                'order_id' => $this->orderId,
                'page_id' => $item->getPageId(),
                'nr_of_items' => $item->getAmount(),
                'price_per_item' => $item->getItemPrice(),
                'price_name' => $item->getPriceName(),
                'price_type' => $item->getPriceType(),
                'vat_per_item' => $item->getVatRatePercent(),
                'nr_of_delivered_items' => $item->getDeliveredAmount() ?: 0,
            ]);

        $this->updateItemOptions($item);
    }

    protected function updateItemOptions(OrderItemInterface $item)
    {
        $newOrderOptions = [];
        $unusedOptionTags = \DB::table('tblShopOrderItemOptions')
            ->where('order_item_id', $item->getOrderItemId())
            ->pluck('option_tag');

        /** @var OrderItem $item */
        foreach ($item->getOptions() as $tag => $option) {
            if ($unusedOptionTags->contains($tag)) {
                $unusedOptionTags->offsetUnset($unusedOptionTags->search($tag));
                \DB::table('tblShopOrderItemOptions')
                    ->where([
                        'order_item_id' => $item->getOrderItemId(),
                        'option_tag' => $tag
                    ])
                    ->update(['option_value' => $option]);
            } else {
                $newOrderOptions[] = [
                    'option_tag' => $tag,
                    'option_value' => $option,
                    'order_item_id' => $item->getOrderItemId()
                ];
            }
        }

        /**
         * Insert new order options in bulk
         */
        if (!empty($newOrderOptions)) {
            \DB::table('tblShopOrderItemOptions')->insert($newOrderOptions);
        }

        /**
         * Delete unused order options in bulk
         */
        if (!empty($unusedOptionTags)) {
            \DB::table('tblShopOrderItemOptions')
                ->where('order_item_id', '=', $item->getOrderItemId())
                ->whereIn('option_tag', $unusedOptionTags->all())
                ->delete();
        }
    }

    /**
     * @param string $offset
     * @return bool
     */
    public function offsetExists($offset)
    {
        $method = 'get' . Str::camel($offset);

        if (method_exists(get_class($this), $method)) {
            return true;
        }

        return (!in_array($offset, self::$db_attributes) && array_key_exists($offset, $this->_data));
    }

    /**
     * @param mixed $offset
     * @return mixed
     */
    public function offsetGet($offset)
    {
        $method = 'get' . Str::camel($offset);

        if (method_exists(get_class($this), $method)) {
            return $this->$method();
        }

        if (!in_array($offset, self::$db_attributes) && isset($this->_data[$offset])) {
            return $this->_data[$offset];
        }
    }

    /**
     * @param mixed $offset
     * @param mixed $value
     */
    public function offsetSet($offset, $value)
    {
        $method = 'set' . Str::camel($offset);
        if (method_exists(get_class($this), $method)) {
            $this->$method($value);
            return;
        }

        if (!in_array($offset, self::$db_attributes)) {
            $this->setData($offset, $value);
        }
    }

    /**
     * @param mixed $offset
     */
    public function offsetUnset($offset)
    {
        if (!in_array($offset, self::$db_attributes)) {
            $this->unsetData($offset);
        }
    }

    /**
     * @return string
     */
    public function getVariableSymbol()
    {
        return $this->getData('variable_symbol');
    }

    /**
     * @param string $variableSymbol
     * @return mixed|void
     */
    public function setVariableSymbol($variableSymbol)
    {
        $this->setData('variable_symbol', $variableSymbol);
    }

    /**
     * @param string $format
     * @return false|string
     */
    public function getOrderDatetime($format = 'Y-m-d H:i:s')
    {
        return date($format, strtotime($this->getData('order_datetime')));
    }

    /**
     * @param string $orderDateTime
     */
    public function setOrderDatetime($orderDateTime)
    {
        $this->setData('order_datetime', $orderDateTime);
    }

    /**
     * @return string
     */
    public function getFirstName()
    {
        return $this->getData('first_name');
    }

    /**
     * @param string $firstName
     */
    public function setFirstName($firstName)
    {
        $this->setData('first_name', $firstName);
    }

    /**
     * @return string
     */
    public function getSurname()
    {
        return $this->getData('surname');
    }

    /**
     * @param string $surname
     */
    public function setSurname($surname)
    {
        $this->setData('surname', $surname);
    }

    /**
     * @return string
     */
    public function getEmail()
    {
        return $this->getData('email');
    }

    /**
     * @param string $email
     */
    public function setEmail($email)
    {
        $this->setData('email', $email);
    }

    /**
     * @return string
     */
    public function getPhone()
    {
        return $this->getData('phone');
    }

    /**
     * @param string $phone
     */
    public function setPhone($phone)
    {
        $this->setData('phone', $phone);
    }

    /**
     * @return GenericDeliveryType
     *
     * @throws \Exception
     */
    public function getTransportType()
    {
        if ($this->transportType === null) {
            $transportTypeTag = $this->getTransportTypeTag();

            $this->transportType = \BuxusSite::executeInSiteContext(
                $this->getSite(),
                static function () use ($transportTypeTag) {
                    return app(DeliveryTypeFactory::class)->getDeliveryTypeByTag($transportTypeTag);
                }
            );
        }

        return $this->transportType;
    }

    /**
     * @param GenericDeliveryType $transportType
     */
    public function setTransportType(GenericDeliveryType $transportType)
    {
        $this->transportType = $transportType;
        $this->setData('delivery_type', $transportType->getTag());
        $this->setData('delivery_type_name', $transportType->getName());
    }

    public function getTransportTypeName(): ?string
    {
        $name = $this->getData('delivery_type_name');

        if (empty($name)) {
            try {
                $name = $this->getTransportType()->getName();
            } catch (\Exception $e) {
                ErrorReporter::reportSilent($e);
            }
        }

        return $name;
    }

    public function getTransportTypeTag(): string
    {
        return $this->getData('delivery_type');
    }

    /**
     * @return string|float|null
     */
    public function getTransportPriceValue()
    {
        return $this->getData('delivery_price');
    }

    /**
     * @return GenericPaymentType
     *
     * @throws \Exception
     */
    public function getPaymentType()
    {
        if ($this->paymentType === null) {
            $paymentTypeTag = $this->getPaymentTypeTag();

            $this->paymentType = \BuxusSite::executeInSiteContext(
                $this->getSite(),
                static function () use ($paymentTypeTag) {
                    return app(PaymentTypeFactory::class)->getPaymentTypeByTag($paymentTypeTag);
                }
            );
        }

        return $this->paymentType;
    }

    /**
     * @param GenericPaymentType $paymentType
     */
    public function setPaymentType(GenericPaymentType $paymentType)
    {
        $this->paymentType = $paymentType;
        $this->setData('payment_type', $paymentType->getTag());
        $this->setData('payment_type_name', $paymentType->getName());
    }

    public function getPaymentTypeName(): ?string
    {
        $name = $this->getData('payment_type_name');

        if (empty($name)) {
            try {
                $name = $this->getPaymentType()->getName();
            } catch (\Exception $e) {
                ErrorReporter::reportSilent($e);
            }
        }

        return $name;
    }

    public function getPaymentTypeTag(): string
    {
        return $this->getData('payment_type');
    }

    /**
     * @return float|string|null
     */
    public function getPaymentPriceValue()
    {
        return $this->getData('payment_price');
    }

    /**
     * @return int|string
     */
    public function getUserId()
    {
        return $this->getData('order_web_user_id');
    }

    /**
     * @param int|string $userId
     */
    public function setUserId($userId)
    {
        $this->setData('order_web_user_id', $userId);
    }

    /**
     * @return string|null
     */
    public function getIco()
    {
        return $this->getData('ico');
    }

    /**
     * @param string $ico
     */
    public function setIco($ico)
    {
        $this->setData('ico', $ico);
    }

    /**
     * @return string|null
     */
    public function getDic()
    {
        return $this->getData('dic');
    }

    /**
     * @param string $dic
     */
    public function setDic($dic)
    {
        $this->setData('dic', $dic);
    }

    /**
     * @return string|null
     */
    public function getIcdph()
    {
        return $this->getData('drc');
    }

    /**
     * @param string $icDph
     */
    public function setIcdph($icDph)
    {
        $this->setData('drc', $icDph);
    }

    /**
     * @return string|null
     */
    public function getNote()
    {
        return $this->getData('note');
    }

    /**
     * @param string|null $note
     */
    public function setNote($note)
    {
        $this->setData('note', $note);
    }

    /**
     * @return string|null
     */
    public function getCurrency()
    {
        return $this->getData('currency');
    }

    /**
     * @param string $currency
     */
    public function setCurrency($currency)
    {
        $this->setData('currency', $currency);
    }

    /**
     * @return float|int|string|null
     */
    public function getDeliveryCity()
    {
        return $this->getData('city');
    }

    /**
     * @param string|null $deliveryCity
     */
    public function setDeliveryCity($deliveryCity)
    {
        $this->setData('city', $deliveryCity);
    }

    /**
     * @return string|null
     */
    public function getDeliveryStreet()
    {
        return $this->getData('street');
    }

    /**
     * @param string $deliveryStreet
     */
    public function setDeliveryStreet($deliveryStreet)
    {
        $this->setData('street', $deliveryStreet);
    }

    /**
     * @return string|null
     */
    public function getDeliveryZip()
    {
        return $this->getData('zip');
    }

    /**
     * @param int|string $deliveryZip
     */
    public function setDeliveryZip($deliveryZip)
    {
        $this->setData('zip', $deliveryZip);
    }

    /**
     * @return string|null
     */
    public function getDeliveryCompanyName()
    {
        return $this->getData('company_name');
    }

    /**
     * @param string $deliveryCompanyName
     */
    public function setDeliveryCompanyName($deliveryCompanyName)
    {
        $this->setData('company_name', $deliveryCompanyName);
    }

    /**
     * @return string|null
     */
    public function getDeliveryName()
    {
        $deliveryName = $this->getData('delivery_name');
        if (empty($deliveryName)) {
            $deliveryName = sprintf('%s %s', $this->getFirstName(), $this->getSurname());
        }

        return $deliveryName;
    }

    /**
     * @param string $deliveryName
     */
    public function setDeliveryName($deliveryName)
    {
        $this->setData('delivery_name', $deliveryName);
    }

    /**
     * @return string|null
     */
    public function getInvoiceCity()
    {
        return $this->getData('inv_city');
    }

    /**
     * @param string $invoiceCity
     */
    public function setInvoiceCity($invoiceCity)
    {
        $this->setData('inv_city', $invoiceCity);
    }

    /**
     * @return string|null
     */
    public function getInvoiceStreet()
    {
        return $this->getData('inv_street');
    }

    /**
     * @param string $invoiceStreet
     */
    public function setInvoiceStreet($invoiceStreet)
    {
        $this->setData('inv_street', $invoiceStreet);
    }

    /**
     * @returnstring|null
     */
    public function getInvoiceZip()
    {
        return $this->getData('inv_zip');
    }

    /**
     * @param string|int $invoiceZip
     */
    public function setInvoiceZip($invoiceZip)
    {
        $this->setData('inv_zip', $invoiceZip);
    }

    /**
     * @return string|null
     */
    public function getInvoiceCompanyName()
    {
        return $this->getData('inv_company_name');
    }

    /**
     * @param string $invoiceCompanyName
     */
    public function setInvoiceCompanyName($invoiceCompanyName)
    {
        $this->setData('inv_company_name', $invoiceCompanyName);
    }

    /**
     * @return float|int|string|null
     */
    public function getPaidPrice()
    {
        return $this->getData('paid_price');
    }

    /**
     * @param float|int|string $paidPrice
     */
    public function setPaidPrice($paidPrice)
    {
        $this->setData('paid_price', $paidPrice);
    }

    /**
     * @return float|int|string|null
     */
    public function getOrderRawPrice()
    {
        return $this->getData('order_raw_price');
    }

    /**
     * @param float|int|string $price
     */
    public function setOrderRawPrice($price)
    {
        $this->setData('order_raw_price', $price);
    }

    /**
     * @param string $format
     * @return false|string
     */
    public function getPaymentDatetime($format = 'Y-m-d H:i:s')
    {
        return date($format, strtotime($this->getData('payment_datetime')));
    }

    /**
     * @param string $paymentDatetime
     */
    public function setPaymentDatetime($paymentDatetime)
    {
        $this->setData('payment_datetime', $paymentDatetime);
    }

    /**
     * @return int|string|null
     */
    public function getOrderState()
    {
        return $this->getData('order_state');
    }

    /**
     * @param int|string|null $state
     */
    public function setOrderState($state)
    {
        if (is_null($this->originalOrderState)) {
            $this->originalOrderState = $this->getData('order_state');
        }
        $this->setData('order_state', $state);
    }

    /**
     * @param int|string $newState
     */
    public function changeOrderStateWithNotification($newState)
    {
        if ($this->getOrderState() == $newState) {
            return;
        }

        $oldState = $this->getOrderState();

        $this->setOrderState($newState);
        $this->save();

        try {
            $oldStateObject = \Buxus\Eshop\Facades\OrderStateManager::getStateById($oldState);
            $newStateObject = \Buxus\Eshop\Facades\OrderStateManager::getStateById($newState);
            $journal = new \ShopJournalRecord(
                $this->getOrderId(),
                'orders',
                \Buxus\User\Facades\BuxusUserManager::getCurrentUserId(),
                'Zmena stavu: ' .
                ($oldStateObject ? $oldStateObject->getLabel() : $oldState) .
                ' &raquo; ' .
                ($newStateObject ? $newStateObject->getLabel() : $newState)
            );
            $journal->save();
        } catch (\Exception $e) {
        }


        OrderNotifier::sendOrderNotification($this);
    }

    /**
     * @param string $site
     */
    public function setSite($site)
    {
        $this->setData('site', $site);
    }

    /**
     * @return string|null
     */
    public function getSite()
    {
        return $this->getData('site');
    }

    /**
     * @param $customType
     */
    public function setCustomerType($customType)
    {
        $this->setData('customer_type', $customType);
    }

    /**
     * @return string|null
     */
    public function getCustomerType()
    {
        return $this->getData('customer_type');
    }

    protected function generateSecurityToken()
    {
        $token = bin2hex(openssl_random_pseudo_bytes(16));
        $this->setData('order_security_token', $token);
    }

    /**
     * @return string
     */
    public function getSecurityHash()
    {
        return base64_encode(md5($this->orderId . '-' . $this->getData('order_security_token') . '-' . $this->getVariableSymbol()));
    }

    /**
     * @param $hash
     * @return bool
     */
    public function matchSecurityHash($hash)
    {
        $originalHash = $this->getSecurityHash();

        return ($hash == $originalHash);
    }

    /**
     * @return array
     *
     * @throws \Exception
     */
    public function getSupportedKeys()
    {
        return array_keys($this->getKeyValueData());
    }

    /**
     * @return array
     */
    public function getKeyValueData()
    {
        // fill basic data
        $data = $this->_data;

        $data['delivery'] = [
            'delivery_type_name' => $this->getTransportTypeName(),
            'delivery_type_tag' => $this->getTransportTypeTag(),
        ];

        try {
            $deliveryType = $this->getTransportType();
            if ($deliveryType instanceof OrderEmailNoteProviderDeliveryItemInterface) {
                $data['delivery']['delivery_type_note'] = $deliveryType->getNoteForOrderEmail($this);
            }
        } catch (\Exception $e) {
            // do nothing
        }

        $data['payment'] = [
            'payment_type_name' => $this->getPaymentTypeName(),
            'payment_type_tag' => $this->getPaymentTypeTag(),
        ];

        // fill user info
        $user_id = $this->getUserId();
        if (!empty($user_id)) {
            try {
                $user = \WebUserFactory::getById($user_id);
                if ($user instanceof KeyValueDataProviderInterface) {
                    $data['user'] = $user->getKeyValueData();
                }
            } catch (\Exception $e) {
            }
        }

        // fill item data
        $itemsData = [];
        $items = $this->getItems();
        foreach ($items as $item) {
            if ($item instanceof KeyValueDataProviderInterface) {
                $itemsData[] = $item->getKeyValueData();
            }
        }

        $data['items'] = $itemsData;

        /**
         * @var PriceViewer $priceViewer
         */
        $priceViewer = app(PriceViewer::class);
        $vat = config('buxus_eshop.vat_rate', 20);

        $data['delivery_price_format'] = $priceViewer->formatRawPrice((float)$data['delivery_price']);
        $data['payment_price_format'] = $priceViewer->formatRawPrice((float)$data['payment_price']);
        $data['paid_price_format'] = $priceViewer->formatRawPrice((float)$data['paid_price']);
        $data['paid_price_novat'] = (float)$data['paid_price'] / (1 + $vat / 100);
        $data['paid_price_novat_format'] = $priceViewer->formatRawPrice($data['paid_price_novat']);

        $data['delivery_price_without_vat'] = (float)$data['delivery_price'] / (1 + $vat / 100);
        $data['delivery_price_vat_rate'] = $vat;
        $data['payment_price_without_vat'] = (float)$data['payment_price'] / (1 + $vat / 100);
        $data['payment_price_vat_rate'] = $vat;

        if ($this->getCustomerType() == 'person') {
            $data['person'] = [
                'first_name' => $this->getFirstName(),
                'surname' => $this->getSurname(),
            ];
        } else {
            $data['company'] = [
                'company_name' => $this->getDeliveryCompanyName(),
            ];
        }

        // normalize delivery data
        if ($this->getDeliveryAddressIsIdentical()) {
            $map = [
                'street' => 'inv_street',
                'city' => 'inv_city',
                'zip' => 'inv_zip',
                'company_name' => 'inv_company_name',
            ];
            foreach ($map as $deliveryTag => $invoiceTag) {
                if (empty($data[$deliveryTag])) {
                    $data[$deliveryTag] = $data[$invoiceTag];
                }
            }
        }

        $data['delivery_name'] = $this->getDeliveryName();
        $data['delivery_address_identical'] = $this->getDeliveryAddressIsIdentical();

        return $data;
    }

    /**
     * @param float|int $price
     */
    public function setPostPrice($price)
    {
        $this->setData('post_price', $price);
    }

    /**
     * @param float|int $vat
     */
    public function setPostPriceVat($vat)
    {
        $this->setData('post_price_vat', $vat);
    }

    /**
     * @return false|float|int|string
     */
    public function getTotalPriceWithoutVat()
    {
        if ($this->totalPriceWithoutVat === null) {
            $totalPriceWithoutVat = 0;

            foreach ($this->getItems() as $item) {
                /** @var OrderItem $item */
                $totalPriceWithoutVat += $item->getItemPriceWithoutVAT() * $item->getAmount();
            }

            \BuxusSite::executeInSiteContext($this->getSite(), function () use (&$totalPriceWithoutVat) {
                $vatRate = config('buxus_eshop.vat_rate');
                $totalPriceWithoutVat += $this->getTransportPriceValue() / (1.0 + ($vatRate / 100));
                $totalPriceWithoutVat += $this->getPaymentPriceValue() / (1.0 + ($vatRate / 100));
            });

            $this->totalPriceWithoutVat = $totalPriceWithoutVat;
        }

        return $this->totalPriceWithoutVat;
    }

    /**
     * @return string|int|float
     */
    public function getTotalVAT()
    {
        // count the VAT from items
        $total_vat = 0;
        foreach ($this->getItems() as $item) {
            /**
             * @var OrderItemInterface $item
             */
            $total_vat += $item->getTotalVAT();
        }

        return $total_vat;
    }

    /**
     * @return bool
     */
    public function getDeliveryAddressIsIdentical()
    {
        return ($this->getData('delivery_address_identical') == 1);
    }

    /**
     * @param bool $isIdentical
     */
    public function setDeliveryAddressIsIdentical($isIdentical)
    {
        $this->setData('delivery_address_identical', ($isIdentical ? '1' : '0'));
    }

    /**
     * @return bool
     */
    public function isPaid()
    {
        return !empty($this->getData('payment_datetime'));
    }

    /**
     * @return string|null
     */
    public function getDeliveryCountry()
    {
        return $this->getData('country');
    }

    /**
     * @param string $deliveryCountry
     */
    public function setDeliveryCountry($deliveryCountry)
    {
        $this->setData('country', $deliveryCountry);
    }

    /**
     * @return string|null
     */
    public function getInvoiceCountry()
    {
        return $this->getData('inv_country');
    }

    /**
     * @param string $invoiceCountry
     */
    public function setInvoiceCountry($invoiceCountry)
    {
        $this->setData('inv_country', $invoiceCountry);
    }

    /**
     * @return bool
     */
    public function isExportedToDelivery()
    {
        return !empty($this->getData('exported_to_delivery'));
    }

    /**
     * @param string $type
     */
    public function setExportedToDelivery($type)
    {
        if ($type !== null) {
            $this->setData('exported_to_delivery', date('Y-m-d H:i:s'));
            $this->setData('exported_to_delivery_type', $type);
        } else {
            $this->unsetData('exported_to_delivery');
            $this->unsetData('exported_to_delivery_type');
        }
    }

    /**
     * @param null $paidDateTime
     */
    public function markPaid($paidDateTime = null)
    {
        if ($paidDateTime === null) {
            $paidDateTime = time();
        } else {
            if (!is_numeric($paidDateTime)) {
                $paidDateTime = strtotime($paidDateTime);
            }
        }
        $this->setPaymentDatetime(date('Y-m-d H:i:s', $paidDateTime));
    }

    /**
     * @return bool
     */
    public function paymentCanBeRepeated()
    {
        if ($this->isPaid()) {
            return false;
        }

        if (OrderStateManager::getStateClass($this->getOrderState()) == ShopOrderState::ORDER_STATE_CLASS_CANCELLED) {
            return false;
        }

        try {
            $payment = $this->getPaymentType();
            if ($payment) {
                if (method_exists($payment, 'supportsRepeatedPayment')) {
                    return $payment->supportsRepeatedPayment();
                }
            }
        } catch (\Exception $e) {
            ErrorReporter::reportSilent($e);
        }

        return false;
    }

    /**
     * @throws \Exception
     */
    public function repeatPayment()
    {
        if (!$this->paymentCanBeRepeated()) {
            throw new \Exception('The payment for this order can not be repeated');
        }

        $this->getPaymentType()->processOrderPayment($this);
    }

    /**
     * @return string
     */
    public function getPaymentRepeatLink()
    {
        return Url::staticUrl(config('buxus_core.base_url') . 'public/eshop/repeat_payment.php?vs=' . urlencode($this->getVariableSymbol()) . '&t=' . urlencode($this->getSecurityHash()));
    }

    /**
     * @return string|null
     */
    public function getInvoiceNumber()
    {
        return $this->getData('invoice_number');
    }

    /**
     * @param string $invoiceNumber
     */
    public function setInvoiceNumber($invoiceNumber)
    {
        $this->setData('invoice_number', $invoiceNumber);
    }

    /**
     * @return string|null
     */
    public function getInternalNote()
    {
        return $this->getData('internal_note');
    }

    /**
     * @param string|null $note
     */
    public function setInternalNote($note)
    {
        $this->setData('internal_note', $note);
    }

    /**
     * @return string|null
     */
    public function getDeliveryPhone()
    {
        return $this->getData('delivery_phone');
    }

    /**
     * @param string $deliveryPhone
     */
    public function setDeliveryPhone($deliveryPhone)
    {
        $this->setData('delivery_phone', $deliveryPhone);
    }

    /**
     * @return bool
     */
    public function isActive(): bool
    {
        return $this->getData('is_active') === 'T';
    }
}
