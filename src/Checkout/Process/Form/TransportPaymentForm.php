<?php
namespace Eshop\ShoppingCart\Checkout\Process\Form;

use Buxus\Eshop\Checkout\Process\Form\AbstractProcessForm;
use Buxus\Eshop\Contracts\DeliveryTypeFactory;
use Buxus\Eshop\Contracts\PaymentTypeFactory;
use Buxus\Eshop\Item\Delivery\GenericDeliveryType;
use Buxus\Eshop\Item\Payment\GenericPaymentType;
use Buxus\Eshop\Item\SpecialShopItemInterface;
use FormBase\Element\HiddenElement;
use FormBase\Element\LinkElement;
use FormBase\Element\SubmitElement;
use Eshop\ShoppingCart\Form\Element\TransportPaymentElement;
use Eshop\ShoppingCart\View;

class TransportPaymentForm extends AbstractProcessForm
{

    protected $payment_types = array();
    protected $transport_types = array();


    /**
     * Initialize form
     */
    public function init()
    {
        $this->loadPaymentTypes();
        $this->loadTransportTypes();

        $shopping_cart = \ShoppingCart::get();

        $transport_type_options = array();

        $allowed_payment_types = array();

        foreach ($this->transport_types as $key => $transport_type_item) {
            /**
             * @var GenericDeliveryType $transport_type_item
             */
            if (!$transport_type_item->isVisible($shopping_cart)) {
                continue;
            }

            $transport_type_options[] = $transport_type_item;

            $selected_payment_tags = [];

            // Set payment options
            $selected_payment_types = $transport_type_item->getSupportedPaymentTypes();
            foreach ($selected_payment_types as $selected_payment_type_id) {
                if (!$selected_payment_type_id->isVisible($shopping_cart)) {
                    continue;
                }

                if ($selected_payment_type_id->isEnabled($shopping_cart)) {
                    $selected_payment_tags[] = $selected_payment_type_id->getTag();
                }
                $allowed_payment_types[$selected_payment_type_id->getTag()] = true;
            }

            $transport_type_payments = new HiddenElement('transport_type_payments[' . $key . ']');
            $transport_type_payments->setValue(implode(';', $selected_payment_tags));
            $transport_type_payments->setAttrib('class', 'transport_type_payments-' . $key);
            $this->addElement($transport_type_payments);
            $transport_type_item->injectItemCustomForm($this);
        }

        // Transportation
        $transport_type = new TransportPaymentElement('transport_type');
        $transport_type->setLabel(\Trans::raw(lang('cart', 'Spôsob doručenia')));
        $transport_type->setItems($transport_type_options);
        $transport_type->setRequired(true);
        $transport_type->setAttrib('class', 'form_autosave');
        $this->addElement($transport_type);

        // Generate payment options
        $payment_type_options = array();
        foreach ($this->payment_types as $key => $payment_type_item) {
            /**
             * @var GenericPaymentType $payment_type_item
             */
            if (!isset($allowed_payment_types[$key])) {
                continue;
            }
            if (!$payment_type_item->isVisible($shopping_cart)) {
                continue;
            }
            $payment_type_options[] = $payment_type_item;
            $payment_type_item->injectItemCustomForm($this);
        }

        $payment_type = new TransportPaymentElement('payment_type');
        $payment_type->setItems($payment_type_options);
        $payment_type->setLabel(\Trans::raw(lang('cart', 'Spôsob platby')));
        $payment_type->setRequired(true);
        $payment_type->setAttrib('class', 'form_autosave');
        $this->addElement($payment_type);

        $previous_process = $this->process->getCheckout()->getPreviousProcess($this->process->getTag());
        if (!is_null($previous_process)) {
            $button = new LinkElement('back');
            $button->addClass('btn btn-cart-back');
            $button->clearValueClasses();
            $button->clearWrapperClasses();
            $button->setLabel(\Trans::raw('<i class="glyphicon glyphicon-chevron-left"></i> ' . lang('cart', 'Späť na obsah košíka')));
            $button->setAttrib('href', $previous_process->getUrl());
            $this->addElement($button);
        }

        $button = new SubmitElement($this->getFormTag());
        $button->addClass('btn-primary');
        $button->clearValueClasses();
        $button->clearWrapperClasses();
        $button->setLabel(\Trans::raw(lang('cart', 'Pokračovať v objednávke')));
        $this->addElement($button);

        /*        // Submit
                $submit = new CartSubmit($this->getFormTag());
                $submit->setLabel('Pokračovať');
                $this->addElement($submit);*/

        $this->populate($this->data);

        $this->selectSingleValuedChoices();

        // Set translation
        $this->setTranslation();
    }

    protected function selectSingleValuedChoices()
    {
        // if we have only one transport type, select it

        $directly_selected_transport_type = null;

        $shopping_cart = \ShoppingCart::get();

        $transport_type = $this->getElement('transport_type');
        if ($transport_type && method_exists($transport_type, 'getItems')) {
            $options = $transport_type->getItems();
            $eligible = [];
            foreach ($options as $transport) {
                /**
                 * @var GenericDeliveryType $transport
                 */
                if (!$transport->isVisible($shopping_cart) || !$transport->isEnabled($shopping_cart)) {
                    continue;
                }
                $eligible[] = $transport;
            }

            if (count($eligible) == 1) {
                $selected_transport_type = reset($eligible);
                if ($selected_transport_type instanceof GenericDeliveryType) {
                    $this->setDefault('transport_type', $selected_transport_type->getTag());
                    $directly_selected_transport_type = $selected_transport_type;
                }
            }
        }

        if (!is_null($directly_selected_transport_type)) {
            $supported_payments = $directly_selected_transport_type->getSupportedPaymentTypes();
            $eligible = [];

            foreach ($supported_payments as $payment) {
                /**
                 * @var GenericPaymentType $payment
                 */
                if (!$payment->isVisible($shopping_cart) || !$payment->isEnabled($shopping_cart)) {
                    continue;
                }
                $eligible[] = $payment;
            }

            if (count($eligible) == 1) {
                $selected_payment = reset($eligible);
                $this->setDefault('payment_type', $selected_payment->getTag());
            }
        }

    }

    /**
     * Load payment types
     */
    protected function loadPaymentTypes()
    {
        /**
         * @var PaymentTypeFactory $factory
         */
        $factory = app(PaymentTypeFactory::class);

        $list = $factory->getPaymentTypes();

        $this->payment_types = [];
        foreach ($list as $payment_type) {
            /**
             * @var SpecialShopItemInterface $payment_type
             */
            $this->payment_types[$payment_type->getTag()] = $payment_type;
        }
    }

    protected function loadTransportTypes()
    {
        /**
         * @var DeliveryTypeFactory $factory
         */
        $factory = app(DeliveryTypeFactory::class);

        $list = $factory->getDeliveryTypes();

        $this->transport_types = [];
        foreach ($list as $delivery_type) {
            /**
             * @var SpecialShopItemInterface $delivery_type
             */
            $this->transport_types[$delivery_type->getTag()] = $delivery_type;
        }
    }

    public function isValid($data)
    {
        $valid = true;
        if ((isset($data['transport_type'])) && (!empty($data['transport_type']))) { // The transport type is defined
            /**
             * @var GenericDeliveryType $transport_type
             */
            $transport_type = app(DeliveryTypeFactory::class)->getDeliveryTypeByTag($data['transport_type']);
            if ((isset($data['payment_type'])) && (!empty($data['payment_type']))) {
                /** @var GenericPaymentType $payment_type_item */
                $payment_type_item = app(PaymentTypeFactory::class)->getPaymentTypeByTag($data['payment_type']);
            } else {
                $payment_type_item = null;
            }

            if ($payment_type_item) {
                $valid &= $payment_type_item->isValid($data, $this, $this->getElement('transport_type'));
            }

            if (!is_null($transport_type)) {
                $valid &= $transport_type->isValid($data, $this, $this->getElement('transport_type'));
                $selected_payment_types = $transport_type->getSupportedPaymentTypes();
                $selected_payment_tags = array();
                if (is_array($selected_payment_types)) {
                    foreach ($selected_payment_types as $selected_payment_type) {
                        /**
                         * @var GenericPaymentType $selected_payment_type
                         */
                        $selected_payment_tags[] = $selected_payment_type->getTag();
                    }
                }
                $payment_type = $this->getElement('payment_type');
                $payment_type->addValidator(new \Zend_Validate_InArray($selected_payment_tags));
            }
        }

        $valid &= parent::isValid($data);

        return $valid;
    }

    public function render(\Zend_View_Interface $view = null)
    {
        $view = new View();
        $this->setView($view);

        $view->form = $this;
        $view->elements = $this->getElements();

        return $view->render('transport-payment-form.phtml');
    }
}
