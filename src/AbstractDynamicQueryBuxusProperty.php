<?php

namespace DynamicCategory;

use CBuxusText;

abstract class AbstractDynamicQueryBuxusProperty extends CBuxusText
{
    public function __construct($name, $value, $parameters = '')
    {
        $parameters .= '<size></size>';
        $parameters .= '<maxlength></maxlength>';
        $parameters .= '<readonly></readonly>';
        $parameters .= '<pattern></pattern>';
        $parameters .= '<onchange-js></onchange-js>';
        $parameters .= '<onkeyup-js></onkeyup-js>';
        $parameters .= '<onkeydown-js></onkeydown-js>';

        parent::__construct($name, $value, $parameters);
    }

    abstract protected function getQueryType();

    public function GetHTML($parameters = [])
    {
        $GLOBALS['IDDDD']++;
        $id = 'id' . $GLOBALS['IDDDD'];

        $supportsInherit = ($this->parsed_parm['inherit_value'] === 'T');
        $inherited = ($this->value === null);
        $inheritedValue = $this->getInheritedPropertyValue();

        $html = '<div style="padding:5px">';

        $realValue = $supportsInherit && $inherited ? $inheritedValue : $this->value;

        $html .= '<input style="display:none" type="text" id="' . $id .
            '" name="' . htmlspecialchars($this->name) .
            '" value="' . htmlspecialchars($realValue) . '">';

        $query = app('buxus:dynamic-query:query-manager')->createQuery(
            $this->getQueryType(),
            $realValue
        );

        $html .= $query->render($id);

        $html .= '</div>';

        if ($supportsInherit) { // The properties should by inherited
            $queryInherited = app('buxus:dynamic-query:query-manager')
                ->createQuery($this->getQueryType(), $inheritedValue);

            // Generate inherit property HTML
            $inherit_property_html = $this->getInheritPropertyButtonsHtml($inherited);
            $inherit_property_html .= '<div style="position:relative;margin:4px 0;' . ($inherited ? '' : 'display: none;') . '">';
            $inherit_property_html .= '<div class="textfield inherited_page_property_' . $this->property->PID . '">';
            $inherit_property_html .= $queryInherited->render($id . '_dummy');
            $inherit_property_html .= '</div>';
            $inherit_property_html .= '<div style="position: absolute;top:0;right:0;left:0;bottom:0;opacity:.6;background: #fdf4eb"></div>';
            $inherit_property_html .= '</div>';
            $html .= '<input type="hidden" id="property_inherit_property_inherit_' . $this->property->PID . '" name="property_inherit_' . $this->property->PID . '" value="' . ($inherited ? '1' : '0') . '" />';
            $html = $inherit_property_html . '<div class="original_page_property_' . $this->property->PID . '" ' . ($inherited ? 'style="display: none;"' : '') . '>' . $html . '</div>';
        }

        return $html;
    }

}
