<?php

namespace DynamicCategory;

class View extends \Zend_View
{
    public function __construct($rootPath = null, $config = [])
    {
        parent::__construct($config);
        $path = __DIR__ . '/views/';
        $this->setScriptPath($path);

        /**
         * @var \Zend_View $main_view
         */
        $main_view = null;
        $mvc_instance = \Zend_Layout::getMvcInstance();
        
        if (!empty($mvc_instance)) {
            $main_view = $mvc_instance->getView();
        }

        if (!empty($main_view)) {
            foreach ($main_view->getHelperPaths() as $prefix => $paths) {
                foreach ($paths as $path) {
                    $this->addHelperPath($path, $prefix);
                }
            }
        }
    }
}