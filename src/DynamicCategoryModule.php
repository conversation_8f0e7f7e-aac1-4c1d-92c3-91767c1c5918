<?php

namespace DynamicCategory;

use Buxus\Module\BuxusModule;
use Buxus\Property\PropertyRegistrar;
use DynamicCategory\Item\ItemManager;
use DynamicCategory\Manager\DynamicList;
use DynamicCategory\Property\FilterMask;
use DynamicCategory\Property\DynamicQueryProperty;

class DynamicCategoryModule extends BuxusModule
{
    public function register()
    {
        $this->customProperty('\DynamicCategory\DynamicQueryProperty');
        $this->customProperty('\DynamicCategory\DynamicQueryFilterMaskProperty');
        $this->customProperty('\DynamicCategory\DynamicQueryFsSortProperty');

        $this->publishes(array(
            __DIR__ . '/../config/' => config_path(),
        ));

        $this->publishes([
            __DIR__ . '/../migrations/' => database_path('migrations/buxus-libs/dynamic-categories/'),
        ], 'migrations');

        $this->publicPath('dynamic-query', __DIR__ . '/tool/');

        PropertyRegistrar::registerProperty(DynamicQueryProperty::class);
        PropertyRegistrar::registerProperty(FilterMask::class);

        $this->app->bindIf('buxus:dynamic-query:default-query-class', DynamicQuery::class);

        $this->app->bindIf('buxus:dynamic-query:query-manager', DynamicQueryManager::class, true);
        $this->app->bindIf('buxus:dynamic-query:item-manager', ItemManager::class, true);
        $this->app->bindIf('buxus:dynamic-query:fs-dynamic-list-manager', DynamicList::class, true);
    }

    public function boot()
    {
        $this->loadBuxusTranslationsFrom(__DIR__ . '/../buxus-lang', 'dynamic-category');
        $this->loadViewsFrom(__DIR__ . '/views', 'dynamic-category');
    }
}
