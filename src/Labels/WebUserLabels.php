<?php

namespace Buxus\WebUser\Labels;

use ArrayAccess;
use AuthenticationLog;
use BuxusDB;
use Iterator;

class WebUserLabels implements Iterator, ArrayAccess
{
    protected $userId;

    public function __construct($userId)
    {
        $this->userId = $userId;
    }

    /**
     * @var WebUserLabel[]
     */
    protected $labels = null;

    protected function loadLabels()
    {
        if (!is_null($this->labels)) {
            return;
        }

        $items = BuxusDB::get()
            ->fetchCol(
                "SELECT label FROM tblWebUserLabels WHERE user_id = :user_id",
                [':user_id' => $this->userId]
            );
        /**
         * @var \Buxus\WebUser\Labels\WebUserLabelsManager $userLabelsManager
         */
        $userLabelsManager = app('buxus:webuser:labels-manager');
        $labels = array();
        foreach ($items as $labelTag) {
            $label = $userLabelsManager->resolveLabel($labelTag);
            if (!is_null($label)) {
                $labels[$labelTag] = $label;
            }
        }

        $this->labels = $labels;
    }

    /**
     * @return WebUserLabel[]
     */
    public function getLabels()
    {
        $this->loadLabels();

        return $this->labels;
    }

    public function rewind()
    {
        $this->loadLabels();
        reset($this->labels);
    }

    public function current()
    {
        $this->loadLabels();

        return current($this->labels);
    }

    public function key()
    {
        $this->loadLabels();

        return key($this->labels);
    }

    public function next()
    {
        $this->loadLabels();
        next($this->labels);
    }

    public function valid()
    {
        $this->loadLabels();

        return ($this->current() !== false);
    }

    public function addLabel($labelTag)
    {
        $this->loadLabels();
        if (isset($this->labels[$labelTag])) {
            return;
        }
        /**
         * @var \Buxus\WebUser\Labels\WebUserLabelsManager $userLabelsManager
         */
        $userLabelsManager = app('buxus:webuser:labels-manager');
        $label = $userLabelsManager->resolveLabel($labelTag);
        if (!is_null($label)) {
            $this->labels[$labelTag] = $label;
        }
    }

    public function removeLabel($labelTag)
    {
        if (isset($this->labels[$labelTag])) {
            unset($this->labels[$labelTag]);
        }
    }

    public function offsetSet($offset, $value)
    {
        if (!empty($offset)) {
            if ($value) {
                $this->addLabel($offset);
            } else {
                $this->removeLabel($offset);
            }
        }
    }

    public function offsetExists($offset)
    {
        $this->loadLabels();

        return isset($this->labels[$offset]);
    }

    public function offsetUnset($offset)
    {
        $this->removeLabel($offset);
    }

    public function offsetGet($offset)
    {
        $this->loadLabels();
        if (isset($this->labels[$offset])) {
            return $this->labels[$offset];
        }

        return null;
    }

    public function save()
    {
        $this->loadLabels();
        $actual = BuxusDB::get()
            ->fetchCol(
                "SELECT label FROM tblWebUserLabels WHERE user_id = :user_id",
                [':user_id' => $this->userId]
            );
        $all = array_keys($this->labels);

        $new = array_diff($all, $actual);
        $deleted = array_diff($actual, $all);

        if (count($deleted)) {
            $stmt = BuxusDB::get()
                ->prepare(
                    "DELETE FROM tblWebUserLabels WHERE user_id = :user_id AND label = :label"
                );
            foreach ($deleted as $label) {
                $stmt->execute(array(
                    ':user_id' => $this->userId,
                    ':label' => $label,
                ));
            }
        }

        if (count($new)) {
            $stmt = BuxusDB::get()
                ->prepare(
                    "REPLACE INTO tblWebUserLabels (user_id, label) VALUES (:user_id, :label)"
                );
            foreach ($new as $label) {
                $stmt->execute(array(
                    ':user_id' => $this->userId,
                    ':label' => $label,
                ));
            }
        }
    }

    public function syncLabels($newLabels)
    {
        $oldLabels = $this->getLabels();
        $oldLabels = array_keys($oldLabels);
        foreach ($newLabels as $new) {
            if (!in_array($new, $oldLabels)) {
                $this->addLabel($new);
            }
        }
        foreach ($oldLabels as $old) {
            if (!in_array($old, $newLabels)) {
                $this->removeLabel($old);
            }
        }
        $this->save();

        $diff = array_diff($newLabels, $oldLabels);
        if (count($diff) || count($newLabels) != count($oldLabels)) {
            $authUserLog = new AuthenticationLog($this->userId, AuthenticationLog::ACTION_LABELS_EDIT);
            $authUserLog->saveAction(implode(',', $newLabels));
        }
    }

    public function getOrderListLabels()
    {
        $this->loadLabels();
        $toList = [];
        foreach ($this->labels as $label) {
            if ($label->showInOrders()) {
                $toList[] = (string)$label;
            }
        }
        if (count($toList)) {
            return '&nbsp;' . implode(' ', $toList);
        }

        return '';
    }
}
