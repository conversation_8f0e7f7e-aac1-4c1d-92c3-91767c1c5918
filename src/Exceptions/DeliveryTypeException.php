<?php

namespace Buxus\Eshop\Exceptions;

class DeliveryTypeException extends EshopException
{
    /**
     * @param $deliveryTypeTag
     *
     * @return DeliveryTypeException
     */
    public function setDeliveryTypeTag($deliveryTypeTag): DeliveryTypeException
    {
        $this->addTag('delivery_type', (string)$deliveryTypeTag);

        return $this;
    }

    /**
     * @param $pageId
     *
     * @return DeliveryTypeException
     */
    public function setPageId($pageId): DeliveryTypeException
    {
        $this->addData('deliveryPageId', $pageId);

        return $this;
    }

    /**
     * @param $pageId
     *
     * @return DeliveryTypeException
     */
    public static function missingTagException($pageId): DeliveryTypeException
    {
        return (new static("Missing `eshop_tag`."))
            ->setPageId($pageId);
    }

    /**
     * @param $tag
     *
     * @return DeliveryTypeException
     */
    public static function invalidTagException($tag): DeliveryTypeException
    {
        return (new static("Invalid delivery type tag."))
            ->setDeliveryTypeTag($tag);
    }
}
