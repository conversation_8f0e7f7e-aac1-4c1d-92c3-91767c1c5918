<?php

namespace DynamicCategory;

class DynamicQueryFilterMaskProperty extends \CBuxusCheckboxGroup
{
    /**
     * Constructor
     *
     * @param string $name
     * @param string $value
     * @param string $parameters
     */
    public function __construct($name, $value, $parameters = '')
    {
        $parameters .= '<sql></sql>';
        $parameters .= '<multiple></multiple>';
        $parameters .= '<show_as_list></show_as_list>';
        $parameters .= '<empty_option></empty_option>';
        $parameters .= '<size></size>';
        $parameters .= '<cols></cols>';
        $parameters .= '<rows></rows>';
        $parameters .= '<onchange-js></onchange-js>';
        $parameters .= '<disabled></disabled>';
        $parameters .= '<check_read_rights></check_read_rights>';

        parent::__construct($name, $value, $parameters);
    }

    public function getOptions($paramName = "options", $forceNoSql = false)
    {
        if ($paramName === 'options') {
            $items = config('dynamic_categories.filter_mask_tags', []);
            if (!is_array($items)) {
                $items = [];
            }

            $moreConfigs = config('dynamic_categories_items', []);
            foreach ($moreConfigs as $moreConfig) {
                if (isset($moreConfig['filter_mask_tags']) && is_array($moreConfig['filter_mask_tags'])) {
                    foreach ($moreConfig['filter_mask_tags'] as $tag => $class) {
                        $items[$tag] = $class;
                    }
                }
            }

            return $items;
        }

        return parent::getOptions($paramName, $forceNoSql);
    }
}
