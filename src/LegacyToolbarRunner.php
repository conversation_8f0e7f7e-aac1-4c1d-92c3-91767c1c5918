<?php

namespace Buxus\Toolbar;

class LegacyToolbarRunner
{
    public function run()
    {
        if ($_SERVER['REQUEST_URI'] == '/buxus' || $_SERVER['REQUEST_URI'] == '/buxus/') {
            header('Location: /buxus/toolbar/index/index');
            exit;
        }

        define('ZF_LIBRARY_PATH', realpath(__DIR__ . '/../buxus/'));
        define('BUXUS_TOOLBAR_APPLICATION_PATH', __DIR__ . '/../buxus/application');

        set_include_path(implode(PATH_SEPARATOR, array(
            ZF_LIBRARY_PATH,
            BUXUS_TOOLBAR_APPLICATION_PATH.'/zend_models',
            BUXUS_TOOLBAR_APPLICATION_PATH.'/library',
            get_include_path(),
        )));

        $application = new \Zend_Application(
            'production',
            BUXUS_TOOLBAR_APPLICATION_PATH . '/configs/application.ini'
        );

        $application->bootstrap();
        $application->run();
    }

    public function showLogin()
    {
        if (!\Buxus\Core\Facades\BuxusAuthentication::isAuthenticated()) {
            app('buxus:toolbar')->setShowLogin();
        }
        return redirect('/buxus/system/toolbar/index/index');
    }
}
