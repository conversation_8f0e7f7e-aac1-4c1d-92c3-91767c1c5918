<?php

namespace DynamicCategory\Util;

class SqlFragment
{
    const JOIN_STRAIGHT = 0;
    const JOIN_LEFT = 1;

    protected $joins = [];
    protected $where = '';

    protected $tableAliasPrefix;

    protected $joinCounter = 0;

    public function __construct($tableAliasPrefix = 'sf')
    {
        $this->tableAliasPrefix = $tableAliasPrefix;
    }

    public function getNewTableAlias()
    {
        $alias = $this->tableAliasPrefix . $this->joinCounter;
        $this->joinCounter++;
        return $alias;
    }

    public function addJoin($table, $conditions, $type = self::JOIN_STRAIGHT, $alias = false)
    {
        if ($alias === false) {
            $alias = $this->getNewTableAlias();
        }

        if (!is_array($conditions)) {
            $conditions = [$conditions];
        }

        $join = "`" . $table . "` AS " . $alias . "\n\tON (" . implode(' AND ', $conditions) . ")";

        $join = preg_replace('@\[%\]@', $alias, $join);

        $this->joins[] = ($type == self::JOIN_STRAIGHT ? "\nJOIN\n\t" : "\nLEFT JOIN\n\t") . $join;

        return $alias;
    }

    public function getJoins()
    {
        return $this->joins;
    }
}