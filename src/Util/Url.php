<?php

namespace DynamicCategory\Util;

class Url
{
    public static function pathToUrl($path)
    {
        $install_dir = base_path('buxus');
        $result = preg_replace('@' . preg_quote($install_dir, '@') . '@', config('buxus_core.base_url'), $path);

        if ($result == $path) {
            $install_dir = preg_replace('@' . preg_quote(config('buxus_core.base_url'), '@') . '$@', '/', $install_dir);
            $result = preg_replace('@' . preg_quote($install_dir, '@') . '@', '/', $path);
        }

        return $result;
    }
}