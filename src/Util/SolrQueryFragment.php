<?php

namespace DynamicCategory\Util;

class SolrQueryFragment
{
    protected $queries = [];
    protected $filterQueries = [];

    public function addQuery($query)
    {
        $this->queries[] = $query;
    }

    public function addFilterQuery($tag, $query)
    {
        $this->filterQueries[$tag] = $query;
    }

    public function getQueries()
    {
        return $this->queries;
    }

    public function getFilterQueries()
    {
        return $this->filterQueries;
    }


}