<?php

namespace Buxus\ImageProcessor;

use Buxus\Stdlib\ArrayUtils;

class ImageProcessor
{
    /**
     * @var ImageProcessorProviderInterface
     */
    protected $provider = null;

    protected $tag;

    protected $tagParams = [];

    protected $overridableOptions = [
        'no_image' => 'noImage',
        'result_extension' => 'resultExtension',
        'result_format' => 'resultFormat',
        'tag_constructor' => 'tagConstructor',
        'source_url' => 'sourceUrl',
        'source_path' => 'sourcePath',
        'destination_url' => 'destinationUrl',
        'destination_path' =>'destinationPath',
    ];

    protected $noImage = '';
    protected $resultExtension = '';
    protected $resultFormat = '';
    protected $tagConstructor = null;
    protected $sourceUrl = '';
    protected $sourcePath = '';
    protected $destinationUrl = '';
    protected $destinationPath = '';

    /**
     * @var ImageOperationInterface[]
     */
    protected $operations = [];

    /**
     * @param string $tag
     * @param ImageProcessorProviderInterface $provider
     * @param mixed $config
     * @param array $tagParams
     */
    public function __construct($tag, ImageProcessorProviderInterface $provider, $config, $tagParams = array())
    {
        $this->tag = $tag;
        $this->provider = $provider;
        $this->tagParams = $tagParams;

        $this->processConfig($config, $tagParams);
    }

    protected function getDefaultParams()
    {
        $params = array();

        foreach ($this->overridableOptions as $key => $paramName) {
            $params[$key] = $this->{$paramName};
        }

        $params['tag'] = $this->getTag();

        return $params;
    }

    protected function setOperations($config, $tagParams)
    {
        if (!is_array($config)) {
            $config = array($config);
        }

        $params = ArrayUtils::merge($this->getDefaultParams(), $tagParams);

        $this->resetOperations();
        foreach ($config as $name => $options) {
            $operation = null;
            if (is_object($options)) {
                if ($options instanceof ImageOperationInterface) {
                    $operation = $options;
                }
            } else {
                $operation = $this->provider->constructOperation($name, $options, $params);
            }

            if (is_null($operation)) {
                throw new Exception\InvalidArgumentException('Invalid image processor operation');
            }

            $this->addOperation($operation);
        }
    }

    protected function processConfig($config, $tagParams)
    {
        $configParams = ArrayUtils::merge(array('tag' => $this->getTag()), $tagParams);

        foreach ($this->overridableOptions as $key => $paramName) {
            if (isset($config[$key])) {
                if (property_exists($this, $paramName)) {
                    $value = $config[$key];
                    if (is_string($value)) {
                        $value = preg_replace_callback('@%([^%]+)%@', function ($matches) use ($configParams) {
                            if (isset($configParams[$matches[1]])) {
                                return $configParams[$matches[1]];
                            }
                            return '';
                        }, $value);
                    }

                    $this->{$paramName} = $value;
                }
            }
        }

        if (isset($config['operations'])) {
            $operations = $config['operations'];
        } else {
            $operations = array();
        }

        if (isset($config['pre_operations'])) {
            $operations = ArrayUtils::merge($config['pre_operations'], $operations);
        }
        if (isset($config['post_operations'])) {
            $operations = ArrayUtils::merge($operations, $config['post_operations']);
        }
        $this->setOperations($operations, $tagParams);
    }

    public function addOperation(ImageOperationInterface $operation)
    {
        $this->operations[] = $operation;
    }

    public function resetOperations()
    {
        $this->operations = [];
    }

    /**
     * @param string $imageName
     * @return ImageResultInterface
     */
    public function prepareImageResult($imageName)
    {
        $image = $this->provider->createImageResult();
        $image->setParam(ImageResultInterface::IMAGE_NAME, $imageName);
        $image->setParam(ImageResultInterface::TAG_PARAMS, $this->tagParams);

        foreach ($this->operations as $operation) {
            $image = $operation->processMetadata($image);
        }

        return $image;
    }

    /**
     * @param ImageResultInterface $image
     * @return ImageResultInterface
     */
    public function process(ImageResultInterface $image)
    {
        foreach ($this->operations as $operation) {
            if (!$operation instanceof ApplyOperationAlsoToBypassedImageInterface &&
                $image->getParam(ImageResultInterface::BYPASS_PROCESS_AND_COPY) === true
            ) {
                continue;
            }

            $image = $operation->process($image);
        }
        return $image;
    }

    public function setResultExtension($resultExtension)
    {
        $this->resultExtension = $resultExtension;
    }

    public function getSuggestedResultName($imageFile)
    {
        $result = $imageFile;
        $extension = pathinfo($imageFile, PATHINFO_EXTENSION);

        if (mb_strtolower($extension) != mb_strtolower($this->resultExtension)) {
            $result .= '.' . $this->resultExtension;
        }

        return $result;
    }

    protected $realTag = null;

    public function getTag()
    {
        if (is_null($this->realTag)) {
            if (!is_null($this->tagConstructor)) {
                $tagParams = $this->tagParams;
                $tag = preg_replace_callback('@%([^%]+)%@', function ($matches) use ($tagParams) {
                    if (isset($tagParams[$matches[1]])) {
                        return $tagParams[$matches[1]];
                    }
                    return '';
                }, $this->tagConstructor);
                $this->realTag = $tag;
            } else {
                $this->realTag = $this->tag;
            }
        }
        return $this->realTag;
    }

    public function setTagConstructor($tagConstructor)
    {
        $this->tagConstructor = $tagConstructor;
    }
}
