<?php

namespace DynamicCategory;

use Buxus\View\View;
use DynamicCategory\Item\AbstractItem;
use DynamicCategory\Util\SolrQueryFragment;
use DynamicCategory\Util\SqlFragment;
use FS\DatabaseBackedFSSearchInterface;
use FS\Facades\FSFactory;
use FS\FSSearchInterface;
use FS\Property\FSPropertyInterface;
use FS\Property\RangePropertyInterface;
use FS\Search;
use FS\SolrBackedFSSearchInterface;

class DynamicQuery
{
    /**
     * @var \DynamicCategory\Item\AbstractItem
     */
    protected $item;

    /** @var string */
    protected $queryType;

    /** @var string */
    protected $fsTag;

    /** @var array */
    protected $staticItems = [];

    /** @var string */
    protected $sort;

    /** @var int */
    protected $sort_mode;

    /** @var FSSearchInterface */
    protected $fs;

    /**
     * @param $queryType
     */
    public function setQueryType($queryType)
    {
        $this->queryType = $queryType;
    }

    /**
     * @param $fsTag
     */
    public function setFsTag($fsTag)
    {
        $this->fsTag = $fsTag;
    }

    /**
     * @param $query
     */
    public function setQuery($query)
    {
        $this->decodeQuery($query);
    }

    /**
     * @param $items
     */
    public function setStaticItems($items)
    {
        $this->staticItems = $items;
    }

    /**
     * @return string
     */
    public function getQueryType()
    {
        return $this->queryType;
    }

    /**
     * @param $sort
     */
    public function setSort($sort)
    {
        $this->sort = $sort;
    }

    /**
     * @param $sort_mode
     */
    public function setSortMode($sort_mode)
    {
        $this->sort_mode = $sort_mode;
    }

    /**
     * @param $data
     * @return AbstractItem
     */
    protected function createItem($data)
    {
        $item_type = $data['type'];
        $item_data = $data['data'];
        $item_inverted = (isset($data['inv']) ? ($data['inv'] == 1) : false);
        $item_id = (isset($data['id']) ? $data['id'] : null);

        /** @var AbstractItem $item */
        $item = app('buxus:dynamic-query:item-manager')->create($item_type, $item_id, $item_data, $item_inverted);
        $item->setParentQuery($this);

        if (isset($data['children']) && is_array($data['children'])) {
            foreach ($data['children'] as $child_data) {
                $child = $this->createItem($child_data);
                $item->addChild($child);
            }
        }

        return $item;
    }

    /**
     * @param $query
     */
    protected function decodeQuery($query)
    {
        $data = @json_decode($query, true);

        if (!is_array($data)) {
            return;
        }

        $this->item = $this->createItem($data);
    }

    /**
     * @param $id
     * @return string
     */
    protected function renderTree($id)
    {
        $html = '<ul>';
        if (!is_null($this->item)) {
            $html .= $this->item->renderTree($id);
        }
        $html .= '</ul>';

        return $html;
    }

    /**
     * @param $id
     * @return string
     */
    protected function renderPanes($id)
    {
        if (is_null($this->item)) {
            $panes = [];
        } else {
            $panes = $this->item->getPanes($id);
        }

        return implode('', $panes);
    }

    /**
     * @param $id
     * @return string
     */
    protected function getContents($id)
    {
        $view = new View(__DIR__, 'dynamic-categories');

        $view->query = $this;
        $view->tree = $this->renderTree($id);
        $view->panes = $this->renderPanes($id);

        $view->include_add = (is_null($this->item));

        return $view->render('contents.phtml');
    }

    /**
     * @param $destination_id
     * @return string
     */
    public function render($destination_id)
    {
        $view = new View(__DIR__, 'dynamic-categories');

        $view->destination_id = $destination_id;
        $view->query = $this;
        $view->contents = $this->getContents($destination_id);

        $view->ajax_url = $this->getAjaxUrl();
        $view->assets_url = $this->getAssetsUrl();

        $view->redis_root = config('buxus_core.base_url');

        return $view->render('query.phtml');
    }

    /**
     * @param bool $include_id
     * @return false|string
     */
    public function build($include_id = false)
    {
        if (is_null($this->item)) {
            return '';
        }

        return json_encode($this->item->build($include_id));
    }

    /**
     * @param $parentId
     * @param $itemType
     */
    public function addItem($parentId, $itemType)
    {
        /** @var AbstractItem $item */
        $item = app('buxus:dynamic-query:item-manager')->create($itemType, null, null, false);
        $item->setParentQuery($this);

        if (is_null($this->item)) {
            $this->item = $item;
        } else {
            $parent = $this->item->getChild($parentId);
            if (!is_null($parent)) {
                $parent->addChild($item);
            }
        }
    }

    /**
     * @param $id
     */
    public function removeItem($id)
    {
        if (!is_null($this->item)) {
            if ($id == $this->item->getId()) {
                $this->item = null;
            } else {
                $item = $this->item->getChild($id);
                if (!is_null($item)) {
                    $item->remove();
                }
            }
        }
    }

    /**
     * @param int $id
     * @return AbstractItem
     */
    public function getChild($id)
    {
        if (is_null($this->item)) {
            return null;
        }

        return $this->item->getChild($id);
    }

    /**
     * @param $id
     * @param $data
     */
    public function setData($id, $data)
    {
        $child = $this->getChild($id);
        if (!is_null($child)) {
            $child->setData($data);
        }
    }

    /**
     * @param $name
     * @param $data
     * @return mixed
     */
    protected function requireParam($name, $data)
    {
        if (!isset($data[$name])) {
            throw new \InvalidArgumentException('Required command parameter missing: ' . $name);
        }

        return $data[$name];
    }

    /**
     * @param $id
     * @return array
     */
    protected function getAjaxData($id)
    {
        return [
            'html' => $this->getContents($id),
            'query' => $this->build(),
            'query_edit' => $this->build(true),
        ];
    }

    /**
     * @param $command
     * @param $data
     * @param $destinationId
     * @return array
     */
    public function processCommand($command, $data, $destinationId)
    {
        switch ($command) {
            case 'add':
                $parent_id = $this->requireParam('parent_id', $data);
                $item_type = $this->requireParam('item_type', $data);
                $this->addItem($parent_id, $item_type);
                break;
            case 'remove':
                $id = $this->requireParam('id', $data);
                $this->removeItem($id);
                break;
            case 'set':
                $id = $this->requireParam('id', $data);
                $set_data = $this->requireParam('data', $data);
                $this->setData($id, $set_data);
                break;
            case 'noop':
                break;
            default:
                throw new \InvalidArgumentException('Invalid command: ' . $command);
                break;
        }
        return $this->getAjaxData($destinationId);
    }

    /**
     * @return string
     */
    public function getAjaxUrl()
    {
        return config('buxus_core.base_url') . 'public/dynamic-query/ajax.php';
    }

    /**
     * @return string
     */
    public function getAssetsUrl()
    {
        return config('buxus_core.base_url') . 'public/dynamic-query/';
    }

    /**
     * @param FSSearchInterface $fs
     * @throws \Exception
     */
    public function apply(FSSearchInterface $fs)
    {
        if ($fs instanceof DatabaseBackedFSSearchInterface) {
            $this->applyToDatabaseFacet($fs);
            return;
        }

        if ($fs instanceof SolrBackedFSSearchInterface) {
            $this->applyToSolrFacet($fs);
            return;
        }

        throw new \Exception('Invalid FS search type: ' . get_class($fs));
    }

    /**
     * @param DatabaseBackedFSSearchInterface $fs
     */
    protected function applyToDatabaseFacet(DatabaseBackedFSSearchInterface $fs)
    {
        /**
         * @var SqlFragment $fragment
         */
        list($sql, $fragment) = $this->generateDatabaseQuery($fs);

        if (!empty($sql)) {
            $fs->addCustomCondition('dynamic_query', $sql, implode(' ', $fragment->getJoins()));
        } else {
            $fs->addCustomCondition('dynamic_query', null);
        }

        $price = $fs->getProperty('price');
        if (!is_null($price) && $price instanceof RangePropertyInterface) {
            $price->markMinMaxReprocess();
        }

        if ($this->sort) {
            /**
             * @var Search $fs
             */
            $fs->setSortMode($this->sort, $this->sort_mode);
        }
    }

    /**
     * @param SolrBackedFSSearchInterface $fs
     */
    protected function applyToSolrFacet(SolrBackedFSSearchInterface $fs)
    {
        /**
         * @var SolrQueryFragment $fragment
         */
        list($solr_query, $fragment) = $this->generateSolrQuery($fs);

        if (!empty($solr_query)) {
            $fs->setCustomSolrQuery('dynamic_query', '(' . $solr_query . ')');
        }

        $custom_filters = $fragment->getFilterQueries();
        foreach ($custom_filters as $tag => $filter_query) {
            $fs->setCustomSolrFilterQuery($tag, $filter_query);
        }

        if ($this->sort) {
            /**
             * @var Search $fs
             */
            $fs->setSortMode($this->sort, $this->sort_mode);

        }
    }

    /**
     * @param FSSearchInterface $fs
     * @param $limit_to_page_id
     * @return string
     */
    public function buildQueryFromFS(FSSearchInterface $fs, $limit_to_page_id)
    {
        return $this->getDatabaseQueryFromFS($fs, $limit_to_page_id)->buildQuery();
    }

    /**
     * @param DatabaseBackedFSSearchInterface $fs
     * @return array
     */
    protected function generateDatabaseQuery(DatabaseBackedFSSearchInterface $fs)
    {
        $fragment = new SqlFragment('dq');
        $sql = '';
        if (!is_null($this->item)) {
            $sql = $this->item->buildQuery($fs, $fragment);
        }

        if (count($this->staticItems)) {
            $query = $fs->getMainTableName() . '.page_id IN (' . implode(',', $this->staticItems) . ')';
            if (empty($sql)) {
                $sql = $query;
            } else {
                $sql = '((' . $sql . ') OR ' . $query . ')';
            }
        }

        return [
            $sql,
            $fragment,
        ];
    }

    /**
     * @param SolrBackedFSSearchInterface $fs
     * @return array
     */
    protected function generateSolrQuery(SolrBackedFSSearchInterface $fs)
    {
        $solr_query = '';
        $fragment = new SolrQueryFragment();
        if (!is_null($this->item)) {
            $solr_query = $this->item->buildSolrQuery($fs, $fragment);
        }

        if (count($this->staticItems)) {
            if (empty($solr_query)) {
                $solr_query = '{!terms f=entity_id}' . implode(',', $this->staticItems);
            } else {
                $solr_query = '(' . $solr_query . ' OR {!terms f=entity_id}' . implode(',', $this->staticItems) . ')';
            }
        }

        return [
            $solr_query,
            $fragment,
        ];
    }

    /**
     * @param DatabaseBackedFSSearchInterface $fs
     * @param int $limit_to_page_id
     * @return \FS\QueryBuilder
     */
    protected function getDatabaseQueryFromFS(DatabaseBackedFSSearchInterface $fs, $limit_to_page_id = null)
    {
        /**
         * @var SqlFragment $fragment
         */
        list($sql, $fragment) = $this->generateDatabaseQuery($fs);

        $query = $fs->buildPrivateQuery(
            $sql,
            implode(' ', $fragment->getJoins()),
            [],
            $this->sort,
            $this->sort_mode
        );

        if (!empty($limit_to_page_id)) {
            $query->addWhere($fs->getMainTableName() . '.page_id = ' . (int)$limit_to_page_id);
        }

        return $query;
    }

    /**
     * @param SolrBackedFSSearchInterface $fs
     * @return \Solarium\QueryType\Select\Query\Query
     */
    protected function getSolrQueryFromFS(SolrBackedFSSearchInterface $fs)
    {
        /**
         * @var SolrQueryFragment $fragment
         */
        list($solr_query, $fragment) = $this->generateSolrQuery($fs);

        $queries = [];
        if (!empty($solr_query)) {
            $queries[] = $solr_query;
        }

        $query = $fs->buildPrivateQuery(
            ['entity_id'],
            $queries,
            $fragment->getFilterQueries(),
            $this->sort,
            $this->sort_mode
        );

        return $query;
    }

    /**
     * @return FSSearchInterface
     */
    protected function getDefaultFS()
    {
        if (is_null($this->fs)) {
            $this->fs = FSFactory::cloneBasicInstance($this->fsTag);
        }

        return $this->fs;
    }

    /**
     * @param null $limit_to_page_id
     * @return string
     */
    public function buildQuery($limit_to_page_id = null)
    {
        $fs = $this->getDefaultFS();
        $sql = $this->buildQueryFromFS($fs, $limit_to_page_id);

        return $sql;
    }

    /**
     * @param DatabaseBackedFSSearchInterface $fs
     * @param null $limit
     * @return array
     */
    protected function selectPagesDatabase(DatabaseBackedFSSearchInterface $fs, $limit = null)
    {
        $query_builder = $this->getDatabaseQueryFromFS($fs);

        if (!empty($limit)) {
            $query_builder->setLimit($limit);
        }

        if (!empty($this->sort)) {
            $sort_query = $this->sort . ' ';
            if ($this->sort_mode == FSPropertyInterface::SORT_DESC) {
                $sort_query .= 'DESC';
            } else {
                $sort_query .= 'ASC';
            }

            $query_builder->addOrderBy($sort_query);
        }

        $items = \BuxusDB::get()->fetchCol($query_builder->buildQuery(), $query_builder->getParams());

        return $items;
    }

    /**
     * @param SolrBackedFSSearchInterface $fs
     * @param null $limit
     * @return array
     */
    protected function selectPageSolr(SolrBackedFSSearchInterface $fs, $limit = null)
    {
        $connection = $fs->getSolrConnection();

        $query = $this->getSolrQueryFromFS($fs);
        if (!empty($limit)) {
            $query->setRows($limit);
        }

        if (!empty($this->sort)) {
            if ($this->sort_mode == FSPropertyInterface::SORT_DESC) {
                $sort_direction = 'desc';
            } else {
                $sort_direction = 'asc';
            }

            $query->addSort($connection->mapPropertyTagToSolrField($this->sort), $sort_direction);
        }

        $result_set = $connection->getClient()->select($query);

        $items = [];
        foreach ($result_set as $row) {
            $items[] = $row['entity_id'];
        }

        return $items;
    }

    /**
     * @param null $limit
     * @return array
     * @throws \Exception
     */
    public function selectPages($limit = null)
    {
        $fs = $this->getDefaultFS();

        if ($fs instanceof DatabaseBackedFSSearchInterface) {
            return $this->selectPagesDatabase($fs, $limit);
        }

        if ($fs instanceof SolrBackedFSSearchInterface) {
            return $this->selectPageSolr($fs, $limit);
        }

        throw new \Exception('Invalid FS search type: ' . get_class($fs));
    }

    /**
     * @return mixed
     */
    public function getAddableItems()
    {
        return app('buxus:dynamic-query:item-manager')->getAddableTypes($this->queryType);
    }
}
