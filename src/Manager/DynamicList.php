<?php

namespace DynamicCategory\Manager;

use Buxus\Page\PageInterface;
use DynamicCategory\DynamicQuery;
use FS\DatabaseBackedFSSearchInterface;
use FS\SolrBackedFSSearchInterface;
use FS\Util\FilterMask;
use FS\FSSearchInterface;
use FS\Manager\ManagerInterface;

class DynamicList implements ManagerInterface
{
    /**
     * @var DynamicQuery
     */
    protected $query = null;

    protected $filter = null;

    public function __construct(PageInterface $page)
    {
        $this->query = app('buxus:dynamic-query:query-manager')
            ->createQuery(
                'basic',
                $page->getValue('query'),
                $this->getStaticItems($page),
                $page->getValue('fs_sort')
            );
        $this->filter = $page->getValue('filter_mask');
    }

    public function isReset(FSSearchInterface $fs, PageInterface $page)
    {
        return ($fs->getCurrentPageId() != $page->getValue('page_id'));
    }

    public function cleanup(FSSearchInterface $fs)
    {
        $fs->setCurrentPageId(null);

        if ($fs instanceof DatabaseBackedFSSearchInterface) {
            $fs->addCustomCondition('dynamic_query', null);
        }

        if ($fs instanceof SolrBackedFSSearchInterface) {
            $fs->setCustomSolrQuery('dynamic_query', null);
        }

        $fs->resetAllFilters();
        FilterMask::resetVisibility($fs, $this->filter);
        $fs->resetSort();
    }

    public function apply(FSSearchInterface $fs, PageInterface $page)
    {
        $fs->setCurrentPageId($page->getPageId());

        $this->query->apply($fs);

        FilterMask::apply($fs, $this->filter);
    }

    protected function getStaticItems(PageInterface $page)
    {
        $tmp = $page->getValue('product_list');
        $list = [];

        if (is_array($tmp)) {
            foreach ($tmp as $row) {
                $list[] = $row['to_page_id'];
            }
        }

        return $list;
    }
}
