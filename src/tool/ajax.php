<?php

require_once 'includes/functions.php';
require_once 'includes/validate.php';

function strip($array)
{
    if (!is_array($array)) {
        $array = stripslashes($array);
    } else {
        foreach ($array as $key => $value) {
            $array[$key] = strip($array[$key]);
        }
    }
    return $array;
}

if (get_magic_quotes_gpc()) {
    $_POST = strip($_POST);
    $_GET = strip($_GET);
}

try {
    $required_params = array(
        'type',
        'query',
        'command',
        'data',
        'id',
    );
    $params = array();
    foreach ($required_params as $key) {
        if (!isset($_POST[$key])) {
            throw new \InvalidArgumentException('Required parameter not set: ' . $key);
        }
        $params[$key] = $_POST[$key];
    }

    $command_data = @json_decode($params['data'], true);
    if (is_null($command_data)) {
        throw new \InvalidArgumentException('Invalid command data provided');
    }

    $query = app('buxus:dynamic-query:query-manager')->createQuery($params['type'], $params['query']);

    $data = $query->processCommand($params['command'], $command_data, $params['id']);

    $result = array(
        'result' => 'OK',
        'data' => $data,
    );
} catch (Exception $e) {
    $result = array(
        'result' => 'ERR',
        'error' => $e->getMessage(),
    );
}

header('Content-type: application/json');
echo json_encode($result);
exit;