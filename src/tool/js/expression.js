define(['jquery', 'dynamic-query/jstree/jquery.jstree', 'jquery-json'], function (jQuery) {
    var $ = jQuery;
    jQuery.fn.DynamicQueryManager = function (options) {
        options = jQuery.extend({
            destination_id: '',
            url: '',
            type: ''
        }, options);

        var $manager = this;
        this.tree = null;

        this.current_query = $('#' + options.destination_id).val();
        this.current_edit_query = $('#' + options.destination_id).val();

        this.selected_pane = null;

        this.stored_state = [];

        this.history = [];

        this.buildTree = function (elm) {
            var tree = $(elm);

            $(elm).jstree({
                "ui": {
                    "select_limit": 1,
                    "selected_parent_close": "select_parent"
                },
                core: {
                    animation: 0,
                    initially_open: $manager.stored_state
                },
                "plugins": ['themes', 'html_data', 'ui']
            }).bind('select_node.jstree', function (event, data) {
                $manager.closeActivePane();
                id = $(data.rslt.obj).attr('id');
                $('#pane-' + id).show();
                $manager.selected_pane = 'pane-' + id;
            });
            return tree;
        };

        this.buildPanes = function (elm) {
            // bind setData events
            $(elm).find('button.cmd_set').click(function () {
                data = $manager.gatherFormData($(this).closest('.form'));
                var cmd_data = {
                    id: data['id'],
                    data: data
                };
                $manager.command('set', cmd_data);
                return false;
            });
            $(elm).find('button.cmd_close').click(function () {
                $manager.closeActivePane();
                return false;
            });
            $(elm).find('button.cmd_add').click(function () {
                data = $manager.gatherFormData($(this).closest('.form'));
                $manager.command('add', data);
                return false;
            });
            $(elm).find('button.cmd_remove').click(function () {
                data = $manager.gatherFormData($(this).closest('.form'));
                $manager.command('remove', data);
                return false;
            });
            $($manager).find('button.cmd_undo').each(function () {
                if ($manager.history.length > 0) {
                    $(this).click(function () {
                        $manager.current_edit_query = $manager.historyPopState();
                        $manager.command('noop', {});
                        return false;
                    });
                    $(this).removeAttr('disabled');
                } else {
                    $(this).attr('disabled', 'disabled');
                }
            });
        };

        this.gatherFormData = function (elm) {
            var data = {};
            $(elm).find('input').each(function () {
                var key = $(this).attr('name');
                var value = '';
                if ($(this).attr('type') == 'checkbox') {
                    if ($(this).is(':checked')) value = $(this).val();
                } else {
                    value = $(this).val();
                }
                if (key) {
                    data[key] = value;
                }
            });
            $(elm).find('select').each(function () {
                var key = $(this).attr('name');
                var value = $(this).children('option:selected').val();
                if (key) {
                    data[key] = value;
                }
            });
            return data;
        };

        this.build = function () {
            $manager.tree = $manager.buildTree($manager.children('.tree').eq(0));
            $manager.buildPanes($manager.children('.panes').eq(0));
            $manager.selected_pane = null;
        };

        this.setQuery = function (query, edit_query) {
            $manager.current_query = query;
            $('#' + options.destination_id).val(query);
            $manager.current_edit_query = edit_query;
        };

        this.pushState = function (id) {
            if ($manager.stored_state.indexOf(id) < 0) {
                $manager.stored_state.push(id);
            }
        };

        this.storeState = function () {
            var state = [];
            $($manager).find('li.jstree-open').each(function () {
                state.push($(this).attr('id'));
            });
            $manager.stored_state = state;
        };

        this.loadState = function () {
            for (id in $manager.stored_state) {
                $manager.tree.jstree("toggle_node", '#' + $manager.stored_state[id]);
            }
        };

        this.command = function (command, command_data, no_history) {
            var encoded_data = $.toJSON(command_data);
            $manager.storeState();
            if (command == 'add') {
                $manager.pushState('node-' + options.destination_id + '-' + command_data.parent_id);
            }
            jQuery.ajax(options.url, {
                type: 'POST',
                data: {
                    command: command,
                    query: $manager.current_edit_query,
                    type: options.type,
                    data: encoded_data,
                    id: options.destination_id
                },
                dataType: 'json',
                success: function (result) {
                    if (result.result != 'OK') {
                        alert('ERROR: ' + result.error);
                    } else {
                        $manager.html(result.data.html);
                        if (command != 'noop') {
                            $manager.historyPushState();
                        }
                        $manager.setQuery(result.data.query, result.data.query_edit);
                        $manager.build();

                    }
                },
                error: function () {
                    alert('ERROR: ' + result.error);
                }
            });
        };

        this.closeActivePane = function () {
            if ($manager.selected_pane) {
                $('#' + $manager.selected_pane).hide();
            }
            $manager.selected_pane = null;
        };

        this.historyPushState = function () {
            $manager.history.push($manager.current_edit_query);
        };

        this.historyPopState = function () {
            return $manager.history.pop();
        }

        this.build();
    };
});