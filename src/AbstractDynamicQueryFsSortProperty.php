<?php

namespace DynamicCategory;

use FS\Facades\FSFactory;
use FS\Property\FSPropertyInterface;
use FS\Property\Property;
use FS\Property\SortablePropertyTrait;

abstract class AbstractDynamicQueryFsSortProperty extends \CBuxusSelect
{
    /**
     * Constructor
     *
     * @param string $name
     * @param string $value
     * @param string $parameters
     */
    public function __construct($name, $value, $parameters = '')
    {
        $parameters .= '<sql></sql>';
        $parameters .= '<multiple>F</multiple>';
        $parameters .= '<show_as_list></show_as_list>';
        $parameters .= '<empty_option>F</empty_option>';
        $parameters .= '<size></size>';
        $parameters .= '<cols></cols>';
        $parameters .= '<rows></rows>';
        $parameters .= '<onchange-js></onchange-js>';
        $parameters .= '<disabled></disabled>';
        $parameters .= '<check_read_rights></check_read_rights>';

        parent::__construct($name, $value, $parameters);
    }

    abstract protected function getFSTag();

    public function getOptions($paramName = "options", $forceNoSql = false)
    {
        if ($paramName != 'options') {
            return parent::getOptions($paramName, $forceNoSql);
        }

        $fs_tag = $this->getFSTag();
        $fs = FSFactory::getInstance($fs_tag);

        $sortables = $fs->getSortableProperties();

        $options = [];

        foreach ($sortables as $sortable) {
            /**
             * @var SortablePropertyTrait $sortable
             */
            $key = $sortable->getTag();

            if ($sortable->isToggle()) {
                $key1 = $key . '|' . FSPropertyInterface::SORT_ASC;
                $options[$key1] = $sortable->getSortLabel() . ' ASC';

                $key2 = $key . '|' . FSPropertyInterface::SORT_DESC;
                $options[$key2] = $sortable->getSortLabel() . ' DESC';
            } else {
                $key .= '|' . $sortable->getSortMode();
                $options[$key] = $sortable->getSortLabel();
            }
        }

        return $options;
    }
}
