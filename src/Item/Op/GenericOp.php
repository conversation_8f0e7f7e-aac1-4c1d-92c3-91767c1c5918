<?php

namespace DynamicCategory\Item\Op;

use DynamicCategory\Item\AbstractItem;
use DynamicCategory\Util\SolrQueryFragment;
use DynamicCategory\Util\SqlFragment;
use DynamicCategory\View;
use FS\DatabaseBackedFSSearchInterface;
use FS\SolrBackedFSSearchInterface;

abstract class GenericOp extends AbstractItem
{
    /**
     * @return string
     */
    public function render()
    {
        $view = new View();
        $view->id = $this->getId();

        $view->inverted = $this->inverted;
        $view->query = $this->parentQuery;

        return $view->render('item/operator.phtml');
    }

    /**
     * @param DatabaseBackedFSSearchInterface $fs
     * @param SqlFragment $sql
     * @return string
     */
    public function buildQuery(DatabaseBackedFSSearchInterface $fs, SqlFragment $sql)
    {
        $children = [];
        foreach ($this->children as $child) {
            $query = $child->buildQuery($fs, $sql);
            if (!empty($sql)) {
                $children[] = '(' . $query . ')';
            }
        }

        $op = ' ' . $this->getOperator() . ' ';

        $query = implode($op, $children);
        if (!empty($query)) {
            if ($this->inverted) {
                $query = 'NOT (' . $query . ')';
            } else {
                $query = '(' . $query . ')';
            }
        }

        return $query;
    }

    abstract protected function getOperator();

    /**
     * @param SolrBackedFSSearchInterface $fs
     * @param SolrQueryFragment $query
     * @return string
     */
    public function buildSolrQuery(SolrBackedFSSearchInterface $fs, SolrQueryFragment $query)
    {
        $children = [];

        foreach ($this->children as $child) {
            $tmp = $child->buildSolrQuery($fs, $query);
            if (!empty($tmp)) {
                $children[] = '(' . $tmp . ')';
            }
        }

        $op = ' ' . $this->getOperator() . ' ';

        $solr_query = implode($op, $children);
        if (!empty($solr_query)) {
            if ($this->inverted) {
                $solr_query = 'NOT (' . $solr_query . ')';
            } else {
                $solr_query = '(' . $solr_query . ')';
            }
        }

        return $solr_query;
    }

    /**
     * @return string
     */
    public function getName()
    {
        $name = parent::getName();

        if (count($this->children)) {
            $name .= ' (' . count($this->children) . ')';
        }

        return $name;
    }

    protected function renderContent()
    {
    }
}
