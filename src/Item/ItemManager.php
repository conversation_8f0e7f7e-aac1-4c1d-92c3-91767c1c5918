<?php

namespace DynamicCategory\Item;

use DynamicCategory\Item\Op\AndItem;
use DynamicCategory\Item\Op\OrItem;

class ItemManager
{
    protected $itemTypes = [];

    public function addItemType($tag, $class_name)
    {
        $this->itemTypes[$tag] = $class_name;
    }

    public function removeItemType($tag)
    {
        unset($this->itemTypes[$tag]);
    }

    public function clearItemTypes()
    {
        $this->itemTypes = [];
    }

    protected function addDefaultItemTypes()
    {
        $this->addItemType(AndItem::TAG, __NAMESPACE__ . '\\Op\\AndItem');
        $this->addItemType(OrItem::TAG, __NAMESPACE__ . '\\Op\\OrItem');
    }

    protected function loadItemTypesFromConfig()
    {
        $items = config('dynamic_categories.item_types', []);
        if (!is_array($items)) {
            $items = [];
        }

        $moreConfigs = config('dynamic_categories_items', []);
        foreach ($moreConfigs as $moreConfig) {
            if (isset($moreConfig['item_types']) && is_array($moreConfig['item_types'])) {
                foreach ($moreConfig['item_types'] as $tag => $class) {
                    $items[$tag] = $class;
                }
            }
        }

        foreach ($items as $tag => $class) {
            $this->addItemType($tag, $class);
        }
    }

    public function __construct()
    {
        $this->addDefaultItemTypes();
        $this->loadItemTypesFromConfig();
    }

    protected $idCounter = 0;

    protected function nextId()
    {
        $id = $this->idCounter;
        $this->idCounter = $this->idCounter + 1;

        return $id;
    }

    public function registerId($id)
    {
        if ($id >= $this->idCounter) {
            $this->idCounter = $id + 1;
        }
    }

    /**
     * @param $itemType
     * @param int $id
     * @param array $data
     * @param bool $inverted
     * @throws \InvalidArgumentException
     * @return AbstractItem
     */
    public function create($itemType, $id, $data, $inverted)
    {
        if (!isset($this->itemTypes[$itemType])) {
            throw new \InvalidArgumentException('Unknown type for term: ' . $itemType);
        }

        if (is_null($id)) {
            $id = $this->nextId();
        } else {
            $this->registerId($id);
        }

        $class = $this->itemTypes[$itemType];
        $item = new $class($itemType, $id, $data, $inverted);

        return $item;
    }

    public function getItemName($itemType)
    {
        if (!isset($this->itemTypes[$itemType])) {
            throw new \InvalidArgumentException('Invalid expression item type: ' . $itemType);
        }

        $name = call_user_func([$this->itemTypes[$itemType], 'getItemName']);

        return $name;
    }

    public function getAddableTypes($queryType)
    {
        $list = app('buxus:dynamic-query:query-manager')->getAddableItemsForType($queryType);

        $result = [];
        foreach ($list as $type) {
            $result[$type] = $this->getItemName($type);
        }

        return $result;
    }
}
