<?php

namespace DynamicCategory\Item\Prop;

use DynamicCategory\Item\AbstractItem;
use DynamicCategory\Util\SolrQueryFragment;
use DynamicCategory\Util\SqlFragment;
use DynamicCategory\View;
use FS\DatabaseBackedFSSearchInterface;
use FS\Property\Numbers\Range;
use FS\SolrBackedFSSearchInterface;

abstract class MinMax extends AbstractItem
{
    /**
     * @var array
     */
    protected $allowedData = [
        'min',
        'max'
    ];

    /**
     * @return string
     */
    protected function renderContent()
    {
        $view = new View();
        $view->id = $this->getId();

        $view->min = $this->getMin();
        $view->max = $this->getMax();

        return $view->render('item/minmax.phtml');
    }

    /**
     * @return float|bool
     */
    protected function getMin()
    {
        return (isset($this->data['min']) && !empty($this->data['min']) ? $this->data['min'] : false);
    }

    /**
     * @return float|bool
     */
    protected function getMax()
    {
        return (isset($this->data['max']) && !empty($this->data['max']) ? $this->data['max'] : false);
    }

    /**
     * @return string
     */
    abstract protected function getFsPropertyTag();

    /**
     * @param DatabaseBackedFSSearchInterface $fs
     * @param SqlFragment $sql
     * @return string
     */
    public function buildQuery(DatabaseBackedFSSearchInterface $fs, SqlFragment $sql)
    {
        $min = $this->getMin();
        $max = $this->getMax();
        $property_tag = $this->getFsPropertyTag();

        $where = [];
        if ($min !== false) {
            $where[] = $fs->getMainTableName() . '.' . $property_tag . ' >= ' . $min;
        }
        if ($max !== false) {
            $where[] = $fs->getMainTableName() . '.' . $property_tag . ' <= ' . $max;
        }

        if (count($where) == 0) {
            return '';
        }

        $query = '(' . implode(' AND ', $where) . ')';

        /** @var Range $fs_property */
        $fs_property = $fs->getProperty($property_tag);

        if ($min !== false) {
            $fs_property->setMinimum($min);
        }

        if ($max !== false) {
            $fs_property->setMaximum($max);
        }

        return $this->processSqlNot($query);
    }

    /**
     * @param SolrBackedFSSearchInterface $fs
     * @param SolrQueryFragment $query
     * @return string
     */
    public function buildSolrQuery(SolrBackedFSSearchInterface $fs, SolrQueryFragment $query)
    {
        $min = $this->getMin();
        $max = $this->getMax();
        $price_tag = $this->getFsPropertyTag();

        if ($min === false && $max === false) {
            return '';
        }

        $connection = $fs->getSolrConnection();
        $solr_query = $connection->mapPropertyTagToSolrField($price_tag) . ':[' . ($min === false ? '*' : $min) . ' TO ' . ($max === false ? '*' : $max) . ']';

        return $this->processSolrNot($solr_query);
    }

    /**
     * @return string
     */
    public function getName()
    {
        $name = parent::getName();
        $min = $this->getMin();
        $max = $this->getMax();
        $items = [];

        if ($min !== false) {
            $items[] = __bx('dynamic-category::property.from') . ' ' . $min;
        }

        if ($max !== false) {
            $items[] = __bx('dynamic-category::property.to') . ' ' . $max;
        }

        if (count($items)) {
            $name .= '(' . implode(',', $items) . ')';
        }

        return $name;
    }
}