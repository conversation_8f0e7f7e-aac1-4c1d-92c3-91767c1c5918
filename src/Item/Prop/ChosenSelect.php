<?php

namespace DynamicCategory\Item\Prop;

use DynamicCategory\Item\AbstractItem;
use DynamicCategory\View;

abstract class ChosenSelect extends AbstractItem
{
    /**
     * @return string
     */
    protected function renderContent() {
        $view = new View(__DIR__);
        $view->id = $this->getId();

        $view->values = $this->getValues();

        $view->active_values = $this->getActiveValues();

        $view->name = $this->getItemName();

        return $view->render('item/chosen-select.phtml');
    }

    public function setData($data)
    {
        parent::setData($data);

        $data = $this->data;

        $result = [];

        $items = [];
        foreach ($data as $key => $value) {
            if (!empty($value)) {
                if (preg_match('@^val([0-9]+)$@', $key, $matches)) {
                    $items[] = (int)$matches[1];
                }
            }
        }
        $result['val'] = $items;

        $this->data = $result;
    }

    /**
     * @return array
     */
    abstract protected function getActiveValues();

    /**
     * @return array
     */
    abstract protected function getValues();
}
