<?php

namespace DynamicCategory\Item\Prop;

use DynamicCategory\Item\AbstractItem;
use DynamicCategory\Util\SolrQueryFragment;
use DynamicCategory\Util\SqlFragment;
use FS\DatabaseBackedFSSearchInterface;
use FS\SolrBackedFSSearchInterface;

abstract class Flag extends AbstractItem
{
    /**
     * @return string
     */
    abstract protected function getTag();

    /**
     * @var array
     */
    protected $allowedData = [];

    /**
     * @param DatabaseBackedFSSearchInterface $fs
     * @param SqlFragment $sql
     * @return string
     */
    public function buildQuery(DatabaseBackedFSSearchInterface $fs, SqlFragment $sql)
    {
        $where = [];

        $where[] = $fs->getMainTableName() . '.' . $this->getTag() . " = 'T'";

        return $this->processSqlNot('(' . implode(' AND ', $where) . ')');
    }

    /**
     * @param SolrBackedFSSearchInterface $fs
     * @param SolrQueryFragment $query
     * @return string
     */
    public function buildSolrQuery(SolrBackedFSSearchInterface $fs, SolrQueryFragment $query)
    {
        $connection = $fs->getSolrConnection();
        $solr_query = $connection->mapPropertyTagToSolrField($this->getTag()) . ':T';
        return $this->processSolrNot($solr_query);
    }
}