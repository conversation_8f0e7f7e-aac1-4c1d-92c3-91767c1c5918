<?php

namespace DynamicCategory\Item\Prop;

use DynamicCategory\Item\AbstractItem;
use DynamicCategory\Util\SqlFragment;
use DynamicCategory\View;
use FS\DatabaseBackedFSSearchInterface;

abstract class Checkboxes extends AbstractItem
{
    const REQUIRE_ALL = 0;
    const ONE_OF_MANY = 1;
    const EXACT_MATCH = 2;

    abstract protected function getValues();

    protected function getApplyModes() {
        return [
            self::REQUIRE_ALL => __bx('dynamic-category::property.require_all'),
            self::ONE_OF_MANY => __bx('dynamic-category::property.one_of_many'),
            self::EXACT_MATCH => __bx('dynamic-category::property.exact_match'),
        ];
    }

    protected function getApplyMode() {
        return (isset($this->data['apply_mode']) ? $this->data['apply_mode'] : self::REQUIRE_ALL);
    }

    protected function getActiveValues() {
        $vals = (isset($this->data['val']) ? $this->data['val'] : []);

        if (!is_array($vals)) {
            $vals = [];
        }

        return $vals;
    }

    public function setData($data) {
        parent::setData($data);

        $data = $this->data;

        $result = [];

        if (isset($data['apply_mode'])) {
            $result['apply_mode'] = $data['apply_mode'];
            unset($data['apply_mode']);
        }

        $items = [];
        foreach ($data as $key => $value) {
            if (!empty($value)) {
                if (preg_match('@^val([0-9]+)$@', $key, $matches)) {
                    $items[] = (int)$matches[1];
                }
            }
        }
        $result['val'] = $items;

        $this->data = $result;
    }

    protected function renderContent() {
        $view = new View();
        $view->id = $this->getId();

        $view->values = $this->getValues();
        $view->name = $this->getName();

        $view->active_values = $this->getActiveValues();

        $view->apply_mode = $this->getApplyMode();
        $view->apply_modes = $this->getApplyModes();

        return $view->render('item/generic-checkboxes.phtml');
    }

    abstract protected function getJoinTableName(DatabaseBackedFSSearchInterface $fs);

    public function buildQuery(DatabaseBackedFSSearchInterface $fs, SqlFragment $sql) {
        $values = $this->getActiveValues();
        $mode = $this->getApplyMode();
        $join_table = $this->getJoinTableName($fs);

        if (count($values) == 0) {
            return '';
        }

        /*
         * APPLY_ANY: should have one of selected
         * APPLY_ALL: should have selected values & can have other values
         * APPLY_EXACT: should have selected values & shouldn't have any other values, that aren't selected
         */
        switch ($mode) {
            case self::ONE_OF_MANY:
                // one JOIN many wheres
                $alias = $sql->addJoin($join_table, '[%].page_id = ' . $fs->getMainTableName() . '.page_id', SqlFragment::JOIN_LEFT);

                if (count($values) == 1) {
                    $query = $alias . '.value = ' . $values[0];
                } else {
                    $query = $alias . '.value IN (' . implode(',', $values) . ')';
                }
                break;
            case self::REQUIRE_ALL:
                $where = array();
                foreach ($values as $value) {
                    $alias = $sql->addJoin($join_table, '[%].page_id = ' . $fs->getMainTableName() . '.page_id', SqlFragment::JOIN_LEFT);
                    $where[] = $alias . '.value = ' . $value;
                }
                $query = implode(' AND ', $where);
                break;
            case self::EXACT_MATCH:
                $where = array();
                foreach ($values as $value) {
                    $alias = $sql->addJoin($join_table, '[%].page_id = ' . $fs->getMainTableName() . '.page_id', SqlFragment::JOIN_LEFT);
                    $where[] = $alias . '.value = ' . $value;
                }
                $alias = $sql->getNewTableAlias();
                $where[] = 'NOT EXISTS (SELECT * FROM ' . $join_table . ' AS ' . $alias . ' WHERE ' . $alias . '.value NOT IN (' . implode(',', $values) . ') AND ' . $alias . '.page_id = '. $fs->getMainTableName() . '.page_id)';
                $query = implode(' AND ', $where);
                break;
            default:
                break;
        }
        return $this->processSqlNot($query);
    }

    protected function processSqlNot($sql)
    {
        if (!empty($sql) && $this->inverted) {
            $sql = 'NOT (' . $sql . ')';
        }

        return $sql;
    }

    protected function processSolrNot($query)
    {
        if (!empty($query) && $this->inverted) {
            $query = 'NOT (' . $query . ')';
        }

        return $query;
    }
}
