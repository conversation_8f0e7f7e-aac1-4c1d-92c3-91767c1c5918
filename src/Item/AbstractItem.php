<?php

namespace DynamicCategory\Item;

use Buxus\Error\ErrorReporter;
use DynamicCategory\DynamicQuery;
use DynamicCategory\Util\SolrQueryFragment;
use DynamicCategory\Util\SqlFragment;
use DynamicCategory\View;
use Exception;
use FS\DatabaseBackedFSSearchInterface;
use FS\SolrBackedFSSearchInterface;

abstract class AbstractItem implements ItemInterface
{
    /** @var string */
    protected $id;

    /** @var string */
    protected $type;

    /** @var string[] */
    protected $data;

    /** @var array */
    protected $children = [];

    /** @var int[] */
    protected $allowedData;

    /** @var bool */
    protected $inverted = false;

    /**
     * @var AbstractItem
     */
    protected $parentItem;

    /**
     * @var DynamicQuery
     */
    protected $parentQuery;

    public function __construct($itemType, $itemId, $data = null, $inverted = false)
    {
        $this->type = $itemType;
        $this->id = $itemId;
        $this->data = $data;
        $this->inverted = $inverted;
    }

    public function setParentQuery($parentQuery)
    {
        $this->parentQuery = $parentQuery;
    }

    public function addChild(AbstractItem $item)
    {
        $this->children[$item->getId()] = $item;
        $item->setParentItem($this);
    }

    public function getId()
    {
        return $this->id;
    }

    public function setParentItem($parentItem)
    {
        $this->parentItem = $parentItem;
    }

    public function remove()
    {
        if (!is_null($this->parentItem)) {
            $this->parentItem->removeChild($this->id);
        }
    }

    public function removeChild($id)
    {
        if (isset($this->children[$id])) {
            unset($this->children[$id]);
        }
    }

    public function build($includeId = false)
    {
        $tmp = [
            'type' => $this->type,
            'data' => $this->data,
            'inv' => ($this->inverted ? '1' : '0'),
        ];

        if ($includeId) {
            $tmp['id'] = $this->id;
        }

        if (count($this->children)) {
            $children = array();
            foreach ($this->children as $child) {
                $children[] = $child->build($includeId);
            }
            $tmp['children'] = $children;
        }

        return $tmp;
    }

    /**
     * @param int $id
     * @return AbstractItem
     */
    public function getChild($id)
    {
        if ($this->id == $id) {
            return $this;
        }

        foreach ($this->children as $child) {
            $result = $child->getChild($id);
            if (!is_null($result)) {
                return $result;
            }
        }

        return null;
    }

    public function setData($data)
    {
        if (isset($data['inv'])) {
            $this->inverted = ($data['inv'] == 1);
            unset($data['inv']);
        }

        if (!is_null($this->allowedData)) {
            $list = [];
            foreach ($this->allowedData as $name) {
                if (isset($data[$name])) {
                    $list[$name] = $data[$name];
                }
            }

            $data = $list;
        }

        $this->data = $data;
    }

    public function getPanes($id)
    {
        $panes = array();

        $html = '<div id="pane-node-' . $id . '-' . $this->getId() . '" style="display:none">';
        $html .= $this->render();
        $html .= '</div>';

        $panes[] = $html;

        foreach ($this->children as $child) {
            $panes = array_merge($panes, $child->getPanes($id));
        }

        return $panes;
    }

    public function render()
    {
        $view = new View();

        $view->id = $this->getId();
        $view->content = $this->renderContent();
        $view->inverted = $this->inverted;

        return $view->render('item/generic.phtml');
    }

    abstract protected function renderContent();

    public function renderTree($id)
    {
        $html = '<li id="node-' . $id . '-' . $this->getId() . '">';
        $html .= '<a href="#">' . htmlspecialchars($this->getName()) . '</a>';
        if (count($this->children)) {
            $html .= '<ul>';
            foreach ($this->children as $child) {
                $html .= $child->renderTree($id);
            }
            $html .= '</ul>';
        }
        $html .= '</li>';

        return $html;
    }

    public function getName()
    {
        return ($this->inverted ? '! ' : '') . static::getItemName();
    }

    abstract public static function getItemName();

    abstract public function buildQuery(DatabaseBackedFSSearchInterface $fs, SqlFragment $sql);

    abstract public function buildSolrQuery(SolrBackedFSSearchInterface $fs, SolrQueryFragment $query);

    protected function appendNameByPageName($name, $tagName = 'val')
    {
        $val = (isset($this->data[$tagName]) ? (int)$this->data[$tagName] : null);

        if (!empty($val)) {
            try {
                $page = \PageFactory::get($val);
                $name .= ' (' . $page->getPageName() . ')';
            } catch (Exception $e) {
                ErrorReporter::report($e, __METHOD__, 'expression item appendNameByPageName: %s', $e->getMessage());
            }
        }

        return $name;
    }

    protected function processSqlNot($sql)
    {
        if (!empty($sql) && $this->inverted) {
            $sql = 'NOT (' . $sql . ')';
        }

        return $sql;
    }

    protected function processSolrNot($query)
    {
        if (!empty($query) && $this->inverted) {
            $query = '*:* NOT (' . $query . ')';
        }

        return $query;
    }
}
