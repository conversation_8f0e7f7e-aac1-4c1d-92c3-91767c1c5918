<?php

namespace DynamicCategory\Item;

use DynamicCategory\Util\SolrQueryFragment;
use DynamicCategory\Util\SqlFragment;
use FS\DatabaseBackedFSSearchInterface;
use FS\SolrBackedFSSearchInterface;

interface ItemInterface
{
    public static function getItemName();

    public function buildQuery(DatabaseBackedFSSearchInterface $fs, SqlFragment $sql);

    public function buildSolrQuery(SolrBackedFSSearchInterface $fs, SolrQueryFragment $query);
}