<?php

namespace Buxus\Scheduler;

use Illuminate\Console\Command;
use Illuminate\Console\Scheduling\Schedule;

class SchedulerCommandList extends Command
{
    protected $signature = 'schedule:list';
    protected $description = 'List scheduler tasks';

    public function handle()
    {
        $schedule = app(Schedule::class);
        $events = $schedule->events();
        $this->getOutput()->writeln('');
        $this->getOutput()->writeln('Current environment <info>' . env('APP_ENV') . '</info>');
        $this->getOutput()->writeln('');
        $table = [];
        foreach ($events as $event) {
            $table[] = [
                $event->getExpression(),
                wordwrap($event->getSummaryForDisplay(), 150),
                $event->runsInEnvironment(env('APP_ENV')) ? 'true' : 'false',
            ];
        }
        if (count($events) === 0) {
            $this->getOutput()
                ->writeln('<info>No scheduler commands defined.</info>');
        } else {
            $this->table(
                [
                    'expression',
                    'command',
                    'run in current environment',
                ],
                $table
            );
        }
    }
}
