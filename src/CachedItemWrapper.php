<?php

namespace Layout;

use <PERSON>uxus\Page\PageInterface;
use Buxus\Substrate\JSManager;
use Illuminate\Contracts\Cache\Repository;

class CachedItem<PERSON>rapper implements LayoutItemInterface
{
    protected $layoutItem;

    protected $cacheMode;

    protected $cacheTime = 0;

    public const CACHE_HTML_RESOLVING_CONTEXT = 'cacheHtmlResolving';

    /**
     * @var LayoutManager
     */
    protected $factory;

    /**
     * @var string
     */
    protected $itemName;

    protected $data = [];

    /**
     * @var Repository
     */
    protected $storage;

    public function setFactory($factory)
    {
        $this->factory = $factory;
    }

    public function setItemName($itemName)
    {
        $this->itemName = $itemName;
    }

    public function setStorage($storage)
    {
        $this->storage = $storage;
    }

    public function setCacheMode($cacheMode)
    {
        $this->cacheMode = $cacheMode;
    }

    public function setCacheTime($cacheTime)
    {
        $this->cacheTime = $cacheTime;
    }

    public function setSourcePage(PageInterface $page)
    {
    }

    public function render(PageInterface $ownerPage, $data = [])
    {
        if ($this->cacheMode == LayoutManager::CACHE_MODE_IGNORE_SOURCE_PAGE) {
            $key = 'html';
            $js_key = 'js';
        } else {
            $key = 'html-' . $ownerPage->getPageId();
            $js_key = 'js-' . $ownerPage->getPageId();
        }

        /**
         * @var JSManager $js_manager
         */
        $js_manager = app('buxus:js-manager');

        $prop = $this->getData($key);
        if ($prop === null) {
            $js_manager->setCollectionTag('layout-' . $this->itemName);

            \Layouts::pushContext(static::CACHE_HTML_RESOLVING_CONTEXT);
            $prop = $this->getLayoutItem()->render($ownerPage, $data);
            \Layouts::popContext();

            $js_code = $js_manager->retrieveCollectedJSForTag('layout-' . $this->itemName);

            $this->setData($key, $prop);
            $this->setData($js_key, $js_code);
        } else {
            $js = $this->getData($js_key);
            if (!empty($js)) {
                $js_manager->addCollectedJSCode($js);
            }
        }

        $placeholderRealHtml = [];
        preg_match_all('/<layout_placeholder__([^>]+)>/', $prop, $matches);
        if (isset($matches[1]) && is_array($matches[1])) {
            foreach ($matches[1] as $matchIndex => $match) {
                if (!empty($match)) {
                    $layoutId = $match;
                    $layoutItem = $this->getLayoutItem()->getSomeChildBySourceId($layoutId);
                    $layoutHtml = $layoutItem->render($ownerPage, $data);
                    $placeholderRealHtml[$matchIndex] = $layoutHtml;
                }
            }
            $prop = preg_replace($matches[0], $placeholderRealHtml, $prop);
        }

        return $prop;
    }

    protected function getData($key)
    {
        if (isset($this->data[$key])) {
            return $this->data[$key];
        }

        return null;
    }

    protected function setData($key, $value)
    {
        if (!isset($this->data[$key]) && $this->data[$key] != $value) {
            $this->data[$key] = $value;
            $this->save();
        }
    }

    /**
     * @return LayoutItemInterface
     */
    protected function getLayoutItem()
    {
        if (is_null($this->layoutItem)) {
            $this->layoutItem = $this->factory->getLayout($this->itemName);
        }
        return $this->layoutItem;
    }

    public function getTitle()
    {
        $prop = $this->getData('title');
        if (is_null($prop)) {
            $prop = $this->getLayoutItem()->getTitle();
            $this->setData('title', $prop);
        }
        return $prop;
    }

    public function getId()
    {
        $prop = $this->getData('id');
        if (is_null($prop)) {
            $prop = $this->getLayoutItem()->getId();
            $this->setData('id', $prop);
        }
        return $prop;
    }

    public function processRequest(\Zend_Controller_Request_Abstract $request)
    {
        $item = $this->getLayoutItem();
        $item->processRequest($request);
    }

    public function getSource()
    {
        $item = $this->getLayoutItem();
        return $item->getSource();
    }

    public function save()
    {
        $this->storage->put($this->itemName, $this, $this->cacheTime);
    }

    public function __sleep()
    {
        return [
            'item_name',
            'data',
        ];
    }

    public function forget()
    {
        $this->storage->forget($this->itemName);
        $this->data = [];
    }

    public function getSomeChildBySourceId($id)
    {
        return null;
    }
}
