<?php

namespace Buxus\Eshop\Variants;

use Buxus\Eshop\Item\ShopItemInterface;

class VariantResolverResult
{
    protected $product = null;

    protected $redirect = null;

    /**
     * @return ShopItemInterface
     */
    public function getProduct()
    {
        return $this->product;
    }

    /**
     * @return string
     */
    public function getRedirect()
    {
        return $this->redirect;
    }

    public function isRedirect()
    {
        return (!is_null($this->redirect));
    }

    public function setProduct($product)
    {
        $this->product = $product;
    }

    public function setRedirect($redirect)
    {
        $this->redirect = $redirect;
    }
}