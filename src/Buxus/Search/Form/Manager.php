<?php

class Buxus_Search_Form_Manager
{
    /**
     * @var Buxus_Search_Form_Manager
     */
    protected static $instance = null;
    protected $form_created = false;

    /**
     * @return Buxus_Search_Form_Manager
     */
    public static function getInstance()
    {
        if (is_null(self::$instance)) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * @param string $search_id
     * @return Buxus_Search_Form_Form
     */
    public function resolveFormById($search_id)
    {
        if (Session::has('buxus.buxus_search.' . $search_id)) {
            $form = gzuncompress(base64_decode(Session::get('buxus.buxus_search.' . $search_id)));
            $form = unserialize($form);
            if (is_object($form) && $form instanceof Buxus_Search_Form_Form) {
                return $form;
            }
        }

        return null;
    }

    public function saveFormToSession(Buxus_Search_Form_Form $form)
    {
        Session::put('buxus.buxus_search.' . $form->getSearchId(), base64_encode(gzcompress(serialize($form))));
    }

    public function getFormCreated()
    {
        return $this->form_created;
    }

    /**
     * @param Zend_Controller_Request_Http $request
     * @return Buxus_Search_Form_Form
     */
    public function getForm(Zend_Controller_Request_Http $request, $throw_exception = false)
    {
        $search_id = $request->getParam('sid');

        $form = null;
        if (!empty($search_id)) {
            $form = $this->resolveFormById($search_id);
        }

        if (is_null($form) && $throw_exception) {
            throw new Exception('Unable to resolve search form');
        }

        if (is_null($form)) {
            $form = new Buxus_Search_Form_Form(str_replace('.', '-', uniqid('', true)));
            $form->processRequest($request);
            $this->form_created = true;
        }

        app()->terminating(function () use ($form) {
            $this->saveFormToSession($form);
        });

        return $form;
    }
}
