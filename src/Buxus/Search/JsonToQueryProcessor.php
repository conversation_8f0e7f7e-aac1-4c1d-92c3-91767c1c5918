<?php

namespace Buxus\Search;

use Buxus\Property\PropertyManager;

class JsonToQueryProcessor
{
    protected $definition;

    public function __construct($json)
    {
        $this->loadDefinition($json);
    }

    protected function loadDefinition($json)
    {
        $definition = @json_decode($json, true);

        if (!is_array($definition)) {
            $definition = null;
        }

        $this->definition = $definition;
    }

    public function applyToQuery(\Buxus_Util_QueryBuilder $query)
    {
        if (!is_null($this->definition)) {
            $string_query = $this->processAction($this->definition, $query);
            $query->addWhere($string_query);
        }
    }

    protected function processAction($action, \Buxus_Util_QueryBuilder $query)
    {
        static $action_params_counter = 0;

        if (!is_array($action)) {
            $key = ':value_from_json' . $action_params_counter;
            $action_params_counter++;
            $query->addParam($key, $action);
            return $key;
        }

        if (!isset($action['type']) || empty($action['type'])) {
            return 1;
        }

        $method = 'processAction' . ucfirst($action['type']);

        if (method_exists($this, $method)) {
            return call_user_func_array([$this, $method], [$action, $query]);
        }

        return 1;
    }

    protected function processActionCompare($action, \Buxus_Util_QueryBuilder $query)
    {
        $operator = (isset($action['operator']) ? $action['operator'] : '');
        $left = (isset($action['left']) ? $action['left'] : '');
        $right = (isset($action['right']) ? $action['right'] : '');

        $sql_op = null;

        switch ($operator) {
            case 'equals':
                $sql_op = '=';
                break;
            case 'not_equals':
                $sql_op = '<>';
                break;
            case 'gt':
                $sql_op = '>';
                break;
            case 'gte':
                $sql_op = '>=';
                break;
            case 'lt':
                $sql_op = '<';
                break;
            case 'lte':
                $sql_op = '<=';
                break;

            case 'contains':
                return '(' . $this->processAction($left, $query) . ' LIKE CONCAT("%",' . $this->processAction($right, $query) . ',"%"))';
                break;
            case 'starts':
                return '(' . $this->processAction($left, $query) . ' LIKE CONCAT(' . $this->processAction($right, $query) . ',"%"))';
                break;
            case 'regexp':
                return '(' . $this->processAction($left, $query) . ' REGEXP ' . $this->processAction($right, $query) . ')';
                break;
            default:
                return 1;
                break;
        }

        return '(' . $this->processAction($left, $query) . ' ' . $sql_op . ' ' . $this->processAction($right, $query) . ')';
    }

    protected function processActionLogic($action, \Buxus_Util_QueryBuilder $query)
    {
        $operator = (isset($action['operator']) ? $action['operator'] : '');
        $left = (isset($action['left']) ? $action['left'] : '');
        $right = (isset($action['right']) ? $action['right'] : '');

        $sql_op = null;

        switch ($operator) {
            case 'AND':
                $sql_op = 'AND';
                break;
            case 'OR':
                $sql_op = 'OR';
                break;
            default:
                return 1;
                break;
        }

        return '((' . $this->processAction($left, $query) . ') ' . $sql_op . ' (' . $this->processAction($right, $query) . '))';
    }

    protected function processActionProperty($action, \Buxus_Util_QueryBuilder $query)
    {
        static $property_counter = 0;

        if (!isset($action['property']) || empty($action['property'])) {
            return 1;
        }

        $property = $action['property'];

        if (in_array($property, [
            'page_id',
            'page_name',
            'page_type_id',
            'page_state_id',
            'sort_date_time',
        ])) {
            return 'p.' . $property;
        }

        try {
            $property_object = PropertyManager::getInstance()->getPropertyByTag($property);

            $alias = $query->addJoin('tblPagePropertyValues', [
                'p.page_id = [%].page_id AND [%].property_id = :property_from_json' . $property_counter,
            ], \Buxus_Util_QueryBuilder::JOIN_STRAIGHT, [
                ':property_from_json' . $property_counter => $property_object->getId(),
            ]);

            $property_counter++;

            return $alias . '.property_value';
        } catch (\Exception $e) {

        }

        return 1;
    }

    protected function processActionNot($action, \Buxus_Util_QueryBuilder $query)
    {
        $value = (isset($action['value']) ? $action['value'] : '');
        return 'NOT (' . $this->processAction($value, $query) . ')';
    }
}
