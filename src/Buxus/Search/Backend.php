<?php

abstract class Buxus_Search_Backend {
    const OPTION_TIMEOUT = 'timeout';

    protected $tag;

    protected $_options = array(
        'timeout' => 30000,
    );

    public function __construct($tag = Buxus_Search_Manager::DEFAULT_BACKEND) {
        $this->tag = $tag;
    }

    public function getTag() {
        return $this->tag;
    }

    public function setOption($name, $value) {
        $this->_options[$name] = $value;
    }

    public function getOption($name, $default_value = null) {
        if (isset($this->_options[$name])) {
            return $this->_options[$name];
        }
        return $default_value;
    }

    abstract public function indexDoc(Buxus_Search_Document $doc, Buxus_Search_Index $index);
    abstract public function getArrayDataForDoc(Buxus_Search_Document $doc, Buxus_Search_Index $index);
    abstract public function removeDoc(Buxus_Search_Document $doc);
    abstract public function commit();
    abstract public function cleanIndex($index_type);
    abstract public function isAvailable();
}
