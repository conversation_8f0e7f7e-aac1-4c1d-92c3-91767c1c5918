<?php

namespace Buxus\PageType;

use Buxus\Core\Constants;
use Buxus\User\Facades\BuxusUserManager;
use Buxus\User\User;

class PageType implements PageTypeInterface
{
    protected $id;
    protected $name;

    /**
     * @var PageTypeInterface
     */
    protected $parent;

    protected $parent_id;

    protected $page_class_id;
    protected $tag;
    protected $description;
    protected $page_details_layout;
    protected $auto_expand;
    protected $page_sort_type_tag;
    protected $presubmit_trigger;
    protected $delete_trigger;
    protected $postsubmit_trigger;
    protected $premove_trigger;
    protected $postmove_trigger;
    protected $default_template_id = 1;
    protected $include_in_sync;
    protected $page_type_order;

    /**
     * @var PageType[]
     */
    protected $superior_page_types;

    /**
     * @var User[]
     */
    protected $users_allowed_to_insert;

    /**
     * @var PageTypePropertyItem[]
     */
    protected $own_properties = array();

    protected $checksum = null;

    public function __construct($id = null)
    {
        $this->id = $id;
    }

    public function getAutoExpand()
    {
        return $this->auto_expand;
    }

    public function setAutoExpand($auto_expand)
    {
        $this->auto_expand = $auto_expand;
    }

    public function getDefaultTemplateId()
    {
        return $this->default_template_id;
    }

    public function setDefaultTemplateId($default_template_id)
    {
        $this->default_template_id = $default_template_id;
    }

    public function getDeleteTrigger()
    {
        return $this->delete_trigger;
    }

    public function setDeleteTrigger($delete_trigger)
    {
        $this->delete_trigger = $delete_trigger;
    }

    public function getDescription()
    {
        return $this->description;
    }

    public function setDescription($description)
    {
        $this->description = $description;
    }

    public function getId()
    {
        return $this->id;
    }

    public function setId($id)
    {
        $this->id = $id;
    }

    public function getIncludeInSync()
    {
        return $this->include_in_sync;
    }

    public function setIncludeInSync($include_in_sync)
    {
        $this->include_in_sync = $include_in_sync;
    }

    public function getName()
    {
        return $this->name;
    }

    public function setName($name)
    {
        $this->name = $name;
    }

    public function getPageClassId()
    {
        return $this->page_class_id;
    }

    public function setPageClassId($page_class_id)
    {
        $this->page_class_id = $page_class_id;

        $this->resetChecksum();
    }

    public function getPageDetailsLayout()
    {
        return $this->page_details_layout;
    }

    public function setPageDetailsLayout($page_details_layout)
    {
        $this->page_details_layout = $page_details_layout;
    }

    public function getPageSortTypeTag()
    {
        return $this->page_sort_type_tag;
    }

    public function setPageSortTypeTag($page_sort_type_tag)
    {
        $this->page_sort_type_tag = $page_sort_type_tag;
    }

    public function getPageTypeOrder()
    {
        return $this->page_type_order;
    }

    public function setPageTypeOrder($page_type_order)
    {
        $this->page_type_order = $page_type_order;
    }

    /**
     * @return PageTypeInterface|null
     */
    public function getParent()
    {
        if (is_null($this->parent) && !is_null($this->parent_id)) {
            $this->parent = PageTypesManager::getInstance()->getPageTypeById($this->parent_id);
        }

        return $this->parent;
    }

    public function setParent(PageTypeInterface $parent = null)
    {
        $this->parent = $parent;
        if (!is_null($parent)) {
            $this->parent_id = $parent->getId();
        } else {
            $this->parent_id = null;
        }

        $this->resetChecksum();
    }

    public function getPostmoveTrigger()
    {
        return $this->postmove_trigger;
    }

    public function setPostmoveTrigger($postmove_trigger)
    {
        $this->postmove_trigger = $postmove_trigger;
    }

    public function getPostsubmitTrigger()
    {
        return $this->postsubmit_trigger;
    }

    public function setPostsubmitTrigger($postsubmit_trigger)
    {
        $this->postsubmit_trigger = $postsubmit_trigger;
    }

    public function getPremoveTrigger()
    {
        return $this->premove_trigger;
    }

    public function setPremoveTrigger($premove_trigger)
    {
        $this->premove_trigger = $premove_trigger;
    }

    public function getPresubmitTrigger()
    {
        return $this->presubmit_trigger;
    }

    public function setPresubmitTrigger($presubmit_trigger)
    {
        $this->presubmit_trigger = $presubmit_trigger;
    }

    public function getTag()
    {
        return $this->tag;
    }

    public function setTag($tag)
    {
        $this->tag = $tag;

        $this->resetChecksum();
    }

    public function setPropertyItems($items)
    {
        $this->own_properties = $items;

        $this->resetChecksum();
    }

    public function getPropertyItems()
    {
        return $this->own_properties;
    }

    public function addPropertyItem(PageTypePropertyItem $item, $adjust_order = true)
    {
        $existing_item = $this->getPropertyItemForPropertyId($item->getProperty()->getId());
        if (!is_null($existing_item) && $existing_item->getInherited()) {
            return false;
        }

        $max = 0;
        foreach ($this->own_properties as $own_item) {
            if ($own_item->getProperty()->getId() == $item->getProperty()->getId()) {
                $modified = ($own_item->getRequired() != $item->getRequired());
                $own_item->setRequired($item->getRequired());

                if ($modified) {
                    $this->resetChecksum();
                }

                return $modified;
            }
            if ($max < $own_item->getOrder()) {
                $max = $own_item->getOrder();
            }
        }
        if ($adjust_order) {
            $max++;
            $item->setOrder($max);
        }
        $item->setSourcePageTypeId($this->getId());
        $this->own_properties[] = $item;

        $this->resetChecksum();

        return true;
    }

    public function removePropertyItemById($property_id)
    {
        $modified = false;
        $properties = array();
        foreach ($this->own_properties as $own_item) {
            if ($own_item->getProperty()->getId() == $property_id) {
                $modified = true;
                continue;
            }
            $properties[] = $own_item;
        }
        $this->own_properties = $properties;

        if ($modified) {
            $this->resetChecksum();
        }

        return $modified;
    }

    /**
     * @param $property_id
     * @return PageTypePropertyItem
     */
    public function getPropertyItemForPropertyId($property_id)
    {
        if (is_object($this->getParent())) {
            $item = $this->getParent()->getPropertyItemForPropertyId($property_id);
            if (is_object($item)) {
                $item = clone $item;
                $item->setInherited(true);
                return $item;
            }
        }

        foreach ($this->own_properties as $item) {
            if ($item->getProperty()->getId() == $property_id) {
                return $item;
            }
        }

        return null;
    }

    /**
     * @return PageTypePropertyItem[]
     */
    public function getAllPropertyItems()
    {
        $items = array();

        foreach ($this->own_properties as $own_item) {
            $items[$own_item->getProperty()->getId()] = $own_item;
        }

        $need_reorder = false;

        $parent = $this->getParent();
        if (!is_null($parent)) {
            $parent_props = $parent->getAllPropertyItems();
            $parent_props = array_reverse($parent_props, true);
            foreach ($parent_props as $parent_item) {
                if (!isset($items[$parent_item->getProperty()->getId()])) {
                    $new_item = clone $parent_item;
                    $new_item->setInherited(true);
//                    $items[$new_item->getProperty()->getId()] = $new_item;
                    $items = array($new_item->getProperty()->getId() => $new_item) + $items;
                    $need_reorder = true;
                } else {
                    $items[$parent_item->getProperty()->getId()]->setInherited(true);
                }
            }

            $new_items = array();
            foreach ($items as $id => $page_type) {
                if ($page_type->getInherited()) {
                    if (isset($parent_props[$id])) {
                        $new_items[$id] = $page_type;
                    }
                } else {
                    $new_items[$id] = $page_type;
                }
            }
            $items = $new_items;
        }

        if ($need_reorder) {
            $counter = 1;
            foreach ($items as $page_type) {
                $page_type->setOrder($counter);
                $counter++;
            }
        }

        return $items;
    }

    public function removeInheritedPropertyItems()
    {
        $items = array();
        foreach ($this->own_properties as $property_item) {
            if (!$property_item->getInherited()) {
                $items[] = $property_item;
            }
        }
        $this->own_properties = $items;
    }

    /**
     * @return PageTypeInterface
     */
    public function getSuperiorPageTypes()
    {
        if (is_null($this->superior_page_types)) {
            $this->superior_page_types = PageTypesManager::getInstance()->getSuperiorPageTypes($this);
        }
        return $this->superior_page_types;
    }

    public function isSuperiorPageType(PageType $page_type)
    {
        $superior_page_types = $this->getSuperiorPageTypes();
        foreach ($superior_page_types as $superior_page_type) {
            if ($superior_page_type->getId() == $page_type->getId()) {
                return true;
            }
        }
        return false;
    }

    public function addSuperiorPageType(PageTypeInterface $page_type)
    {
        $superior_page_types = $this->getSuperiorPageTypes();
        foreach ($superior_page_types as $item) {
            if ($item->getId() == $page_type->getId()) {
                return;
            }
        }
        $superior_page_types[] = $page_type;
        $this->superior_page_types = $superior_page_types;
    }

    public function resetSuperiorPageTypes()
    {
        $this->superior_page_types = array();
    }

    public function removeSuperiorPageType(PageTypeInterface $page_type)
    {
        $superior_page_types = $this->getSuperiorPageTypes();
        $new_list = array();
        foreach ($superior_page_types as $item) {
            if ($item->getId() == $page_type->getId()) {
                continue;
            }
            $new_list[] = $item;
        }
        $this->superior_page_types = $new_list;
    }

    public function getClassName()
    {
        switch ($this->page_class_id) {
            case Constants::C_pc_Web_page:
                return __bx('pages::page_type_details.PC_Web_Page');
            case Constants::C_pc_Item:
                return __bx('pages::page_type_details.PC_Item');
            case Constants::C_pc_ParentItem:
                return __bx('pages::page_type_details.PC_ParentItem');
            case Constants::C_pc_Template:
                return __bx('pages::page_type_details.PC_Template');
            case Constants::C_pc_System_Option:
                return __bx('pages::page_type_details.PC_System_Option');
            case Constants::C_pc_OtherType:
                return __bx('pages::page_type_details.PC_OtherType');
            default:
                return '';
        }
    }

    public function getNameWithClass()
    {
        return $this->getClassName() . ': ' . $this->getName();
    }

    /**
     * @return User[]
     */
    public function getUsersAllowedToInsert()
    {
        // in case this is a new type and nobody set the users, we assign all users
        if (is_null($this->users_allowed_to_insert)) {
            $this->users_allowed_to_insert = BuxusUserManager::getAllUsers();
        }
        return $this->users_allowed_to_insert;
    }

    public function isUserAllowedToInsert(User $user)
    {
        if ($user->hasAdminRights()) {
            return true;
        }

        foreach ($this->users_allowed_to_insert as $owner) {
            if ($owner->getId() == $user->getId()) {
                return true;
            }
        }
        return false;
    }

    /**
     * @param User[] $users_allowed_to_insert
     */
    public function setUsersAllowedToInsert($users_allowed_to_insert)
    {
        $this->users_allowed_to_insert = $users_allowed_to_insert;
    }

    /**
     * @param User $user
     */
    public function addUserAllowedToInsert(User $user)
    {
        foreach ($this->users_allowed_to_insert as $old_user) {
            if ($old_user->getId() == $user->getId()) {
                return;
            }
        }
        $this->users_allowed_to_insert[] = $user;
    }

    public function __sleep()
    {
        return array_diff(array_keys(get_object_vars($this)), array('parent'));
    }

    protected function computeChecksum()
    {
        $items = [];

        $items[] = $this->getTag();
        $items[] = $this->getPageClassId();
        $parent = $this->getParent();
        if ($parent) {
            $items[] = $parent->getChecksum();
        }

        $properties = [];

        foreach ($this->own_properties as $property_item) {
            /**
             * @var PageTypePropertyItem $property_item
             */
            if (!$property_item->getInherited()) {
                $properties[] = $property_item->getProperty()->getChecksum();
            }
        }

        asort($properties);

        $items = array_merge($items, $properties);

        return md5(implode(':', $items));
    }

    public function getChecksum()
    {
        if (is_null($this->checksum)) {
            $this->checksum = $this->computeChecksum();
        }
        return $this->checksum;
    }

    protected function resetChecksum()
    {
        $this->checksum = null;
    }

    public function save(): void
    {
        \Buxus\PageType\Facade\PageTypesManager::savePageType($this);
    }
}
