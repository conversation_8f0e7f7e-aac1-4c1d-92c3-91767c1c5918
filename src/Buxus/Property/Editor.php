<?php

namespace Buxus\Property;

use Buxus\Core\Constants;
use Buxus\Legacy\LegacyMethods;
use Buxus\PageType\PageType;
use Buxus\PageType\PageTypePropertyItem;
use Buxus\PageType\PageTypesManager;
use Buxus\Util\Url;

class Editor
{
    protected $property_id;

    /**
     * @var Property
     */
    protected $property = null;

    public function __construct($property_id = null)
    {
        if ($property_id == 0) {
            $property_id = null;
        }
        $this->property_id = $property_id;
    }

    public function processSubmit($submited_data)
    {
        $property = $this->getProperty();
        $property->setValueType($submited_data['value_type']);

        $value_type = $property->getValueTypeObject();

        $value_type->processSubmit($submited_data);
    }

    /**
     * @return Property
     */
    public function getProperty()
    {
        if (is_null($this->property)) {
            if (empty($this->property_id)) {
                $this->property = new Property();
            } else {
                $this->property = PropertyManager::getInstance()->getPropertyById($this->property_id);
            }
        }
        return $this->property;
    }

    public function renderEditForm()
    {
        $saved = \RequestValidator::forceString('saved') == 'T';

        $property = $this->getProperty();

        $property_value_type = $property->getValueTypeObject();

        $property_properties = $property->getProperties();

        if ((preg_match('/select/i', $property_properties)) && (preg_match('/from/i', $property_properties))) { // Property contain SQL commmand
            $message = __bx('pages::property_details.IncorrectSQLProperty');
            $message = str_replace(
                '<convert_property_link>',
                config('buxus_core.base_url') . 'admin/convert_property.php?property_id=' . $this->property_id,
                $message
            );
            \ResultMessage::warnMessage($message);
        }

        if (is_null($this->property_id)) {
            $content = HTML_PageTitle(
                __bx('pages::property_details.PageTitle1'),
                __bx('pages::property_details.PageAnotation1')
            );
        } else {
            $content = HTML_PageTitle(
                __bx('pages::property_details.PageTitle2'),
                __bx('pages::property_details.PageAnotation2')
            );
        }

        if ($saved) {
            $content .= '
				<div class="info">
					' . __bx('pages::property_details.PropertyWasSaved') . '
				</div>';
        }

        $content .=
            HTML_FormBegin('property_details', 'property_details_submit.php') .
            //HTML_HiddenField('old_property_id', $this->property_id) .
            HTML_HiddenField('property_id', $this->property_id) .

            HTML_TableBegin(__bx('pages::property_details.TableHeader1'), 2) .

            HTML_RowBegin() .
            HTML_HeaderCell(__bx('pages::property_details.Property_ID') . ':', 1, 1, '', '25%') .
            HTML_ValueCell($this->property_id) .
            HTML_RowEnd() .

            HTML_RowBegin() .
            HTML_HeaderCell(__bx('pages::property_details.Property_Name') . ':') .
            HTML_ValueCell(HTML_TextField('property_name', $property->getName())) .
            HTML_RowEnd() .

            HTML_RowBegin() .
            HTML_HeaderCell(__bx('pages::property_details.Property_Tag') . ':') .
            HTML_ValueCell(HTML_TextField('property_tag', $property->getTag())) .
            HTML_RowEnd() .

            HTML_RowBegin() .
            HTML_HeaderCell(__bx('pages::property_details.Property_Description') . ':') .
            HTML_ValueCell(HTML_Textarea('property_description', $property->getDescription())) .
            HTML_RowEnd() .

            HTML_RowBegin() .
            HTML_HeaderCell(__bx('pages::property_details.rozsireny_popis_vlastnosti') . ':') .
            HTML_ValueCell(
                HTML_Textarea('property_extended_description', $property->getExtendedDescription())
                .
                '<input class="btn btn-primary btn--small m-t-xs" id="property_extended_description_button" type="button" name="edithtml" value="Vypnúť HTML editor" />' // phpcs:ignore
            ) .
            HTML_RowEnd();

        $language_tiny = GetSystemOption("C_language");
        if ($language_tiny == "cz") {
            $language_tiny = "cs";
        }

        $tiny_options = file_get_contents(config_path('tinymce_configs/full.js'));
        $cr_root_images_regexp = str_replace('/', '.', config('buxus_core.image_upload_url'));
        $cr_root_docs_regexp = str_replace('/', '.', config('buxus_core.doc_upload_url'));

        $d_root = $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
        $path = explode("/", $d_root);

        array_pop($path);

        $current_url = '';

        foreach ($path as $value) {
            $current_url .= $value . "/";
        }
        $current_url = Url::getProtocol() . $current_url;
        $content .= '
                        <script type="text/javascript">
                            document_root = "' . $current_url . '";
							document_host ="' . $_SERVER['HTTP_HOST'] . '";
							docs_dir ="' . $cr_root_docs_regexp . '";
							imgs_dir ="' . $cr_root_images_regexp . '";
							c_redis_root ="' . config('buxus_core.base_url') . '"

                            require(["jquery"], function($) {
							    var property_extended_description_editor =
							        $("#property_extended_description").buxusTiny({
	            						button_value_off: "Vypnúť HTML editor",
	            						button_value_on: "Zapnúť HTML editor",
	            						c_redis_root: "' . config('buxus_core.base_url') . '",
	            						c_redis_root_images: "' . config('buxus_core.image_upload_url') . '",
	            						c_redis_root_docs: "' . config('buxus_core.doc_upload_url') . '",
	            						submit_form_id: "order_details_form",
	            						content_css: "' . GetSystemOption("C_html_editor_css_file") . '",
	            						cache_suffix: "?' . LegacyMethods::getStaticFileRefresh() . '",
	            						language: "' . $language_tiny . '"},
	  							        ' . $tiny_options . '
	            					).initClassic();
                            });
	            		</script>
        ';

        $content .= HTML_RowBegin() .
            HTML_HeaderCell(__bx('pages::property_details.Property_Value_Type') . ':') .
            HTML_ValueCell(
                HTML_Select(
                    'value_type',
                    \PropertyValueTypes::getAllTypesList(),
                    'tag',
                    'caption',
                    $property_value_type->getTag(),
                    '',
                    'changeValueType("' . $this->property_id . '");'
                ) .
                HTML_HiddenField('property_properties', $property->getProperties(), 'property_properties')
            ) .
            HTML_RowEnd() .

            HTML_RowBegin() .
            HTML_HeaderCell(__bx('pages::property_details.Property_Value_Type_Attributes') . ':') .
            HTML_ValueCell('
							<div id="value_type_form_holder">
								' . $property_value_type->renderForm() . '
							</div>
							<div id="value_type_form_loading" style="display:none">
								<img src="../system/images/wait.gif" />
							</div>') .
            HTML_RowEnd() .

            HTML_RowBegin() .
            HTML_HeaderCell(__bx('pages::property_details.Property_DefaultValue') . ':') .
            HTML_ValueCell(HTML_TextField('property_default_value', $property->getDefaultValue())) .
            HTML_RowEnd() .

            HTML_RowBegin() .
            HTML_HeaderCell(__bx('pages::property_details.Property_Class') . ':') .
            HTML_ValueCell(CreateSelectforPropertyClass("property_class_id", $property->getClassId(), false)) .
            HTML_RowEnd() .

            HTML_RowBegin() .
            HTML_HeaderCell(__bx('pages::property_details.Multi_Operations') . ':') .
            HTML_ValueCell(
                HTML_SelectBegin('multi_operations') .
                HTML_Option(
                    Constants::C_True_Char,
                    __bx('pages::property_details.Yes'),
                    $property->getMultiOperations()
                ) .
                HTML_Option(
                    Constants::C_False_Char,
                    __bx('pages::property_details.No'),
                    !$property->getMultiOperations()
                ) .
                HTML_SelectEnd()
            ) .
            HTML_RowEnd() .

            HTML_TableEnd();

        //##### Show Page Types #####

        $content .= '<div id="page-types-table">';
        $content .= $this->generatePageTypesTable();
        $content .= '</div>';

        if (empty($this->property_id)) {
            $content .= HTML_SubmitButton(
                __bx('pages::property_details.Apply_Insert'),
                __bx('pages::property_details.Apply_Insert')
            );
        } else {
            $content .= HTML_SubmitButton(
                __bx('pages::property_details.Apply_Edit'),
                __bx('pages::property_details.Apply_Edit')
            );
        }

        $content .= HTML_FormEnd();

        DisplayHtmlHeader(
            __bx('pages::property_details.TemplatesPageTitle'),
            Constants::C_MenuType_Templates,
            $this->getJavascript()
        );
        echo $content;
        DisplayHtmlFooter();
    }

    public function generatePageTypesTable()
    {
        $content =
            HTML_TableBegin(__bx('pages::property_details.TableHeader2'), 4) .

            HTML_RowBegin() .
            HTML_HeaderCell(__bx('pages::property_details.Page_Type_Name'), 1, 1, '', '25%') .
            HTML_HeaderCell(__bx('pages::property_details.AssignedToPageType'), 1, 1, '', 100) .
            HTML_HeaderCell(__bx('pages::property_details.RequiedOnPageType')) .
            HTML_HeaderCell(__bx('pages::property_details.zdedene_od')) .
            HTML_RowEnd();

        $page_types = PageTypesManager::getInstance()->getAllPageTypes();

        foreach ($page_types as $page_type) {
            $page_type = PageTypesManager::getInstance()->getPageTypeById($page_type->getId());

            $property_item = $page_type->getPropertyItemForPropertyId($this->property_id);

            $assigned = (!is_null($property_item));
            $inherited = (is_null($property_item) ? false : $property_item->getInherited());
            $required = (is_null($property_item) ? false : $property_item->getRequired());

            $content .=
                HTML_RowBegin('page_type_row_' . $page_type->getId(), $inherited ? 'background:#aaaaaa' : '') .
                HTML_HeaderCell(
                    HTML_Link("page_type_details.php?page_type_id=" . $page_type->getId(), $page_type->getName()),
                    1,
                    1,
                    ($inherited ? 'left inherited' : 'left')
                );
            if ($inherited) {
                $content .=
                    HTML_ValueCell(
                        HTML_Checkbox(
                            "dummy_assigned_" . $page_type->getId(),
                            'on',
                            true,
                            '',
                            'disabled="disabled"'
                        )
                    ) .
                    HTML_ValueCell(
                        HTML_Checkbox(
                            "dummy_required_" . $page_type->getId(),
                            '1',
                            $required,
                            '',
                            'disabled="disabled"'
                        )
                    );
            } else {
                $content .=
                    HTML_ValueCell(
                        HTML_Checkbox(
                            "assigned_" . $page_type->getId(),
                            'on',
                            $assigned,
                            'setRequiredCheckboxState("' . $page_type->getId() . '")',
                            ($inherited) ? 'disabled="disabled"' : ''
                        )
                    ) .
                    HTML_ValueCell(
                        HTML_Checkbox(
                            "required_" . $page_type->getId(),
                            '1',
                            $required,
                            'setRequiredCheckboxState("' . $page_type->getId() . '")',
                            (!$assigned) ? 'disabled="disabled"' : '',
                            "required_" . $page_type->getId()
                        )
                    );
            }

            if (!is_null($property_item) && $property_item->getInherited()) {
                $inherited_page_type =
                    PageTypesManager::getInstance()->getPageTypeById($property_item->getSourcePageTypeId());
                $tmp = '<span data-inherited-from="' .
                    $inherited_page_type->getId() . '">' .
                    htmlspecialchars($inherited_page_type->getName()) . '</span>';
            } else {
                $tmp = '&nbsp;';
            }
            $content .= HTML_ValueCell($tmp);
            $content .= HTML_RowEnd();
        }

        $content .= HTML_TableEnd();

        return $content;
    }

    protected function getJavascript()
    {
        // phpcs:disable
        $content = '<script type="text/javascript" src="../includes/javascript/property_details.js?' . LegacyMethods::getStaticFileRefresh() . '"></script>';
        $content .= '<script type="text/javascript" src="../includes/SelectFieldUtils.js?' . LegacyMethods::getStaticFileRefresh() . '"></script>';
        $content .= '<script type="text/javascript" src="' . config('buxus_core.base_url') . 'includes/jquery/js/jquery.autosize.min.js?' . LegacyMethods::getStaticFileRefresh() . '"></script>
                        <script type="text/javascript" src="' . config('buxus_core.base_url') . 'includes/tinymce/tinymce.full.min.js?' . LegacyMethods::getStaticFileRefresh() . '"></script>
                        <script type="text/javascript" src="' . config('buxus_core.base_url') . 'includes/tinymce/jquery.tinymce.min.js?' . LegacyMethods::getStaticFileRefresh() . '"></script>
                        <script type="text/javascript" src="' . config('buxus_core.base_url') . 'includes/javascript/jquery.buxusTiny.js?' . LegacyMethods::getStaticFileRefresh() . '"></script>';
        // phpcs:enable
        return $content;
    }

    public function getValueTypeForm($value_type)
    {
        $property = $this->getProperty();
        $property->setValueType($value_type);
        $value_type_object = $property->getValueTypeObject();
        return $value_type_object->renderForm();
    }

    public function simulateCheckState($request)
    {
        $page_types = PageTypesManager::getInstance()->getAllPageTypes();

        $processed_page_types = array();
        foreach ($page_types as $page_type) {
            $page_type = PageTypesManager::getInstance()->getPageTypeById($page_type->getId());
            $this->simulateCheckStateItem($page_type, $request, $processed_page_types);
        }

        return $page_types;
    }

    protected function simulateCheckStateItem(PageType $page_type, $request, &$processed_page_types)
    {
        if (in_array($page_type->getId(), $processed_page_types)) {
            return;
        }
        $processed_page_types[] = $page_type->getId();

        $parent = $page_type->getParent();
        if (!is_null($parent)) {
            $this->simulateCheckStateItem($parent, $request, $processed_page_types);
        }

        $page_type_id = $page_type->getId();
        $page_type_checked = ($request['assigned_' . $page_type_id] ?? '');
        $required_on_page_type = ($request['required_' . $page_type_id] ?? '0');

        if (!$page_type_checked) {
            $page_type->removePropertyItemById($this->property_id);
        } else {
            $item = new PageTypePropertyItem($this->getProperty());
            $item->setRequired(($required_on_page_type == 1));
            $page_type->addPropertyItem($item);
        }
    }
}
