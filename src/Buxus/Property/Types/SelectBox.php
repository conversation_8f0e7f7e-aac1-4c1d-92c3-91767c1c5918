<?php

namespace Buxus\Property\Types;

use Buxus\Property\Attributes\PropertyAttribute;
use Buxus\Property\Property;
use Buxus\Property\PropertyValueInterface;

class SelectBox extends Property
{
    public function getType()
    {
        return __bx('core::property_type.selectbox');
    }

    public function loadDefaultAttributes()
    {
        parent::loadDefaultAttributes();

        $this->addDefaultAttribute(
            'options',
            __bx('core::property_attribute.items'),
            PropertyAttribute::LIST_TYPE
        );
        $this->addDefaultAttribute(
            'multiple',
            __bx('core::property_attribute.multivalued'),
            PropertyAttribute::CHECKBOX_TYPE
        );

        return $this->default_property_attributes;
    }

    public function getLabelFor($key)
    {
        $options = $this->getPropertyAttribute('options')->value;
        if (is_array($options)) {
            foreach ($options as $option) {
                if ($option->key == $key) {
                    return $option->value;
                }
            }
        }

        return false;
    }

    public function getOptions()
    {
        return $this->getPropertyAttribute('options')->value;
    }

    public function saveValue(PropertyValueInterface $value, $page_id)
    {
        if ($this->isMultivalued()) {
            $value_export = json_encode($value->getValue());
            $value->setValue($value_export);
        }
        parent::saveValue($value, $page_id);
    }

    public function isMultivalued()
    {
        return ($this->getPropertyAttribute('multiple')->value == 'T');
    }

    public function getValueForPage($page_id)
    {
        $value = parent::getValueForPage($page_id);

        if ($this->isMultivalued()) {
            if ($value) {
                $value = json_decode($value);
            } else {
                $value = [];
            }
        }

        return $value;
    }
}
