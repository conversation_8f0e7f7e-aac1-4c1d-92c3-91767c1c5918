<?php

namespace FS\Property;

use Buxus\Page\PageInterface;
use Buxus\Property\Exception\NotFoundException;
use Buxus\Property\PropertyInterface;
use Buxus\Util\Url;
use FS\FSSearchInterface;
use FS\Property\Seo\SeoCrawlerRuleAwarePropertyInterface;
use FS\Property\Seo\SeoCrawlerRuleAwarePropertyTrait;
use FS\Property\Seo\SeoTitleAppendablePropertyInterface;
use FS\Property\Seo\SeoTitleAppendablePropertyTrait;
use FS\Value\FSValueInterface;

abstract class Property implements FSPropertyInterface, SeoTitleAppendablePropertyInterface, SeoCrawlerRuleAwarePropertyInterface
{
    use SeoCrawlerRuleAwarePropertyTrait;
    use SeoTitleAppendablePropertyTrait;

    protected $label = null;

    protected $tag;

    protected $values;

    protected $filters;

    protected $multivalued = false;

    protected $lines_limit = false;

    protected $hide_empty_values = false;

    protected $results_ajax_url;

    protected $hidden = false;

    protected $is_virtual = false;

    protected $url_name = '';

    use SortablePropertyTrait;

    /**
     * @var FSSearchInterface
     */
    protected $owner;

    public function __construct($property_tag)
    {
        $this->tag = $property_tag;
    }

    public function isVirtual()
    {
        return $this->is_virtual;
    }

    public function setIsVirtual($is_virtual)
    {
        $this->is_virtual = $is_virtual;
    }

    public function setMultivalued($is_multivalued)
    {
        $this->multivalued = $is_multivalued;
    }

    public function isMultivalued()
    {
        return $this->multivalued;
    }

    public function tryFilter($filter)
    {
        $this->addFilter($filter);
    }

    public function addFilter($filter)
    {
        $this->filters[$filter] = $filter;
    }

    public function applyPossibilityFilter()
    {
        $this->resetFilters();
    }

    public function resetFilters()
    {
        $this->filters = null;
    }

    public function removeFilter($filter)
    {
        unset($this->filters[$filter]);
    }

    public function resetAllFilters($user_initiated = false)
    {
        $this->resetFilters();
    }

    public function isFilter($filter_value)
    {
        return isset($this->filters[$filter_value]);
    }

    public function getLabel()
    {
        if (is_null($this->label)) {
            $this->label = $this->getDefaultLabelFromProperty();
        }
        return $this->label;
    }

    public function setLabel($label)
    {
        $this->label = $label;
    }

    protected function getDefaultLabelFromProperty()
    {
        try {
            /**
             * @var PropertyInterface $property
             */
            $property = \Buxus\Property\Facade\PropertyManager::getPropertyByTag($this->getTag());
            $label = $property->getName();
        } catch (NotFoundException $e) {
            $label = $this->getTag();
        }
        return $label;
    }

    public function getTag()
    {
        return $this->tag;
    }

    public function addValue(FSValueInterface $value)
    {
        $this->values[] = $value;
    }

    public function getValues()
    {
        return $this->values;
    }

    public function setValues(array $value_list)
    {
        $this->values = $value_list;
    }

    public function processValueForPage(PageInterface $page)
    {
        return $page->getValue($this->getTag());
    }

    public function getHtml()
    {
        return '';
    }

    abstract public function getSummaryLine();

    public function isAplied()
    {
        return !empty($this->filters);
    }

    public function setHideEmptyValues($boolean)
    {
        $this->hide_empty_values = $boolean;
    }

    public function getLinesLimit()
    {
        return $this->lines_limit;
    }

    public function setLinesLimit($lines_limit)
    {
        $this->lines_limit = $lines_limit;
    }

    public function setHidden($value)
    {
        $this->hidden = $value;
    }

    public function isHidden()
    {
        return $this->hidden;
    }

    public function __clone()
    {
        if (is_array($this->values)) {
            $values = array();
            foreach ($this->values as $key => $value) {
                if (is_object($value)) {
                    $values[$key] = clone $value;
                } else {
                    $values[$key] = $value;
                }
            }
            $this->values = $values;
        } elseif (is_object($this->values)) {
            $this->values = clone $this->values;
        }
    }

    public function filtersChanged()
    {
    }

    public function getUrlSegment()
    {
        $url = $this->getUrlName() . '=' . ($this->urlencodeFilters());
        return $url;
    }

    public function getUrlName()
    {
        return $this->url_name ? $this->url_name : $this->getTag();
    }

    public function setUrlName($url_name)
    {
        $this->url_name = $url_name;
    }

    protected function urlencodeFilters()
    {
        $encoded = implode(',', $this->getFilters());
        return urlencode($encoded);
    }

    public function getFilters()
    {
        return $this->filters;
    }

    public function setFilters($filters)
    {
        $this->filters = $filters;
    }

    public function filterApplicableValues()
    {
        return $this->getOwner()->filterApplicableValues($this);
    }

    /**
     * @return FSSearchInterface
     */
    public function getOwner()
    {
        return $this->owner;
    }

    public function setOwner($owner)
    {
        $this->owner = $owner;
    }

    public function getFilterClassName()
    {
        return $this->getTag();
    }

    public function isValueApplicable($value_value, $possible_values)
    {
        if (!is_array($possible_values)) {
            return false;
        }
        return in_array($value_value, $possible_values);
    }

    public function getValueUrl($value)
    {
        if ($value instanceof FSValueInterface) {
            $value = $value->getValue();
        }
        return $this->owner->getUrlForFilter($this->getTag(), $value);
    }

    public function getValueDirectUrlWithFilterParams($value, $excluded_tags = [])
    {
        if (!is_array($excluded_tags)) {
            $excluded_tags = [];
        }
        $excluded_tags[] = $this->getTag();
        return Url::page($value->getValue(), $this->owner->generateParamsHash($excluded_tags));
    }

    public function getValueActionUrl($value)
    {
        if ($value instanceof FSValueInterface) {
            $value = $value->getValue();
        }
        return '&action=add&filter_tag=' . $this->getTag() . '&filter_value=' . $value;
    }

    public function getValueUnsetUrl($value)
    {
        if ($value instanceof FSValueInterface) {
            $value = $value->getValue();
        }
        return '&action=del&filter_tag=' . $this->getTag() . '&filter_value=' . $value;
    }

    /**
     * @param $id
     * @return FSValueInterface
     */
    public function getValueById($id)
    {
        if (is_array($this->values)) {
            foreach ($this->values as $value) {
                /**
                 * @var FSValueInterface $value
                 */
                if ($value->getValue() == $id) {
                    return $value;
                }
            }
        }
        return null;
    }

    public function getResetUrl()
    {
        return '&action=resetone&filter_tag=' . $this->getTag();
    }

    public function setFiltersFromParam($param_value)
    {
        $old_filters = (is_array($this->filters) ? $this->filters : array());

        $this->resetFilters();

        if (!is_null($param_value)) {
            $filter_values = $this->urldecodeFilters($param_value);
            foreach ($filter_values as $filter_value) {
                $this->addFilter($filter_value);
            }
        }

        $new_filters = (is_array($this->filters) ? $this->filters : array());

        if (count($old_filters) != count($new_filters) || count(array_intersect($old_filters,
                $new_filters)) < count($new_filters)) {
            $this->owner->markChange(FSSearchInterface::CHANGED_FILTERS + FSSearchInterface::CHANGED_RESULTS + FSSearchInterface::CHANGED_PAGING);
        }
    }

    protected function escapeDecodedValues($value)
    {
        return $this->getOwner()->escapeDecodedPropertyValue($this, $value);
    }

    public function urldecodeFilters($values)
    {
        $values = explode(',', $values);
        $result = [];
        foreach ($values as $value) {
            $escapedValue = $this->escapeDecodedValues($value);

            if ($escapedValue !== null && $escapedValue !== false) {
                $result[$escapedValue] = $escapedValue;
            }
        }
        return $result;
    }
}
