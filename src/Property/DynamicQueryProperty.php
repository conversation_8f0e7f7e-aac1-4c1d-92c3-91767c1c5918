<?php
namespace DynamicCategory\Property;

use Buxus\Property\Attributes\PropertyAttribute;
use Buxus\Property\Property;

class DynamicQueryProperty extends Property
{
    /**
     * @return array|\Illuminate\Support\Collection
     */
    public function loadDefaultAttributes()
    {
        parent::loadDefaultAttributes();

        $this->addDefaultAttribute('query_type', __bx('dynamic-category::property.query_type_attribute'), PropertyAttribute::INPUT_TYPE, 'default');

        return $this->default_property_attributes;
    }

    public function getType()
    {
        return __bx('dynamic-category::property.dynamic_query');
    }

    public function getViewTemplate()
    {
        return 'dynamic-category::property.dynamic_query';
    }
}