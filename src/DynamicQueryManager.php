<?php

namespace DynamicCategory;

use Buxus\Stdlib\ArrayUtils;

class DynamicQueryManager
{
    /**
     * @var array
     */
    protected $queryTypes = [];

    public function __construct()
    {
        $this->loadQueryTypesFromConfig();
    }

    protected function loadQueryTypesFromConfig()
    {
        $items = config('dynamic_categories.queries', []);
        if (!is_array($items)) {
            $items = [];
        }

        $moreConfigs = config('dynamic_categories_items', []);
        foreach ($moreConfigs as $moreConfig) {
            if (isset($moreConfig['queries']) && is_array($moreConfig['queries'])) {
                $items = ArrayUtils::merge($items, $moreConfig['queries']);
            }
        }

        foreach ($items as $tag => $data) {
            $this->addQueryType($tag, $data['fs_tag'], $data['items']);
        }
    }

    /**
     * @param $tag
     * @param $fs_tag
     * @param $items
     */
    public function addQueryType($tag, $fs_tag, $items)
    {
        $this->queryTypes[$tag] = [
            'fs_tag' => $fs_tag,
            'items' => $items,
        ];
    }

    /**
     * @param $type
     * @return mixed
     */
    public function getAddableItemsForType($type)
    {
        if (!isset($this->queryTypes[$type])) {
            throw new \InvalidArgumentException('Invalid query type: ' . $type);
        }
        
        return $this->queryTypes[$type]['items'];
    }

    /**
     * @param string $type
     * @param string $query
     * @param array $static_items
     * @param null $sort
     * @return DynamicQuery
     */
    public function createQuery($type, $query, $static_items = [], $sort = null)
    {
        if (!isset($this->queryTypes[$type])) {
            throw new \InvalidArgumentException('Invalid query type: ' . $type);
        }

        $params = $this->queryTypes[$type];

        /**
         * @var DynamicQuery $item
         */
        $item = app()->make('buxus:dynamic-query:default-query-class');
        $item->setQueryType($type);
        $item->setFsTag($params['fs_tag']);
        $item->setQuery($query);

        if (count($static_items)) {
            $item->setStaticItems($static_items);
        }

        if (!empty($sort)) {
            $parts = explode('|', $sort);
            if (count($parts)) {
                $item->setSort($parts[0]);
                if (count($parts) > 1) {
                    $item->setSortMode($parts[1]);
                }
            }
        }

        return $item;
    }
}
