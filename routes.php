<?php

Route::group(['prefix' => 'buxus/dashboard', 'middleware' => ['buxus.auth', 'buxus_admin']], static function () {
    Route::get('/', '\Buxus\Dashboard\Controller\BuxusDashboardController@dashboard')->name('buxus:admin:dashboard');
    Route::get('/settings', '\Buxus\Dashboard\Controller\BuxusDashboardController@settings')->name('buxus:admin:dashboard:settings');

    Route::post('/save-grid', '\Buxus\Dashboard\Controller\BuxusDashboardController@saveGrid');
    Route::post('/remove-widget', '\Buxus\Dashboard\Controller\BuxusDashboardController@removeWidget');
    Route::post('/save-settings', '\Buxus\Dashboard\Controller\BuxusDashboardController@saveSettings');

    Route::get('/widget-settings/{tag}', '\Buxus\Dashboard\Controller\BuxusDashboardController@widgetSettings')
        ->where('tag', '[a-zA-Z0-9.\-_]+');
    Route::post(
        '/save-widget-settings/{tag}',
        '\Buxus\Dashboard\Controller\BuxusDashboardController@saveWidgetSettings'
    )->where('tag', '[a-zA-Z0-9.\-_]+');
});
