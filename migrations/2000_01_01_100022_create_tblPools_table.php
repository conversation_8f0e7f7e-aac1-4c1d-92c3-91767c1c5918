<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateTblPoolsTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('tblPools', function(Blueprint $table)
		{
			$table->integer('pool_id', true);
			$table->string('pool_tag', 30)->nullable()->index('pool_tag');
			$table->char('is_active', 1)->nullable();
			$table->integer('user_id')->nullable();
			$table->dateTime('time_created')->nullable();
			$table->char('disalow_multiple_votes_flag', 10)->nullable();
			$table->text('the_question', 65535)->nullable();
			$table->string('pool_type', 100)->nullable();
			$table->integer('page_id');
			$table->string('language', 20)->nullable();
			$table->dateTime('sort_date_time')->nullable();
			$table->string('show_style', 10)->nullable()->default('active');
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('tblPools');
	}

}
