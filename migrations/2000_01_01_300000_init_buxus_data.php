<?php

use Buxus\Migration\AbstractMigration;

class InitBuxusData extends AbstractMigration
{
    public function dependencies()
    {
        return [
            \CreateTblAccessRightsPageUserTable::class,
            \CreateTblAccessRightsWorkflowPageUserTable::class,
            \CreateTblPageStatesTable::class,
            \CreateTblPageTypesTable::class,
            \CreateTblUsersTable::class,
        ];
    }

    public function up()
    {
        $init_script_path = __DIR__ . DIRECTORY_SEPARATOR . 'sql' . DIRECTORY_SEPARATOR . 'init_buxus_data.sql';
        DB::unprepared(File::get($init_script_path));
    }

    public function down()
    {
    }
}
