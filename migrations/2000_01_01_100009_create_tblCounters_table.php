<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;

class CreateTblCountersTable extends Migration {

	/**
	 * Run the migrations.
	 *
	 * @return void
	 */
	public function up()
	{
		Schema::create('tblCounters', function(Blueprint $table)
		{
			$table->integer('counter_id', true);
			$table->string('counter_tag', 50)->nullable()->index('counter_tag');
			$table->char('is_active', 1)->nullable();
			$table->string('counter_type', 16)->nullable();
			$table->integer('total_value')->nullable();
			$table->integer('page_id');
		});
	}


	/**
	 * Reverse the migrations.
	 *
	 * @return void
	 */
	public function down()
	{
		Schema::drop('tblCounters');
	}

}
