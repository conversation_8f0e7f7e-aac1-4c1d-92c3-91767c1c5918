<?php

namespace DynamicCategory\Migrations;

use Buxus\Migration\AbstractMigration;
use Buxus\Property\Property;
use Buxus\Property\Types\PageList;
use DynamicCategory\Property\DynamicQueryProperty;
use DynamicCategory\Property\FilterMask;
use TreeProperty\Property\TreeProperty;

class DynamicCategories extends AbstractMigration
{
    public function up()
    {
        // page type: Eshop (eshop)
        $pageTypeEshop = $this->pageTypesManager()->pageTypeExistsByTag('eshop');
        if ($pageTypeEshop === false) {
            $pageTypeEshop = new \Buxus\PageType\PageType();
            $pageTypeEshop->setTag('eshop');
            $pageTypeEshop->setName('Eshop');
            $pageTypeEshop->setPageClassId('1');
            $pageTypeEshop->setDefaultTemplateId('2');
            $pageTypeEshop->setDeleteTrigger('');
            $pageTypeEshop->setIncludeInSync(NULL);
            $pageTypeEshop->setPageDetailsLayout('');
            $pageTypeEshop->setPageSortTypeTag('sort_date_time');
            $pageTypeEshop->setPageTypeOrder('0');
            $pageTypeEshop->setPostmoveTrigger('');
            $pageTypeEshop->setPostsubmitTrigger('');
            $pageTypeEshop->setPresubmitTrigger('');
            $pageTypeEshop->setParent(NULL);

        } else {
            $this->writeLine('Page type with tag eshop already exists');
            $this->setDataKey('page_type_eshop_existed', true);
        }
        $this->pageTypesManager()->savePageType($pageTypeEshop);

        // page type: Eshop kategórie (eshop_categories)
        $pageTypeEshopCategories = $this->pageTypesManager()->pageTypeExistsByTag('eshop_categories');
        if ($pageTypeEshopCategories === false) {
            $pageTypeEshopCategories = new \Buxus\PageType\PageType();
            $pageTypeEshopCategories->setTag('eshop_categories');
            $pageTypeEshopCategories->setName('Eshop kategórie');
            $pageTypeEshopCategories->setPageClassId('1');
            $pageTypeEshopCategories->setDefaultTemplateId('2');
            $pageTypeEshopCategories->setDeleteTrigger('');
            $pageTypeEshopCategories->setIncludeInSync(NULL);
            $pageTypeEshopCategories->setPageDetailsLayout('');
            $pageTypeEshopCategories->setPageSortTypeTag('sort_date_time');
            $pageTypeEshopCategories->setPageTypeOrder('0');
            $pageTypeEshopCategories->setPostmoveTrigger('');
            $pageTypeEshopCategories->setPostsubmitTrigger('');
            $pageTypeEshopCategories->setPresubmitTrigger('');
            $parent = $this->pageTypesManager()->getPageTypeByTag('eshop');
            $pageTypeEshopCategories->setParent($parent);

        } else {
            $this->writeLine('Page type with tag eshop_categories already exists');
            $this->setDataKey('page_type_eshop_categories_existed', true);
        }
        $this->pageTypesManager()->savePageType($pageTypeEshopCategories);

        // property: Titulok(title)
        $propertyTitle = $this->propertyManager()->propertyExistsByTag('title');
        if ($propertyTitle === false) {
            $propertyTitle = new \Buxus\Property\Types\Input();
            $propertyTitle->setTag('title');
            $propertyTitle->setDescription('Štandardný názov stránky. Text, ktorý sa zobrazí v záložke prehliadača. Podľa SEO odporúčaní max. 65 znakov.');
            $propertyTitle->setExtendedDescription('');
            $propertyTitle->setName('Titulok');
            $propertyTitle->setClassId('4');
            $propertyTitle->setShowType(NULL);
            $propertyTitle->setShowTypeTag('text');
            $propertyTitle->setValueType('oneline_text');
            $propertyTitle->setDefaultValue('');
            $propertyTitle->setMultiOperations(false);
            $propertyTitle->setInputString(NULL);
            $propertyTitle->setAttribute('tab', '');
            $propertyTitle->setAttribute('size', '60');
            $propertyTitle->setAttribute('maxlength', '');
            $propertyTitle->setAttribute('readonly', 'F');
            $propertyTitle->setAttribute('pattern', '');
            $propertyTitle->setAttribute('inherit_value', 'F');
            $propertyTitle->setAttribute('onchange-js', '');
            $propertyTitle->setAttribute('onkeyup-js', '');
            $propertyTitle->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertyTitle);

        } else {
            $this->writeLine('Property with tag title already exists');
            $this->setDataKey('property_title_existed', true);
        }

        // property: Anotácia(annotation)
        $propertyAnnotation = $this->propertyManager()->propertyExistsByTag('annotation');
        if ($propertyAnnotation === false) {
            $propertyAnnotation = new \Buxus\Property\Types\Textarea();
            $propertyAnnotation->setTag('annotation');
            $propertyAnnotation->setDescription('Štandardná anotácia stránky.');
            $propertyAnnotation->setExtendedDescription('');
            $propertyAnnotation->setName('Anotácia');
            $propertyAnnotation->setClassId('4');
            $propertyAnnotation->setShowType(NULL);
            $propertyAnnotation->setShowTypeTag('textarea');
            $propertyAnnotation->setValueType('multiline_text');
            $propertyAnnotation->setDefaultValue('');
            $propertyAnnotation->setMultiOperations(false);
            $propertyAnnotation->setInputString(NULL);
            $propertyAnnotation->setAttribute('tab', '');
            $propertyAnnotation->setAttribute('cols', '60');
            $propertyAnnotation->setAttribute('rows', '');
            $propertyAnnotation->setAttribute('dhtml-edit', '1');
            $propertyAnnotation->setAttribute('dhtml-configuration', 'full');
            $propertyAnnotation->setAttribute('import-word', '0');
            $propertyAnnotation->setAttribute('auto', '1');
            $propertyAnnotation->setAttribute('inherit_value', 'F');
            $propertyAnnotation->setAttribute('onchange-js', '');
            $propertyAnnotation->setAttribute('onkeyup-js', '');
            $propertyAnnotation->setAttribute('onkeydown-js', '');
            $propertyAnnotation->setAttribute('pattern', '');
            $this->propertyManager()->saveProperty($propertyAnnotation);

        } else {
            $this->writeLine('Property with tag annotation already exists');
            $this->setDataKey('property_annotation_existed', true);
        }

        // property: Text(text)
        $propertyText = $this->propertyManager()->propertyExistsByTag('text');
        if ($propertyText === false) {
            $propertyText = new \Buxus\Property\Types\Textarea();
            $propertyText->setTag('text');
            $propertyText->setDescription('Štandardný text stránky.');
            $propertyText->setExtendedDescription('');
            $propertyText->setName('Text');
            $propertyText->setClassId('4');
            $propertyText->setShowType(NULL);
            $propertyText->setShowTypeTag('textarea');
            $propertyText->setValueType('multiline_text');
            $propertyText->setDefaultValue('');
            $propertyText->setMultiOperations(false);
            $propertyText->setInputString(NULL);
            $propertyText->setAttribute('tab', '');
            $propertyText->setAttribute('cols', '60');
            $propertyText->setAttribute('rows', '');
            $propertyText->setAttribute('dhtml-edit', '1');
            $propertyText->setAttribute('dhtml-configuration', 'full');
            $propertyText->setAttribute('import-word', '0');
            $propertyText->setAttribute('auto', '1');
            $propertyText->setAttribute('inherit_value', 'F');
            $propertyText->setAttribute('onchange-js', '');
            $propertyText->setAttribute('onkeyup-js', '');
            $propertyText->setAttribute('onkeydown-js', '');
            $propertyText->setAttribute('pattern', '');
            $propertyText->setPropertyAttribute('dhtml_edit', 1);

            $this->propertyManager()->saveProperty($propertyText);

        } else {
            $this->writeLine('Property with tag text already exists');
            $this->setDataKey('property_text_existed', true);
        }

        // property: META title(meta_title)
        $propertyMetaTitle = $this->propertyManager()->propertyExistsByTag('meta_title');
        if ($propertyMetaTitle === false) {
            $propertyMetaTitle = new TreeProperty();
            $propertyMetaTitle->setTag('meta_title');
            $propertyMetaTitle->setDescription('Titulok stránky');
            $propertyMetaTitle->setExtendedDescription('');
            $propertyMetaTitle->setName('META title');
            $propertyMetaTitle->setClassId('4');
            $propertyMetaTitle->setShowType(NULL);
            $propertyMetaTitle->setShowTypeTag('custom_property');
            $propertyMetaTitle->setValueType('custom_property');
            $propertyMetaTitle->setDefaultValue('');
            $propertyMetaTitle->setMultiOperations(false);
            $propertyMetaTitle->setInputString('');
            $propertyMetaTitle->setAttribute('tab', 'SEO');
            $propertyMetaTitle->setAttribute('class_name', \TreeProperty\BuxusProperty::class);
            $propertyMetaTitle->setAttribute('inherit_value', 'F');
            $this->propertyManager()->saveProperty($propertyMetaTitle);

        } else {
            $this->writeLine('Property with tag meta_title already exists');
            $this->setDataKey('property_meta_title_existed', true);
        }

        // property: META description(meta_description)
        $propertyMetaDescription = $this->propertyManager()->propertyExistsByTag('meta_description');
        if ($propertyMetaDescription === false) {
            $propertyMetaDescription = new TreeProperty();
            $propertyMetaDescription->setTag('meta_description');
            $propertyMetaDescription->setDescription('Podľa SEO odporúčaní max. 170 znakov.');
            $propertyMetaDescription->setExtendedDescription('');
            $propertyMetaDescription->setName('META description');
            $propertyMetaDescription->setClassId('4');
            $propertyMetaDescription->setShowType(NULL);
            $propertyMetaDescription->setShowTypeTag('custom_property');
            $propertyMetaDescription->setValueType('custom_property');
            $propertyMetaDescription->setDefaultValue('');
            $propertyMetaDescription->setMultiOperations(false);
            $propertyMetaDescription->setInputString(NULL);
            $propertyMetaDescription->setAttribute('tab', 'SEO');
            $propertyMetaDescription->setAttribute('class_name', \TreeProperty\BuxusProperty::class);
            $propertyMetaDescription->setAttribute('inherit_value', 'F');
            $this->propertyManager()->saveProperty($propertyMetaDescription);

        } else {
            $this->writeLine('Property with tag meta_description already exists');
            $this->setDataKey('property_meta_description_existed', true);
        }

        // property: Dotaz na produkty(query)
        $propertyQuery = $this->propertyManager()->propertyExistsByTag('query');
        if ($propertyQuery === false) {
            $propertyQuery = new DynamicQueryProperty();
            $propertyQuery->setTag('query');
            $propertyQuery->setDescription('');
            $propertyQuery->setExtendedDescription('');
            $propertyQuery->setName('Dotaz na produkty');
            $propertyQuery->setClassId('4');
            $propertyQuery->setShowType(NULL);
            $propertyQuery->setShowTypeTag('custom_property');
            $propertyQuery->setValueType('custom_property');
            $propertyQuery->setDefaultValue('');
            $propertyQuery->setMultiOperations(false);
            $propertyQuery->setInputString('');
            $propertyQuery->setAttribute('tab', '');
            $propertyQuery->setAttribute('class_name', \DynamicCategory\DynamicQueryProperty::class);
            $propertyQuery->setAttribute('inherit_value', 'F');
            $this->propertyManager()->saveProperty($propertyQuery);

        } else {
            $this->writeLine('Property with tag query already exists');
            $this->setDataKey('property_query_existed', true);
        }

        // property: Maska pre filter(filter_mask)
        $propertyFilterMask = $this->propertyManager()->propertyExistsByTag('filter_mask');
        if ($propertyFilterMask === false) {
            $propertyFilterMask = new FilterMask();
            $propertyFilterMask->setTag('filter_mask');
            $propertyFilterMask->setDescription('');
            $propertyFilterMask->setExtendedDescription('');
            $propertyFilterMask->setName('Maska pre filter');
            $propertyFilterMask->setClassId('4');
            $propertyFilterMask->setShowType(NULL);
            $propertyFilterMask->setShowTypeTag('custom_property');
            $propertyFilterMask->setValueType('custom_property');
            $propertyFilterMask->setDefaultValue('');
            $propertyFilterMask->setMultiOperations(false);
            $propertyFilterMask->setInputString('');
            $propertyFilterMask->setAttribute('tab', '');
            $propertyFilterMask->setAttribute('class_name', \DynamicCategory\DynamicQueryFilterMaskProperty::class);
            $propertyFilterMask->setAttribute('inherit_value', 'F');
            $this->propertyManager()->saveProperty($propertyFilterMask);

        } else {
            $this->writeLine('Property with tag filter_mask already exists');
            $this->setDataKey('property_filter_mask_existed', true);
        }

        // property: Statický zoznam produktov(product_list)
        $propertyProductList = $this->propertyManager()->propertyExistsByTag('product_list');
        if ($propertyProductList === false) {
            $propertyProductList = new PageList();
            $propertyProductList->setTag('product_list');
            $propertyProductList->setDescription('Statický zoznam produktov, ktoré budú pridané k zoznamu dynamických produktov');
            $propertyProductList->setExtendedDescription('');
            $propertyProductList->setName('Statický zoznam produktov');
            $propertyProductList->setClassId('4');
            $propertyProductList->setShowType(NULL);
            $propertyProductList->setShowTypeTag('page_list');
            $propertyProductList->setValueType('page_list');
            $propertyProductList->setDefaultValue('');
            $propertyProductList->setMultiOperations(false);
            $propertyProductList->setInputString('');
            $propertyProductList->setAttribute('tab', '');
            $propertyProductList->setAttribute('root_page_id', '');
            $propertyProductList->setAttribute('page_type_id', '');
            $propertyProductList->setAttribute('default_sort', 'tblPages.sort_date_time');
            $propertyProductList->setAttribute('advanced_mode', 'T');
            $propertyProductList->setAttribute('external_url', 'F');
            $propertyProductList->setAttribute('max_items', '');
            $propertyProductList->setAttribute('middle_col_width', '');
            $propertyProductList->setAttribute('apply_user_rights', 'T');
            $propertyProductList->setAttribute('property_for_link_name', 'title');
            $propertyProductList->setAttribute('properties_for_search', 'tblPages.page_name,title');
            $this->propertyManager()->saveProperty($propertyProductList);

        } else {
            $this->writeLine('Property with tag product_list already exists');
            $this->setDataKey('property_product_list_existed', true);
        }

        // property: SEO URL name(seo_url_name)
        $propertySeoUrlName = $this->propertyManager()->propertyExistsByTag('seo_url_name');
        if ($propertySeoUrlName === false) {
            $propertySeoUrlName = new Property();
            $propertySeoUrlName->setTag('seo_url_name');
            $propertySeoUrlName->setDescription('Podľa SEO odporúčaní max. 35 znakov.');
            $propertySeoUrlName->setExtendedDescription('');
            $propertySeoUrlName->setName('SEO URL name');
            $propertySeoUrlName->setClassId('4');
            $propertySeoUrlName->setShowType(NULL);
            $propertySeoUrlName->setShowTypeTag('seo_url_name');
            $propertySeoUrlName->setValueType('seo_url_name');
            $propertySeoUrlName->setDefaultValue('');
            $propertySeoUrlName->setMultiOperations(false);
            $propertySeoUrlName->setInputString(NULL);
            $propertySeoUrlName->setAttribute('tab', 'SEO');
            $propertySeoUrlName->setAttribute('size', '80');
            $propertySeoUrlName->setAttribute('onchange-js', '');
            $propertySeoUrlName->setAttribute('onkeyup-js', '');
            $propertySeoUrlName->setAttribute('onkeydown-js', '');
            $this->propertyManager()->saveProperty($propertySeoUrlName);

        } else {
            $this->writeLine('Property with tag seo_url_name already exists');
            $this->setDataKey('property_seo_url_name_existed', true);
        }

        // page type: Dynamická kategória (dynamic_category)
        $pageTypeDynamicCategory = $this->pageTypesManager()->pageTypeExistsByTag('dynamic_category');
        if ($pageTypeDynamicCategory === false) {
            $pageTypeDynamicCategory = new \Buxus\PageType\PageType();
            $pageTypeDynamicCategory->setTag('dynamic_category');
            $pageTypeDynamicCategory->setName('Dynamická kategória');
            $pageTypeDynamicCategory->setPageClassId('1');
            $pageTypeDynamicCategory->setDefaultTemplateId('1');
            $pageTypeDynamicCategory->setDeleteTrigger('');
            $pageTypeDynamicCategory->setIncludeInSync(NULL);
            $pageTypeDynamicCategory->setPageDetailsLayout('');
            $pageTypeDynamicCategory->setPageSortTypeTag('sort_date_time');
            $pageTypeDynamicCategory->setPageTypeOrder('0');
            $pageTypeDynamicCategory->setPostmoveTrigger('');
            $pageTypeDynamicCategory->setPostsubmitTrigger('');
            $pageTypeDynamicCategory->setPresubmitTrigger('');
            $parent = $this->pageTypesManager()->getPageTypeByTag('eshop_categories');
            $pageTypeDynamicCategory->setParent($parent);

        } else {
            $this->writeLine('Page type with tag dynamic_category already exists');
            $this->setDataKey('page_type_dynamic_category_existed', true);
        }
        $property = $this->propertyManager()->getPropertyByTag('title');
        $propertyId = $property->getId();
        $tmp = $pageTypeDynamicCategory->getPropertyItemForPropertyId($propertyId);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('1');
            $tmp->setRequired(true);
            $pageTypeDynamicCategory->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('annotation');
        $propertyId = $property->getId();
        $tmp = $pageTypeDynamicCategory->getPropertyItemForPropertyId($propertyId);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('2');
            $tmp->setRequired(false);
            $pageTypeDynamicCategory->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('text');
        $propertyId = $property->getId();
        $tmp = $pageTypeDynamicCategory->getPropertyItemForPropertyId($propertyId);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('3');
            $tmp->setRequired(false);
            $pageTypeDynamicCategory->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('meta_title');
        $propertyId = $property->getId();
        $tmp = $pageTypeDynamicCategory->getPropertyItemForPropertyId($propertyId);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('4');
            $tmp->setRequired(false);
            $pageTypeDynamicCategory->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('meta_description');
        $propertyId = $property->getId();
        $tmp = $pageTypeDynamicCategory->getPropertyItemForPropertyId($propertyId);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('5');
            $tmp->setRequired(false);
            $pageTypeDynamicCategory->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('query');
        $propertyId = $property->getId();
        $tmp = $pageTypeDynamicCategory->getPropertyItemForPropertyId($propertyId);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('6');
            $tmp->setRequired(false);
            $pageTypeDynamicCategory->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('filter_mask');
        $propertyId = $property->getId();
        $tmp = $pageTypeDynamicCategory->getPropertyItemForPropertyId($propertyId);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('7');
            $tmp->setRequired(false);
            $pageTypeDynamicCategory->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('product_list');
        $propertyId = $property->getId();
        $tmp = $pageTypeDynamicCategory->getPropertyItemForPropertyId($propertyId);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('8');
            $tmp->setRequired(false);
            $pageTypeDynamicCategory->addPropertyItem($tmp);
        }
        $property = $this->propertyManager()->getPropertyByTag('seo_url_name');
        $propertyId = $property->getId();
        $tmp = $pageTypeDynamicCategory->getPropertyItemForPropertyId($propertyId);
        if ($tmp === null) {
            $tmp = new \Buxus\PageType\PageTypePropertyItem($property);
            $tmp->setOrder('9');
            $tmp->setRequired(false);
            $pageTypeDynamicCategory->addPropertyItem($tmp);
        }
        if ($this->pageTypeExists('eshop_catalog')) {
            $pageTypeDynamicCategory->addSuperiorPageType($this->getPageTypeByTag('eshop_catalog'));
        }
        if ($this->pageTypeExists('eshop_category')) {
            $pageTypeDynamicCategory->addSuperiorPageType($this->getPageTypeByTag('eshop_category'));
        }
        if ($this->pageTypeExists('eshop_subcategory')) {
            $pageTypeDynamicCategory->addSuperiorPageType($this->getPageTypeByTag('eshop_subcategory'));
        }
        if ($this->pageTypeExists('eshop_subcategory_3')) {
            $pageTypeDynamicCategory->addSuperiorPageType($this->getPageTypeByTag('eshop_subcategory_3'));
        }

        $this->pageTypesManager()->savePageType($pageTypeDynamicCategory);
        // set template on MAIN PAGE eshop_catalog::dynamic-product-list
        $this->setPageTemplate($this->getMainPageId(), $this->getPageTypeIdByTag('dynamic_category'), 'product-catalog', 'dynamic-product-list');

        // regenerate page types constants
        $this->pageTypesManager()->generateConstants();

        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }

    public function down()
    {
        // remove page type: Dynamická kategória (dynamic_category)
        $pageTypeDynamicCategory = $this->pageTypesManager()->pageTypeExistsByTag('dynamic_category');
        if (($pageTypeDynamicCategory != false) && (is_null($this->getDataKey('page_type_dynamic_category_existed')))) {
            $this->pageTypesManager()->removePageType($pageTypeDynamicCategory);
        }

        // remove property: SEO URL name(seo_url_name)
        $propertySeoUrlName = $this->propertyManager()->propertyExistsByTag('seo_url_name');
        if ($propertySeoUrlName != false) {
            $supportedPageTypes = $this->propertyManager()->getSupportedPageTypes($propertySeoUrlName);
            if ((is_null($this->getDataKey('property_seo_url_name_existed'))) && (count($supportedPageTypes) == 0)) {
                $this->propertyManager()->removeProperty($propertySeoUrlName);
            }
        }

        // remove property: Statický zoznam produktov(product_list)
        $propertyProductList = $this->propertyManager()->propertyExistsByTag('product_list');
        if ($propertyProductList != false) {
            $supportedPageTypes = $this->propertyManager()->getSupportedPageTypes($propertyProductList);
            if ((is_null($this->getDataKey('property_product_list_existed'))) && (count($supportedPageTypes) == 0)) {
                $this->propertyManager()->removeProperty($propertyProductList);
            }
        }

        // remove property: Maska pre filter(filter_mask)
        $propertyFilterMask = $this->propertyManager()->propertyExistsByTag('filter_mask');
        if ($propertyFilterMask != false) {
            $supportedPageTypes = $this->propertyManager()->getSupportedPageTypes($propertyFilterMask);
            if ((is_null($this->getDataKey('property_filter_mask_existed'))) && (count($supportedPageTypes) == 0)) {
                $this->propertyManager()->removeProperty($propertyFilterMask);
            }
        }

        // remove property: Dotaz na produkty(query)
        $propertyQuery = $this->propertyManager()->propertyExistsByTag('query');
        if ($propertyQuery != false) {
            $supportedPageTypes = $this->propertyManager()->getSupportedPageTypes($propertyQuery);
            if ((is_null($this->getDataKey('property_query_existed'))) && (count($supportedPageTypes) == 0)) {
                $this->propertyManager()->removeProperty($propertyQuery);
            }
        }

        // remove property: META description(meta_description)
        $propertyMetaDescription = $this->propertyManager()->propertyExistsByTag('meta_description');
        if ($propertyMetaDescription != false) {
            $supportedPageTypes = $this->propertyManager()->getSupportedPageTypes($propertyMetaDescription);
            if ((is_null($this->getDataKey('property_meta_description_existed'))) && (count($supportedPageTypes) == 0)) {
                $this->propertyManager()->removeProperty($propertyMetaDescription);
            }
        }

        // remove property: META title(meta_title)
        $propertyMetaTitle = $this->propertyManager()->propertyExistsByTag('meta_title');
        if ($propertyMetaTitle != false) {
            $supportedPageTypes = $this->propertyManager()->getSupportedPageTypes($propertyMetaTitle);
            if ((is_null($this->getDataKey('property_meta_title_existed'))) && (count($supportedPageTypes) == 0)) {
                $this->propertyManager()->removeProperty($propertyMetaTitle);
            }
        }

        // remove property: Text(text)
        $propertyText = $this->propertyManager()->propertyExistsByTag('text');
        if ($propertyText != false) {
            $supportedPageTypes = $this->propertyManager()->getSupportedPageTypes($propertyText);
            if ((is_null($this->getDataKey('property_text_existed'))) && (count($supportedPageTypes) == 0)) {
                $this->propertyManager()->removeProperty($propertyText);
            }
        }

        // remove property: Anotácia(annotation)
        $propertyAnnotation = $this->propertyManager()->propertyExistsByTag('annotation');
        if ($propertyAnnotation != false) {
            $supportedPageTypes = $this->propertyManager()->getSupportedPageTypes($propertyAnnotation);
            if ((is_null($this->getDataKey('property_annotation_existed'))) && (count($supportedPageTypes) == 0)) {
                $this->propertyManager()->removeProperty($propertyAnnotation);
            }
        }

        // remove property: Titulok(title)
        $propertyTitle = $this->propertyManager()->propertyExistsByTag('title');
        if ($propertyTitle != false) {
            $supportedPageTypes = $this->propertyManager()->getSupportedPageTypes($propertyTitle);
            if ((is_null($this->getDataKey('property_title_existed'))) && (count($supportedPageTypes) == 0)) {
                $this->propertyManager()->removeProperty($propertyTitle);
            }
        }

        // remove page type: Eshop kategórie (eshop_categories)
        $pageTypeEshopCategories = $this->pageTypesManager()->pageTypeExistsByTag('eshop_categories');
        if (($pageTypeEshopCategories != false) && (is_null($this->getDataKey('page_type_eshop_categories_existed')))) {
            $this->pageTypesManager()->removePageType($pageTypeEshopCategories);
        }

        // remove page type: Eshop (eshop)
        $pageTypeEshop = $this->pageTypesManager()->pageTypeExistsByTag('eshop');
        if (($pageTypeEshop != false) && (is_null($this->getDataKey('page_type_eshop_existed')))) {
            $this->pageTypesManager()->removePageType($pageTypeEshop);
        }

        // regenerate page types constants
        $this->pageTypesManager()->generateConstants();

        // regenerate property constants
        $this->propertyManager()->generateConstants();
    }
}
