<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Extra marža - Port<PERSON><PERSON> Vaša Le<PERSON>áreň 2.0</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">🏥 Portál Vaša Lekáreň 2.0</div>
                <div class="user-info">
                    <span>👤 Lekáreň ABC</span>
                    <span>📧 <EMAIL></span>
                    <a href="#" class="btn btn-secondary">Odhlásiť</a>
                </div>
            </div>
        </div>
    </header>

    <nav class="main-nav">
        <div class="container">
            <ul class="nav-list">
                <li class="nav-item">
                    <a href="prehľad.html" class="nav-link">🏠 Prehľad</a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link active">💰 Vaše benefity</a>
                    <div class="dropdown">
                        <a href="bonusy-prehľad.html" class="nav-link">💰 Bonusy</a>
                        <a href="nakupovanie.html" class="nav-link active">🛍️ Nakupovanie</a>
                        <a href="vystavenie.html" class="nav-link">🗂️ Vystavenie</a>
                        <a href="marketing.html" class="nav-link">📄 Marketing</a>
                    </div>
                </li>
                <li class="nav-item">
                    <a href="ostatne.html" class="nav-link">📚 Ostatné</a>
                    <div class="dropdown">
                        <a href="ostatne.html#elearning" class="nav-link">🎓 E-learning</a>
                        <a href="ostatne.html#sutaze" class="nav-link">🏆 Súťaže</a>
                        <a href="ostatne.html#eshop" class="nav-link">🛍️ Interný e-shop</a>
                    </div>
                </li>
                <li class="nav-item">
                    <a href="kontakt.html" class="nav-link">📞 Kontakt</a>
                </li>
            </ul>
        </div>
    </nav>

    <main class="main-content">
        <div class="container">
            <div class="breadcrumb">
                <a href="index.html">Wireframes</a> > <a href="nakupovanie.html">Nakupovanie</a> > Extra marža
            </div>

            <h1 class="page-title">2️⃣ Extra marža</h1>
            <p style="margin-bottom: 2rem; color: #666;">Zvýhodnený nákup produktov z akciového letáka s vyšším rabatom a dodatočným bonusom</p>

            <!-- Upozornenie o platnosti -->
            <div style="background: #d4edda; border-left: 4px solid #28a745; padding: 1rem; border-radius: 4px; margin-bottom: 2rem;">
                <h3 style="color: #155724; margin-bottom: 0.5rem;">🎉 Extra marža je aktívna!</h3>
                <p style="color: #155724; margin-bottom: 0.5rem;">Platnosť: <strong>29.12.2025 - 3.1.2026</strong></p>
                <p style="color: #155724; margin: 0;">Využite zvýhodnené ceny a extra bonusy na vybrané produkty z akciového letáka.</p>
            </div>

            <!-- Filter a vyhľadávanie -->
            <div class="table-container" style="margin-bottom: 2rem;">
                <h2 style="margin-bottom: 1rem;">Filter produktov</h2>
                <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 1rem; margin-bottom: 1rem;">
                    <input type="text" placeholder="Názov produktu..." class="form-input">
                    <select class="form-input">
                        <option value="">Všetky firmy</option>
                        <option value="SANOFI">SANOFI</option>
                        <option value="Pfizer">Pfizer</option>
                        <option value="GSK">GSK</option>
                    </select>
                    <select class="form-input">
                        <option value="">Všetky zľavy</option>
                        <option value="10">10% a viac</option>
                        <option value="15">15% a viac</option>
                        <option value="20">20% a viac</option>
                    </select>
                    <select class="form-input">
                        <option value="">Všetky bonusy</option>
                        <option value="extra">Len s extra bonusom ⭐</option>
                        <option value="no-extra">Bez extra bonusu</option>
                    </select>
                </div>
                <div style="text-align: center;">
                    <button class="btn btn-primary" onclick="applyFilters()">Filtrovať</button>
                    <button class="btn btn-secondary" onclick="clearFilters()">Vymazať filtre</button>
                </div>
            </div>

            <!-- Tabuľka produktov -->
            <div class="table-container" style="margin-bottom: 2rem;">
                <h2 style="margin-bottom: 1rem;">Produkty z akciového letáka</h2>

                <div style="overflow-x: auto;">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Kód</th>
                                <th>Názov produktu</th>
                                <th>Firma</th>
                                <th>Odporúčané (ks)</th>
                                <th>Množstvo (ks)</th>
                                <th>Zľava (%)</th>
                                <th>CB (%)</th>
                                <th>Pôvodná cena (€)</th>
                                <th>Cena po zľave (€)</th>
                                <th>Medzisúčet (€)</th>
                                <th>CB (€)</th>
                            </tr>
                        </thead>
                        <tbody id="produktyTable">
                            <tr class="extra-bonus">
                                <td><strong>P001</strong></td>
                                <td><strong>Produkt A</strong><br><small style="color: #666;">Vitamín D3 2000IU</small></td>
                                <td><a href="#" onclick="showFirmaDetail('SANOFI')" style="color: #2c5aa0; text-decoration: none;"><strong>SANOFI</strong></a></td>
                                <td>🎯 10</td>
                                <td><input type="number" value="8" min="0" class="editable-input" onchange="updateRow(this, 'P001')"></td>
                                <td><span style="color: #28a745; font-weight: bold;">-20%</span></td>
                                <td><span style="color: #ffc107; font-weight: bold;">⭐ +5%</span></td>
                                <td>12,00</td>
                                <td><strong>9,60</strong></td>
                                <td class="medzisucet"><strong>76,80</strong></td>
                                <td class="cb-amount"><strong>3,84</strong></td>
                            </tr>
                            <tr class="extra-bonus">
                                <td><strong>P002</strong></td>
                                <td><strong>Produkt B</strong><br><small style="color: #666;">Omega 3 1000mg</small></td>
                                <td><a href="#" onclick="showFirmaDetail('SANOFI')" style="color: #2c5aa0; text-decoration: none;"><strong>SANOFI</strong></a></td>
                                <td>🎯 6</td>
                                <td><input type="number" value="5" min="0" class="editable-input" onchange="updateRow(this, 'P002')"></td>
                                <td><span style="color: #28a745; font-weight: bold;">-15%</span></td>
                                <td><span style="color: #ffc107; font-weight: bold;">⭐ +3%</span></td>
                                <td>15,00</td>
                                <td><strong>12,75</strong></td>
                                <td class="medzisucet"><strong>63,75</strong></td>
                                <td class="cb-amount"><strong>1,91</strong></td>
                            </tr>
                            <tr>
                                <td><strong>P003</strong></td>
                                <td><strong>Produkt C</strong><br><small style="color: #666;">Magnesium 400mg</small></td>
                                <td><a href="#" onclick="showFirmaDetail('Pfizer')" style="color: #2c5aa0; text-decoration: none;"><strong>Pfizer</strong></a></td>
                                <td>🎯 5</td>
                                <td><input type="number" value="5" min="0" class="editable-input" onchange="updateRow(this, 'P003')"></td>
                                <td><span style="color: #28a745; font-weight: bold;">-10%</span></td>
                                <td>—</td>
                                <td>9,50</td>
                                <td><strong>8,55</strong></td>
                                <td class="medzisucet"><strong>42,75</strong></td>
                                <td class="cb-amount">0,00</td>
                            </tr>
                            <tr class="extra-bonus">
                                <td><strong>P004</strong></td>
                                <td><strong>Produkt D</strong><br><small style="color: #666;">Probiotikum 30 kps</small></td>
                                <td><a href="#" onclick="showFirmaDetail('GSK')" style="color: #2c5aa0; text-decoration: none;"><strong>GSK</strong></a></td>
                                <td>🎯 3</td>
                                <td><input type="number" value="4" min="0" class="editable-input" onchange="updateRow(this, 'P004')"></td>
                                <td><span style="color: #28a745; font-weight: bold;">-25%</span></td>
                                <td><span style="color: #ffc107; font-weight: bold;">⭐ +7%</span></td>
                                <td>28,00</td>
                                <td><strong>21,00</strong></td>
                                <td class="medzisucet"><strong>84,00</strong></td>
                                <td class="cb-amount"><strong>5,88</strong></td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div style="margin-top: 1rem; padding: 1rem; background: #f8f9fa; border-radius: 4px;">
                    <h4>💡 UX logika a funkcionalita:</h4>
                    <ul style="margin: 0.5rem 0; padding-left: 1.5rem;">
                        <li><strong>Odporúčané (ks)</strong> – fixná hodnota, len na informáciu (🎯)</li>
                        <li><strong>Množstvo (ks)</strong> – editovateľné pole, defaultne vyplnené odporúčanou hodnotou</li>
                        <li>Pri zmene množstva sa automaticky prepočíta Medzisúčet a aktualizuje sa sumárny panel</li>
                        <li><strong>⭐ Extra bonus</strong> – počíta sa len z produktov s označením</li>
                        <li>Klik na názov firmy zobrazí detail bonusov (aby lekáreň videla dopad)</li>
                    </ul>
                </div>
            </div>

            <!-- Sticky sumár -->
            <div class="table-container sticky-summary">
                <h2 style="margin-bottom: 1rem; color: #2c5aa0;">📊 Sumár objednávky</h2>
                <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 2rem; text-align: center; margin-bottom: 1rem;">
                    <div>
                        <div id="totalSum" style="font-size: 2rem; font-weight: bold; color: #2c5aa0;">267,30 €</div>
                        <div style="color: #666;">Celkový nákup</div>
                    </div>
                    <div>
                        <div id="extraBonus" style="font-size: 2rem; font-weight: bold; color: #ffc107;">11,63 €</div>
                        <div style="color: #666;">Extra bonus ⭐</div>
                    </div>
                    <div>
                        <div id="totalSavings" style="font-size: 2rem; font-weight: bold; color: #28a745;">57,95 €</div>
                        <div style="color: #666;">Celková úspora</div>
                    </div>
                    <div>
                        <div id="finalPrice" style="font-size: 2rem; font-weight: bold; color: #17a2b8;">267,30 €</div>
                        <div style="color: #666;">Finálna cena</div>
                    </div>
                </div>

                <div style="text-align: center; display: flex; gap: 1rem; justify-content: center;">
                    <button class="btn btn-primary" onclick="submitOrder()" style="font-size: 1.1rem; padding: 0.75rem 2rem;">
                        🛒 Pridať do objednávky
                    </button>
                    <a href="marketing.html#letak" class="btn btn-secondary" style="font-size: 1.1rem; padding: 0.75rem 2rem;">
                        📄 Zobraziť leták
                    </a>
                    <a href="nakupovanie.html" class="btn btn-secondary" style="font-size: 1.1rem; padding: 0.75rem 2rem;">
                        ← Späť
                    </a>
                </div>
            </div>
        </div>
    </main>

    <footer class="footer">
        <div class="container">
            <p>&copy; 2025 Vaša Lekáreň - Wireframes portálu 2.0</p>
        </div>
    </footer>

    <script>
        const produkty = {
            'P001': { cena: 12.00, zlava: 0.20, cb: 0.05, puvodnaCena: 12.00 },
            'P002': { cena: 15.00, zlava: 0.15, cb: 0.03, puvodnaCena: 15.00 },
            'P003': { cena: 9.50, zlava: 0.10, cb: 0, puvodnaCena: 9.50 },
            'P004': { cena: 28.00, zlava: 0.25, cb: 0.07, puvodnaCena: 28.00 }
        };

        function updateRow(input, kod) {
            const pocet = parseInt(input.value) || 0;
            const produkt = produkty[kod];
            const row = input.closest('tr');

            const cenaPoZlave = produkt.cena * (1 - produkt.zlava);
            const medzisucet = pocet * cenaPoZlave;
            const cbAmount = medzisucet * produkt.cb;

            row.querySelector('.medzisucet').textContent = medzisucet.toFixed(2);
            row.querySelector('.cb-amount').textContent = cbAmount.toFixed(2);

            updateSummary();
        }

        function updateSummary() {
            let totalSum = 0;
            let extraBonus = 0;
            let totalSavings = 0;

            document.querySelectorAll('#produktyTable tr').forEach(row => {
                const medzisucet = parseFloat(row.querySelector('.medzisucet')?.textContent) || 0;
                const cbAmount = parseFloat(row.querySelector('.cb-amount')?.textContent) || 0;
                const input = row.querySelector('.editable-input');

                if (input) {
                    const pocet = parseInt(input.value) || 0;
                    const kod = Object.keys(produkty).find(k => row.textContent.includes(k));
                    if (kod) {
                        const produkt = produkty[kod];
                        const savings = pocet * produkt.puvodnaCena * produkt.zlava;
                        totalSavings += savings;
                    }
                }

                totalSum += medzisucet;
                extraBonus += cbAmount;
            });

            document.getElementById('totalSum').textContent = totalSum.toFixed(2) + ' €';
            document.getElementById('extraBonus').textContent = extraBonus.toFixed(2) + ' €';
            document.getElementById('totalSavings').textContent = totalSavings.toFixed(2) + ' €';
            document.getElementById('finalPrice').textContent = totalSum.toFixed(2) + ' €';
        }

        function showFirmaDetail(firma) {
            alert(`Zobrazenie detailu bonusov pre firmu: ${firma}\n\nToto by otvorilo detail bonusov, aby lekáreň videla dopad nákupu na plnenie bonusových úrovní.`);
        }

        function submitOrder() {
            const total = document.getElementById('totalSum').textContent;
            const bonus = document.getElementById('extraBonus').textContent;
            const savings = document.getElementById('totalSavings').textContent;

            alert(`🎉 Objednávka z extra marže!\n\nCelková suma: ${total}\nExtra bonus: ${bonus}\nÚspora: ${savings}\n\nObjednávka bola úspešne odoslaná.`);
        }

        function applyFilters() {
            alert('Filtre aplikované - simulácia filtrovania produktov');
        }

        function clearFilters() {
            document.querySelectorAll('.form-input').forEach(input => {
                if (input.tagName === 'INPUT') input.value = '';
                if (input.tagName === 'SELECT') input.selectedIndex = 0;
            });
        }

        // Inicializácia
        document.addEventListener('DOMContentLoaded', function() {
            updateSummary();
        });
    </script>
</body>
</html>
