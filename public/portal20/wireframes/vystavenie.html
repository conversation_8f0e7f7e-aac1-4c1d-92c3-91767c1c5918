<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vystavenie - Portál Vaša Lekáreň 2.0</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">🏥 Portál Vaša Lekáreň 2.0</div>
                <div class="user-info">
                    <span>👤 Lekáreň ABC</span>
                    <span>📧 <EMAIL></span>
                    <a href="#" class="btn btn-secondary">Odhlásiť</a>
                </div>
            </div>
        </div>
    </header>

    <nav class="main-nav">
        <div class="container">
            <ul class="nav-list">
                <li class="nav-item">
                    <a href="prehľad.html" class="nav-link">🏠 Prehľad</a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link active">💰 Vaše benefity</a>
                    <div class="dropdown">
                        <a href="bonusy-prehľad.html" class="nav-link">💰 Bonusy</a>
                        <a href="nakupovanie.html" class="nav-link">🛍️ Nakupovanie</a>
                        <a href="vystavenie.html" class="nav-link active">🗂️ Vystavenie</a>
                        <a href="marketing.html" class="nav-link">📄 Marketing</a>
                    </div>
                </li>
                <li class="nav-item">
                    <a href="ostatne.html" class="nav-link">📚 Ostatné</a>
                    <div class="dropdown">
                        <a href="ostatne.html#elearning" class="nav-link">🎓 E-learning</a>
                        <a href="ostatne.html#sutaze" class="nav-link">🏆 Súťaže</a>
                        <a href="ostatne.html#eshop" class="nav-link">🛍️ Interný e-shop</a>
                    </div>
                </li>
                <li class="nav-item">
                    <a href="kontakt.html" class="nav-link">📞 Kontakt</a>
                </li>
            </ul>
        </div>
    </nav>

    <main class="main-content">
        <div class="container">
            <div class="breadcrumb">
                <a href="index.html">Wireframes</a> > <a href="bonusy-prehľad.html">Vaše benefity</a> > Vystavenie
            </div>

            <h1 class="page-title">🗂️ Vystavenie</h1>
            <p style="margin-bottom: 2rem; color: #666;">Sekcia obsahuje dva samostatné moduly – Sezónne vystavenie a Impulzný box. Cieľom je poskytnúť lekárni všetky podklady na realizáciu vystavenia, kontrolu stavu plnenia a prepojenie na bonusy za vystavenie.</p>

            <!-- 1. Sezónne vystavenie -->
            <div id="sezonny" class="table-container" style="margin-bottom: 2rem;">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                    <h2>1️⃣ Sezónne vystavenie</h2>
                    <a href="bonusy-vystavenie.html" class="btn btn-primary">Pozrieť bonus za toto vystavenie →</a>
                </div>

                <div style="background: #e7f3ff; border-left: 4px solid #2c5aa0; padding: 1rem; border-radius: 4px; margin-bottom: 1.5rem;">
                    <p><strong>Platnosť:</strong> 4-mesačný cyklus (máj–august)</p>
                    <p><strong>Bonus:</strong> 50 € / mesiac pri splnení podmienok</p>
                </div>

                <div style="display: grid; grid-template-columns: 1fr 2fr; gap: 2rem;">
                    <!-- Náhľad plánogramu -->
                    <div>
                        <h3 style="margin-bottom: 1rem;">Aktuálny plánogram - December 2025</h3>
                        <div style="border: 2px dashed #ccc; padding: 2rem; text-align: center; border-radius: 8px; background: #f8f9fa; cursor: pointer;" onclick="showPlanogram('sezonny-december')">
                            <div style="font-size: 4rem; color: #ccc; margin-bottom: 1rem;">🗂️</div>
                            <div style="font-weight: bold; margin-bottom: 0.5rem;">Sezónne vystavenie</div>
                            <div style="color: #666; font-size: 0.9rem;">Kliknite pre zväčšenie</div>
                        </div>

                        <div style="margin-top: 1rem; display: flex; gap: 0.5rem;">
                            <a href="#" class="btn btn-primary" onclick="downloadFile('planogram-sezonny-december.pdf')">📄 Stiahnuť PDF</a>
                            <a href="#" class="btn btn-secondary" onclick="downloadFile('produkty-sezonny-december.xlsx')">📊 XLSX</a>
                        </div>
                    </div>

                    <!-- Stav a informácie -->
                    <div>
                        <h3 style="margin-bottom: 1rem;">Stav plnenia</h3>
                        <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 1rem; margin-bottom: 1.5rem;">
                            <div style="text-align: center; padding: 1rem; background: #f8f9fa; border-radius: 4px;">
                                <div style="font-size: 1.5rem; font-weight: bold; color: #28a745;">🟢 Splnené</div>
                                <div style="color: #666;">Aktuálny stav</div>
                            </div>
                            <div style="text-align: center; padding: 1rem; background: #f8f9fa; border-radius: 4px;">
                                <div style="font-size: 1.5rem; font-weight: bold; color: #2c5aa0;">08.12.2025</div>
                                <div style="color: #666;">Dátum splnenia</div>
                            </div>
                        </div>

                        <h4 style="margin-bottom: 0.5rem;">Nahrávanie fotodôkazov</h4>
                        <div style="border: 2px dashed #28a745; padding: 1rem; border-radius: 4px; text-align: center; background: #f8fff8; margin-bottom: 1rem;">
                            <div style="font-size: 2rem; color: #28a745; margin-bottom: 0.5rem;">📷</div>
                            <div style="font-weight: bold; color: #28a745;">Fotodôkaz nahratý</div>
                            <div style="color: #666; font-size: 0.9rem;">3 fotky • 08.12.2025</div>
                            <button class="btn btn-success" style="margin-top: 0.5rem;" onclick="showPhotos('sezonny-december')">Zobraziť fotky</button>
                        </div>

                        <h4 style="margin-bottom: 0.5rem;">Budúce vystavenie</h4>
                        <div style="background: #fff3cd; padding: 1rem; border-radius: 4px;">
                            <div style="font-weight: bold; margin-bottom: 0.5rem;">Január 2026</div>
                            <div style="display: flex; gap: 0.5rem;">
                                <a href="#" class="btn btn-secondary" onclick="showPlanogram('sezonny-januar')">Náhľad</a>
                                <a href="#" class="btn btn-secondary" onclick="downloadFile('planogram-sezonny-januar.pdf')">PDF</a>
                                <a href="#" class="btn btn-secondary" onclick="downloadFile('produkty-sezonny-januar.xlsx')">XLSX</a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sumár bonusu -->
                <div style="margin-top: 1.5rem; padding: 1rem; background: #d4edda; border-radius: 4px; text-align: center;">
                    <strong>Možný bonus za sezónne vystavenie: 50,00 € • Stav plnenia: Splnené ✓</strong>
                </div>
            </div>

            <!-- 2. Impulzný box -->
            <div id="impulzny" class="table-container" style="margin-bottom: 2rem;">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                    <h2>2️⃣ Impulzný box</h2>
                    <a href="bonusy-vystavenie.html" class="btn btn-primary">Pozrieť bonus za toto vystavenie →</a>
                </div>

                <div style="background: #e7f3ff; border-left: 4px solid #2c5aa0; padding: 1rem; border-radius: 4px; margin-bottom: 1.5rem;">
                    <p><strong>Platnosť:</strong> 2 zmeny vystavenia mesačne (1. deň a 15. deň mesiaca)</p>
                    <p><strong>Bonus:</strong> 20 € / mesiac pri splnení oboch období</p>
                </div>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem;">
                    <!-- Prvé obdobie (1.-14.) -->
                    <div>
                        <h3 style="margin-bottom: 1rem;">1.-14. December 2025</h3>
                        <div style="border: 2px dashed #ccc; padding: 2rem; text-align: center; border-radius: 8px; background: #f8f9fa; cursor: pointer;" onclick="showPlanogram('impulzny-1-14')">
                            <div style="font-size: 3rem; color: #ccc; margin-bottom: 1rem;">📦</div>
                            <div style="font-weight: bold; margin-bottom: 0.5rem;">Impulzný box 1-14</div>
                            <div style="color: #666; font-size: 0.9rem;">Kliknite pre zväčšenie</div>
                        </div>

                        <div style="margin-top: 1rem;">
                            <div style="display: flex; gap: 0.5rem; margin-bottom: 1rem;">
                                <a href="#" class="btn btn-primary" onclick="downloadFile('planogram-impulzny-1-14.pdf')">📄 PDF</a>
                                <a href="#" class="btn btn-secondary" onclick="downloadFile('produkty-impulzny-1-14.xlsx')">📊 XLSX</a>
                            </div>

                            <div style="text-align: center; padding: 0.5rem; background: #d4edda; border-radius: 4px;">
                                <span class="status status-success">🟢 Splnené</span>
                                <div style="font-size: 0.8rem; color: #666; margin-top: 0.25rem;">Fotodôkaz: 01.12.2025</div>
                            </div>
                        </div>
                    </div>

                    <!-- Druhé obdobie (15.-31.) -->
                    <div>
                        <h3 style="margin-bottom: 1rem;">15.-31. December 2025</h3>
                        <div style="border: 2px dashed #ffc107; padding: 2rem; text-align: center; border-radius: 8px; background: #fffbf0; cursor: pointer;" onclick="showPlanogram('impulzny-15-31')">
                            <div style="font-size: 3rem; color: #ffc107; margin-bottom: 1rem;">📦</div>
                            <div style="font-weight: bold; margin-bottom: 0.5rem;">Impulzný box 15-31</div>
                            <div style="color: #666; font-size: 0.9rem;">Aktuálne obdobie</div>
                        </div>

                        <div style="margin-top: 1rem;">
                            <div style="display: flex; gap: 0.5rem; margin-bottom: 1rem;">
                                <a href="#" class="btn btn-primary" onclick="downloadFile('planogram-impulzny-15-31.pdf')">📄 PDF</a>
                                <a href="#" class="btn btn-secondary" onclick="downloadFile('produkty-impulzny-15-31.xlsx')">📊 XLSX</a>
                            </div>

                            <div style="text-align: center; padding: 0.5rem; background: #fff3cd; border-radius: 4px;">
                                <span class="status status-warning">🟡 Prebieha</span>
                                <div style="font-size: 0.8rem; color: #666; margin-top: 0.25rem;">
                                    <button class="btn btn-success" onclick="uploadPhoto('impulzny-15-31')">📷 Nahrať fotodôkaz</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Budúce vystavenie -->
                <div style="margin-top: 1.5rem;">
                    <h4 style="margin-bottom: 1rem;">Náhľad budúceho vystavenia - Január 2026</h4>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                        <div style="background: #f8f9fa; padding: 1rem; border-radius: 4px; text-align: center;">
                            <div style="font-weight: bold; margin-bottom: 0.5rem;">1.-14. Január</div>
                            <div style="display: flex; gap: 0.5rem; justify-content: center;">
                                <a href="#" class="btn btn-secondary" onclick="showPlanogram('impulzny-jan-1-14')">Náhľad</a>
                                <a href="#" class="btn btn-secondary" onclick="downloadFile('planogram-impulzny-jan-1-14.pdf')">PDF</a>
                            </div>
                        </div>
                        <div style="background: #f8f9fa; padding: 1rem; border-radius: 4px; text-align: center;">
                            <div style="font-weight: bold; margin-bottom: 0.5rem;">15.-31. Január</div>
                            <div style="display: flex; gap: 0.5rem; justify-content: center;">
                                <a href="#" class="btn btn-secondary" onclick="showPlanogram('impulzny-jan-15-31')">Náhľad</a>
                                <a href="#" class="btn btn-secondary" onclick="downloadFile('planogram-impulzny-jan-15-31.pdf')">PDF</a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sumár bonusu -->
                <div style="margin-top: 1.5rem; padding: 1rem; background: #fff3cd; border-radius: 4px; text-align: center;">
                    <strong>Možný bonus za impulzný box: 20,00 € • Podmienka: splnenie oboch období (1 splnené, 1 prebieha)</strong>
                </div>
            </div>

            <!-- Spoločné prvky -->
            <div class="table-container">
                <h2 style="margin-bottom: 1rem;">🔄 Spoločné prvky pre oba typy vystavenia</h2>
                <div class="tiles-grid" style="grid-template-columns: repeat(3, 1fr);">
                    <div class="tile">
                        <div class="tile-icon">📷</div>
                        <div class="tile-title">Fotodôkazy povinné</div>
                        <div class="tile-description">Pre získanie bonusu je potrebné nahrať fotodôkazy vystavenia</div>
                        <a href="#" class="tile-cta" onclick="uploadPhoto('current')">Nahrať foto</a>
                    </div>

                    <div class="tile">
                        <div class="tile-icon">📋</div>
                        <div class="tile-title">Zoznam produktov</div>
                        <div class="tile-description">K plánogramu dostupný len na stiahnutie (PDF / XLSX)</div>
                        <a href="#" class="tile-cta" onclick="downloadFile('produkty-current.xlsx')">Stiahnuť zoznam</a>
                    </div>

                    <div class="tile">
                        <div class="tile-icon">🔗</div>
                        <div class="tile-title">Prepojenie na bonusy</div>
                        <div class="tile-description">Prelinkovanie priamo na relevantnú časť "Bonusy za vystavenie"</div>
                        <a href="bonusy-vystavenie.html" class="tile-cta">Zobraziť bonusy</a>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Modal pre zobrazenie plánogramov -->
    <div id="planogramModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.8); z-index: 1000;">
        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 2rem; border-radius: 8px; max-width: 90%; max-height: 90%;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                <h3 id="planogramTitle">Plánogram</h3>
                <button onclick="closePlanogramModal()" class="btn btn-secondary">✕</button>
            </div>
            <div id="planogramContent" style="text-align: center;">
                <!-- Obsah sa načíta dynamicky -->
            </div>
        </div>
    </div>

    <footer class="footer">
        <div class="container">
            <p>&copy; 2025 Vaša Lekáreň - Wireframes portálu 2.0</p>
        </div>
    </footer>

    <script>
        function showPlanogram(planogramId) {
            const modal = document.getElementById('planogramModal');
            const title = document.getElementById('planogramTitle');
            const content = document.getElementById('planogramContent');

            const planogramData = {
                'sezonny-december': 'Sezónne vystavenie - December 2025',
                'sezonny-januar': 'Sezónne vystavenie - Január 2026',
                'impulzny-1-14': 'Impulzný box 1.-14. December',
                'impulzny-15-31': 'Impulzný box 15.-31. December',
                'impulzny-jan-1-14': 'Impulzný box 1.-14. Január',
                'impulzny-jan-15-31': 'Impulzný box 15.-31. Január'
            };

            title.textContent = planogramData[planogramId] || 'Plánogram';
            content.innerHTML = `
                <div style="border: 2px dashed #ccc; padding: 4rem; border-radius: 8px; background: #f8f9fa;">
                    <div style="font-size: 6rem; color: #ccc; margin-bottom: 1rem;">🗂️</div>
                    <div style="font-size: 1.5rem; font-weight: bold; margin-bottom: 1rem;">${planogramData[planogramId]}</div>
                    <div style="color: #666;">Náhľad plánogramu</div>
                </div>
                <div style="margin-top: 1rem;">
                    <button class="btn btn-primary" onclick="downloadFile('${planogramId}.pdf')">Stiahnuť PDF</button>
                    <button class="btn btn-secondary" onclick="closePlanogramModal()">Zavrieť</button>
                </div>
            `;
            modal.style.display = 'block';
        }

        function closePlanogramModal() {
            document.getElementById('planogramModal').style.display = 'none';
        }

        function downloadFile(filename) {
            alert('Sťahovanie súboru: ' + filename);
        }

        function uploadPhoto(photoId) {
            alert('Simulácia nahrávania fotodôkazu pre: ' + photoId);
        }

        function showPhotos(photoId) {
            alert('Zobrazenie nahratých fotiek pre: ' + photoId);
        }

        // Zatvorenie modalu pri kliknutí mimo obsah
        document.getElementById('planogramModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closePlanogramModal();
            }
        });
    </script>
</body>
</html>
