/* Wireframes CSS pre Port<PERSON><PERSON> 2.0 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header a navigácia */
.header {
    background: #2c5aa0;
    color: white;
    padding: 1rem 0;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    font-size: 1.5rem;
    font-weight: bold;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.main-nav {
    background: #1e4080;
    padding: 0;
}

.nav-list {
    list-style: none;
    display: flex;
    margin: 0;
    padding: 0;
}

.nav-item {
    position: relative;
}

.nav-link {
    display: block;
    padding: 1rem 1.5rem;
    color: white;
    text-decoration: none;
    transition: background-color 0.3s;
}

.nav-link:hover,
.nav-link.active {
    background-color: #2c5aa0;
}

/* Dropdown menu */
.dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    background: #1e4080;
    min-width: 200px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    display: none;
    z-index: 1000;
}

.nav-item:hover .dropdown {
    display: block;
}

.dropdown .nav-link {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #2c5aa0;
}

.dropdown .nav-link:last-child {
    border-bottom: none;
}

/* Main content */
.main-content {
    padding: 2rem 0;
    min-height: calc(100vh - 200px);
}

.page-title {
    font-size: 2rem;
    margin-bottom: 1.5rem;
    color: #2c5aa0;
}

.breadcrumb {
    margin-bottom: 1.5rem;
    color: #666;
}

.breadcrumb a {
    color: #2c5aa0;
    text-decoration: none;
}

.breadcrumb a:hover {
    text-decoration: underline;
}

/* Dlaždice a karty */
.tiles-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.tile {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: transform 0.3s, box-shadow 0.3s;
    cursor: pointer;
}

.tile:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.tile-icon {
    font-size: 2rem;
    margin-bottom: 1rem;
    color: #2c5aa0;
}

.tile-title {
    font-size: 1.2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
    color: #333;
}

.tile-description {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.tile-value {
    font-size: 1.5rem;
    font-weight: bold;
    color: #2c5aa0;
    margin-bottom: 0.5rem;
}

.tile-cta {
    background: #2c5aa0;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
    font-size: 0.9rem;
    transition: background-color 0.3s;
}

.tile-cta:hover {
    background: #1e4080;
}

/* Rýchle skratky */
.quick-shortcuts {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 1rem;
    margin-bottom: 2rem;
}

.shortcut {
    background: white;
    border-radius: 8px;
    padding: 1rem;
    text-align: center;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    transition: transform 0.3s;
    cursor: pointer;
    text-decoration: none;
    color: inherit;
}

.shortcut:hover {
    transform: translateY(-2px);
}

.shortcut-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    color: #2c5aa0;
}

.shortcut-text {
    font-weight: bold;
    font-size: 0.9rem;
}

/* Banner carousel */
.banner-carousel {
    background: white;
    border-radius: 8px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    text-align: center;
}

.banner-content {
    background: linear-gradient(135deg, #2c5aa0, #4a7bc8);
    color: white;
    padding: 3rem;
    border-radius: 8px;
    margin-bottom: 1rem;
}

.banner-title {
    font-size: 1.8rem;
    margin-bottom: 1rem;
}

.banner-description {
    margin-bottom: 1.5rem;
}

.banner-cta {
    background: white;
    color: #2c5aa0;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 4px;
    cursor: pointer;
    font-weight: bold;
    text-decoration: none;
    display: inline-block;
}

.carousel-dots {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
}

.dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: #ccc;
    cursor: pointer;
}

.dot.active {
    background: #2c5aa0;
}

/* Tabuľky */
.table-container {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
    overflow-x: auto;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
}

.data-table th,
.data-table td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.data-table th {
    background: #f8f9fa;
    font-weight: bold;
    color: #333;
}

.data-table tr:hover {
    background: #f8f9fa;
}

/* Status indikátory */
.status {
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: bold;
}

.status-success {
    background: #d4edda;
    color: #155724;
}

.status-warning {
    background: #fff3cd;
    color: #856404;
}

.status-danger {
    background: #f8d7da;
    color: #721c24;
}

/* Progress bary */
.progress-bar {
    background: #e9ecef;
    border-radius: 4px;
    height: 20px;
    overflow: hidden;
    margin: 0.5rem 0;
}

.progress-fill {
    background: #2c5aa0;
    height: 100%;
    transition: width 0.3s;
}

/* Formuláre */
.form-group {
    margin-bottom: 1rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: bold;
}

.form-input {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
}

.form-input:focus {
    outline: none;
    border-color: #2c5aa0;
    box-shadow: 0 0 0 2px rgba(44, 90, 160, 0.2);
}

/* Tlačidlá */
.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
    font-size: 0.9rem;
    transition: background-color 0.3s;
}

.btn-primary {
    background: #2c5aa0;
    color: white;
}

.btn-primary:hover {
    background: #1e4080;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #545b62;
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-success:hover {
    background: #1e7e34;
}

/* Footer */
.footer {
    background: #333;
    color: white;
    text-align: center;
    padding: 1rem 0;
    margin-top: 2rem;
}

/* Sticky sumár */
.sticky-summary {
    position: sticky;
    bottom: 20px;
    background: white;
    border: 2px solid #2c5aa0;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    z-index: 100;
}

/* Editovateľné polia v tabuľkách */
.editable-input {
    border: 1px solid #ddd;
    padding: 0.25rem;
    width: 80px;
    text-align: center;
    border-radius: 4px;
}

.editable-input:focus {
    outline: none;
    border-color: #2c5aa0;
    box-shadow: 0 0 0 2px rgba(44, 90, 160, 0.2);
}

/* Zvýraznenie produktov s extra bonusom */
.extra-bonus {
    background: linear-gradient(90deg, #fff3cd, #ffffff);
    border-left: 3px solid #ffc107;
}

.extra-bonus .tile-icon::after {
    content: '⭐';
    position: absolute;
    top: -5px;
    right: -5px;
    font-size: 1rem;
}

/* Responsive design */
@media (max-width: 768px) {
    .quick-shortcuts {
        grid-template-columns: repeat(2, 1fr);
    }

    .nav-list {
        flex-direction: column;
    }

    .tiles-grid {
        grid-template-columns: 1fr;
    }

    .header-content {
        flex-direction: column;
        gap: 1rem;
    }

    .sticky-summary {
        position: relative;
        bottom: auto;
    }
}
