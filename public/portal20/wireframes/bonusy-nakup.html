<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bonusy za nákup - <PERSON><PERSON><PERSON> Vaša Lekáreň 2.0</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">🏥 Portál Vaša Lekáreň 2.0</div>
                <div class="user-info">
                    <span>👤 Lekáreň ABC</span>
                    <span>📧 <EMAIL></span>
                    <a href="#" class="btn btn-secondary">Odhlásiť</a>
                </div>
            </div>
        </div>
    </header>

    <nav class="main-nav">
        <div class="container">
            <ul class="nav-list">
                <li class="nav-item">
                    <a href="prehľad.html" class="nav-link">🏠 Prehľad</a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link active">💰 Vaše benefity</a>
                    <div class="dropdown">
                        <a href="bonusy-prehľad.html" class="nav-link active">💰 Bonusy</a>
                        <a href="nakupovanie.html" class="nav-link">🛍️ Nakupovanie</a>
                        <a href="vystavenie.html" class="nav-link">🗂️ Vystavenie</a>
                        <a href="marketing.html" class="nav-link">📄 Marketing</a>
                    </div>
                </li>
                <li class="nav-item">
                    <a href="ostatne.html" class="nav-link">📚 Ostatné</a>
                    <div class="dropdown">
                        <a href="ostatne.html#elearning" class="nav-link">🎓 E-learning</a>
                        <a href="ostatne.html#sutaze" class="nav-link">🏆 Súťaže</a>
                        <a href="ostatne.html#eshop" class="nav-link">🛍️ Interný e-shop</a>
                    </div>
                </li>
                <li class="nav-item">
                    <a href="kontakt.html" class="nav-link">📞 Kontakt</a>
                </li>
            </ul>
        </div>
    </nav>

    <main class="main-content">
        <div class="container">
            <div class="breadcrumb">
                <a href="index.html">Wireframes</a> > <a href="bonusy-prehľad.html">Bonusy</a> > Bonusy za nákup
            </div>

            <h1 class="page-title">📦 Bonusy za nákup</h1>
            <p style="margin-bottom: 2rem; color: #666;">Prehľadný a detailný pohľad na bonusy za nákup produktov</p>

            <!-- 1. Prehľadný pohľad – Aktívny cyklus -->
            <div class="table-container" style="margin-bottom: 2rem;">
                <h2 style="margin-bottom: 1rem;">1. Prehľadný pohľad – Aktívny cyklus</h2>
                <p style="margin-bottom: 1rem; color: #666;">(1 firma = 1 riadok)</p>

                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Firma</th>
                            <th>Plán (€)</th>
                            <th>Nákup (€)</th>
                            <th>Plnenie (%)</th>
                            <th>Chýba do ďalšej úrovne</th>
                            <th>Garantovaný (€)</th>
                            <th>Obratový (€)</th>
                            <th>Rastový (€)</th>
                            <th>Celkom bonus (€)</th>
                            <th>Akcie</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr onclick="showDetail('SANOFI')" style="cursor: pointer;">
                            <td><strong>SANOFI</strong></td>
                            <td>16 000</td>
                            <td>15 500</td>
                            <td>
                                <span style="color: #ffc107; font-weight: bold;">96,9 %</span>
                                <div class="progress-bar" style="margin-top: 0.25rem;">
                                    <div class="progress-fill" style="width: 96.9%; background: #ffc107;"></div>
                                </div>
                            </td>
                            <td>500 €</td>
                            <td>387,50</td>
                            <td>0,00</td>
                            <td>0,00</td>
                            <td><strong>387,50</strong></td>
                            <td>
                                <a href="doplnkovy-nakup.html?firma=SANOFI" class="btn btn-primary" style="font-size: 0.8rem;">Doplniť nákup</a>
                            </td>
                        </tr>
                        <tr onclick="showDetail('Pfizer')" style="cursor: pointer;">
                            <td><strong>Pfizer</strong></td>
                            <td>18 000</td>
                            <td>19 000</td>
                            <td>
                                <span style="color: #28a745; font-weight: bold;">105,6 %</span>
                                <div class="progress-bar" style="margin-top: 0.25rem;">
                                    <div class="progress-fill" style="width: 100%; background: #28a745;"></div>
                                </div>
                            </td>
                            <td>1 000 €</td>
                            <td>540,00</td>
                            <td>900,00</td>
                            <td>0,00</td>
                            <td><strong>1 440,00</strong></td>
                            <td>
                                <a href="doplnkovy-nakup.html?firma=Pfizer" class="btn btn-success" style="font-size: 0.8rem;">Zvýšiť bonus</a>
                            </td>
                        </tr>
                        <tr onclick="showDetail('GSK')" style="cursor: pointer;">
                            <td><strong>GSK</strong></td>
                            <td>12 000</td>
                            <td>13 200</td>
                            <td>
                                <span style="color: #28a745; font-weight: bold;">110,0 %</span>
                                <div class="progress-bar" style="margin-top: 0.25rem;">
                                    <div class="progress-fill" style="width: 100%; background: #28a745;"></div>
                                </div>
                            </td>
                            <td>–</td>
                            <td>300,00</td>
                            <td>600,00</td>
                            <td>900,00</td>
                            <td><strong>1 800,00</strong></td>
                            <td>
                                <span class="status status-success">Maximálne</span>
                            </td>
                        </tr>
                    </tbody>
                </table>

                <div style="margin-top: 1rem; padding: 1rem; background: #f8f9fa; border-radius: 4px;">
                    <h4>💡 Funkcie v portáli:</h4>
                    <ul style="margin: 0.5rem 0; padding-left: 1.5rem;">
                        <li>Klik na riadok firmy → otvorí sa detail</li>
                        <li>"Potenciálny bonus" = hodnota konkrétneho bonusu</li>
                        <li>"Celkom pri dosiahnutí" = súčet všetkých bonusov pri splnení úrovne</li>
                        <li>Možnosť progress barov pre jednotlivé úrovne</li>
                    </ul>
                </div>
            </div>

            <!-- Detail firmy (zobrazí sa po kliknutí) -->
            <div id="detailSection" class="table-container" style="display: none; margin-bottom: 2rem;">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                    <h2 id="detailTitle">2. Detailný pohľad – SANOFI</h2>
                    <button onclick="hideDetail()" class="btn btn-secondary">✕ Zavrieť detail</button>
                </div>

                <div id="detailContent">
                    <!-- Obsah sa načíta dynamicky -->
                </div>
            </div>

            <!-- Celkový súhrn -->
            <div class="table-container">
                <h2 style="margin-bottom: 1rem;">Celkový súhrn bonusov za nákup</h2>
                <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 2rem; text-align: center;">
                    <div>
                        <div style="font-size: 2rem; font-weight: bold; color: #2c5aa0;">3 627,50 €</div>
                        <div style="color: #666;">Celkové bonusy</div>
                    </div>
                    <div>
                        <div style="font-size: 2rem; font-weight: bold; color: #28a745;">3</div>
                        <div style="color: #666;">Aktívne firmy</div>
                    </div>
                    <div>
                        <div style="font-size: 2rem; font-weight: bold; color: #ffc107;">1 500,00 €</div>
                        <div style="color: #666;">Potenciálne bonusy</div>
                    </div>
                    <div>
                        <div style="font-size: 2rem; font-weight: bold; color: #17a2b8;">104,2%</div>
                        <div style="color: #666;">Priemerné plnenie</div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <footer class="footer">
        <div class="container">
            <p>&copy; 2025 Vaša Lekáreň - Wireframes portálu 2.0</p>
        </div>
    </footer>

    <script>
        // Detailné údaje pre firmy
        const firmDetails = {
            'SANOFI': {
                plan: 10000,
                nakup: 9000,
                plnenie: 90.0,
                chybaNakup: 500,
                bonusy: [
                    { typ: 'Garantovaný', podmienka: '–', sadzba: '2,5 %', chyba: '–', potencial: 225.00, celkom: 225.00 },
                    { typ: 'Obratový', podmienka: '100 % plnenie', sadzba: '5 %', chyba: 1000, potencial: 500.00, celkom: 725.00 },
                    { typ: 'Rastový', podmienka: '105 % plnenie', sadzba: '+1 %', chyba: 1500, potencial: 105.00, celkom: 830.00 },
                    { typ: 'Rastový', podmienka: '110 % plnenie', sadzba: '+2,5 %', chyba: 2000, potencial: 275.00, celkom: 1000.00 }
                ]
            },
            'Pfizer': {
                plan: 18000,
                nakup: 19000,
                plnenie: 105.6,
                bonusy: [
                    { typ: 'Garantovaný', podmienka: '–', sadzba: '3,0 %', chyba: '–', potencial: 540.00, celkom: 540.00 },
                    { typ: 'Obratový', podmienka: '100 % plnenie', sadzba: '5 %', chyba: 0, potencial: 900.00, celkom: 1440.00 },
                    { typ: 'Rastový', podmienka: '110 % plnenie', sadzba: '+2 %', chyba: 800, potencial: 380.00, celkom: 1820.00 }
                ]
            },
            'GSK': {
                plan: 12000,
                nakup: 13200,
                plnenie: 110.0,
                bonusy: [
                    { typ: 'Garantovaný', podmienka: '–', sadzba: '2,5 %', chyba: '–', potencial: 300.00, celkom: 300.00 },
                    { typ: 'Obratový', podmienka: '100 % plnenie', sadzba: '5 %', chyba: 0, potencial: 600.00, celkom: 900.00 },
                    { typ: 'Rastový', podmienka: '110 % plnenie', sadzba: '+7,5 %', chyba: 0, potencial: 900.00, celkom: 1800.00 }
                ]
            }
        };

        function showDetail(firma) {
            const detail = firmDetails[firma];
            const detailSection = document.getElementById('detailSection');
            const detailTitle = document.getElementById('detailTitle');
            const detailContent = document.getElementById('detailContent');

            detailTitle.textContent = `2. Detailný pohľad – ${firma} (plán ${detail.plan.toLocaleString()} €, nákup ${detail.nakup.toLocaleString()} €)`;

            let bonusyTable = `
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Typ bonusu</th>
                            <th>Podmienka</th>
                            <th>Sadzba bonusu</th>
                            <th>Chýba nakúpiť (€)</th>
                            <th>Potenciálny bonus (€)</th>
                            <th>Celkom pri dosiahnutí (€)</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            detail.bonusy.forEach(bonus => {
                bonusyTable += `
                    <tr>
                        <td><strong>${bonus.typ}</strong></td>
                        <td>${bonus.podmienka}</td>
                        <td>${bonus.sadzba}</td>
                        <td>${bonus.chyba === '–' ? '–' : bonus.chyba.toLocaleString()}</td>
                        <td>${bonus.potencial.toFixed(2)}</td>
                        <td><strong>${bonus.celkom.toFixed(2)}</strong></td>
                    </tr>
                `;
            });

            bonusyTable += `
                    </tbody>
                </table>
                <div style="margin-top: 1rem; text-align: center;">
                    <a href="doplnkovy-nakup.html?firma=${firma}" class="btn btn-primary">🛍️ Doplniť nákup pre ${firma}</a>
                </div>
            `;

            // Pridanie doplnkového nákupu ak je potrebný
            if (detail.chybaNakup && detail.chybaNakup > 0) {
                bonusyTable += `
                    <div style="margin-top: 2rem; padding: 1rem; background: #fff3cd; border-radius: 4px; border-left: 4px solid #ffc107;">
                        <h4>3. Doplnkový nákup – ${firma} (cieľ: ${detail.chybaNakup} € do 100 % plnenia)</h4>
                        <table class="data-table" style="margin-top: 1rem;">
                            <thead>
                                <tr>
                                    <th>Produkt</th>
                                    <th>Cena/ks (€)</th>
                                    <th>Počet ks</th>
                                    <th>Medzisúčet (€)</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr><td>produkt_1</td><td>25,00</td><td>4</td><td>100,00</td></tr>
                                <tr><td>produkt_2</td><td>15,50</td><td>6</td><td>93,00</td></tr>
                                <tr><td>produkt_3</td><td>42,00</td><td>5</td><td>210,00</td></tr>
                                <tr><td>produkt_4</td><td>10,00</td><td>3</td><td>30,00</td></tr>
                                <tr><td>produkt_5</td><td>13,50</td><td>5</td><td>67,50</td></tr>
                                <tr style="font-weight: bold; background: #f8f9fa;">
                                    <td>Sumár</td><td></td><td></td><td>500,50</td>
                                </tr>
                                <tr style="font-weight: bold; color: #28a745;">
                                    <td>Chýba nakúpiť</td><td></td><td></td><td>0,00</td>
                                </tr>
                            </tbody>
                        </table>
                        <div style="margin-top: 1rem;">
                            <h5>💡 Funkcie:</h5>
                            <ul style="margin: 0.5rem 0; padding-left: 1.5rem;">
                                <li>Dynamický výpočet "Medzisúčet" a "Chýba nakúpiť"</li>
                                <li>Farebný indikátor (červená → žltá → zelená) podľa % splnenia</li>
                                <li>Po splnení → potvrdenie + návrat do detailu</li>
                            </ul>
                        </div>
                    </div>
                `;
            }

            detailContent.innerHTML = bonusyTable;
            detailSection.style.display = 'block';
            detailSection.scrollIntoView({ behavior: 'smooth' });
        }

        function hideDetail() {
            document.getElementById('detailSection').style.display = 'none';
        }

        document.addEventListener('DOMContentLoaded', function() {
            // Animácia progress barov
            const progressBars = document.querySelectorAll('.progress-fill');
            progressBars.forEach(bar => {
                const width = bar.style.width;
                bar.style.width = '0%';
                setTimeout(() => {
                    bar.style.width = width;
                }, 500);
            });
        });
    </script>
</body>
</html>
