<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bonusy za predaj - <PERSON><PERSON><PERSON> Vaša Lekáreň 2.0</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">🏥 Portál Vaša Lekáreň 2.0</div>
                <div class="user-info">
                    <span>👤 Lekáreň ABC</span>
                    <span>📧 <EMAIL></span>
                    <a href="#" class="btn btn-secondary">Odhlásiť</a>
                </div>
            </div>
        </div>
    </header>

    <nav class="main-nav">
        <div class="container">
            <ul class="nav-list">
                <li class="nav-item">
                    <a href="prehľad.html" class="nav-link">🏠 Prehľad</a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link active">💰 Vaše benefity</a>
                    <div class="dropdown">
                        <a href="bonusy-prehľad.html" class="nav-link active">💰 Bonusy</a>
                        <a href="nakupovanie.html" class="nav-link">🛍️ Nakupovanie</a>
                        <a href="vystavenie.html" class="nav-link">🗂️ Vystavenie</a>
                        <a href="marketing.html" class="nav-link">📄 Marketing</a>
                    </div>
                </li>
                <li class="nav-item">
                    <a href="ostatne.html" class="nav-link">📚 Ostatné</a>
                    <div class="dropdown">
                        <a href="ostatne.html#elearning" class="nav-link">🎓 E-learning</a>
                        <a href="ostatne.html#sutaze" class="nav-link">🏆 Súťaže</a>
                        <a href="ostatne.html#eshop" class="nav-link">🛍️ Interný e-shop</a>
                    </div>
                </li>
                <li class="nav-item">
                    <a href="kontakt.html" class="nav-link">📞 Kontakt</a>
                </li>
            </ul>
        </div>
    </nav>

    <main class="main-content">
        <div class="container">
            <div class="breadcrumb">
                <a href="index.html">Wireframes</a> > <a href="bonusy-prehľad.html">Bonusy</a> > Bonusy za predaj
            </div>

            <h1 class="page-title">📊 Bonusy za predaj</h1>
            <p style="margin-bottom: 2rem; color: #666;">Rozpis bonusov za predaj produktov zo sezónneho vystavenia a impulzného boxu</p>

            <!-- Filter a navigácia -->
            <div class="table-container" style="margin-bottom: 2rem;">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                    <h2>Filter zobrazenia</h2>
                    <div style="display: flex; gap: 1rem;">
                        <select id="viewFilter" class="form-input" style="width: auto;">
                            <option value="summary">Súhrn po mesiacoch</option>
                            <option value="sezonny">Sezónne vystavenie</option>
                            <option value="impulzny">Impulzný box</option>
                            <option value="detail">Detail mesiaca</option>
                        </select>
                        <select id="monthFilter" class="form-input" style="width: auto; display: none;">
                            <option value="maj">Máj</option>
                            <option value="jun">Jún</option>
                            <option value="jul" selected>Júl</option>
                            <option value="august">August</option>
                        </select>
                        <button class="btn btn-primary" onclick="applyFilter()">Filtrovať</button>
                    </div>
                </div>
            </div>

            <!-- 1. Sezónne vystavenie -->
            <div id="sezonnySection" class="table-container" style="margin-bottom: 2rem;">
                <h2 style="margin-bottom: 1rem;">1. Sezónne vystavenie (0,20 €/ks)</h2>

                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Mesiac</th>
                            <th>Predané ks</th>
                            <th>Bonus (€)</th>
                            <th>Akcie</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr onclick="showMonthDetail('maj', 'sezonny')" style="cursor: pointer;">
                            <td><strong>Máj</strong></td>
                            <td>150</td>
                            <td><strong>30,00</strong></td>
                            <td><a href="#" class="btn btn-primary" onclick="event.stopPropagation(); showMonthDetail('maj', 'sezonny')">Detail</a></td>
                        </tr>
                        <tr onclick="showMonthDetail('jun', 'sezonny')" style="cursor: pointer;">
                            <td><strong>Jún</strong></td>
                            <td>120</td>
                            <td><strong>24,00</strong></td>
                            <td><a href="#" class="btn btn-primary" onclick="event.stopPropagation(); showMonthDetail('jun', 'sezonny')">Detail</a></td>
                        </tr>
                        <tr onclick="showMonthDetail('jul', 'sezonny')" style="cursor: pointer;">
                            <td><strong>Júl</strong></td>
                            <td>180</td>
                            <td><strong>36,00</strong></td>
                            <td><a href="#" class="btn btn-primary" onclick="event.stopPropagation(); showMonthDetail('jul', 'sezonny')">Detail</a></td>
                        </tr>
                        <tr onclick="showMonthDetail('august', 'sezonny')" style="cursor: pointer;">
                            <td><strong>August</strong></td>
                            <td>140</td>
                            <td><strong>28,00</strong></td>
                            <td><a href="#" class="btn btn-primary" onclick="event.stopPropagation(); showMonthDetail('august', 'sezonny')">Detail</a></td>
                        </tr>
                        <tr style="background: #f8f9fa; font-weight: bold;">
                            <td><strong>Súčet</strong></td>
                            <td><strong>590</strong></td>
                            <td><strong>118,00</strong></td>
                            <td></td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 2. Impulzný box -->
            <div id="impulznySection" class="table-container" style="margin-bottom: 2rem;">
                <h2 style="margin-bottom: 1rem;">2. Impulzný box (0,25 €/ks)</h2>

                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Mesiac</th>
                            <th>Predané ks</th>
                            <th>Bonus (€)</th>
                            <th>Akcie</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr onclick="showMonthDetail('maj', 'impulzny')" style="cursor: pointer;">
                            <td><strong>Máj</strong></td>
                            <td>80</td>
                            <td><strong>20,00</strong></td>
                            <td><a href="#" class="btn btn-primary" onclick="event.stopPropagation(); showMonthDetail('maj', 'impulzny')">Detail</a></td>
                        </tr>
                        <tr onclick="showMonthDetail('jun', 'impulzny')" style="cursor: pointer;">
                            <td><strong>Jún</strong></td>
                            <td>70</td>
                            <td><strong>17,50</strong></td>
                            <td><a href="#" class="btn btn-primary" onclick="event.stopPropagation(); showMonthDetail('jun', 'impulzny')">Detail</a></td>
                        </tr>
                        <tr onclick="showMonthDetail('jul', 'impulzny')" style="cursor: pointer;">
                            <td><strong>Júl</strong></td>
                            <td>100</td>
                            <td><strong>25,00</strong></td>
                            <td><a href="#" class="btn btn-primary" onclick="event.stopPropagation(); showMonthDetail('jul', 'impulzny')">Detail</a></td>
                        </tr>
                        <tr onclick="showMonthDetail('august', 'impulzny')" style="cursor: pointer;">
                            <td><strong>August</strong></td>
                            <td>90</td>
                            <td><strong>22,50</strong></td>
                            <td><a href="#" class="btn btn-primary" onclick="event.stopPropagation(); showMonthDetail('august', 'impulzny')">Detail</a></td>
                        </tr>
                        <tr style="background: #f8f9fa; font-weight: bold;">
                            <td><strong>Súčet</strong></td>
                            <td><strong>340</strong></td>
                            <td><strong>85,00</strong></td>
                            <td></td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 3. Rozpis po mesiacoch (spolu) -->
            <div id="summarySection" class="table-container" style="margin-bottom: 2rem;">
                <h2 style="margin-bottom: 1rem;">3. Rozpis po mesiacoch (spolu)</h2>

                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Mesiac</th>
                            <th>Sezónne ks</th>
                            <th>Sezónne €</th>
                            <th>Impulzný ks</th>
                            <th>Impulzný €</th>
                            <th>Spolu ks</th>
                            <th>Spolu €</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>Máj</strong></td>
                            <td>150</td>
                            <td>30,00</td>
                            <td>80</td>
                            <td>20,00</td>
                            <td><strong>230</strong></td>
                            <td><strong>50,00</strong></td>
                        </tr>
                        <tr>
                            <td><strong>Jún</strong></td>
                            <td>120</td>
                            <td>24,00</td>
                            <td>70</td>
                            <td>17,50</td>
                            <td><strong>190</strong></td>
                            <td><strong>41,50</strong></td>
                        </tr>
                        <tr>
                            <td><strong>Júl</strong></td>
                            <td>180</td>
                            <td>36,00</td>
                            <td>100</td>
                            <td>25,00</td>
                            <td><strong>280</strong></td>
                            <td><strong>61,00</strong></td>
                        </tr>
                        <tr>
                            <td><strong>August</strong></td>
                            <td>140</td>
                            <td>28,00</td>
                            <td>90</td>
                            <td>22,50</td>
                            <td><strong>230</strong></td>
                            <td><strong>50,50</strong></td>
                        </tr>
                        <tr style="background: #f8f9fa; font-weight: bold;">
                            <td><strong>Súčet</strong></td>
                            <td><strong>590</strong></td>
                            <td><strong>118,00</strong></td>
                            <td><strong>340</strong></td>
                            <td><strong>85,00</strong></td>
                            <td><strong>930</strong></td>
                            <td><strong>203,00</strong></td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Detail mesiaca (zobrazí sa po kliknutí) -->
            <div id="monthDetailSection" class="table-container" style="display: none; margin-bottom: 2rem;">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                    <h2 id="monthDetailTitle">4. Detail mesiaca – Júl</h2>
                    <button onclick="hideMonthDetail()" class="btn btn-secondary">✕ Zavrieť detail</button>
                </div>

                <div id="monthDetailContent">
                    <!-- Obsah sa načíta dynamicky -->
                </div>
            </div>

            <!-- Celkový súhrn -->
            <div class="table-container">
                <h2 style="margin-bottom: 1rem;">Celkový súhrn bonusov za predaj</h2>
                <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 2rem; text-align: center;">
                    <div>
                        <div style="font-size: 2rem; font-weight: bold; color: #2c5aa0;">203,00 €</div>
                        <div style="color: #666;">Celkové bonusy</div>
                    </div>
                    <div>
                        <div style="font-size: 2rem; font-weight: bold; color: #28a745;">930</div>
                        <div style="color: #666;">Predané kusy</div>
                    </div>
                    <div>
                        <div style="font-size: 2rem; font-weight: bold; color: #ffc107;">0,22 €</div>
                        <div style="color: #666;">Priemerný bonus/ks</div>
                    </div>
                    <div>
                        <div style="font-size: 2rem; font-weight: bold; color: #17a2b8;">4</div>
                        <div style="color: #666;">Aktívne mesiace</div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <footer class="footer">
        <div class="container">
            <p>&copy; 2025 Vaša Lekáreň - Wireframes portálu 2.0</p>
        </div>
    </footer>

    <script>
        // Detailné údaje pre mesiace
        const monthDetails = {
            'jul': {
                sezonny: [
                    { produkt: 'produkt_1', ks: 40, bonus: 8.00 },
                    { produkt: 'produkt_2', ks: 35, bonus: 7.00 },
                    { produkt: 'produkt_3', ks: 50, bonus: 10.00 },
                    { produkt: 'produkt_4', ks: 30, bonus: 6.00 },
                    { produkt: 'produkt_5', ks: 25, bonus: 5.00 }
                ],
                impulzny: [
                    { produkt: 'produkt_6', ks: 45, bonus: 11.25 },
                    { produkt: 'produkt_7', ks: 30, bonus: 7.50 },
                    { produkt: 'produkt_8', ks: 25, bonus: 6.25 }
                ]
            }
        };

        function showMonthDetail(mesiac, typ) {
            const detailSection = document.getElementById('monthDetailSection');
            const detailTitle = document.getElementById('monthDetailTitle');
            const detailContent = document.getElementById('monthDetailContent');

            detailTitle.textContent = `4. Detail mesiaca – ${mesiac.charAt(0).toUpperCase() + mesiac.slice(1)}`;

            let content = '';
            const data = monthDetails[mesiac];

            if (data) {
                if (typ === 'sezonny' || !typ) {
                    content += `
                        <h3>Sezónne vystavenie (0,20 €/ks)</h3>
                        <table class="data-table" style="margin-bottom: 2rem;">
                            <thead>
                                <tr><th>Produkt</th><th>Predané ks</th><th>Bonus (€)</th></tr>
                            </thead>
                            <tbody>
                    `;

                    let sezonnyTotal = { ks: 0, bonus: 0 };
                    data.sezonny.forEach(item => {
                        content += `<tr><td>${item.produkt}</td><td>${item.ks}</td><td>${item.bonus.toFixed(2)}</td></tr>`;
                        sezonnyTotal.ks += item.ks;
                        sezonnyTotal.bonus += item.bonus;
                    });

                    content += `
                                <tr style="background: #f8f9fa; font-weight: bold;">
                                    <td><strong>Súčet</strong></td>
                                    <td><strong>${sezonnyTotal.ks}</strong></td>
                                    <td><strong>${sezonnyTotal.bonus.toFixed(2)}</strong></td>
                                </tr>
                            </tbody>
                        </table>
                    `;
                }

                if (typ === 'impulzny' || !typ) {
                    content += `
                        <h3>Impulzný box (0,25 €/ks)</h3>
                        <table class="data-table">
                            <thead>
                                <tr><th>Produkt</th><th>Predané ks</th><th>Bonus (€)</th></tr>
                            </thead>
                            <tbody>
                    `;

                    let impulznyTotal = { ks: 0, bonus: 0 };
                    data.impulzny.forEach(item => {
                        content += `<tr><td>${item.produkt}</td><td>${item.ks}</td><td>${item.bonus.toFixed(2)}</td></tr>`;
                        impulznyTotal.ks += item.ks;
                        impulznyTotal.bonus += item.bonus;
                    });

                    content += `
                                <tr style="background: #f8f9fa; font-weight: bold;">
                                    <td><strong>Súčet</strong></td>
                                    <td><strong>${impulznyTotal.ks}</strong></td>
                                    <td><strong>${impulznyTotal.bonus.toFixed(2)}</strong></td>
                                </tr>
                            </tbody>
                        </table>
                    `;

                    if (!typ) {
                        const totalKs = sezonnyTotal.ks + impulznyTotal.ks;
                        const totalBonus = sezonnyTotal.bonus + impulznyTotal.bonus;
                        content += `
                            <div style="margin-top: 1rem; padding: 1rem; background: #e7f3ff; border-radius: 4px; text-align: center;">
                                <strong>${mesiac.charAt(0).toUpperCase() + mesiac.slice(1)} spolu: ${totalKs} ks → ${totalBonus.toFixed(2)} €</strong>
                            </div>
                        `;
                    }
                }
            } else {
                content = `<p>Detailné údaje pre mesiac ${mesiac} nie sú dostupné.</p>`;
            }

            detailContent.innerHTML = content;
            detailSection.style.display = 'block';
            detailSection.scrollIntoView({ behavior: 'smooth' });
        }

        function hideMonthDetail() {
            document.getElementById('monthDetailSection').style.display = 'none';
        }

        function applyFilter() {
            const viewFilter = document.getElementById('viewFilter').value;
            const monthFilter = document.getElementById('monthFilter');

            // Skryť všetky sekcie
            document.getElementById('sezonnySection').style.display = 'none';
            document.getElementById('impulznySection').style.display = 'none';
            document.getElementById('summarySection').style.display = 'none';
            document.getElementById('monthDetailSection').style.display = 'none';

            // Zobraziť podľa filtra
            switch(viewFilter) {
                case 'sezonny':
                    document.getElementById('sezonnySection').style.display = 'block';
                    monthFilter.style.display = 'none';
                    break;
                case 'impulzny':
                    document.getElementById('impulznySection').style.display = 'block';
                    monthFilter.style.display = 'none';
                    break;
                case 'detail':
                    monthFilter.style.display = 'block';
                    showMonthDetail(monthFilter.value);
                    break;
                default:
                    document.getElementById('sezonnySection').style.display = 'block';
                    document.getElementById('impulznySection').style.display = 'block';
                    document.getElementById('summarySection').style.display = 'block';
                    monthFilter.style.display = 'none';
            }
        }

        // Inicializácia
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('viewFilter').addEventListener('change', function() {
                if (this.value === 'detail') {
                    document.getElementById('monthFilter').style.display = 'block';
                } else {
                    document.getElementById('monthFilter').style.display = 'none';
                }
            });
        });
    </script>
</body>
</html>
