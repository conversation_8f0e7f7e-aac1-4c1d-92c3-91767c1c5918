<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prehľad bonusov - Port<PERSON><PERSON> Vaša Lekáreň 2.0</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">🏥 Portál Vaša Lekáreň 2.0</div>
                <div class="user-info">
                    <span>👤 Lekáreň ABC</span>
                    <span>📧 <EMAIL></span>
                    <a href="#" class="btn btn-secondary">Odhlásiť</a>
                </div>
            </div>
        </div>
    </header>

    <nav class="main-nav">
        <div class="container">
            <ul class="nav-list">
                <li class="nav-item">
                    <a href="prehľad.html" class="nav-link">🏠 Prehľad</a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link active">💰 Vaše benefity</a>
                    <div class="dropdown">
                        <a href="bonusy-prehľad.html" class="nav-link active">💰 Bonusy</a>
                        <a href="nakupovanie.html" class="nav-link">🛍️ Nakupovanie</a>
                        <a href="vystavenie.html" class="nav-link">🗂️ Vystavenie</a>
                        <a href="marketing.html" class="nav-link">📄 Marketing</a>
                    </div>
                </li>
                <li class="nav-item">
                    <a href="ostatne.html" class="nav-link">📚 Ostatné</a>
                    <div class="dropdown">
                        <a href="ostatne.html#elearning" class="nav-link">🎓 E-learning</a>
                        <a href="ostatne.html#sutaze" class="nav-link">🏆 Súťaže</a>
                        <a href="ostatne.html#eshop" class="nav-link">🛍️ Interný e-shop</a>
                    </div>
                </li>
                <li class="nav-item">
                    <a href="kontakt.html" class="nav-link">📞 Kontakt</a>
                </li>
            </ul>
        </div>
    </nav>

    <main class="main-content">
        <div class="container">
            <div class="breadcrumb">
                <a href="index.html">Wireframes</a> > <a href="bonusy-prehľad.html">Vaše benefity</a> > Prehľad bonusov
            </div>

            <h1 class="page-title">💰 Prehľad bonusov</h1>

            <!-- Filter za cyklus/rok -->
            <div class="" style="margin-bottom: 2rem;">
                <div style="display: flex; justify-content: end;  margin-bottom: 1rem;">
                    <div style="display: flex; gap: 1rem;">
                        <select class="form-input" style="width: auto;">
                            <option>Aktuálny cyklus (Sep-Dec 2025)</option>
                            <option>Predchádzajúci cyklus (Máj-Aug 2025)</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- Sumárny dashboard všetkých typov bonusov -->
            <div class="tiles-grid" style="margin-bottom: 3rem;">
                <div class="tile" onclick="location.href='bonusy-nakup.html'">
                    <div class="tile-icon">📦</div>
                    <div class="tile-title">Bonusy za nákup</div>
                    <div style="display: flex; justify-content: space-between; margin: 1rem 0;">
                        <div>
                            <div style="font-size: 0.9rem; color: #666;">Splnené / Potenciál</div>
                            <div style="font-weight: bold; color: #28a745;">8 / 12 úrovní</div>
                        </div>
                        <div>
                            <div style="font-size: 0.9rem; color: #666;">získané</div>
                            <div class="tile-value">3 627,50 €</div>
                        </div>
                    </div>
                    <div class="progress-bar" style="margin-bottom: 1rem;">
                        <div class="progress-fill" style="width: 67%;"></div>
                    </div>
                    <a href="bonusy-nakup.html" class="tile-cta">Zobraziť detail</a>
                </div>

                <div class="tile" onclick="location.href='bonusy-vystavenie.html'">
                    <div class="tile-icon">🛒</div>
                    <div class="tile-title">Bonusy za vystavenie</div>
                    <div style="display: flex; justify-content: space-between; margin: 1rem 0;">
                        <div>
                            <div style="font-size: 0.9rem; color: #666;">Splnené / Potenciál</div>
                            <div style="font-weight: bold; color: #28a745;">6 / 8 mesiacov</div>
                        </div>
                        <div>
                            <div style="font-size: 0.9rem; color: #666;">získané</div>
                            <div class="tile-value">210,00 €</div>
                        </div>
                    </div>
                    <div class="progress-bar" style="margin-bottom: 1rem;">
                        <div class="progress-fill" style="width: 75%;"></div>
                    </div>
                    <a href="bonusy-vystavenie.html" class="tile-cta">Zobraziť detail</a>
                </div>

                <div class="tile" onclick="location.href='bonusy-predaj.html'">
                    <div class="tile-icon">📊</div>
                    <div class="tile-title">Bonusy za predaj</div>
                    <div style="display: flex; justify-content: space-between; margin: 1rem 0;">
                        <div>
                            <div style="font-size: 0.9rem; color: #666;">Splnené / Potenciál</div>
                            <div style="font-weight: bold; color: #28a745;">930 / 1200 ks</div>
                        </div>
                        <div>
                            <div style="font-size: 0.9rem; color: #666;">získané</div>
                            <div class="tile-value">203,00 €</div>
                        </div>
                    </div>
                    <div class="progress-bar" style="margin-bottom: 1rem;">
                        <div class="progress-fill" style="width: 78%;"></div>
                    </div>
                    <a href="bonusy-predaj.html" class="tile-cta">Zobraziť detail</a>
                </div>
            </div>

            <!-- Celkový súhrn -->
            <div class="table-container" style="margin-bottom: 2rem;">
                <h2 style="margin-bottom: 1rem;">Celkový súhrn za cyklus</h2>
                <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 2rem; text-align: center;">
                    <div>
                        <div style="font-size: 2rem; font-weight: bold; color: #2c5aa0;">4 040,50 €</div>
                        <div style="color: #666;">Celkové bonusy</div>
                    </div>
                    <div>
                        <div style="font-size: 2rem; font-weight: bold; color: #28a745;">89,7%</div>
                        <div style="color: #666;">Priemerné plnenie</div>
                    </div>
                    <div>
                        <div style="font-size: 2rem; font-weight: bold; color: #ffc107;">1 250,00 €</div>
                        <div style="color: #666;">Potenciálne bonusy</div>
                    </div>
                </div>
            </div>

        </div>
    </main>

    <footer class="footer">
        <div class="container">
            <p>&copy; 2025 Vaša Lekáreň - Wireframes portálu 2.0</p>
        </div>
    </footer>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Simulácia interaktívnych funkcií
            const filterSelect = document.querySelector('select');
            const filterBtn = document.querySelector('.btn-primary');

            filterBtn.addEventListener('click', function() {
                alert('Filter aplikovaný: ' + filterSelect.value);
            });

            // Animácia progress barov
            const progressBars = document.querySelectorAll('.progress-fill');
            progressBars.forEach(bar => {
                const width = bar.style.width;
                bar.style.width = '0%';
                setTimeout(() => {
                    bar.style.width = width;
                }, 500);
            });
        });
    </script>
</body>
</html>
