<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bonusy za vystavenie - Portál Vaša Lekáreň 2.0</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">🏥 Portál Vaša Lekáreň 2.0</div>
                <div class="user-info">
                    <span>👤 Lekáreň ABC</span>
                    <span>📧 <EMAIL></span>
                    <a href="#" class="btn btn-secondary">Odhlásiť</a>
                </div>
            </div>
        </div>
    </header>

        <nav class="main-nav">
        <div class="container">
            <ul class="nav-list">
                <li class="nav-item">
                    <a href="prehľad.html" class="nav-link">🏠 Prehľad</a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link active">💰 Vaše benefity</a>
                    <div class="dropdown">
                        <a href="bonusy-prehľad.html" class="nav-link">💰 Prehľad bonusov</a>
                        <a href="bonusy-nakup.html" class="nav-link">📦 Bonusy za nákup</a>
                        <a href="bonusy-vystavenie.html" class="nav-link active">🛒 Bonusy za vystavenie</a>
                        <a href="bonusy-predaj.html" class="nav-link">📊 Bonusy za predaj</a>
                        <a href="doplnkovy-nakup.html" class="nav-link">1️⃣ Doplnkový nákup</a>
                        <a href="extra-marza.html" class="nav-link">2️⃣ Extra marža</a>
                        <a href="centralny-nakup.html" class="nav-link">3️⃣ Centrálny nákup</a>
                        <a href="vystavenie.html" class="nav-link">🗂️ Vystavenie</a>
                        <a href="marketing.html" class="nav-link">📄 Marketing</a>
                    </div>
                </li>
                <li class="nav-item">
                    <a href="ostatne.html#elearning" class="nav-link">🎓 E-learning</a>
                </li>
                <li class="nav-item">
                    <a href="ostatne.html#sutaze" class="nav-link">🏆 Súťaže</a>
                </li>
                <li class="nav-item">
                    <a href="ostatne.html#eshop" class="nav-link">🛍️ E-shop</a>
                </li>
                <li class="nav-item">
                    <a href="kontakt.html" class="nav-link">📞 Kontakt</a>
                </li>
            </ul>

            <!-- Rýchle skratky pod navigáciou -->
            <div class="quick-shortcuts-nav">
                <a href="bonusy-prehľad.html" class="shortcut-nav">
                    <div class="shortcut-icon">💰</div>
                    <div class="shortcut-text">Bonusy</div>
                </a>
                <a href="doplnkovy-nakup.html" class="shortcut-nav">
                    <div class="shortcut-icon">1️⃣</div>
                    <div class="shortcut-text">Doplnkový nákup</div>
                </a>
                <a href="extra-marza.html" class="shortcut-nav">
                    <div class="shortcut-icon">2️⃣</div>
                    <div class="shortcut-text">Extra marža</div>
                </a>
                <a href="vystavenie.html" class="shortcut-nav">
                    <div class="shortcut-icon">🗂️</div>
                    <div class="shortcut-text">Vystavenie</div>
                </a>
                <a href="marketing.html" class="shortcut-nav">
                    <div class="shortcut-icon">📄</div>
                    <div class="shortcut-text">Marketing</div>
                </a>
            </div>
        </div>
    </nav>

    <main class="main-content">
        <div class="container">
            <div class="breadcrumb">
                <a href="index.html">Wireframes</a> > <a href="bonusy-prehľad.html">Bonusy</a> > Bonusy za vystavenie
            </div>

            <h1 class="page-title">🛒 Bonusy za vystavenie</h1>

            <!-- Cyklový sumár -->
            <div class="table-container" style="margin-bottom: 2rem;">
                <h2 style="margin-bottom: 1rem;">Cyklový sumár (máj–august)</h2>
                <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 2rem; text-align: center;">
                    <div>
                        <div style="font-size: 2rem; font-weight: bold; color: #2c5aa0;">150,00 €</div>
                        <div style="color: #666;">Sezónne vystavenie</div>
                    </div>
                    <div>
                        <div style="font-size: 2rem; font-weight: bold; color: #28a745;">60,00 €</div>
                        <div style="color: #666;">Impulzný box</div>
                    </div>
                    <div>
                        <div style="font-size: 2rem; font-weight: bold; color: #ffc107;">210,00 €</div>
                        <div style="color: #666;">Spolu</div>
                    </div>
                </div>
            </div>


            <!-- Sezónne vystavenie -->
            <div class="table-container" style="margin-bottom: 2rem;">
                <h2 style="margin-bottom: 1rem;">Sezónne vystavenie (50 € / mesiac, cyklus: máj–august)</h2>

                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Mesiac</th>
                            <th>Stav</th>
                            <th>Dátum splnenia</th>
                            <th>Odmena (€)</th>
                            <th>Dôkaz / Foto</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>Máj</strong></td>
                            <td><span class="status status-success">🟢 Splnené</span></td>
                            <td>08.05.2025</td>
                            <td><strong>50,00</strong></td>
                            <td>
                                <a href="#" class="btn btn-primary" onclick="showPhoto('maj-sezonny')">📷 zobraziť</a>
                                <a href="#" class="btn btn-success" onclick="uploadPhoto('maj-sezonny')">➕ nahrať</a>
                            </td>
                        </tr>
                        <tr>
                            <td><strong>Jún</strong></td>
                            <td><span class="status status-warning">🟡 Čaká na schválenie</span></td>
                            <td>25.06.2025</td>
                            <td><strong>50,00*</strong></td>
                            <td>
                                <a href="#" class="btn btn-primary" onclick="showPhoto('jun-sezonny')">📷 zobraziť</a>
                                <a href="#" class="btn btn-success" onclick="uploadPhoto('jun-sezonny')">➕ nahrať</a>
                            </td>
                        </tr>
                        <tr>
                            <td><strong>Júl</strong></td>
                            <td><span class="status status-danger">🔴 Nesplnené</span></td>
                            <td>–</td>
                            <td>0,00</td>
                            <td>
                                <a href="#" class="btn btn-success" onclick="uploadPhoto('jul-sezonny')">➕ nahrať</a>
                            </td>
                        </tr>
                        <tr>
                            <td><strong>August</strong></td>
                            <td><span class="status status-success">🟢 Splnené</span></td>
                            <td>03.08.2025</td>
                            <td><strong>50,00</strong></td>
                            <td>
                                <a href="#" class="btn btn-primary" onclick="showPhoto('aug-sezonny')">📷 zobraziť</a>
                                <a href="#" class="btn btn-success" onclick="uploadPhoto('aug-sezonny')">➕ nahrať</a>
                            </td>
                        </tr>
                        <tr style="background: #f8f9fa; font-weight: bold;">
                            <td><strong>Súčet</strong></td>
                            <td></td>
                            <td></td>
                            <td><strong>150,00</strong></td>
                            <td></td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Impulzný box -->
            <div class="table-container" style="margin-bottom: 2rem;">
                <h2 style="margin-bottom: 1rem;">Impulzný box (20 € / mesiac, cyklus: máj–august)</h2>

                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Mesiac</th>
                            <th>Stav</th>
                            <th>Dátum splnenia</th>
                            <th>Odmena (€)</th>
                            <th>Dôkaz / Foto</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><strong>Máj</strong></td>
                            <td><span class="status status-success">🟢 Splnené</span></td>
                            <td>15.05.2025</td>
                            <td><strong>20,00</strong></td>
                            <td>
                                <a href="#" class="btn btn-primary" onclick="showPhoto('maj-impulzny')">📷 zobraziť</a>
                                <a href="#" class="btn btn-success" onclick="uploadPhoto('maj-impulzny')">➕ nahrať</a>
                            </td>
                        </tr>
                        <tr>
                            <td><strong>Jún</strong></td>
                            <td><span class="status status-success">🟢 Splnené</span></td>
                            <td>29.06.2025</td>
                            <td><strong>20,00</strong></td>
                            <td>
                                <a href="#" class="btn btn-primary" onclick="showPhoto('jun-impulzny')">[CAMERA] zobraziť</a>
                                <a href="#" class="btn btn-success" onclick="uploadPhoto('jun-impulzny')" style="margin-left: 0.5rem;">[PLUS] nahrať</a>
                            </td>
                        </tr>
                        <tr>
                            <td><strong>Júl</strong></td>
                            <td><span class="status status-danger">🔴 Nesplnené</span></td>
                            <td>–</td>
                            <td>0,00</td>
                            <td><a href="#" class="btn btn-success" onclick="uploadPhoto('jul-impulzny')">[PLUS] nahrať</a></td>
                        </tr>
                        <tr>
                            <td><strong>August</strong></td>
                            <td><span class="status status-success">🟢 Splnené</span></td>
                            <td>04.08.2025</td>
                            <td><strong>20,00</strong></td>
                            <td>
                                <a href="#" class="btn btn-primary" onclick="showPhoto('aug-impulzny')">[CAMERA] zobraziť</a>
                                <a href="#" class="btn btn-success" onclick="uploadPhoto('aug-impulzny')" style="margin-left: 0.5rem;">[PLUS] nahrať</a>
                            </td>
                        </tr>
                        <tr style="background: #f8f9fa; font-weight: bold;">
                            <td><strong>Súčet</strong></td>
                            <td></td>
                            <td></td>
                            <td><strong>60,00</strong></td>
                            <td></td>
                        </tr>
                    </tbody>
                </table>
            </div>



        </div>
    </main>

    <!-- Modal pre zobrazenie fotiek -->
    <div id="photoModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.8); z-index: 1000;">
        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 2rem; border-radius: 8px; max-width: 80%; max-height: 80%;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                <h3 id="photoTitle">Fotodôkaz vystavenia</h3>
                <button onclick="closePhotoModal()" class="btn btn-secondary">[X]</button>
            </div>
            <div id="photoContent">
                <!-- Obsah sa načíta dynamicky -->
            </div>
        </div>
    </div>

    <footer class="footer">
        <div class="container">
            <p>&copy; 2025 Vaša Lekáreň - Wireframes portálu 2.0</p>
        </div>
    </footer>

    <script>
        function showPhoto(photoId) {
            const modal = document.getElementById('photoModal');
            const title = document.getElementById('photoTitle');
            const content = document.getElementById('photoContent');

            const photoData = {
                'maj-sezonny': {
                    title: 'Sezónne vystavenie - Máj 2025',
                    date: '08.05.2025',
                    photos: ['foto1.jpg', 'foto2.jpg']
                },
                'jun-sezonny': {
                    title: 'Sezónne vystavenie - Jún 2025',
                    date: '25.06.2025',
                    photos: ['foto3.jpg']
                },
                'aug-sezonny': {
                    title: 'Sezónne vystavenie - August 2025',
                    date: '03.08.2025',
                    photos: ['foto4.jpg', 'foto5.jpg']
                },
                'maj-impulzny': {
                    title: 'Impulzný box - Máj 2025',
                    date: '15.05.2025',
                    photos: ['foto6.jpg']
                },
                'jun-impulzny': {
                    title: 'Impulzný box - Jún 2025',
                    date: '29.06.2025',
                    photos: ['foto7.jpg']
                },
                'aug-impulzny': {
                    title: 'Impulzný box - August 2025',
                    date: '04.08.2025',
                    photos: ['foto8.jpg']
                }
            };

            const data = photoData[photoId];
            if (data) {
                title.textContent = data.title;
                content.innerHTML = `
                    <p><strong>Dátum nahratia:</strong> ${data.date}</p>
                    <p><strong>Počet fotiek:</strong> ${data.photos.length}</p>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-top: 1rem;">
                        ${data.photos.map((photo, index) => `
                            <div style="border: 2px dashed #ccc; padding: 2rem; text-align: center; border-radius: 4px;">
                                <div style="font-size: 3rem; color: #ccc;">[IMG]</div>
                                <div>Foto ${index + 1}</div>
                                <div style="font-size: 0.8rem; color: #666;">${photo}</div>
                            </div>
                        `).join('')}
                    </div>
                    <div style="margin-top: 1rem; text-align: center;">
                        <button class="btn btn-primary">Stiahnuť všetky</button>
                        <button class="btn btn-success" onclick="approvePhotos('${photoId}')" style="margin-left: 0.5rem;">Prepnúť na schválené</button>
                        <button class="btn btn-secondary" onclick="closePhotoModal()" style="margin-left: 0.5rem;">Zavrieť</button>
                    </div>
                `;
                modal.style.display = 'block';
            }
        }

        function uploadPhoto(photoId) {
            alert('Simulácia nahrávania fotodôkazu pre: ' + photoId);
        }

        function approvePhotos(photoId) {
            if (confirm('Naozaj chcete prepnúť fotky na schválené pre: ' + photoId + '?')) {
                alert('Fotky boli prepnuté na schválené pre: ' + photoId);
                closePhotoModal();
                // Tu by sa aktualizoval stav v tabuľke
            }
        }

        function closePhotoModal() {
            document.getElementById('photoModal').style.display = 'none';
        }

        function showHistory() {
            alert('Zobrazenie histórie bonusov za vystavenie z predchádzajúcich cyklov');
        }

        // Zatvorenie modalu pri kliknutí mimo obsah
        document.getElementById('photoModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closePhotoModal();
            }
        });
    </script>
</body>
</html>
