<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Doplnkový nákup - Port<PERSON><PERSON> Vaša Le<PERSON>áreň 2.0</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">🏥 Portál Vaša Lekáreň 2.0</div>
                <div class="user-info">
                    <span>👤 Lekáreň ABC</span>
                    <span>📧 <EMAIL></span>
                    <a href="#" class="btn btn-secondary">Odhlásiť</a>
                </div>
            </div>
        </div>
    </header>

    <nav class="main-nav">
        <div class="container">
            <ul class="nav-list">
                <li class="nav-item">
                    <a href="prehľad.html" class="nav-link">🏠 Prehľad</a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link active">💰 Vaše benefity</a>
                    <div class="dropdown">
                        <a href="bonusy-prehľad.html" class="nav-link">💰 Bonusy</a>
                        <a href="nakupovanie.html" class="nav-link active">🛍️ Nakupovanie</a>
                        <a href="vystavenie.html" class="nav-link">🗂️ Vystavenie</a>
                        <a href="marketing.html" class="nav-link">📄 Marketing</a>
                    </div>
                </li>
                <li class="nav-item">
                    <a href="ostatne.html" class="nav-link">📚 Ostatné</a>
                    <div class="dropdown">
                        <a href="ostatne.html#elearning" class="nav-link">🎓 E-learning</a>
                        <a href="ostatne.html#sutaze" class="nav-link">🏆 Súťaže</a>
                        <a href="ostatne.html#eshop" class="nav-link">🛍️ Interný e-shop</a>
                    </div>
                </li>
                <li class="nav-item">
                    <a href="kontakt.html" class="nav-link">📞 Kontakt</a>
                </li>
            </ul>
        </div>
    </nav>

    <main class="main-content">
        <div class="container">
            <div class="breadcrumb">
                <a href="index.html">Wireframes</a> > <a href="nakupovanie.html">Nakupovanie</a> > Doplnkový nákup
            </div>

            <h1 class="page-title">1️⃣ Doplnkový nákup</h1>
            <p style="margin-bottom: 2rem; color: #666;">Pomôcť lekárni splniť konkrétnu úroveň bonusu (obratový, rastový) ešte počas prebiehajúceho cyklu.</p>

            <!-- Filter firmy -->
            <div class="table-container" style="margin-bottom: 2rem;">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                    <h2>Filter firmy</h2>
                    <div style="display: flex; gap: 1rem; align-items: center;">
                        <label for="firmaFilter" class="form-label" style="margin-bottom: 0;">Vybrať firmu:</label>
                        <select id="firmaFilter" class="form-input" style="width: auto;">
                            <option value="all">Všetky firmy</option>
                            <option value="SANOFI" selected>SANOFI (chýba 500 €)</option>
                            <option value="Pfizer">Pfizer (chýba 1000 €)</option>
                            <option value="GSK">GSK (splnené)</option>
                        </select>
                        <button class="btn btn-primary" onclick="applyFirmaFilter()">Filtrovať</button>
                    </div>
                </div>
            </div>

            <!-- Cieľ a progress -->
            <div class="table-container" style="margin-bottom: 2rem;">
                <h2 style="margin-bottom: 1rem;">Cieľ doplnkového nákupu</h2>
                <div id="goalSection">
                    <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 2rem; text-align: center; margin-bottom: 2rem;">
                        <div>
                            <div style="font-size: 2rem; font-weight: bold; color: #2c5aa0;">SANOFI</div>
                            <div style="color: #666;">Vybraná firma</div>
                        </div>
                        <div>
                            <div style="font-size: 2rem; font-weight: bold; color: #ffc107;">500,00 €</div>
                            <div style="color: #666;">Chýba nakúpiť</div>
                        </div>
                        <div>
                            <div style="font-size: 2rem; font-weight: bold; color: #28a745;">725,00 €</div>
                            <div style="color: #666;">Potenciálny bonus</div>
                        </div>
                    </div>

                    <div style="background: #fff3cd; border-left: 4px solid #ffc107; padding: 1rem; border-radius: 4px;">
                        <h4 style="margin-bottom: 0.5rem;">🎯 Cieľ: Dosiahnuť 100% plnenie pre obratový bonus</h4>
                        <p style="margin-bottom: 1rem;">Aktuálne plnenie: 9 000 € / 10 000 € (90%)</p>
                        <div class="progress-bar" style="height: 30px;">
                            <div class="progress-fill" style="width: 90%; background: #ffc107; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold;">
                                90%
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tabuľka produktov -->
            <div class="table-container" style="margin-bottom: 2rem;">
                <h2 style="margin-bottom: 1rem;">Zoznam bonusových produktov</h2>

                <div style="margin-bottom: 1rem; display: flex; gap: 1rem; align-items: center;">
                    <input type="text" placeholder="Hľadať produkt..." class="form-input" style="width: 300px;">
                    <select class="form-input" style="width: auto;">
                        <option>Všetky kategórie</option>
                        <option>Vitamíny</option>
                        <option>Analgetiká</option>
                        <option>Probiótiká</option>
                    </select>
                    <button class="btn btn-secondary">Hľadať</button>
                </div>

                <table class="data-table">
                    <thead>
                        <tr>
                            <th>Produkt</th>
                            <th>Cena/ks (€)</th>
                            <th>Počet ks</th>
                            <th>Medzisúčet (€)</th>
                            <th>Akcie</th>
                        </tr>
                    </thead>
                    <tbody id="produktyTable">
                        <tr>
                            <td><strong>produkt_1</strong><br><small style="color: #666;">Vitamín C 1000mg</small></td>
                            <td>25,00</td>
                            <td>
                                <input type="number" value="4" min="0" class="form-input" style="width: 80px;" onchange="updateMedzisucet(this, 25.00)">
                            </td>
                            <td class="medzisucet">100,00</td>
                            <td>
                                <button class="btn btn-success" onclick="addToCart(this)">+ Košík</button>
                            </td>
                        </tr>
                        <tr>
                            <td><strong>produkt_2</strong><br><small style="color: #666;">Ibalgin 400mg</small></td>
                            <td>15,50</td>
                            <td>
                                <input type="number" value="6" min="0" class="form-input" style="width: 80px;" onchange="updateMedzisucet(this, 15.50)">
                            </td>
                            <td class="medzisucet">93,00</td>
                            <td>
                                <button class="btn btn-success" onclick="addToCart(this)">+ Košík</button>
                            </td>
                        </tr>
                        <tr>
                            <td><strong>produkt_3</strong><br><small style="color: #666;">Lactobacillus</small></td>
                            <td>42,00</td>
                            <td>
                                <input type="number" value="5" min="0" class="form-input" style="width: 80px;" onchange="updateMedzisucet(this, 42.00)">
                            </td>
                            <td class="medzisucet">210,00</td>
                            <td>
                                <button class="btn btn-success" onclick="addToCart(this)">+ Košík</button>
                            </td>
                        </tr>
                        <tr>
                            <td><strong>produkt_4</strong><br><small style="color: #666;">Paralen 500mg</small></td>
                            <td>10,00</td>
                            <td>
                                <input type="number" value="3" min="0" class="form-input" style="width: 80px;" onchange="updateMedzisucet(this, 10.00)">
                            </td>
                            <td class="medzisucet">30,00</td>
                            <td>
                                <button class="btn btn-success" onclick="addToCart(this)">+ Košík</button>
                            </td>
                        </tr>
                        <tr>
                            <td><strong>produkt_5</strong><br><small style="color: #666;">Magnesium B6</small></td>
                            <td>13,50</td>
                            <td>
                                <input type="number" value="5" min="0" class="form-input" style="width: 80px;" onchange="updateMedzisucet(this, 13.50)">
                            </td>
                            <td class="medzisucet">67,50</td>
                            <td>
                                <button class="btn btn-success" onclick="addToCart(this)">+ Košík</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Sticky sumár -->
            <div class="table-container" style="position: sticky; bottom: 20px; background: white; border: 2px solid #2c5aa0;">
                <h2 style="margin-bottom: 1rem; color: #2c5aa0;">📊 Sumár objednávky</h2>
                <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 2rem; text-align: center; margin-bottom: 1rem;">
                    <div>
                        <div id="totalSum" style="font-size: 2rem; font-weight: bold; color: #2c5aa0;">500,50 €</div>
                        <div style="color: #666;">Celkový medzisúčet</div>
                    </div>
                    <div>
                        <div id="remaining" style="font-size: 2rem; font-weight: bold; color: #28a745;">0,00 €</div>
                        <div style="color: #666;">Chýba nakúpiť</div>
                    </div>
                    <div>
                        <div id="goalProgress" style="font-size: 2rem; font-weight: bold; color: #28a745;">100,1%</div>
                        <div style="color: #666;">Plnenie cieľa</div>
                    </div>
                    <div>
                        <div id="potentialBonus" style="font-size: 2rem; font-weight: bold; color: #ffc107;">725,00 €</div>
                        <div style="color: #666;">Bonus pri dosiahnutí</div>
                    </div>
                </div>

                <div style="text-align: center; margin-bottom: 1rem;">
                    <div class="progress-bar" style="height: 30px;">
                        <div id="progressBar" class="progress-fill" style="width: 100%; background: #28a745; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold;">
                            Cieľ splnený! 🎉
                        </div>
                    </div>
                </div>

                <div style="text-align: center; display: flex; gap: 1rem; justify-content: center;">
                    <button class="btn btn-primary" onclick="submitOrder()" style="font-size: 1.1rem; padding: 0.75rem 2rem;">
                        🛒 Pridať do objednávky
                    </button>
                    <a href="bonusy-nakup.html" class="btn btn-secondary" style="font-size: 1.1rem; padding: 0.75rem 2rem;">
                        ← Späť na bonusy
                    </a>
                </div>
            </div>
        </div>
    </main>

    <footer class="footer">
        <div class="container">
            <p>&copy; 2025 Vaša Lekáreň - Wireframes portálu 2.0</p>
        </div>
    </footer>

    <script>
        let currentGoal = 500.00;
        let currentTotal = 500.50;

        function updateMedzisucet(input, cena) {
            const pocet = parseInt(input.value) || 0;
            const medzisucet = pocet * cena;
            const row = input.closest('tr');
            const medzisucetCell = row.querySelector('.medzisucet');
            medzisucetCell.textContent = medzisucet.toFixed(2);

            updateSummary();
        }

        function updateSummary() {
            let total = 0;
            const medzisucetCells = document.querySelectorAll('.medzisucet');
            medzisucetCells.forEach(cell => {
                total += parseFloat(cell.textContent) || 0;
            });

            const remaining = Math.max(0, currentGoal - total);
            const progress = (total / currentGoal) * 100;

            document.getElementById('totalSum').textContent = total.toFixed(2) + ' €';
            document.getElementById('remaining').textContent = remaining.toFixed(2) + ' €';
            document.getElementById('goalProgress').textContent = progress.toFixed(1) + '%';

            const progressBar = document.getElementById('progressBar');
            progressBar.style.width = Math.min(100, progress) + '%';

            if (progress >= 100) {
                progressBar.style.background = '#28a745';
                progressBar.textContent = 'Cieľ splnený! 🎉';
                document.getElementById('remaining').style.color = '#28a745';
            } else {
                progressBar.style.background = '#ffc107';
                progressBar.textContent = progress.toFixed(1) + '%';
                document.getElementById('remaining').style.color = '#dc3545';
            }
        }

        function addToCart(button) {
            const row = button.closest('tr');
            const produkt = row.querySelector('td:first-child strong').textContent;
            const pocet = row.querySelector('input[type="number"]').value;
            const medzisucet = row.querySelector('.medzisucet').textContent;

            alert(`Pridané do košíka:\n${produkt}\nPočet: ${pocet} ks\nSuma: ${medzisucet} €`);
        }

        function submitOrder() {
            const total = document.getElementById('totalSum').textContent;
            const progress = document.getElementById('goalProgress').textContent;

            if (parseFloat(progress) >= 100) {
                alert(`🎉 Gratulujeme!\n\nObjednávka bola úspešne odoslaná.\nCelková suma: ${total}\nCieľ bol splnený na ${progress}\n\nBonus 725,00 € bude pripísaný po spracovaní objednávky.`);
                // Simulácia návratu na bonusy
                setTimeout(() => {
                    window.location.href = 'bonusy-nakup.html';
                }, 2000);
            } else {
                const remaining = document.getElementById('remaining').textContent;
                if (confirm(`Cieľ ešte nie je splnený.\nChýba ešte: ${remaining}\n\nChcete napriek tomu odoslať objednávku?`)) {
                    alert('Objednávka bola odoslaná. Bonus nebude pripísaný, pretože cieľ nebol splnený.');
                }
            }
        }

        function applyFirmaFilter() {
            const firma = document.getElementById('firmaFilter').value;
            // Simulácia zmeny údajov podľa firmy
            alert('Filter aplikovaný pre firmu: ' + firma);
        }

        // Inicializácia
        document.addEventListener('DOMContentLoaded', function() {
            updateSummary();
        });
    </script>
</body>
</html>
