(function(l,o){typeof exports=="object"&&typeof module<"u"?o(exports,require("vue")):typeof define=="function"&&define.amd?define(["exports","vue"],o):(l=typeof globalThis<"u"?globalThis:l||self,o(l.PortalVue={},l.Vue))})(this,function(l,o){"use strict";const s=Symbol("wormhole");function m(){const e=o.inject(s);if(!e)throw new Error(`
    [portal-vue]: Necessary Injection not found. Make sur you installed the plugin properly.`);return e}function M(e){o.provide(s,e)}const y=typeof window<"u";function S(e,t){return e.map((n,c)=>[c,n]).sort(function(n,c){return t(n[1],c[1])||n[0]-c[0]}).map(n=>n[1])}function C(e,t){const n=m();function c(){if(!y)return;const{to:f,name:r,order:a}=e;t.default?n.open({to:f,from:r,order:a,content:t.default}):d()}function d(f){n.close({to:f??e.to,from:e.name})}o.onMounted(()=>{e.disabled||c()}),o.onUpdated(()=>{e.disabled?d():c()}),o.onBeforeUnmount(()=>{d()}),o.watch(()=>e.to,(f,r)=>{e.disabled||(r&&r!==f&&d(r),c())})}const w=o.defineComponent({compatConfig:{MODE:3},name:"portal",props:{disabled:{type:Boolean},name:{type:[String,Symbol],default:()=>Symbol()},order:{type:Number},slotProps:{type:Object,default:()=>({})},to:{type:String,default:()=>String(Math.round(Math.random()*1e7))}},setup(e,{slots:t}){return C(e,t),()=>e.disabled&&t.default?t.default(e.slotProps):null}}),W=(e,{slots:t})=>{var n;return(n=t.default)==null?void 0:n.call(t)},p=o.defineComponent({compatConfig:{MODE:3},name:"portalTarget",props:{multiple:{type:Boolean,default:!1},name:{type:String,required:!0},slotProps:{type:Object,default:()=>({})}},emits:["change"],setup(e,{emit:t,slots:n}){const c=m(),d=o.computed(()=>{const f=c.getContentForTarget(e.name,e.multiple),r=n.wrapper,a=f.map(u=>u.content(e.slotProps)),i=r?a.flatMap(u=>u.length?r(u):[]):a.flat(1);return{vnodes:i,vnodesFn:()=>i}});return o.watch(d,({vnodes:f})=>{const r=f.length>0,a=c.transports.get(e.name),i=a?[...a.keys()]:[];t("change",{hasContent:r,sources:i})},{flush:"post"}),()=>{var r;return d.value.vnodes.length?[o.h("div",{style:"display: none",key:"__portal-vue-hacky-scoped-slot-repair__"}),o.h(W,d.value.vnodesFn)]:(r=n.default)==null?void 0:r.call(n)}}});function b(e=!0){const t=o.reactive(new Map);function n(r){if(!y)return;const{to:a,from:i,content:u,order:h=1/0}=r;if(!a||!i||!u)return;t.has(a)||t.set(a,new Map);const g=t.get(a),v={to:a,from:i,content:u,order:h};g.set(i,v)}function c(r){const{to:a,from:i}=r;if(!a||!i)return;const u=t.get(a);!u||(u.delete(i),u.size||t.delete(a))}function d(r,a){const i=t.get(r);if(!i)return[];const u=Array.from((i==null?void 0:i.values())||[]);return a?S(u,(h,g)=>h.order-g.order):[u.pop()]}const f={open:n,close:c,transports:t,getContentForTarget:d};return e?o.readonly(f):f}const P=b();function _(e,t){const n=o.createApp({render:()=>o.h(p,e)});if(!e.multiple){const c=o.getCurrentInstance().provides??{};n._context.provides=Object.create(c)}o.onMounted(()=>{n.mount(t)}),o.onBeforeUnmount(()=>{n.unmount()})}function T(e,t={}){t.portalName!==!1&&e.component(t.portalName||"Portal",w),t.portalTargetName!==!1&&e.component(t.portalTargetName||"PortalTarget",p);const n=t.wormhole??P;e.provide(s,n)}const j=P,N="3.0.0";l.Portal=w,l.PortalTarget=p,l.Wormhole=j,l.createWormhole=b,l.default=T,l.install=T,l.mountPortalTarget=_,l.provideWormhole=M,l.useWormhole=m,l.version=N,Object.defineProperties(l,{__esModule:{value:!0},[Symbol.toStringTag]:{value:"Module"}})});
//# sourceMappingURL=portal-vue.umd.js.map
