/*!
 * BootstrapVue 2.19.0
 *
 * @link https://bootstrap-vue.org
 * @source https://github.com/bootstrap-vue/bootstrap-vue
 * @copyright (c) 2016-2020 BootstrapVue
 * @license MIT
 * https://github.com/bootstrap-vue/bootstrap-vue/blob/master/LICENSE
 */
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e(require("vue")):"function"==typeof define&&define.amd?define(["vue"],e):(t="undefined"!=typeof globalThis?globalThis:t||self).bootstrapVue=e(t.Vue)}(this,(function(t){"use strict";function e(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var i=e(t);function n(t){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function o(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function r(t,e){for(var i=0;i<e.length;i++){var n=e[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function a(t,e,i){return e&&r(t.prototype,e),i&&r(t,i),t}function s(t,e,i){return e in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}function l(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),i.push.apply(i,n)}return i}function u(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?l(Object(i),!0).forEach((function(e){s(t,e,i[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):l(Object(i)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))}))}return t}function c(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),e&&h(t,e)}function d(t){return(d=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function h(t,e){return(h=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t})(t,e)}function f(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(t){return!1}}function p(t,e,i){return(p=f()?Reflect.construct:function(t,e,i){var n=[null];n.push.apply(n,e);var o=new(Function.bind.apply(t,n));return i&&h(o,i.prototype),o}).apply(null,arguments)}function m(t){var e="function"==typeof Map?new Map:void 0;return(m=function(t){if(null===t||(i=t,-1===Function.toString.call(i).indexOf("[native code]")))return t;var i;if("function"!=typeof t)throw new TypeError("Super expression must either be null or a function");if("undefined"!=typeof e){if(e.has(t))return e.get(t);e.set(t,n)}function n(){return p(t,arguments,d(this).constructor)}return n.prototype=Object.create(t.prototype,{constructor:{value:n,enumerable:!1,writable:!0,configurable:!0}}),h(n,t)})(t)}function g(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function v(t,e){return!e||"object"!=typeof e&&"function"!=typeof e?g(t):e}function b(t){var e=f();return function(){var i,n=d(t);if(e){var o=d(this).constructor;i=Reflect.construct(n,arguments,o)}else i=n.apply(this,arguments);return v(this,i)}}function y(t,e,i){return(y="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(t,e,i){var n=function(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=d(t)););return t}(t,e);if(n){var o=Object.getOwnPropertyDescriptor(n,e);return o.get?o.get.call(i):o.value}})(t,e,i||t)}function S(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){if("undefined"==typeof Symbol||!(Symbol.iterator in Object(t)))return;var i=[],n=!0,o=!1,r=void 0;try{for(var a,s=t[Symbol.iterator]();!(n=(a=s.next()).done)&&(i.push(a.value),!e||i.length!==e);n=!0);}catch(t){o=!0,r=t}finally{try{n||null==s.return||s.return()}finally{if(o)throw r}}return i}(t,e)||T(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function w(t){return function(t){if(Array.isArray(t))return C(t)}(t)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(t)||T(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function T(t,e){if(t){if("string"==typeof t)return C(t,e);var i=Object.prototype.toString.call(t).slice(8,-1);return"Object"===i&&t.constructor&&(i=t.constructor.name),"Map"===i||"Set"===i?Array.from(t):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?C(t,e):void 0}}function C(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,n=new Array(e);i<e;i++)n[i]=t[i];return n}var k=function(){return(k=Object.assign||function(t){for(var e,i=1,n=arguments.length;i<n;i++)for(var o in e=arguments[i])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t}).apply(this,arguments)},B=/-(\w)/g,x=/:(.*)/,$=/;(?![^(]*\))/g;function _(t,e){return e?e.toUpperCase():""}function D(t){for(var e,i={},n=0,o=t.split($);n<o.length;n++){var r=o[n].split(x),a=r[0],s=r[1];(a=a.trim())&&("string"==typeof s&&(s=s.trim()),i[(e=a,e.replace(B,_))]=s)}return i}function F(){for(var t,e,i={},n=arguments.length;n--;)for(var o=0,r=Object.keys(arguments[n]);o<r.length;o++)switch(t=r[o]){case"class":case"style":case"directives":if(Array.isArray(i[t])||(i[t]=[]),"style"===t){var a=void 0;a=Array.isArray(arguments[n].style)?arguments[n].style:[arguments[n].style];for(var s=0;s<a.length;s++){var l=a[s];"string"==typeof l&&(a[s]=D(l))}arguments[n].style=a}i[t]=i[t].concat(arguments[n][t]);break;case"staticClass":if(!arguments[n][t])break;void 0===i[t]&&(i[t]=""),i[t]&&(i[t]+=" "),i[t]+=arguments[n][t].trim();break;case"on":case"nativeOn":i[t]||(i[t]={});for(var u=0,c=Object.keys(arguments[n][t]||{});u<c.length;u++)e=c[u],i[t][e]?i[t][e]=[].concat(i[t][e],arguments[n][t][e]):i[t][e]=arguments[n][t][e];break;case"attrs":case"props":case"domProps":case"scopedSlots":case"staticStyle":case"hook":case"transition":i[t]||(i[t]={}),i[t]=k({},arguments[n][t],i[t]);break;case"slot":case"key":case"ref":case"tag":case"show":case"keepAlive":default:i[t]||(i[t]=arguments[n][t])}return i}var I,O,P="BvConfig",A=["xs","sm","md","lg","xl"],V="undefined"!=typeof window,E="undefined"!=typeof document,L="undefined"!=typeof navigator,R="undefined"!=typeof Promise,N="undefined"!=typeof MutationObserver||"undefined"!=typeof WebKitMutationObserver||"undefined"!=typeof MozMutationObserver,M=V&&E&&L,H=M?window.navigator.userAgent.toLowerCase():"",z=H.indexOf("jsdom")>0,j=(/msie|trident/.test(H),function(){var t=!1;if(M)try{var e={get passive(){t=!0}};window.addEventListener("test",e,e),window.removeEventListener("test",e,e)}catch(e){t=!1}return t}()),G=M&&("ontouchstart"in document.documentElement||navigator.maxTouchPoints>0),W=M&&Boolean(window.PointerEvent||window.MSPointerEvent),Y=M&&"IntersectionObserver"in window&&"IntersectionObserverEntry"in window&&"intersectionRatio"in window.IntersectionObserverEntry.prototype,U=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,i="undefined"!=typeof process&&process&&process.env||{};return t?i[t]||e:i},q=function(){return U("BOOTSTRAP_VUE_NO_WARN")||"production"===U("NODE_ENV")},K=V?window:{},X=V?K.Element:function(t){c(Element,t);var e=b(Element);function Element(){return o(this,Element),e.apply(this,arguments)}return Element}(m(Object)),HTMLElement=V?K.HTMLElement:function(t){c(HTMLElement,t);var e=b(HTMLElement);function HTMLElement(){return o(this,HTMLElement),e.apply(this,arguments)}return HTMLElement}(X),SVGElement=V?K.SVGElement:function(t){c(SVGElement,t);var e=b(SVGElement);function SVGElement(){return o(this,SVGElement),e.apply(this,arguments)}return SVGElement}(X),Z=V?K.File:function(t){c(i,t);var e=b(i);function i(){return o(this,i),e.apply(this,arguments)}return i}(m(Object)),J=function(t){return n(t)},Q=function(t){return void 0===t},tt=function(t){return null===t},et=function(t){return Q(t)||tt(t)},it=function(t){return"function"===J(t)},nt=function(t){return"boolean"===J(t)},ot=function(t){return"string"===J(t)},rt=function(t){return"number"===J(t)},at=function(t){return!isNaN(parseInt(t,10))},st=function(t){return Array.isArray(t)},lt=function(t){return null!==t&&"object"===n(t)},ut=function(t){return"[object Object]"===Object.prototype.toString.call(t)},ct=function(t){return t instanceof Date},dt=function(t){return t instanceof Event},ht=function(t){return"RegExp"===function(t){return Object.prototype.toString.call(t).slice(8,-1)}(t)},ft=function(){return Object.assign.apply(Object,arguments)},pt=function(t,e){return Object.create(t,e)},mt=function(t,e){return Object.defineProperties(t,e)},gt=function(t,e,i){return Object.defineProperty(t,e,i)},vt=function(t){return Object.getOwnPropertyNames(t)},bt=function(t){return Object.keys(t)},yt=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},St=function(t){return u({},t)},wt=function(t,e){return bt(t).filter((function(t){return-1!==e.indexOf(t)})).reduce((function(e,i){return u(u({},e),{},s({},i,t[i]))}),{})},Tt=function(t,e){return bt(t).filter((function(t){return-1===e.indexOf(t)})).reduce((function(e,i){return u(u({},e),{},s({},i,t[i]))}),{})},Ct=function t(e,i){return lt(e)&&lt(i)&&bt(i).forEach((function(n){lt(i[n])?(e[n]&&lt(e[n])||(e[n]=i[n]),t(e[n],i[n])):ft(e,s({},n,i[n]))})),e},kt=function t(e){var i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e;return st(e)?e.reduce((function(e,i){return[].concat(w(e),[t(i,i)])}),[]):ut(e)?bt(e).reduce((function(i,n){return u(u({},i),{},s({},n,t(e[n],e[n])))}),{}):i},Bt=/\[(\d+)]/g,xt=/^\d+$/,$t=/^\..+/,_t=/^#/,Dt=/^#[A-Za-z]+[\w\-:.]*$/,Ft=/(<([^>]+)>)/gi,It=/\B([A-Z])/g,Ot=/([a-z])([A-Z])/g,Pt=/^[0-9]*\.?[0-9]+$/,At=/\+/g,Vt=/[-/\\^$*+?.()|[\]{}]/g,Et=/[\s\uFEFF\xA0]+/g,Lt=/\s+/,Rt=/\/\*$/,Nt=/(\s|^)(\w)/g,Mt=/^\s+/,Ht=/_/g,zt=/-(\w)/g,jt=/^\d+-\d\d?-\d\d?(?:\s|T|$)/,Gt=/-|\s|T/,Wt=/^([0-1]?[0-9]|2[0-3]):[0-5]?[0-9](:[0-5]?[0-9])?$/,Yt=/^.*(#[^#]+)$/,Ut=/%2C/g,qt=/[!'()*]/g,Kt=/^(\?|#|&)/,Xt=/^\d+(\.\d*)?[/:]\d+(\.\d*)?$/,Zt=/[/:]/,Jt=/^col-/,Qt=/^BIcon/,te=/-u-.+/,ee=function(t){return t},ie=function(t,e){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:void 0;if(!(e=st(e)?e.join("."):e)||!lt(t))return i;if(e in t)return t[e];var n=(e=String(e).replace(Bt,".$1")).split(".").filter(ee);return 0===n.length?i:n.every((function(e){return lt(t)&&e in t&&!et(t=t[e])}))?t:tt(t)?null:i},ne=function(t,e){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,n=ie(t,e);return et(n)?i:n},oe=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;q()||console.warn("[BootstrapVue warn]: ".concat(e?"".concat(e," - "):"").concat(t))},re=function(t){return!M&&(oe("".concat(t,": Can not be called during SSR.")),!0)},ae=function(t){return!R&&(oe("".concat(t,": Requires Promise support.")),!0)},se=function(){function t(){o(this,t),this.$_config={}}return a(t,[{key:"setConfig",value:function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(ut(e)){var i=vt(e);i.forEach((function(i){var n=e[i];"breakpoints"===i?!st(n)||n.length<2||n.some((function(t){return!ot(t)||0===t.length}))?oe('"breakpoints" must be an array of at least 2 breakpoint names',P):t.$_config[i]=kt(n):ut(n)&&(t.$_config[i]=vt(n).reduce((function(t,e){return Q(n[e])||(t[e]=kt(n[e])),t}),t.$_config[i]||{}))}))}}},{key:"resetConfig",value:function(){this.$_config={}}},{key:"getConfig",value:function(){return kt(this.$_config)}},{key:"getConfigValue",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0;return kt(ie(this.$_config,t,e))}}]),t}(),le=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:i.default;e.prototype.$bvConfig=i.default.prototype.$bvConfig=e.prototype.$bvConfig||i.default.prototype.$bvConfig||new se,e.prototype.$bvConfig.setConfig(t)},ue=(I=!1,O=["Multiple instances of Vue detected!","You may need to set up an alias for Vue in your bundler config.","See: https://bootstrap-vue.org/docs#using-module-bundlers"].join("\n"),function(t){I||i.default===t||z||oe(O),I=!0}),ce=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.components,i=t.directives,n=t.plugins,o=function t(o){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};t.installed||(t.installed=!0,ue(o),le(r,o),pe(o,e),ge(o,i),he(o,n))};return o.installed=!1,o},de=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return u(u({},e),{},{install:ce(t)})},he=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};for(var i in e)i&&e[i]&&t.use(e[i])},fe=function(t,e,i){t&&e&&i&&t.component(e,i)},pe=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};for(var i in e)fe(t,i,e[i])},me=function(t,e,i){t&&e&&i&&t.directive(e.replace(/^VB/,"B"),i)},ge=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};for(var i in e)me(t,i,e[i])},ve="BAlert",be="BAspect",ye="BAvatar",Se="BAvatarGroup",we="BBadge",Te="BBreadcrumb",Ce="BBreadcrumbItem",ke="BBreadcrumbLink",Be="BButton",xe="BButtonClose",$e="BButtonGroup",_e="BButtonToolbar",De="BCalendar",Fe="BCard",Ie="BCardBody",Oe="BCardFooter",Pe="BCardGroup",Ae="BCardHeader",Ve="BCardImg",Ee="BCardImgLazy",Le="BCardSubTitle",Re="BCardText",Ne="BCardTitle",Me="BCarousel",He="BCarouselSlide",ze="BCollapse",je="BContainer",Ge="BDropdown",We="BDropdownDivider",Ye="BDropdownForm",Ue="BDropdownGroup",qe="BDropdownHeader",Ke="BDropdownItem",Xe="BDropdownItemButton",Ze="BDropdownText",Je="BEmbed",Qe="BForm",ti="BFormCheckbox",ei="BFormCheckboxGroup",ii="BFormDatalist",ni="BFormDatepicker",oi="BFormFile",ri="BFormGroup",ai="BFormInput",si="BFormInvalidFeedback",li="BFormRadio",ui="BFormRadioGroup",ci="BFormRating",di="BFormRow",hi="BFormSelect",fi="BFormSelectOption",pi="BFormSelectOptionGroup",mi="BFormSpinbutton",gi="BFormTag",vi="BFormTags",bi="BFormText",yi="BFormTextarea",Si="BFormTimepicker",wi="BFormValidFeedback",Ti="BIcon",Ci="BIconstack",ki="BImg",Bi="BImgLazy",xi="BInputGroup",$i="BInputGroupAddon",_i="BInputGroupAppend",Di="BInputGroupPrepend",Fi="BInputGroupText",Ii="BJumbotron",Oi="BLink",Pi="BListGroup",Ai="BListGroupItem",Vi="BMedia",Ei="BMediaAside",Li="BMediaBody",Ri="BModal",Ni="BNav",Mi="BNavbar",Hi="BNavbarBrand",zi="BNavbarNav",ji="BNavbarToggle",Gi="BNavForm",Wi="BNavItem",Yi="BNavItemDropdown",Ui="BOverlay",qi="BPagination",Ki="BPaginationNav",Xi="BPopover",Zi="BProgress",Ji="BProgressBar",Qi="BRow",tn="BSidebar",en="BSkeleton",nn="BSkeletonIcon",on="BSkeletonImg",rn="BSkeletonTable",an="BSkeletonWrapper",sn="BSpinner",ln="BTab",un="BTable",cn="BTableCell",dn="BTabs",hn="BTbody",fn="BTfoot",pn="BThead",mn="BTime",gn="BToast",vn="BToaster",bn="BTooltip",yn=function(t){var e=pt(null);return function(){for(var i=arguments.length,n=new Array(i),o=0;o<i;o++)n[o]=arguments[o];var r=JSON.stringify(n);return e[r]=e[r]||t.apply(null,n)}},Sn=i.default.prototype,wn=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0,i=Sn.$bvConfig;return i?i.getConfigValue(t,e):kt(e)},Tn=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:void 0;return e?wn("".concat(t,".").concat(e),i):wn(t,{})},Cn=function(){return wn("breakpoints",A)},kn=yn((function(){return Cn()})),Bn=yn((function(){var t=kt(kn());return t[0]="",t})),xn=function(t,e){return bt(t).reduce((function(i,n){var o=t[n],r=o.default;return i[n]=u(u({},kt(o)),{},{default:function(){return Tn(e,n,it(r)?r():r)}}),i}),{})},$n=function(){return Array.from.apply(Array,arguments)},_n=function(t,e){return-1!==t.indexOf(e)},Dn=function(){for(var t=arguments.length,e=new Array(t),i=0;i<t;i++)e[i]=arguments[i];return Array.prototype.concat.apply([],e)},Fn=function(t,e){return Array(t).fill(e)},In=function(t){return t.reduce((function(t,e){return t.concat(e)}),[])},On=function t(e){return e.reduce((function(e,i){return e.concat(Array.isArray(i)?t(i):i)}),[])},Pn=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:NaN,i=parseInt(t,10);return isNaN(i)?e:i},An=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:NaN,i=parseFloat(t);return isNaN(i)?e:i},Vn=function(t,e){return An(t).toFixed(Pn(e,0))},En=function(t){return t.replace(It,"-$1").toLowerCase()},Ln=function(t){return(t=En(t).replace(zt,(function(t,e){return e?e.toUpperCase():""}))).charAt(0).toUpperCase()+t.slice(1)},Rn=function(t){return t.replace(Ht," ").replace(Ot,(function(t,e,i){return e+" "+i})).replace(Nt,(function(t,e,i){return e+i.toUpperCase()}))},Nn=function(t){return(t=ot(t)?t.trim():String(t)).charAt(0).toUpperCase()+t.slice(1)},Mn=function(t){return t.replace(Vt,"\\$&")},Hn=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:2;return et(t)?"":st(t)||ut(t)&&t.toString===Object.prototype.toString?JSON.stringify(t,null,e):String(t)},zn=function(t){return Hn(t).trim()},jn=function(t){return Hn(t).toLowerCase()},Gn=["button","[href]:not(.disabled)","input","select","textarea","[tabindex]","[contenteditable]"].map((function(t){return"".concat(t,":not(:disabled):not([disabled])")})).join(", "),Wn=V?window:{},Yn=E?document:{},Un="undefined"!=typeof Element?Element.prototype:{},qn=Un.matches||Un.msMatchesSelector||Un.webkitMatchesSelector,Kn=Un.closest||function(t){var e=this;do{if(so(e,t))return e;e=e.parentElement||e.parentNode}while(!tt(e)&&e.nodeType===Node.ELEMENT_NODE);return null},Xn=Wn.requestAnimationFrame||Wn.webkitRequestAnimationFrame||Wn.mozRequestAnimationFrame||Wn.msRequestAnimationFrame||Wn.oRequestAnimationFrame||function(t){return setTimeout(t,16)},Zn=Wn.MutationObserver||Wn.WebKitMutationObserver||Wn.MozMutationObserver||null,Jn=function(t){return!(!t||t.nodeType!==Node.ELEMENT_NODE)},Qn=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],e=Yn.activeElement;return e&&!t.some((function(t){return t===e}))?e:null},to=function(t,e){return Hn(t).toLowerCase()===Hn(e).toLowerCase()},eo=function(t){return Jn(t)&&t===Qn()},io=function(t){if(!Jn(t)||!t.parentNode||!uo(Yn.body,t))return!1;if("none"===wo(t,"display"))return!1;var e=To(t);return!!(e&&e.height>0&&e.width>0)},no=function(t){return!Jn(t)||t.disabled||bo(t,"disabled")||po(t,"disabled")},oo=function(t){return Jn(t)&&t.offsetHeight},ro=function(t,e){return $n((Jn(e)?e:Yn).querySelectorAll(t))},ao=function(t,e){return(Jn(e)?e:Yn).querySelector(t)||null},so=function(t,e){return!!Jn(t)&&qn.call(t,e)},lo=function(t,e){var i=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(!Jn(e))return null;var n=Kn.call(e,t);return i?n:n===e?null:n},uo=function(t,e){return!(!t||!it(t.contains))&&t.contains(e)},co=function(t){return Yn.getElementById(/^#/.test(t)?t.slice(1):t)||null},ho=function(t,e){e&&Jn(t)&&t.classList&&t.classList.add(e)},fo=function(t,e){e&&Jn(t)&&t.classList&&t.classList.remove(e)},po=function(t,e){return!!(e&&Jn(t)&&t.classList)&&t.classList.contains(e)},mo=function(t,e,i){e&&Jn(t)&&t.setAttribute(e,i)},go=function(t,e){e&&Jn(t)&&t.removeAttribute(e)},vo=function(t,e){return e&&Jn(t)?t.getAttribute(e):null},bo=function(t,e){return e&&Jn(t)?t.hasAttribute(e):null},yo=function(t,e,i){e&&Jn(t)&&(t.style[e]=i)},So=function(t,e){e&&Jn(t)&&(t.style[e]="")},wo=function(t,e){return e&&Jn(t)&&t.style[e]||null},To=function(t){return Jn(t)?t.getBoundingClientRect():null},Co=function(t){return V&&Jn(t)?Wn.getComputedStyle(t):{}},ko=function(){return V&&Wn.getSelection?Wn.getSelection():null},Bo=function(t){var e={top:0,left:0};if(!Jn(t)||0===t.getClientRects().length)return e;var i=To(t);if(i){var n=t.ownerDocument.defaultView;e.top=i.top+n.pageYOffset,e.left=i.left+n.pageXOffset}return e},xo=function(t){var e={top:0,left:0};if(!Jn(t))return e;var i={top:0,left:0},n=Co(t);if("fixed"===n.position)e=To(t)||e;else{e=Bo(t);for(var o=t.ownerDocument,r=t.offsetParent||o.documentElement;r&&(r===o.body||r===o.documentElement)&&"static"===Co(r).position;)r=r.parentNode;if(r&&r!==t&&r.nodeType===Node.ELEMENT_NODE){i=Bo(r);var a=Co(r);i.top+=An(a.borderTopWidth,0),i.left+=An(a.borderLeftWidth,0)}}return{top:e.top-i.top-An(n.marginTop,0),left:e.left-i.left-An(n.marginLeft,0)}},$o=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:document;return ro(Gn,t).filter(io).filter((function(t){return t.tabIndex>-1&&!t.disabled}))},_o=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};try{t.focus(e)}catch(t){}return eo(t)},Do=function(t){try{t.blur()}catch(t){}return!eo(t)},Fo={name:"",enterClass:"",enterActiveClass:"",enterToClass:"show",leaveClass:"show",leaveActiveClass:"",leaveToClass:""},Io=u(u({},Fo),{},{enterActiveClass:"fade",leaveActiveClass:"fade"}),Oo=i.default.extend({name:"BVTransition",functional:!0,props:{noFade:{type:Boolean,default:!1},appear:{type:Boolean,default:!1},mode:{type:String},transProps:{type:Object,default:null}},render:function(t,e){var i=e.children,n=e.data,o=e.props,r=o.transProps;return ut(r)||(r=o.noFade?Fo:Io,o.appear&&(r=u(u({},r),{},{appear:!0,appearClass:r.enterClass,appearActiveClass:r.enterActiveClass,appearToClass:r.enterToClass}))),t("transition",F(n,{props:r=u(u({mode:o.mode},r),{},{css:!0})}),i)}}),Po="append",Ao="button-content",Vo="default",Eo="first",Lo="footer",Ro="header",No="label",Mo="lead",Ho="prepend",zo="title",jo=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return(t=Dn(t).filter(ee)).some((function(t){return e[t]||i[t]}))},Go=function(t){var e,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};t=Dn(t).filter(ee);for(var r=0;r<t.length&&!e;r++){var a=t[r];e=n[a]||o[a]}return it(e)?e(i):e},Wo={methods:{hasNormalizedSlot:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Vo;return jo(t,this.$scopedSlots,this.$slots)},normalizeSlot:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Vo,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=Go(t,e,this.$scopedSlots,this.$slots);return i?Dn(i):i}}},Yo=function(t){return j?lt(t)?t:{capture:!!t||!1}:!!(lt(t)?t.capture:t)},Uo=function(t,e,i,n){t&&t.addEventListener&&t.addEventListener(e,i,Yo(n))},qo=function(t,e,i,n){t&&t.removeEventListener&&t.removeEventListener(e,i,Yo(n))},Ko=function(t){for(var e=t?Uo:qo,i=arguments.length,n=new Array(i>1?i-1:0),o=1;o<i;o++)n[o-1]=arguments[o];e.apply(void 0,n)},Xo=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=e.preventDefault,n=void 0===i||i,o=e.propagation,r=void 0===o||o,a=e.immediatePropagation,s=void 0!==a&&a;n&&t.preventDefault(),r&&t.stopPropagation(),s&&t.stopImmediatePropagation()},Zo=xn({content:{type:String,default:"&times;"},disabled:{type:Boolean,default:!1},ariaLabel:{type:String,default:"Close"},textVariant:{type:String}},xe),Jo=i.default.extend({name:xe,functional:!0,props:Zo,render:function(t,e){var i=e.props,n=e.data,o=e.slots,r=e.scopedSlots,a=o(),l=r||{},u={staticClass:"close",class:s({},"text-".concat(i.textVariant),i.textVariant),attrs:{type:"button",disabled:i.disabled,"aria-label":i.ariaLabel?String(i.ariaLabel):null},on:{click:function(t){i.disabled&&dt(t)&&Xo(t)}}};return jo(Vo,l,a)||(u.domProps={innerHTML:i.content}),t("button",F(n,u),Go(Vo,{},l,a))}}),Qo=function(t){return""===t||nt(t)?0:(t=Pn(t,0))>0?t:0},tr=function(t){return""===t||!0===t||!(Pn(t,0)<1)&&!!t},er=de({components:{BAlert:i.default.extend({name:ve,mixins:[Wo],model:{prop:"show",event:"input"},props:xn({variant:{type:String,default:"info"},dismissible:{type:Boolean,default:!1},dismissLabel:{type:String,default:"Close"},show:{type:[Boolean,Number,String],default:!1},fade:{type:Boolean,default:!1}},ve),data:function(){return{countDown:0,localShow:tr(this.show)}},watch:{show:function(t){this.countDown=Qo(t),this.localShow=tr(t)},countDown:function(t){var e=this;this.clearCountDownInterval(),at(this.show)&&(this.$emit("dismiss-count-down",t),this.show!==t&&this.$emit("input",t),t>0?(this.localShow=!0,this.$_countDownTimeout=setTimeout((function(){e.countDown--}),1e3)):this.$nextTick((function(){Xn((function(){e.localShow=!1}))})))},localShow:function(t){t||!this.dismissible&&!at(this.show)||this.$emit("dismissed"),at(this.show)||this.show===t||this.$emit("input",t)}},created:function(){this.$_filterTimer=null,this.countDown=Qo(this.show),this.localShow=tr(this.show)},mounted:function(){this.countDown=Qo(this.show),this.localShow=tr(this.show)},beforeDestroy:function(){this.clearCountDownInterval()},methods:{dismiss:function(){this.clearCountDownInterval(),this.countDown=0,this.localShow=!1},clearCountDownInterval:function(){clearTimeout(this.$_countDownTimeout),this.$_countDownTimeout=null}},render:function(t){var e;if(this.localShow){var i=t();this.dismissible&&(i=t(Jo,{attrs:{"aria-label":this.dismissLabel},on:{click:this.dismiss}},[this.normalizeSlot("dismiss")])),e=[e=t("div",{key:this._uid,staticClass:"alert",class:s({"alert-dismissible":this.dismissible},"alert-".concat(this.variant),this.variant),attrs:{role:"alert","aria-live":"polite","aria-atomic":!0}},[i,this.normalizeSlot()])]}return t(Oo,{props:{noFade:!this.fade}},e)}})}}),ir=Math.min,nr=Math.max,or=Math.abs,rr=Math.ceil,ar=Math.floor,sr=Math.pow,lr=Math.round,ur="b-aspect",cr=i.default.extend({name:be,mixins:[Wo],props:xn({aspect:{type:[Number,String],default:"1:1"},tag:{type:String,default:"div"}},be),computed:{padding:function(){var t=this.aspect,e=1;if(Xt.test(t)){var i=S(t.split(Zt).map((function(t){return An(t)||1})),2);e=i[0]/i[1]}else e=An(t)||1;return"".concat(100/or(e),"%")}},render:function(t){var e=t("div",{staticClass:"".concat(ur,"-sizer flex-grow-1"),style:{paddingBottom:this.padding,height:0}}),i=t("div",{staticClass:"".concat(ur,"-content flex-grow-1 w-100 mw-100"),style:{marginLeft:"-100%"}},[this.normalizeSlot()]);return t(this.tag,{staticClass:"".concat(ur," d-flex")},[e,i])}}),dr=de({components:{BAspect:cr}}),hr=function(t,e){return t+Nn(e)},fr=function(t,e){return i=e.replace(t,""),(i=ot(i)?i.trim():String(i)).charAt(0).toLowerCase()+i.slice(1);var i},pr=function(t,e){return e+(t?Nn(t):"")},mr=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:ee;if(st(t))return t.map(e);var i={};for(var n in t)yt(t,n)&&(i[e(n)]=lt(t[n])?St(t[n]):t[n]);return i},gr=function(t,e){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:ee;return(st(t)?t.slice():bt(t)).reduce((function(t,n){return t[i(n)]=e[n],t}),{})},vr=function(t){return"%"+t.charCodeAt(0).toString(16)},br=function(t){return encodeURIComponent(Hn(t)).replace(qt,vr).replace(Ut,",")},yr=decodeURIComponent,Sr=function(t){if(!ut(t))return"";var e=bt(t).map((function(e){var i=t[e];return Q(i)?"":tt(i)?br(e):st(i)?i.reduce((function(t,i){return tt(i)?t.push(br(e)):Q(i)||t.push(br(e)+"="+br(i)),t}),[]).join("&"):br(e)+"="+br(i)})).filter((function(t){return t.length>0})).join("&");return e?"?".concat(e):""},wr=function(t){var e={};return(t=Hn(t).trim().replace(Kt,""))?(t.split("&").forEach((function(t){var i=t.replace(At," ").split("="),n=yr(i.shift()),o=i.length>0?yr(i.join("=")):null;Q(e[n])?e[n]=o:st(e[n])?e[n].push(o):e[n]=[e[n],o]})),e):e},Tr=function(t){return!(!t.href&&!t.to)},Cr=function(t){return!(!t||to(t,"a"))},kr=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.href,i=t.to,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"a",o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"#",r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"/";if(e)return e;if(Cr(n))return null;if(ot(i))return i||r;if(ut(i)&&(i.path||i.query||i.hash)){var a=Hn(i.path),s=Sr(i.query),l=Hn(i.hash);return l=l&&"#"!==l.charAt(0)?"#".concat(l):l,"".concat(a).concat(s).concat(l)||r}return o},Br=40,xr=35,$r=13,_r=36,Dr=37,Fr=39,Ir=32,Or=38,Pr=function(t,e){if(t.length!==e.length)return!1;for(var i=!0,n=0;i&&n<t.length;n++)i=Ar(t[n],e[n]);return i},Ar=function t(e,i){if(e===i)return!0;var n=ct(e),o=ct(i);if(n||o)return!(!n||!o)&&e.getTime()===i.getTime();if(n=st(e),o=st(i),n||o)return!(!n||!o)&&Pr(e,i);if(n=lt(e),o=lt(i),n||o){if(!n||!o)return!1;if(bt(e).length!==bt(i).length)return!1;for(var r in e){var a=yt(e,r),s=yt(i,r);if(a&&!s||!a&&s||!t(e[r],i[r]))return!1}}return String(e)===String(i)},Vr=function(t){return!t||0===bt(t).length},Er=function(t){return{handler:function(e,i){if(!Ar(e,i))if(Vr(e)||Vr(i))this[t]=kt(e);else{for(var n in i)yt(e,n)||this.$delete(this.$data[t],n);for(var o in e)this.$set(this.$data[t],o,e[o])}}}},Lr=function(t,e){return{data:function(){return s({},e,kt(this[t]))},watch:s({},t,Er(e))}},Rr=Lr("$attrs","bvAttrs"),Nr=Lr("$listeners","bvListeners"),Mr={to:{type:[String,Object],default:null},append:{type:Boolean,default:!1},replace:{type:Boolean,default:!1},event:{type:[String,Array],default:"click"},activeClass:{type:String},exact:{type:Boolean,default:!1},exactActiveClass:{type:String},routerTag:{type:String,default:"a"}},Hr={prefetch:{type:Boolean,default:null},noPrefetch:{type:Boolean,default:!1}},zr=xn(u(u(u({href:{type:String,default:null},rel:{type:String,default:null},target:{type:String,default:"_self"},active:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1}},Mr),Hr),{},{routerComponentName:{type:String}}),Oi),jr=i.default.extend({name:Oi,mixins:[Rr,Nr,Wo],inheritAttrs:!1,props:zr,computed:{computedTag:function(){return function(t,e){var i=t.to,n=t.disabled,o=t.routerComponentName,r=!!e.$router;return!r||r&&(n||!i)?"a":o||(e.$nuxt?"nuxt-link":"router-link")}({to:this.to,disabled:this.disabled,routerComponentName:this.routerComponentName},this)},isRouterLink:function(){return Cr(this.computedTag)},computedRel:function(){return function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.target,i=t.rel;return"_blank"===e&&tt(i)?"noopener":i||null}({target:this.target,rel:this.rel})},computedHref:function(){var t=this.to,e=this.href;return kr({to:t,href:e},this.computedTag)},computedProps:function(){var t=this.prefetch;return this.isRouterLink?u(u({},gr(u(u({},Mr),Hr),this)),{},{prefetch:nt(t)?t:void 0,tag:this.routerTag}):{}},computedAttrs:function(){var t=this.bvAttrs,e=this.computedHref,i=this.computedRel,n=this.disabled,o=this.target,r=this.routerTag,a=this.isRouterLink;return u(u(u(u({},t),e?{href:e}:{}),a&&!to(r,"a")?{}:{rel:i,target:o}),{},{tabindex:n?"-1":Q(t.tabindex)?null:t.tabindex,"aria-disabled":n?"true":null})},computedListeners:function(){return u(u({},this.bvListeners),{},{click:this.onClick})}},methods:{onClick:function(t){var e=arguments,i=dt(t),n=this.isRouterLink,o=this.bvListeners.click;i&&this.disabled?Xo(t,{immediatePropagation:!0}):(n&&t.currentTarget.__vue__&&t.currentTarget.__vue__.$emit("click",t),Dn(o).filter((function(t){return it(t)})).forEach((function(t){t.apply(void 0,w(e))})),this.$root.$emit("clicked::link",t)),i&&!n&&"#"===this.computedHref&&Xo(t,{propagation:!1})},focus:function(){_o(this.$el)},blur:function(){Do(this.$el)}},render:function(t){var e=this.active,i=this.disabled;return t(this.computedTag,s({class:{active:e,disabled:i},attrs:this.computedAttrs,props:this.computedProps},this.isRouterLink?"nativeOn":"on",this.computedListeners),this.normalizeSlot())}}),Gr=Tt(zr,["event","routerTag"]);delete Gr.href.default,delete Gr.to.default;var Wr=xn(u({block:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},size:{type:String},variant:{type:String,default:"secondary"},type:{type:String,default:"button"},tag:{type:String,default:"button"},pill:{type:Boolean,default:!1},squared:{type:Boolean,default:!1},pressed:{type:Boolean,default:null}},Gr),Be),Yr=function(t){"focusin"===t.type?ho(t.target,"focus"):"focusout"===t.type&&fo(t.target,"focus")},Ur=function(t){return Tr(t)||to(t.tag,"a")},qr=function(t){return nt(t.pressed)},Kr=function(t){return!(Ur(t)||t.tag&&!to(t.tag,"button"))},Xr=function(t){return!Ur(t)&&!Kr(t)},Zr=function(t){var e;return["btn-".concat(t.variant||"secondary"),(e={},s(e,"btn-".concat(t.size),t.size),s(e,"btn-block",t.block),s(e,"rounded-pill",t.pill),s(e,"rounded-0",t.squared&&!t.pill),s(e,"disabled",t.disabled),s(e,"active",t.pressed),e)]},Jr=function(t){return Ur(t)?gr(Gr,t):{}},Qr=function(t,e){var i=Kr(t),n=Ur(t),o=qr(t),r=Xr(t),a=n&&"#"===t.href,s=e.attrs&&e.attrs.role?e.attrs.role:null,l=e.attrs?e.attrs.tabindex:null;return(r||a)&&(l="0"),{type:i&&!n?t.type:null,disabled:i?t.disabled:null,role:r||a?"button":s,"aria-disabled":r?String(t.disabled):null,"aria-pressed":o?String(t.pressed):null,autocomplete:o?"off":null,tabindex:t.disabled&&!i?"-1":l}},ta=i.default.extend({name:Be,functional:!0,props:Wr,render:function(t,e){var i=e.props,n=e.data,o=e.listeners,r=e.children,a=qr(i),s=Ur(i),l=Xr(i),u=s&&"#"===i.href,c={keydown:function(t){if(!i.disabled&&(l||u)){var e=t.keyCode;if(e===Ir||e===$r&&l){var n=t.currentTarget||t.target;Xo(t,{propagation:!1}),n.click()}}},click:function(t){i.disabled&&dt(t)?Xo(t):a&&o&&o["update:pressed"]&&Dn(o["update:pressed"]).forEach((function(t){it(t)&&t(!i.pressed)}))}};a&&(c.focusin=Yr,c.focusout=Yr);var d={staticClass:"btn",class:Zr(i),props:Jr(i),attrs:Qr(i,n),on:c};return t(s?jr:i.tag,F(n,d),r)}}),ea={title:{type:String},variant:{type:String,default:null},fontScale:{type:[Number,String],default:1},scale:{type:[Number,String],default:1},rotate:{type:[Number,String],default:0},flipH:{type:Boolean,default:!1},flipV:{type:Boolean,default:!1},shiftH:{type:[Number,String],default:0},shiftV:{type:[Number,String],default:0},animation:{type:String,default:null}},ia={viewBox:"0 0 16 16",width:"1em",height:"1em",focusable:"false",role:"img","aria-label":"icon"},na={width:null,height:null,focusable:null,role:null,"aria-label":null},oa=i.default.extend({name:"BIconBase",functional:!0,props:u({content:{type:String},stacked:{type:Boolean,default:!1}},ea),render:function(t,e){var i,n=e.data,o=e.props,r=e.children,a=nr(An(o.fontScale,1),0)||1,l=nr(An(o.scale,1),0)||1,u=An(o.rotate,0),c=An(o.shiftH,0),d=An(o.shiftV,0),h=o.flipH,f=o.flipV,p=o.animation,m=h||f||1!==l,g=m||u,v=c||d,b=[g?"translate(8 8)":null,m?"scale(".concat((h?-1:1)*l," ").concat((f?-1:1)*l,")"):null,u?"rotate(".concat(u,")"):null,g?"translate(-8 -8)":null].filter(ee),y=o.stacked,S=!et(o.content),w=t("g",{attrs:{transform:b.join(" ")||null},domProps:S?{innerHTML:o.content||""}:{}},r);v&&(w=t("g",{attrs:{transform:"translate(".concat(16*c/16," ").concat(-16*d/16,")")}},[w])),y&&(w=t("g",{},[w]));var T=o.title?t("title",o.title):null;return t("svg",F({staticClass:"b-icon bi",class:(i={},s(i,"text-".concat(o.variant),!!o.variant),s(i,"b-icon-animation-".concat(p),!!p),i),attrs:ia,style:y?{}:{fontSize:1===a?null:"".concat(100*a,"%")}},n,y?{attrs:na}:{},{attrs:{xmlns:y?null:"http://www.w3.org/2000/svg",fill:"currentColor"}}),[T,w])}}),ra=function(t,e){var n=En(t),o="BIcon".concat(Ln(t)),r="bi-".concat(n),a=n.replace(/-/g," "),s=zn(e||"");return i.default.extend({name:o,functional:!0,props:u(u({},ea),{},{stacked:{type:Boolean,default:!1}}),render:function(t,e){var i=e.data,n=e.props;return t(oa,F({props:{title:a},attrs:{"aria-label":a}},i,{staticClass:r,props:u(u({},n),{},{content:s})}))}})},aa=ra("Blank",""),sa=ra("Calendar",'<path fill-rule="evenodd" d="M3.5 0a.5.5 0 0 1 .5.5V1h8V.5a.5.5 0 0 1 1 0V1h1a2 2 0 0 1 2 2v11a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V3a2 2 0 0 1 2-2h1V.5a.5.5 0 0 1 .5-.5zM1 4v10a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V4H1z"/>'),la=ra("CalendarFill",'<path d="M3.5 0a.5.5 0 0 1 .5.5V1h8V.5a.5.5 0 0 1 1 0V1h1a2 2 0 0 1 2 2v11a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V5h16V4H0V3a2 2 0 0 1 2-2h1V.5a.5.5 0 0 1 .5-.5z"/>'),ua=ra("ChevronBarLeft",'<path fill-rule="evenodd" d="M11.854 3.646a.5.5 0 0 1 0 .708L8.207 8l3.647 3.646a.5.5 0 0 1-.708.708l-4-4a.5.5 0 0 1 0-.708l4-4a.5.5 0 0 1 .708 0zM4.5 1a.5.5 0 0 0-.5.5v13a.5.5 0 0 0 1 0v-13a.5.5 0 0 0-.5-.5z"/>'),ca=ra("ChevronDoubleLeft",'<path fill-rule="evenodd" d="M8.354 1.646a.5.5 0 0 1 0 .708L2.707 8l5.647 5.646a.5.5 0 0 1-.708.708l-6-6a.5.5 0 0 1 0-.708l6-6a.5.5 0 0 1 .708 0z"/><path fill-rule="evenodd" d="M12.354 1.646a.5.5 0 0 1 0 .708L6.707 8l5.647 5.646a.5.5 0 0 1-.708.708l-6-6a.5.5 0 0 1 0-.708l6-6a.5.5 0 0 1 .708 0z"/>'),da=ra("ChevronDown",'<path fill-rule="evenodd" d="M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z"/>'),ha=ra("ChevronLeft",'<path fill-rule="evenodd" d="M11.354 1.646a.5.5 0 0 1 0 .708L5.707 8l5.647 5.646a.5.5 0 0 1-.708.708l-6-6a.5.5 0 0 1 0-.708l6-6a.5.5 0 0 1 .708 0z"/>'),fa=ra("ChevronUp",'<path fill-rule="evenodd" d="M7.646 4.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1-.708.708L8 5.707l-5.646 5.647a.5.5 0 0 1-.708-.708l6-6z"/>'),pa=ra("CircleFill",'<circle cx="8" cy="8" r="8"/>'),ma=ra("Clock",'<path fill-rule="evenodd" d="M8 15A7 7 0 1 0 8 1a7 7 0 0 0 0 14zm8-7A8 8 0 1 1 0 8a8 8 0 0 1 16 0z"/><path fill-rule="evenodd" d="M7.5 3a.5.5 0 0 1 .5.5v5.21l3.248 1.856a.5.5 0 0 1-.496.868l-3.5-2A.5.5 0 0 1 7 9V3.5a.5.5 0 0 1 .5-.5z"/>'),ga=ra("ClockFill",'<path fill-rule="evenodd" d="M16 8A8 8 0 1 1 0 8a8 8 0 0 1 16 0zM8 3.5a.5.5 0 0 0-1 0V9a.5.5 0 0 0 .252.434l3.5 2a.5.5 0 0 0 .496-.868L8 8.71V3.5z"/>'),va=ra("Dash",'<path fill-rule="evenodd" d="M4 8a.5.5 0 0 1 .5-.5h7a.5.5 0 0 1 0 1h-7A.5.5 0 0 1 4 8z"/>'),ba=ra("PersonFill",'<path fill-rule="evenodd" d="M3 14s-1 0-1-1 1-4 6-4 6 3 6 4-1 1-1 1H3zm5-6a3 3 0 1 0 0-6 3 3 0 0 0 0 6z"/>'),ya=ra("Plus",'<path fill-rule="evenodd" d="M8 4a.5.5 0 0 1 .5.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3A.5.5 0 0 1 8 4z"/>'),Sa=ra("Star",'<path fill-rule="evenodd" d="M2.866 14.85c-.078.444.36.791.746.593l4.39-2.256 4.389 2.256c.386.198.824-.149.746-.592l-.83-4.73 3.523-3.356c.329-.314.158-.888-.283-.95l-4.898-.696L8.465.792a.513.513 0 0 0-.927 0L5.354 5.12l-4.898.696c-.441.062-.612.636-.283.95l3.523 3.356-.83 4.73zm4.905-2.767l-3.686 1.894.694-3.957a.565.565 0 0 0-.163-.505L1.71 6.745l4.052-.576a.525.525 0 0 0 .393-.288l1.847-3.658 1.846 3.658a.525.525 0 0 0 .393.288l4.052.575-2.906 2.77a.564.564 0 0 0-.163.506l.694 3.957-3.686-1.894a.503.503 0 0 0-.461 0z"/>'),wa=ra("StarFill",'<path d="M3.612 15.443c-.386.198-.824-.149-.746-.592l.83-4.73L.173 6.765c-.329-.314-.158-.888.283-.95l4.898-.696L7.538.792c.197-.39.73-.39.927 0l2.184 4.327 4.898.696c.441.062.612.636.283.95l-3.523 3.356.83 4.73c.078.443-.36.79-.746.592L8 13.187l-4.389 2.256z"/>'),Ta=ra("StarHalf",'<path fill-rule="evenodd" d="M5.354 5.119L7.538.792A.516.516 0 0 1 8 .5c.183 0 .366.097.465.292l2.184 4.327 4.898.696A.537.537 0 0 1 16 6.32a.55.55 0 0 1-.17.445l-3.523 3.356.83 4.73c.078.443-.36.79-.746.592L8 13.187l-4.389 2.256a.519.519 0 0 1-.146.05c-.341.06-.668-.254-.6-.642l.83-4.73L.173 6.765a.55.55 0 0 1-.171-.403.59.59 0 0 1 .084-.302.513.513 0 0 1 .37-.245l4.898-.696zM8 12.027c.08 0 .16.018.232.056l3.686 1.894-.694-3.957a.564.564 0 0 1 .163-.505l2.906-2.77-4.052-.576a.525.525 0 0 1-.393-.288L8.002 2.223 8 2.226v9.8z"/>'),Ca=ra("X",'<path fill-rule="evenodd" d="M4.646 4.646a.5.5 0 0 1 .708 0L8 7.293l2.646-2.647a.5.5 0 0 1 .708.708L8.707 8l2.647 2.646a.5.5 0 0 1-.708.708L8 8.707l-2.646 2.647a.5.5 0 0 1-.708-.708L7.293 8 4.646 5.354a.5.5 0 0 1 0-.708z"/>'),ka=function t(e,i){return e?(e.$options||{}).components[i]||t(e.$parent,i):null},Ba=i.default.extend({name:Ti,functional:!0,props:xn(u(u({icon:{type:String,default:null}},ea),{},{stacked:{type:Boolean,default:!1}}),Ti),render:function(t,e){var i=e.data,n=e.props,o=e.parent,r=Ln(zn(n.icon||"")).replace(Qt,"");return t(r&&ka(o,"BIcon".concat(r))||aa,F(i,{props:u(u({},n),{},{icon:null})}))}}),xa="b-avatar",$a=["sm",null,"lg"],_a=function(t){return t=ot(t)&&Pt.test(t)?An(t,0):t,rt(t)?"".concat(t,"px"):t||null},Da=Tt(zr,["active","event","routerTag"]),Fa=de({components:{BAvatar:i.default.extend({name:ye,mixins:[Wo],inject:{bvAvatarGroup:{default:null}},props:xn(u(u({src:{type:String},text:{type:String},icon:{type:String},alt:{type:String,default:"avatar"},variant:{type:String,default:"secondary"},size:{type:[Number,String]},square:{type:Boolean,default:!1},rounded:{type:[Boolean,String],default:!1},button:{type:Boolean,default:!1},buttonType:{type:String,default:"button"},badge:{type:[Boolean,String],default:!1},badgeVariant:{type:String,default:"primary"},badgeTop:{type:Boolean,default:!1},badgeLeft:{type:Boolean,default:!1},badgeOffset:{type:String,default:"0px"}},Da),{},{ariaLabel:{type:String}}),ye),data:function(){return{localSrc:this.src||null}},computed:{computedSize:function(){var t=this.bvAvatarGroup;return _a(t?t.size:this.size)},computedVariant:function(){var t=this.bvAvatarGroup;return t&&t.variant?t.variant:this.variant},computedRounded:function(){var t=this.bvAvatarGroup,e=!(!t||!t.square)||this.square,i=t&&t.rounded?t.rounded:this.rounded;return e?"0":""===i||(i||"circle")},fontStyle:function(){var t=this.computedSize,e=-1===$a.indexOf(t)?"calc(".concat(t," * ").concat(.4,")"):null;return e?{fontSize:e}:{}},marginStyle:function(){var t=this.computedSize,e=this.bvAvatarGroup,i=e?e.overlapScale:0,n=t&&i?"calc(".concat(t," * -").concat(i,")"):null;return n?{marginLeft:n,marginRight:n}:{}},badgeStyle:function(){var t=this.computedSize,e=this.badgeTop,i=this.badgeLeft,n=this.badgeOffset||"0px";return{fontSize:-1===$a.indexOf(t)?"calc(".concat(t," * ").concat(.27999999999999997," )"):null,top:e?n:null,bottom:e?null:n,left:i?n:null,right:i?null:n}}},watch:{src:function(t,e){t!==e&&(this.localSrc=t||null)}},methods:{onImgError:function(t){this.localSrc=null,this.$emit("img-error",t)},onClick:function(t){this.$emit("click",t)}},render:function(t){var e,i=this.computedVariant,n=this.disabled,o=this.computedRounded,r=this.icon,a=this.localSrc,l=this.text,c=this.fontStyle,d=this.marginStyle,h=this.computedSize,f=this.button,p=this.buttonType,m=this.badge,g=this.badgeVariant,v=this.badgeStyle,b=!f&&Tr(this),y=f?ta:b?jr:"span",S=this.alt,w=this.ariaLabel||null,T=null;this.hasNormalizedSlot()?T=t("span",{staticClass:"b-avatar-custom"},[this.normalizeSlot()]):a?(T=t("img",{style:i?{}:{width:"100%",height:"100%"},attrs:{src:a,alt:S},on:{error:this.onImgError}}),T=t("span",{staticClass:"b-avatar-img"},[T])):T=r?t(Ba,{props:{icon:r},attrs:{"aria-hidden":"true",alt:S}}):l?t("span",{staticClass:"b-avatar-text",style:c},[t("span",l)]):t(ba,{attrs:{"aria-hidden":"true",alt:S}});var C=t(),k=this.hasNormalizedSlot("badge");if(m||""===m||k){var B=!0===m?"":m;C=t("span",{staticClass:"b-avatar-badge",class:s({},"badge-".concat(g),!!g),style:v},[k?this.normalizeSlot("badge"):B])}return t(y,{staticClass:xa,class:(e={},s(e,"".concat(xa,"-").concat(h),h&&-1!==$a.indexOf(h)),s(e,"badge-".concat(i),!f&&i),s(e,"rounded",!0===o),s(e,"rounded-".concat(o),o&&!0!==o),s(e,"disabled",n),e),style:u(u({},d),{},{width:h,height:h}),attrs:{"aria-label":w||null},props:f?{variant:i,disabled:n,type:p}:b?gr(Da,this):{},on:f||b?{click:this.onClick}:{}},[T,C])}}),BAvatarGroup:i.default.extend({name:Se,mixins:[Wo],provide:function(){return{bvAvatarGroup:this}},props:xn({variant:{type:String,default:null},size:{type:String},overlap:{type:[Number,String],default:.3},square:{type:Boolean,default:!1},rounded:{type:[Boolean,String],default:!1},tag:{type:String,default:"div"}},Se),computed:{computedSize:function(){return _a(this.size)},overlapScale:function(){return ir(nr(An(this.overlap,0),0),1)/2},paddingStyle:function(){var t=this.computedSize;return(t=t?"calc(".concat(t," * ").concat(this.overlapScale,")"):null)?{paddingLeft:t,paddingRight:t}:{}}},render:function(t){var e=t("div",{staticClass:"b-avatar-group-inner",style:this.paddingStyle},[this.normalizeSlot()]);return t(this.tag,{staticClass:"b-avatar-group",attrs:{role:"group"}},[e])}})}}),Ia=Tt(zr,["event","routerTag"]);delete Ia.href.default,delete Ia.to.default;var Oa=xn(u({tag:{type:String,default:"span"},variant:{type:String,default:"secondary"},pill:{type:Boolean,default:!1}},Ia),we),Pa=i.default.extend({name:we,functional:!0,props:Oa,render:function(t,e){var i=e.props,n=e.data,o=e.children,r=Tr(i);return t(r?jr:i.tag,F(n,{staticClass:"badge",class:[i.variant?"badge-".concat(i.variant):"badge-secondary",{"badge-pill":i.pill,active:i.active,disabled:i.disabled}],props:r?gr(Ia,i):{}}),o)}}),Aa=de({components:{BBadge:Pa}}),Va=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return String(t).replace(Ft,"")},Ea=function(t,e){return t?{innerHTML:t}:e?{textContent:e}:{}},La=xn(u({text:{type:String,default:null},html:{type:String,default:null},ariaCurrent:{type:String,default:"location"}},Tt(zr,["event","routerTag"])),ke),Ra=i.default.extend({name:ke,functional:!0,props:La,render:function(t,e){var i=e.props,n=e.data,o=e.children,r=i.active,a=r?"span":jr,s={attrs:{"aria-current":r?i.ariaCurrent:null},props:gr(La,i)};return o||(s.domProps=Ea(i.html,i.text)),t(a,F(n,s),o)}}),Na=i.default.extend({name:Ce,functional:!0,props:xn(La,Ce),render:function(t,e){var i=e.props,n=e.data,o=e.children;return t("li",F(n,{staticClass:"breadcrumb-item",class:{active:i.active}}),[t(Ra,{props:i},o)])}}),Ma=xn({items:{type:Array,default:null}},Te),Ha=de({components:{BBreadcrumb:i.default.extend({name:Te,functional:!0,props:Ma,render:function(t,e){var i=e.props,n=e.data,o=e.children;if(st(i.items)){var r=!1;o=i.items.map((function(e,n){lt(e)||(e={text:Hn(e)});var o=e.active;return o&&(r=!0),o||r||(o=n+1===i.items.length),t(Na,{props:u(u({},e),{},{active:o})})}))}return t("ol",F(n,{staticClass:"breadcrumb"}),o)}}),BBreadcrumbItem:Na,BBreadcrumbLink:Ra}}),za=de({components:{BButton:ta,BBtn:ta,BButtonClose:Jo,BBtnClose:Jo}}),ja=xn(u({vertical:{type:Boolean,default:!1},size:{type:String},tag:{type:String,default:"div"},ariaRole:{type:String,default:"group"}},wt(Wr,["size"])),$e),Ga=i.default.extend({name:$e,functional:!0,props:ja,render:function(t,e){var i=e.props,n=e.data,o=e.children;return t(i.tag,F(n,{class:s({"btn-group":!i.vertical,"btn-group-vertical":i.vertical},"btn-group-".concat(i.size),i.size),attrs:{role:i.ariaRole}}),o)}}),Wa=de({components:{BButtonGroup:Ga,BBtnGroup:Ga}}),Ya=[".btn:not(.disabled):not([disabled]):not(.dropdown-item)",".form-control:not(.disabled):not([disabled])","select:not(.disabled):not([disabled])",'input[type="checkbox"]:not(.disabled)','input[type="radio"]:not(.disabled)'].join(","),Ua=i.default.extend({name:_e,mixins:[Wo],props:xn({justify:{type:Boolean,default:!1},keyNav:{type:Boolean,default:!1}},_e),mounted:function(){this.keyNav&&this.getItems()},methods:{getItems:function(){var t=ro(Ya,this.$el);return t.forEach((function(t){t.tabIndex=-1})),t.filter((function(t){return io(t)}))},focusFirst:function(){var t=this.getItems();_o(t[0])},focusPrev:function(t){var e=this.getItems(),i=e.indexOf(t.target);i>-1&&(e=e.slice(0,i).reverse(),_o(e[0]))},focusNext:function(t){var e=this.getItems(),i=e.indexOf(t.target);i>-1&&(e=e.slice(i+1),_o(e[0]))},focusLast:function(){var t=this.getItems().reverse();_o(t[0])},onFocusin:function(t){var e=this.$el;t.target!==e||uo(e,t.relatedTarget)||(Xo(t),this.focusFirst(t))},onKeydown:function(t){var e=t.keyCode,i=t.shiftKey;e===Or||e===Dr?(Xo(t),i?this.focusFirst(t):this.focusPrev(t)):e!==Br&&e!==Fr||(Xo(t),i?this.focusLast(t):this.focusNext(t))}},render:function(t){return t("div",{staticClass:"btn-toolbar",class:{"justify-content-between":this.justify},attrs:{role:"toolbar",tabindex:this.keyNav?"0":null},on:this.keyNav?{focusin:this.onFocusin,keydown:this.onKeydown}:{}},[this.normalizeSlot()])}}),qa=de({components:{BButtonToolbar:Ua,BBtnToolbar:Ua}}),Ka="gregory",Xa="long",Za="short",Ja="2-digit",Qa="numeric",ts=function(){for(var t=arguments.length,e=new Array(t),i=0;i<t;i++)e[i]=arguments[i];return p(Date,e)},es=function(t){if(ot(t)&&jt.test(t.trim())){var e=S(t.split(Gt).map((function(t){return Pn(t,1)})),3),i=e[0],n=e[1],o=e[2];return ts(i,n-1,o)}return ct(t)?ts(t.getFullYear(),t.getMonth(),t.getDate()):null},is=function(t){if(!(t=es(t)))return null;var e=t.getFullYear(),i="0".concat(t.getMonth()+1).slice(-2),n="0".concat(t.getDate()).slice(-2);return"".concat(e,"-").concat(i,"-").concat(n)},ns=function(t,e){return new Intl.DateTimeFormat(t,e).format},os=function(t,e){return is(t)===is(e)},rs=function(t){return(t=ts(t)).setDate(1),t},as=function(t){return(t=ts(t)).setMonth(t.getMonth()+1),t.setDate(0),t},ss=function(t,e){var i=(t=ts(t)).getMonth();return t.setFullYear(t.getFullYear()+e),t.getMonth()!==i&&t.setDate(0),t},ls=function(t){var e=(t=ts(t)).getMonth();return t.setMonth(e-1),t.getMonth()===e&&t.setDate(0),t},us=function(t){var e=(t=ts(t)).getMonth();return t.setMonth(e+1),t.getMonth()===(e+2)%12&&t.setDate(0),t},cs=function(t){return ss(t,-1)},ds=function(t){return ss(t,1)},hs=function(t){return ss(t,-10)},fs=function(t){return ss(t,10)},ps=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;return t=es(t),e=es(e)||t,i=es(i)||t,t?t<e?e:t>i?i:t:null},ms=["ar","az","ckb","fa","he","ks","lrc","mzn","ps","sd","te","ug","ur","yi"].map((function(t){return t.toLowerCase()})),gs=function(t){var e=Hn(t).toLowerCase().replace(te,"").split("-"),i=e.slice(0,2).join("-"),n=e[0];return _n(ms,i)||_n(ms,n)},vs={props:{id:{type:String}},data:function(){return{localId_:null}},computed:{safeId:function(){var t=this.id||this.localId_;return function(e){return t?(e=String(e||"").replace(/\s+/g,"_"))?t+"_"+e:t:null}}},mounted:function(){var t=this;this.$nextTick((function(){t.localId_="__BVID__".concat(t._uid)}))}},bs=xn({value:{type:[String,Date]},valueAsDate:{type:Boolean,default:!1},initialDate:{type:[String,Date]},disabled:{type:Boolean,default:!1},readonly:{type:Boolean,default:!1},min:{type:[String,Date]},max:{type:[String,Date]},dateDisabledFn:{type:Function},startWeekday:{type:[Number,String],default:0},locale:{type:[String,Array]},direction:{type:String},selectedVariant:{type:String,default:"primary"},todayVariant:{type:String},navButtonVariant:{type:String,default:"secondary"},noHighlightToday:{type:Boolean,default:!1},dateInfoFn:{type:Function},width:{type:String,default:"270px"},block:{type:Boolean,default:!1},hideHeader:{type:Boolean,default:!1},showDecadeNav:{type:Boolean,default:!1},hidden:{type:Boolean,default:!1},ariaControls:{type:String},noKeyNav:{type:Boolean,default:!1},roleDescription:{type:String},labelPrevDecade:{type:String,default:"Previous decade"},labelPrevYear:{type:String,default:"Previous year"},labelPrevMonth:{type:String,default:"Previous month"},labelCurrentMonth:{type:String,default:"Current month"},labelNextMonth:{type:String,default:"Next month"},labelNextYear:{type:String,default:"Next year"},labelNextDecade:{type:String,default:"Next decade"},labelToday:{type:String,default:"Today"},labelSelected:{type:String,default:"Selected date"},labelNoDateSelected:{type:String,default:"No date selected"},labelCalendar:{type:String,default:"Calendar"},labelNav:{type:String,default:"Calendar navigation"},labelHelp:{type:String,default:"Use cursor keys to navigate calendar dates"},dateFormatOptions:{type:Object,default:function(){return{year:Qa,month:Xa,day:Qa,weekday:Xa}}},weekdayHeaderFormat:{type:String,default:Za,validator:function(t){return _n([Xa,Za,"narrow"],t)}}},De),ys=i.default.extend({name:De,mixins:[Rr,vs,Wo],model:{prop:"value",event:"input"},props:bs,data:function(){var t=is(this.value)||"";return{selectedYMD:t,activeYMD:t||is(ps(this.initialDate||this.getToday()),this.min,this.max),gridHasFocus:!1,isLive:!1}},computed:{valueId:function(){return this.safeId()},widgetId:function(){return this.safeId("_calendar-wrapper_")},navId:function(){return this.safeId("_calendar-nav_")},gridId:function(){return this.safeId("_calendar-grid_")},gridCaptionId:function(){return this.safeId("_calendar-grid-caption_")},gridHelpId:function(){return this.safeId("_calendar-grid-help_")},activeId:function(){return this.activeYMD?this.safeId("_cell-".concat(this.activeYMD,"_")):null},selectedDate:function(){return es(this.selectedYMD)},activeDate:function(){return es(this.activeYMD)},computedMin:function(){return es(this.min)},computedMax:function(){return es(this.max)},computedWeekStarts:function(){return nr(Pn(this.startWeekday,0),0)%7},computedLocale:function(){return function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Ka;return t=Dn(t).filter(ee),new Intl.DateTimeFormat(t,{calendar:e}).resolvedOptions().locale}(Dn(this.locale).filter(ee),Ka)},computedDateDisabledFn:function(){var t=this.dateDisabledFn,e=null;try{e=t()}catch(t){}return Q(e)?function(){return!1}:t},computedDateInfoFn:function(){var t=this.dateInfoFn,e=null;try{e=t()}catch(t){}return Q(e)?function(){return{}}:t},calendarLocale:function(){var t=new Intl.DateTimeFormat(this.computedLocale,{calendar:Ka}),e=t.resolvedOptions().calendar,i=t.resolvedOptions().locale;return e!==Ka&&(i=i.replace(/-u-.+$/i,"").concat("-u-ca-gregory")),i},calendarYear:function(){return this.activeDate.getFullYear()},calendarMonth:function(){return this.activeDate.getMonth()},calendarFirstDay:function(){return ts(this.calendarYear,this.calendarMonth,1,12)},calendarDaysInMonth:function(){var t=ts(this.calendarFirstDay);return t.setMonth(t.getMonth()+1,0),t.getDate()},computedVariant:function(){return"btn-".concat(this.selectedVariant||"primary")},computedTodayVariant:function(){return"btn-outline-".concat(this.todayVariant||this.selectedVariant||"primary")},computedNavButtonVariant:function(){return"btn-outline-".concat(this.navButtonVariant||"primary")},isRTL:function(){var t=Hn(this.direction).toLowerCase();return"rtl"===t||"ltr"!==t&&gs(this.computedLocale)},context:function(){var t=this.selectedYMD,e=this.activeYMD,i=es(t),n=es(e);return{selectedYMD:t,selectedDate:i,selectedFormatted:i?this.formatDateString(i):this.labelNoDateSelected,activeYMD:e,activeDate:n,activeFormatted:n?this.formatDateString(n):"",disabled:this.dateDisabled(n),locale:this.computedLocale,calendarLocale:this.calendarLocale,rtl:this.isRTL}},dateOutOfRange:function(){var t=this.computedMin,e=this.computedMax;return function(i){return i=es(i),t&&i<t||e&&i>e}},dateDisabled:function(){var t=this,e=this.dateOutOfRange;return function(i){i=es(i);var n=is(i);return!(!e(i)&&!t.computedDateDisabledFn(n,i))}},formatDateString:function(){return ns(this.calendarLocale,u(u({year:Qa,month:Ja,day:Ja},this.dateFormatOptions),{},{hour:void 0,minute:void 0,second:void 0,calendar:Ka}))},formatYearMonth:function(){return ns(this.calendarLocale,{year:Qa,month:Xa,calendar:Ka})},formatWeekdayName:function(){return ns(this.calendarLocale,{weekday:Xa,calendar:Ka})},formatWeekdayNameShort:function(){return ns(this.calendarLocale,{weekday:this.weekdayHeaderFormat||Za,calendar:Ka})},formatDay:function(){var t=new Intl.NumberFormat([this.computedLocale],{style:"decimal",minimumIntegerDigits:1,minimumFractionDigits:0,maximumFractionDigits:0,notation:"standard"});return function(e){return t.format(e.getDate())}},prevDecadeDisabled:function(){var t=this.computedMin;return this.disabled||t&&as(hs(this.activeDate))<t},prevYearDisabled:function(){var t=this.computedMin;return this.disabled||t&&as(cs(this.activeDate))<t},prevMonthDisabled:function(){var t=this.computedMin;return this.disabled||t&&as(ls(this.activeDate))<t},thisMonthDisabled:function(){return this.disabled},nextMonthDisabled:function(){var t=this.computedMax;return this.disabled||t&&rs(us(this.activeDate))>t},nextYearDisabled:function(){var t=this.computedMax;return this.disabled||t&&rs(ds(this.activeDate))>t},nextDecadeDisabled:function(){var t=this.computedMax;return this.disabled||t&&rs(fs(this.activeDate))>t},calendar:function(){for(var t=[],e=this.calendarFirstDay,i=e.getFullYear(),n=e.getMonth(),o=this.calendarDaysInMonth,r=e.getDay(),a=0-((this.computedWeekStarts>r?7:0)-this.computedWeekStarts)-r,s=0;s<6&&a<o;s++){t[s]=[];for(var l=0;l<7;l++){a++;var c=ts(i,n,a),d=c.getMonth(),h=is(c),f=this.dateDisabled(c),p=this.computedDateInfoFn(h,es(h));p=ot(p)||st(p)?{class:p}:ut(p)?u({class:""},p):{class:""},t[s].push({ymd:h,day:this.formatDay(c),label:this.formatDateString(c),isThisMonth:d===n,isDisabled:f,info:p})}}return t},calendarHeadings:function(){var t=this;return this.calendar[0].map((function(e){return{text:t.formatWeekdayNameShort(es(e.ymd)),label:t.formatWeekdayName(es(e.ymd))}}))}},watch:{value:function(t,e){var i=is(t)||"",n=is(e)||"";os(i,n)||(this.activeYMD=i||this.activeYMD,this.selectedYMD=i)},selectedYMD:function(t,e){t!==e&&this.$emit("input",this.valueAsDate?es(t)||null:t||"")},context:function(t,e){Ar(t,e)||this.$emit("context",t)},hidden:function(t){this.activeYMD=this.selectedYMD||is(this.value||this.constrainDate(this.initialDate||this.getToday())),this.setLive(!t)}},created:function(){var t=this;this.$nextTick((function(){t.$emit("context",t.context)}))},mounted:function(){this.setLive(!0)},activated:function(){this.setLive(!0)},deactivated:function(){this.setLive(!1)},beforeDestroy:function(){this.setLive(!1)},methods:{focus:function(){this.disabled||_o(this.$refs.grid)},blur:function(){this.disabled||Do(this.$refs.grid)},setLive:function(t){var e=this;t?this.$nextTick((function(){Xn((function(){e.isLive=!0}))})):this.isLive=!1},getToday:function(){return es(ts())},constrainDate:function(t){return ps(t,this.computedMin,this.computedMax)},emitSelected:function(t){var e=this;this.$nextTick((function(){e.$emit("selected",is(t)||"",es(t)||null)}))},setGridFocusFlag:function(t){this.gridHasFocus=!this.disabled&&"focus"===t.type},onKeydownWrapper:function(t){if(!this.noKeyNav){var e=t.altKey,i=t.ctrlKey,n=t.keyCode;if(_n([33,34,xr,_r,Dr,Or,Fr,Br],n)){Xo(t);var o=ts(this.activeDate),r=ts(this.activeDate),a=o.getDate(),s=this.constrainDate(this.getToday()),l=this.isRTL;33===n?(o=(e?i?hs:cs:ls)(o),(r=ts(o)).setDate(1)):34===n?(o=(e?i?fs:ds:us)(o),(r=ts(o)).setMonth(r.getMonth()+1),r.setDate(0)):n===Dr?(o.setDate(a+(l?1:-1)),r=o=this.constrainDate(o)):n===Fr?(o.setDate(a+(l?-1:1)),r=o=this.constrainDate(o)):n===Or?(o.setDate(a-7),r=o=this.constrainDate(o)):n===Br?(o.setDate(a+7),r=o=this.constrainDate(o)):n===_r?r=o=s:n===xr&&(r=o=es(this.selectedDate)||s),this.dateOutOfRange(r)||os(o,this.activeDate)||(this.activeYMD=is(o)),this.focus()}}},onKeydownGrid:function(t){var e=t.keyCode,i=this.activeDate;e!==$r&&e!==Ir||(Xo(t),this.disabled||this.readonly||this.dateDisabled(i)||(this.selectedYMD=is(i),this.emitSelected(i)),this.focus())},onClickDay:function(t){var e=this.selectedDate,i=this.activeDate,n=es(t.ymd);this.disabled||t.isDisabled||this.dateDisabled(n)||(this.readonly||(this.selectedYMD=is(os(n,e)?e:n),this.emitSelected(n)),this.activeYMD=is(os(n,i)?i:ts(n)),this.focus())},gotoPrevDecade:function(){this.activeYMD=is(this.constrainDate(hs(this.activeDate)))},gotoPrevYear:function(){this.activeYMD=is(this.constrainDate(cs(this.activeDate)))},gotoPrevMonth:function(){this.activeYMD=is(this.constrainDate(ls(this.activeDate)))},gotoCurrentMonth:function(){this.activeYMD=is(this.constrainDate(this.getToday()))},gotoNextMonth:function(){this.activeYMD=is(this.constrainDate(us(this.activeDate)))},gotoNextYear:function(){this.activeYMD=is(this.constrainDate(ds(this.activeDate)))},gotoNextDecade:function(){this.activeYMD=is(this.constrainDate(fs(this.activeDate)))},onHeaderClick:function(){this.disabled||(this.activeYMD=this.selectedYMD||is(this.getToday()),this.focus())}},render:function(t){var e=this;if(this.hidden)return t();var i=this.valueId,n=this.widgetId,o=this.navId,r=this.gridId,a=this.gridCaptionId,l=this.gridHelpId,c=this.activeId,d=this.disabled,h=this.noKeyNav,f=this.isLive,p=this.isRTL,m=this.activeYMD,g=this.selectedYMD,v=this.safeId,b=!this.showDecadeNav,y=is(this.getToday()),S=!this.noHighlightToday,w=t("output",{staticClass:"form-control form-control-sm text-center",class:{"text-muted":d,readonly:this.readonly||d},attrs:{id:i,for:r,role:"status",tabindex:d?null:"-1","data-selected":Hn(g),"aria-live":f?"polite":"off","aria-atomic":f?"true":null},on:{click:this.onHeaderClick,focus:this.onHeaderClick}},this.selectedDate?[t("bdi",{staticClass:"sr-only"}," (".concat(Hn(this.labelSelected),") ")),t("bdi",this.formatDateString(this.selectedDate))]:this.labelNoDateSelected||" ");w=t("header",{staticClass:"b-calendar-header",class:{"sr-only":this.hideHeader},attrs:{title:this.selectedDate&&this.labelSelectedDate||null}},[w]);var T={isRTL:p},C={shiftV:.5},k=u(u({},C),{},{flipH:p}),B=u(u({},C),{},{flipH:!p}),x=this.normalizeSlot("nav-prev-decade",T)||t(ua,{props:k}),$=this.normalizeSlot("nav-prev-year",T)||t(ca,{props:k}),_=this.normalizeSlot("nav-prev-month",T)||t(ha,{props:k}),D=this.normalizeSlot("nav-this-month",T)||t(pa,{props:C}),F=this.normalizeSlot("nav-next-month",T)||t(ha,{props:B}),I=this.normalizeSlot("nav-next-year",T)||t(ca,{props:B}),O=this.normalizeSlot("nav-next-decade",T)||t(ua,{props:B}),P=function(i,n,o,r,a){return t("button",{staticClass:"btn btn-sm border-0 flex-fill",class:[e.computedNavButtonVariant,{disabled:r}],attrs:{title:n||null,type:"button",tabindex:h?"-1":null,"aria-label":n||null,"aria-disabled":r?"true":null,"aria-keyshortcuts":a||null},on:r?{}:{click:o}},[t("div",{attrs:{"aria-hidden":"true"}},[i])])},A=t("div",{staticClass:"b-calendar-nav d-flex",attrs:{id:o,role:"group",tabindex:h?"-1":null,"aria-hidden":d?"true":null,"aria-label":this.labelNav||null,"aria-controls":r}},[b?t():P(x,this.labelPrevDecade,this.gotoPrevDecade,this.prevDecadeDisabled,"Ctrl+Alt+PageDown"),P($,this.labelPrevYear,this.gotoPrevYear,this.prevYearDisabled,"Alt+PageDown"),P(_,this.labelPrevMonth,this.gotoPrevMonth,this.prevMonthDisabled,"PageDown"),P(D,this.labelCurrentMonth,this.gotoCurrentMonth,this.thisMonthDisabled,"Home"),P(F,this.labelNextMonth,this.gotoNextMonth,this.nextMonthDisabled,"PageUp"),P(I,this.labelNextYear,this.gotoNextYear,this.nextYearDisabled,"Alt+PageUp"),b?t():P(O,this.labelNextDecade,this.gotoNextDecade,this.nextDecadeDisabled,"Ctrl+Alt+PageUp")]),V=t("header",{key:"grid-caption",staticClass:"b-calendar-grid-caption text-center font-weight-bold",class:{"text-muted":d},attrs:{id:a,"aria-live":f?"polite":null,"aria-atomic":f?"true":null}},this.formatYearMonth(this.calendarFirstDay)),E=t("div",{staticClass:"b-calendar-grid-weekdays row no-gutters border-bottom",attrs:{"aria-hidden":"true"}},this.calendarHeadings.map((function(e,i){return t("small",{key:i,staticClass:"col text-truncate",class:{"text-muted":d},attrs:{title:e.label===e.text?null:e.label,"aria-label":e.label}},e.text)}))),L=this.calendar.map((function(i){var n=i.map((function(i,n){var o,r=i.ymd===g,a=i.ymd===m,l=i.ymd===y,u=v("_cell-".concat(i.ymd,"_")),c=t("span",{staticClass:"btn border-0 rounded-circle text-nowrap",class:(o={focus:a&&e.gridHasFocus,disabled:i.isDisabled||d,active:r},s(o,e.computedVariant,r),s(o,e.computedTodayVariant,l&&S&&!r&&i.isThisMonth),s(o,"btn-outline-light",!(l&&S||r||a)),s(o,"btn-light",!(l&&S)&&!r&&a),s(o,"text-muted",!i.isThisMonth&&!r),s(o,"text-dark",!(l&&S)&&!r&&!a&&i.isThisMonth),s(o,"font-weight-bold",(r||i.isThisMonth)&&!i.isDisabled),o),on:{click:function(){return e.onClickDay(i)}}},i.day);return t("div",{key:n,staticClass:"col p-0",class:i.isDisabled?"bg-light":i.info.class||"",attrs:{id:u,role:"button","data-date":i.ymd,"aria-hidden":i.isThisMonth?null:"true","aria-disabled":i.isDisabled||d?"true":null,"aria-label":[i.label,r?"(".concat(e.labelSelected,")"):null,l?"(".concat(e.labelToday,")"):null].filter(ee).join(" "),"aria-selected":r?"true":null,"aria-current":r?"date":null}},[c])}));return t("div",{key:i[0].ymd,staticClass:"row no-gutters"},n)}));L=t("div",{staticClass:"b-calendar-grid-body",style:d?{pointerEvents:"none"}:{}},L);var R=t("footer",{staticClass:"b-calendar-grid-help border-top small text-muted text-center bg-light",attrs:{id:l}},[t("div",{staticClass:"small"},this.labelHelp)]),N=t("div",{ref:"grid",staticClass:"b-calendar-grid form-control h-auto text-center",attrs:{id:r,role:"application",tabindex:h?"-1":d?null:"0","data-month":m.slice(0,-3),"aria-roledescription":this.labelCalendar||null,"aria-labelledby":a,"aria-describedby":l,"aria-disabled":d?"true":null,"aria-activedescendant":c},on:{keydown:this.onKeydownGrid,focus:this.setGridFocusFlag,blur:this.setGridFocusFlag}},[V,E,L,R]),M=this.normalizeSlot();M=M?t("footer",{staticClass:"b-calendar-footer"},M):t();var H=t("div",{staticClass:"b-calendar-inner",style:this.block?{}:{width:this.width},attrs:{id:n,dir:p?"rtl":"ltr",lang:this.computedLocale||null,role:"group","aria-disabled":d?"true":null,"aria-controls":this.ariaControls||null,"aria-roledescription":this.roleDescription||null,"aria-describedby":[this.bvAttrs["aria-describedby"],i,l].filter(ee).join(" ")},on:{keydown:this.onKeydownWrapper}},[w,A,N,M]);return t("div",{staticClass:"b-calendar",class:{"d-block":this.block}},[H])}}),Ss=de({components:{BCalendar:ys}}),ws=xn({tag:{type:String,default:"div"},bgVariant:{type:String},borderVariant:{type:String},textVariant:{type:String}},Fe),Ts=xn({title:{type:String},titleTag:{type:String,default:"h4"}},Ne),Cs=i.default.extend({name:Ne,functional:!0,props:Ts,render:function(t,e){var i=e.props,n=e.data,o=e.children;return t(i.titleTag,F(n,{staticClass:"card-title"}),o||Hn(i.title))}}),ks=xn({subTitle:{type:String},subTitleTag:{type:String,default:"h6"},subTitleTextVariant:{type:String,default:"muted"}},Le),Bs=i.default.extend({name:Le,functional:!0,props:ks,render:function(t,e){var i=e.props,n=e.data,o=e.children;return t(i.subTitleTag,F(n,{staticClass:"card-subtitle",class:[i.subTitleTextVariant?"text-".concat(i.subTitleTextVariant):null]}),o||Hn(i.subTitle))}}),xs=xn(u(u(u(u({},mr(ws,hr.bind(null,"body"))),{},{bodyClass:{type:[String,Object,Array]}},Ts),ks),{},{overlay:{type:Boolean,default:!1}}),Ie),$s=i.default.extend({name:Ie,functional:!0,props:xs,render:function(t,e){var i,n=e.props,o=e.data,r=e.children,a=t(),l=t(),u=r||[t()];return n.title&&(a=t(Cs,{props:gr(Ts,n)})),n.subTitle&&(l=t(Bs,{props:gr(ks,n),class:["mb-2"]})),t(n.bodyTag,F(o,{staticClass:"card-body",class:[(i={"card-img-overlay":n.overlay},s(i,"bg-".concat(n.bodyBgVariant),n.bodyBgVariant),s(i,"border-".concat(n.bodyBorderVariant),n.bodyBorderVariant),s(i,"text-".concat(n.bodyTextVariant),n.bodyTextVariant),i),n.bodyClass||{}]}),[a,l].concat(w(u)))}}),_s=xn(u(u({},mr(ws,hr.bind(null,"header"))),{},{header:{type:String},headerHtml:{type:String},headerClass:{type:[String,Object,Array]}}),Ae),Ds=i.default.extend({name:Ae,functional:!0,props:_s,render:function(t,e){var i,n=e.props,o=e.data,r=e.children,a=n.headerBgVariant,l=n.headerBorderVariant,u=n.headerTextVariant;return t(n.headerTag,F(o,{staticClass:"card-header",class:[n.headerClass,(i={},s(i,"bg-".concat(a),a),s(i,"border-".concat(l),l),s(i,"text-".concat(u),u),i)],domProps:r?{}:Ea(n.headerHtml,n.header)}),r)}}),Fs=xn(u(u({},mr(ws,hr.bind(null,"footer"))),{},{footer:{type:String},footerHtml:{type:String},footerClass:{type:[String,Object,Array]}}),Oe),Is=i.default.extend({name:Oe,functional:!0,props:Fs,render:function(t,e){var i,n=e.props,o=e.data,r=e.children,a=n.footerBgVariant,l=n.footerBorderVariant,u=n.footerTextVariant;return t(n.footerTag,F(o,{staticClass:"card-footer",class:[n.footerClass,(i={},s(i,"bg-".concat(a),a),s(i,"border-".concat(l),l),s(i,"text-".concat(u),u),i)],domProps:r?{}:Ea(n.footerHtml,n.footer)}),r)}}),Os=xn({src:{type:String,required:!0},alt:{type:String,default:null},top:{type:Boolean,default:!1},bottom:{type:Boolean,default:!1},start:{type:Boolean,default:!1},left:{type:Boolean,default:!1},end:{type:Boolean,default:!1},right:{type:Boolean,default:!1},height:{type:[Number,String]},width:{type:[Number,String]}},Ve),Ps=i.default.extend({name:Ve,functional:!0,props:Os,render:function(t,e){var i=e.props,n=e.data,o="card-img";return i.top?o+="-top":i.right||i.end?o+="-right":i.bottom?o+="-bottom":(i.left||i.start)&&(o+="-left"),t("img",F(n,{class:[o],attrs:{src:i.src||null,alt:i.alt,height:i.height||null,width:i.width||null}}))}}),As=mr(Os,hr.bind(null,"img"));As.imgSrc.required=!1;var Vs=xn(u(u(u(u(u(u({},xs),_s),Fs),As),ws),{},{align:{type:String},noBody:{type:Boolean,default:!1}}),Fe),Es=i.default.extend({name:Fe,functional:!0,props:Vs,render:function(t,e){var i,n=e.props,o=e.data,r=e.slots,a=e.scopedSlots,l=n.imgSrc,u=n.imgLeft,c=n.imgRight,d=n.imgStart,h=n.imgEnd,f=n.imgBottom,p=n.header,m=n.headerHtml,g=n.footer,v=n.footerHtml,b=n.align,y=n.textVariant,S=n.bgVariant,w=n.borderVariant,T=a||{},C=r(),k={},B=t(),x=t();if(l){var $=t(Ps,{props:gr(As,n,fr.bind(null,"img"))});f?x=$:B=$}var _=t(),D=jo(Ro,T,C);(D||p||m)&&(_=t(Ds,{props:gr(_s,n),domProps:D?{}:Ea(m,p)},Go(Ro,k,T,C)));var I=Go(Vo,k,T,C);n.noBody||(I=t($s,{props:gr(xs,n)},I),n.overlay&&l&&(I=t("div",{staticClass:"position-relative"},[B,I,x]),B=t(),x=t()));var O=t();return(jo(Lo,T,C)||g||v)&&(O=t(Is,{props:gr(Fs,n),domProps:D?{}:Ea(v,g)},Go(Lo,k,T,C))),t(n.tag,F(o,{staticClass:"card",class:(i={"flex-row":u||d,"flex-row-reverse":(c||h)&&!(u||d)},s(i,"text-".concat(b),b),s(i,"bg-".concat(S),S),s(i,"border-".concat(w),w),s(i,"text-".concat(y),y),i)}),[B,_,I,O,x])}}),Ls="__bv__visibility_observer",Rs=function(){function t(e,i,n){o(this,t),this.el=e,this.callback=i.callback,this.margin=i.margin||0,this.once=i.once||!1,this.observer=null,this.visible=void 0,this.doneOnce=!1,this.createObserver(n)}return a(t,[{key:"createObserver",value:function(t){var e=this;if(this.observer&&this.stop(),!this.doneOnce&&it(this.callback)){try{this.observer=new IntersectionObserver(this.handler.bind(this),{root:null,rootMargin:this.margin,threshold:0})}catch(t){return this.doneOnce=!0,this.observer=void 0,void this.callback(null)}t.context.$nextTick((function(){Xn((function(){e.observer&&e.observer.observe(e.el)}))}))}}},{key:"handler",value:function(t){var e=t?t[0]:{},i=Boolean(e.isIntersecting||e.intersectionRatio>0);i!==this.visible&&(this.visible=i,this.callback(i),this.once&&this.visible&&(this.doneOnce=!0,this.stop()))}},{key:"stop",value:function(){this.observer&&this.observer.disconnect(),this.observer=null}}]),t}(),Ns=function(t){var e=t[Ls];e&&e.stop&&e.stop(),delete t[Ls]},Ms=function(t,e,i){var n=e.value,o=e.modifiers,r={margin:"0px",once:!1,callback:n};bt(o).forEach((function(t){xt.test(t)?r.margin="".concat(t,"px"):"once"===t.toLowerCase()&&(r.once=!0)})),Ns(t),t[Ls]=new Rs(t,r,i),t[Ls]._prevModifiers=St(o)},Hs={bind:Ms,componentUpdated:function(t,e,i){var n=e.value,o=e.oldValue,r=e.modifiers;r=St(r),!t||n===o&&t[Ls]&&Ar(r,t[Ls]._prevModifiers)||Ms(t,{value:n,modifiers:r},i)},unbind:function(t){Ns(t)}},zs='<svg width="%{w}" height="%{h}" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 %{w} %{h}" preserveAspectRatio="none"><rect width="100%" height="100%" style="fill:%{f};"></rect></svg>',js=xn({src:{type:String},srcset:{type:[String,Array]},sizes:{type:[String,Array]},alt:{type:String,default:null},width:{type:[Number,String]},height:{type:[Number,String]},block:{type:Boolean,default:!1},fluid:{type:Boolean,default:!1},fluidGrow:{type:Boolean,default:!1},rounded:{type:[Boolean,String],default:!1},thumbnail:{type:Boolean,default:!1},left:{type:Boolean,default:!1},right:{type:Boolean,default:!1},center:{type:Boolean,default:!1},blank:{type:Boolean,default:!1},blankColor:{type:String,default:"transparent"}},ki),Gs=i.default.extend({name:ki,functional:!0,props:js,render:function(t,e){var i,n=e.props,o=e.data,r=n.src,a=Pn(n.width)||null,l=Pn(n.height)||null,u=null,c=n.block,d=Dn(n.srcset).filter(ee).join(","),h=Dn(n.sizes).filter(ee).join(",");return n.blank&&(!l&&a?l=a:!a&&l&&(a=l),a||l||(a=1,l=1),r=function(t,e,i){var n=encodeURIComponent(zs.replace("%{w}",Hn(t)).replace("%{h}",Hn(e)).replace("%{f}",i));return"data:image/svg+xml;charset=UTF-8,".concat(n)}(a,l,n.blankColor||"transparent"),d=null,h=null),n.left?u="float-left":n.right?u="float-right":n.center&&(u="mx-auto",c=!0),t("img",F(o,{attrs:{src:r,alt:n.alt,width:a?Hn(a):null,height:l?Hn(l):null,srcset:d||null,sizes:h||null},class:(i={"img-thumbnail":n.thumbnail,"img-fluid":n.fluid||n.fluidGrow,"w-100":n.fluidGrow,rounded:""===n.rounded||!0===n.rounded},s(i,"rounded-".concat(n.rounded),ot(n.rounded)&&""!==n.rounded),s(i,u,u),s(i,"d-block",c),i)}))}}),Ws=xn(u(u({},Tt(js,["blank"])),{},{blankSrc:{type:String,default:null},blankColor:{type:String,default:"transparent"},blankWidth:{type:[Number,String]},blankHeight:{type:[Number,String]},show:{type:Boolean,default:!1},offset:{type:[Number,String],default:360}}),Bi),Ys=i.default.extend({name:Bi,directives:{bVisible:Hs},props:Ws,data:function(){return{isShown:this.show}},computed:{computedSrc:function(){return!this.blankSrc||this.isShown?this.src:this.blankSrc},computedBlank:function(){return!(this.isShown||this.blankSrc)},computedWidth:function(){return this.isShown?this.width:this.blankWidth||this.width},computedHeight:function(){return this.isShown?this.height:this.blankHeight||this.height},computedSrcset:function(){var t=Dn(this.srcset).filter(ee).join(",");return!this.blankSrc||this.isShown?t:null},computedSizes:function(){var t=Dn(this.sizes).filter(ee).join(",");return!this.blankSrc||this.isShown?t:null}},watch:{show:function(t,e){if(t!==e){var i=!Y||t;this.isShown=i,i!==t&&this.$nextTick(this.updateShowProp)}},isShown:function(t,e){t!==e&&this.updateShowProp()}},mounted:function(){this.isShown=!Y||this.show},methods:{updateShowProp:function(){this.$emit("update:show",this.isShown)},doShow:function(t){!t&&null!==t||this.isShown||(this.isShown=!0)}},render:function(t){var e,i=[];this.isShown||i.push({name:"b-visible",value:this.doShow,modifiers:(e={},s(e,"".concat(Pn(this.offset,0)),!0),s(e,"once",!0),e)});return t(Gs,{directives:i,props:{src:this.computedSrc,blank:this.computedBlank,width:this.computedWidth,height:this.computedHeight,srcset:this.computedSrcset||null,sizes:this.computedSizes||null,alt:this.alt,blankColor:this.blankColor,fluid:this.fluid,fluidGrow:this.fluidGrow,block:this.block,thumbnail:this.thumbnail,rounded:this.rounded,left:this.left,right:this.right,center:this.center}})}}),Us=Tt(Ws,["left","right","center","block","rounded","thumbnail","fluid","fluidGrow"]),qs=xn(u(u({},Us),{},{top:{type:Boolean,default:!1},bottom:{type:Boolean,default:!1},start:{type:Boolean,default:!1},left:{type:Boolean,default:!1},end:{type:Boolean,default:!1},right:{type:Boolean,default:!1}}),Ee),Ks=i.default.extend({name:Ee,functional:!0,props:qs,render:function(t,e){var i=e.props,n=e.data,o="card-img";i.top?o+="-top":i.right||i.end?o+="-right":i.bottom?o+="-bottom":(i.left||i.start)&&(o+="-left");var r=u(u({},i),{},{left:!1,right:!1,center:!1});return t(Ys,F(n,{class:[o],props:r}))}}),Xs=xn({textTag:{type:String,default:"p"}},Re),Zs=i.default.extend({name:Re,functional:!0,props:Xs,render:function(t,e){var i=e.props,n=e.data,o=e.children;return t(i.textTag,F(n,{staticClass:"card-text"}),o)}}),Js=xn({tag:{type:String,default:"div"},deck:{type:Boolean,default:!1},columns:{type:Boolean,default:!1}},Pe),Qs=de({components:{BCard:Es,BCardHeader:Ds,BCardBody:$s,BCardTitle:Cs,BCardSubTitle:Bs,BCardFooter:Is,BCardImg:Ps,BCardImgLazy:Ks,BCardText:Zs,BCardGroup:i.default.extend({name:Pe,functional:!0,props:Js,render:function(t,e){var i=e.props,n=e.data,o=e.children;return t(i.tag,F(n,{class:i.deck?"card-deck":i.columns?"card-columns":"card-group"}),o)}})}}),tl={passive:!0},el={passive:!0,capture:!1},il=function(){},nl=function(t,e,i){if(t=t?t.$el||t:null,!Jn(t))return null;if(n="observeDom",!N&&(oe("".concat(n,": Requires MutationObserver support.")),1))return null;var n,o=new Zn((function(t){for(var i=!1,n=0;n<t.length&&!i;n++){var o=t[n],r=o.type,a=o.target;("characterData"===r&&a.nodeType===Node.TEXT_NODE||"attributes"===r||"childList"===r&&(o.addedNodes.length>0||o.removedNodes.length>0))&&(i=!0)}i&&e()}));return o.observe(t,u({childList:!0,subtree:!0},i)),o},ol={next:{dirClass:"carousel-item-left",overlayClass:"carousel-item-next"},prev:{dirClass:"carousel-item-right",overlayClass:"carousel-item-prev"}},rl={TOUCH:"touch",PEN:"pen"},al={WebkitTransition:"webkitTransitionEnd",MozTransition:"transitionend",OTransition:"otransitionend oTransitionEnd",transition:"transitionend"},sl=i.default.extend({name:Me,mixins:[vs,Wo],provide:function(){return{bvCarousel:this}},model:{prop:"value",event:"input"},props:xn({labelPrev:{type:String,default:"Previous slide"},labelNext:{type:String,default:"Next slide"},labelGotoSlide:{type:String,default:"Goto slide"},labelIndicators:{type:String,default:"Select a slide to display"},interval:{type:Number,default:5e3},indicators:{type:Boolean,default:!1},controls:{type:Boolean,default:!1},noAnimation:{type:Boolean,default:!1},fade:{type:Boolean,default:!1},noWrap:{type:Boolean,default:!1},noTouch:{type:Boolean,default:!1},noHoverPause:{type:Boolean,default:!1},imgWidth:{type:[Number,String]},imgHeight:{type:[Number,String]},background:{type:String},value:{type:Number,default:0}},Me),data:function(){return{index:this.value||0,isSliding:!1,transitionEndEvent:null,slides:[],direction:null,isPaused:!(Pn(this.interval,0)>0),touchStartX:0,touchDeltaX:0}},computed:{numSlides:function(){return this.slides.length}},watch:{value:function(t,e){t!==e&&this.setSlide(Pn(t,0))},interval:function(t,e){t!==e&&(t?(this.pause(!0),this.start(!1)):this.pause(!1))},isPaused:function(t,e){t!==e&&this.$emit(t?"paused":"unpaused")},index:function(t,e){t===e||this.isSliding||this.doSlide(t,e)}},created:function(){this.$_interval=null,this.$_animationTimeout=null,this.$_touchTimeout=null,this.$_observer=null,this.isPaused=!(Pn(this.interval,0)>0)},mounted:function(){this.transitionEndEvent=function(t){for(var e in al)if(!Q(t.style[e]))return al[e];return null}(this.$el)||null,this.updateSlides(),this.setObserver(!0)},beforeDestroy:function(){this.clearInterval(),this.clearAnimationTimeout(),this.clearTouchTimeout(),this.setObserver(!1)},methods:{clearInterval:function(t){function e(){return t.apply(this,arguments)}return e.toString=function(){return t.toString()},e}((function(){clearInterval(this.$_interval),this.$_interval=null})),clearAnimationTimeout:function(){clearTimeout(this.$_animationTimeout),this.$_animationTimeout=null},clearTouchTimeout:function(){clearTimeout(this.$_touchTimeout),this.$_touchTimeout=null},setObserver:function(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.$_observer&&this.$_observer.disconnect(),this.$_observer=null,t&&(this.$_observer=nl(this.$refs.inner,this.updateSlides.bind(this),{subtree:!1,childList:!0,attributes:!0,attributeFilter:["id"]}))},setSlide:function(t){var e=this,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if(!(M&&document.visibilityState&&document.hidden)){var n=this.noWrap,o=this.numSlides;t=ar(t),0!==o&&(this.isSliding?this.$once("sliding-end",(function(){Xn((function(){return e.setSlide(t,i)}))})):(this.direction=i,this.index=t>=o?n?o-1:0:t<0?n?0:o-1:t,n&&this.index!==t&&this.index!==this.value&&this.$emit("input",this.index)))}},prev:function(){this.setSlide(this.index-1,"prev")},next:function(){this.setSlide(this.index+1,"next")},pause:function(t){t||(this.isPaused=!0),this.clearInterval()},start:function(t){t||(this.isPaused=!1),this.clearInterval(),this.interval&&this.numSlides>1&&(this.$_interval=setInterval(this.next,nr(1e3,this.interval)))},restart:function(){this.$el.contains(Qn())||this.start()},doSlide:function(t,e){var i=this,n=Boolean(this.interval),o=this.calcDirection(this.direction,e,t),r=o.overlayClass,a=o.dirClass,s=this.slides[e],l=this.slides[t];if(s&&l){if(this.isSliding=!0,n&&this.pause(!1),this.$emit("sliding-start",t),this.$emit("input",this.index),this.noAnimation)ho(l,"active"),fo(s,"active"),this.isSliding=!1,this.$nextTick((function(){return i.$emit("sliding-end",t)}));else{ho(l,r),oo(l),ho(s,a),ho(l,a);var u=!1,c=function e(){if(!u){if(u=!0,i.transitionEndEvent)i.transitionEndEvent.split(/\s+/).forEach((function(t){return qo(l,t,e,el)}));i.clearAnimationTimeout(),fo(l,a),fo(l,r),ho(l,"active"),fo(s,"active"),fo(s,a),fo(s,r),mo(s,"aria-current","false"),mo(l,"aria-current","true"),mo(s,"aria-hidden","true"),mo(l,"aria-hidden","false"),i.isSliding=!1,i.direction=null,i.$nextTick((function(){return i.$emit("sliding-end",t)}))}};if(this.transitionEndEvent)this.transitionEndEvent.split(/\s+/).forEach((function(t){return Uo(l,t,c,el)}));this.$_animationTimeout=setTimeout(c,650)}n&&this.start(!1)}},updateSlides:function(){this.pause(!0),this.slides=ro(".carousel-item",this.$refs.inner);var t=this.slides.length,e=nr(0,ir(ar(this.index),t-1));this.slides.forEach((function(i,n){var o=n+1;n===e?(ho(i,"active"),mo(i,"aria-current","true")):(fo(i,"active"),mo(i,"aria-current","false")),mo(i,"aria-posinset",String(o)),mo(i,"aria-setsize",String(t))})),this.setSlide(e),this.start(this.isPaused)},calcDirection:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;return t?ol[t]:i>e?ol.next:ol.prev},handleClick:function(t,e){var i=t.keyCode;"click"!==t.type&&i!==Ir&&i!==$r||(Xo(t),e())},handleSwipe:function(){var t=or(this.touchDeltaX);if(!(t<=40)){var e=t/this.touchDeltaX;this.touchDeltaX=0,e>0?this.prev():e<0&&this.next()}},touchStart:function(t){W&&rl[t.pointerType.toUpperCase()]?this.touchStartX=t.clientX:W||(this.touchStartX=t.touches[0].clientX)},touchMove:function(t){t.touches&&t.touches.length>1?this.touchDeltaX=0:this.touchDeltaX=t.touches[0].clientX-this.touchStartX},touchEnd:function(t){W&&rl[t.pointerType.toUpperCase()]&&(this.touchDeltaX=t.clientX-this.touchStartX),this.handleSwipe(),this.pause(!1),this.clearTouchTimeout(),this.$_touchTimeout=setTimeout(this.start,500+nr(1e3,this.interval))}},render:function(t){var e=this,i=t("div",{ref:"inner",class:["carousel-inner"],attrs:{id:this.safeId("__BV_inner_"),role:"list"}},[this.normalizeSlot()]),n=t();if(this.controls){var o=function(t){e.isSliding?Xo(t,{propagation:!1}):e.handleClick(t,e.prev)},r=function(t){e.isSliding?Xo(t,{propagation:!1}):e.handleClick(t,e.next)};n=[t("a",{class:["carousel-control-prev"],attrs:{href:"#",role:"button","aria-controls":this.safeId("__BV_inner_"),"aria-disabled":this.isSliding?"true":null},on:{click:o,keydown:o}},[t("span",{class:["carousel-control-prev-icon"],attrs:{"aria-hidden":"true"}}),t("span",{class:["sr-only"]},[this.labelPrev])]),t("a",{class:["carousel-control-next"],attrs:{href:"#",role:"button","aria-controls":this.safeId("__BV_inner_"),"aria-disabled":this.isSliding?"true":null},on:{click:r,keydown:r}},[t("span",{class:["carousel-control-next-icon"],attrs:{"aria-hidden":"true"}}),t("span",{class:["sr-only"]},[this.labelNext])])]}var a=t("ol",{class:["carousel-indicators"],directives:[{name:"show",rawName:"v-show",value:this.indicators,expression:"indicators"}],attrs:{id:this.safeId("__BV_indicators_"),"aria-hidden":this.indicators?"false":"true","aria-label":this.labelIndicators,"aria-owns":this.safeId("__BV_inner_")}},this.slides.map((function(i,n){return t("li",{key:"slide_".concat(n),class:{active:n===e.index},attrs:{role:"button",id:e.safeId("__BV_indicator_".concat(n+1,"_")),tabindex:e.indicators?"0":"-1","aria-current":n===e.index?"true":"false","aria-label":"".concat(e.labelGotoSlide," ").concat(n+1),"aria-describedby":e.slides[n].id||null,"aria-controls":e.safeId("__BV_inner_")},on:{click:function(t){e.handleClick(t,(function(){e.setSlide(n)}))},keydown:function(t){e.handleClick(t,(function(){e.setSlide(n)}))}}})}))),s={mouseenter:this.noHoverPause?il:this.pause,mouseleave:this.noHoverPause?il:this.restart,focusin:this.pause,focusout:this.restart,keydown:function(t){if(!/input|textarea/i.test(t.target.tagName)){var i=t.keyCode;i!==Dr&&i!==Fr||(Xo(t),e[i===Dr?"prev":"next"]())}}};return!this.noTouch&&G&&(W?(s["&pointerdown"]=this.touchStart,s["&pointerup"]=this.touchEnd):(s["&touchstart"]=this.touchStart,s["&touchmove"]=this.touchMove,s["&touchend"]=this.touchEnd)),t("div",{staticClass:"carousel",class:{slide:!this.noAnimation,"carousel-fade":!this.noAnimation&&this.fade,"pointer-event":!this.noTouch&&G&&W},style:{background:this.background},attrs:{role:"region",id:this.safeId(),"aria-busy":this.isSliding?"true":"false"},on:s},[i,n,a])}}),ll={imgSrc:{type:String},imgAlt:{type:String},imgWidth:{type:[Number,String]},imgHeight:{type:[Number,String]},imgBlank:{type:Boolean,default:!1},imgBlankColor:{type:String,default:"transparent"}},ul=xn(u(u({},ll),{},{contentVisibleUp:{type:String},contentTag:{type:String,default:"div"},caption:{type:String},captionHtml:{type:String},captionTag:{type:String,default:"h3"},text:{type:String},textHtml:{type:String},textTag:{type:String,default:"p"},background:{type:String}}),He),cl=de({components:{BCarousel:sl,BCarouselSlide:i.default.extend({name:He,mixins:[vs,Wo],inject:{bvCarousel:{default:function(){return{noTouch:!0}}}},props:ul,computed:{contentClasses:function(){return[this.contentVisibleUp?"d-none":"",this.contentVisibleUp?"d-".concat(this.contentVisibleUp,"-block"):""]},computedWidth:function(){return this.imgWidth||this.bvCarousel.imgWidth||null},computedHeight:function(){return this.imgHeight||this.bvCarousel.imgHeight||null}},render:function(t){var e=this.normalizeSlot("img");if(!e&&(this.imgSrc||this.imgBlank)){var i={};!this.bvCarousel.noTouch&&G&&(i.dragstart=function(t){return Xo(t,{propagation:!1})}),e=t(Gs,{props:u(u({},gr(ll,this.$props,fr.bind(null,"img"))),{},{width:this.computedWidth,height:this.computedHeight,fluidGrow:!0,block:!0}),on:i})}var n=[!(!this.caption&&!this.captionHtml)&&t(this.captionTag,{domProps:Ea(this.captionHtml,this.caption)}),!(!this.text&&!this.textHtml)&&t(this.textTag,{domProps:Ea(this.textHtml,this.text)}),this.normalizeSlot()||!1],o=t();return n.some(Boolean)&&(o=t(this.contentTag,{staticClass:"carousel-caption",class:this.contentClasses},n.map((function(e){return e||t()})))),t("div",{staticClass:"carousel-item",style:{background:this.background||this.bvCarousel.background||null},attrs:{id:this.safeId(),role:"listitem"}},[e,o])}})}}),dl={css:!0,enterClass:"",enterActiveClass:"collapsing",enterToClass:"collapse show",leaveClass:"collapse show",leaveActiveClass:"collapsing",leaveToClass:"collapse"},hl={enter:function(t){yo(t,"height",0),Xn((function(){oo(t),yo(t,"height","".concat(t.scrollHeight,"px"))}))},afterEnter:function(t){So(t,"height")},leave:function(t){yo(t,"height","auto"),yo(t,"display","block"),yo(t,"height","".concat(To(t).height,"px")),oo(t),yo(t,"height",0)},afterLeave:function(t){So(t,"height")}},fl=i.default.extend({name:"BVCollapse",functional:!0,props:{appear:{type:Boolean,default:!1}},render:function(t,e){var i=e.props,n=e.data,o=e.children;return t("transition",F(n,{props:dl,on:hl},{props:i}),o)}}),pl={methods:{listenOnRoot:function(t,e){var i=this;this.$root.$on(t,e),this.$on("hook:beforeDestroy",(function(){i.$root.$off(t,e)}))},listenOnRootOnce:function(t,e){var i=this;this.$root.$once(t,e),this.$on("hook:beforeDestroy",(function(){i.$root.$off(t,e)}))},emitOnRoot:function(t){for(var e,i=arguments.length,n=new Array(i>1?i-1:0),o=1;o<i;o++)n[o-1]=arguments[o];(e=this.$root).$emit.apply(e,[t].concat(n))}}},ml="collapsed",gl="not-collapsed",vl="__BV_toggle",bl="".concat(vl,"_HANDLER__"),yl="".concat(vl,"_CLICK__"),Sl="".concat(vl,"_STATE__"),wl="".concat(vl,"_TARGETS__"),Tl="aria-controls",Cl="aria-expanded",kl="role",Bl="tabindex",xl="overflow-anchor",$l="bv::toggle::collapse",_l="bv::collapse::state",Dl="bv::collapse::sync::state",Fl="bv::request::collapse::state",Il=[$r,Ir],Ol=function(t){return!_n(["button","a"],t.tagName.toLowerCase())},Pl=function(t){var e=t[yl];e&&(qo(t,"click",e,tl),qo(t,"keydown",e,tl)),t[yl]=null},Al=function(t,e){t[bl]&&e.context&&e.context.$root.$off([_l,Dl],t[bl]),t[bl]=null},Vl=function(t,e){e?(fo(t,ml),ho(t,gl),mo(t,Cl,"true")):(fo(t,gl),ho(t,ml),mo(t,Cl,"false"))},El=function(t,e){t[e]=null,delete t[e]},Ll=function(t,e,i){if(M&&i.context){Ol(t)&&(bo(t,kl)||mo(t,kl,"button"),bo(t,Bl)||mo(t,Bl,"0")),Vl(t,t[Sl]);var n=function(t,e){var i=t.modifiers,n=t.arg,o=t.value,r=bt(i||{});if(o=ot(o)?o.split(Lt):o,to(e.tagName,"a")){var a=vo(e,"href")||"";Dt.test(a)&&r.push(a.replace(_t,""))}return Dn(n,o).forEach((function(t){return ot(t)&&r.push(t)})),r.filter((function(t,e,i){return t&&i.indexOf(t)===e}))}(e,t);n.length>0?(mo(t,Tl,n.join(" ")),yo(t,xl,"none")):(go(t,Tl),So(t,xl)),Xn((function(){!function(t,e){if(Pl(t),e.context){var i=function(i){"keydown"===i.type&&!_n(Il,i.keyCode)||no(t)||(t[wl]||[]).forEach((function(t){e.context.$root.$emit($l,t)}))};t[yl]=i,Uo(t,"click",i,tl),Ol(t)&&Uo(t,"keydown",i,tl)}}(t,i)})),Ar(n,t[wl])||(t[wl]=n,n.forEach((function(t){i.context.$root.$emit(Fl,t)})))}},Rl={bind:function(t,e,i){t[Sl]=!1,t[wl]=[],function(t,e){if(Al(t,e),e.context){var i=function(e,i){_n(t[wl]||[],e)&&(t[Sl]=i,Vl(t,i))};t[bl]=i,e.context.$root.$on([_l,Dl],i)}}(t,i),Ll(t,e,i)},componentUpdated:Ll,updated:Ll,unbind:function(t,e,i){Pl(t),Al(t,i),El(t,bl),El(t,yl),El(t,Sl),El(t,wl),fo(t,ml),fo(t,gl),go(t,Cl),go(t,Tl),go(t,kl),So(t,xl)}},Nl="bv::collapse::accordion",Ml=i.default.extend({name:ze,mixins:[vs,pl,Wo],model:{prop:"visible",event:"input"},props:xn({isNav:{type:Boolean,default:!1},accordion:{type:String},visible:{type:Boolean,default:!1},tag:{type:String,default:"div"},appear:{type:Boolean,default:!1}},ze),data:function(){return{show:this.visible,transitioning:!1}},computed:{classObject:function(){return{"navbar-collapse":this.isNav,collapse:!this.transitioning,show:this.show&&!this.transitioning}}},watch:{visible:function(t){t!==this.show&&(this.show=t)},show:function(t,e){t!==e&&this.emitState()}},created:function(){this.show=this.visible},mounted:function(){var t=this;this.show=this.visible,this.listenOnRoot($l,this.handleToggleEvt),this.listenOnRoot(Nl,this.handleAccordionEvt),this.isNav&&(this.setWindowEvents(!0),this.handleResize()),this.$nextTick((function(){t.emitState()})),this.listenOnRoot(Fl,(function(e){e===t.safeId()&&t.$nextTick(t.emitSync)}))},updated:function(){this.emitSync()},deactivated:function(){this.isNav&&this.setWindowEvents(!1)},activated:function(){this.isNav&&this.setWindowEvents(!0),this.emitSync()},beforeDestroy:function(){this.show=!1,this.isNav&&M&&this.setWindowEvents(!1)},methods:{setWindowEvents:function(t){Ko(t,window,"resize",this.handleResize,el),Ko(t,window,"orientationchange",this.handleResize,el)},toggle:function(){this.show=!this.show},onEnter:function(){this.transitioning=!0,this.$emit("show")},onAfterEnter:function(){this.transitioning=!1,this.$emit("shown")},onLeave:function(){this.transitioning=!0,this.$emit("hide")},onAfterLeave:function(){this.transitioning=!1,this.$emit("hidden")},emitState:function(){this.$emit("input",this.show),this.emitOnRoot(_l,this.safeId(),this.show),this.accordion&&this.show&&this.emitOnRoot(Nl,this.safeId(),this.accordion)},emitSync:function(){this.emitOnRoot(Dl,this.safeId(),this.show)},checkDisplayBlock:function(){var t=po(this.$el,"show");fo(this.$el,"show");var e="block"===Co(this.$el).display;return t&&ho(this.$el,"show"),e},clickHandler:function(t){var e=t.target;this.isNav&&e&&"block"===Co(this.$el).display&&(so(e,".nav-link,.dropdown-item")||lo(".nav-link,.dropdown-item",e))&&(this.checkDisplayBlock()||(this.show=!1))},handleToggleEvt:function(t){t===this.safeId()&&this.toggle()},handleAccordionEvt:function(t,e){this.accordion&&e===this.accordion&&(t===this.safeId()?this.show||this.toggle():this.show&&this.toggle())},handleResize:function(){this.show="block"===Co(this.$el).display}},render:function(t){var e=this,i={visible:this.show,close:function(){return e.show=!1}},n=t(this.tag,{class:this.classObject,directives:[{name:"show",value:this.show}],attrs:{id:this.safeId()},on:{click:this.clickHandler}},[this.normalizeSlot(Vo,i)]);return t(fl,{props:{appear:this.appear},on:{enter:this.onEnter,afterEnter:this.onAfterEnter,leave:this.onLeave,afterLeave:this.onAfterLeave}},[n])}}),Hl=de({directives:{VBToggle:Rl}}),zl=de({components:{BCollapse:Ml},plugins:{VBTogglePlugin:Hl}}),jl="undefined"!=typeof window&&"undefined"!=typeof document&&"undefined"!=typeof navigator,Gl=function(){for(var t=["Edge","Trident","Firefox"],e=0;e<t.length;e+=1)if(jl&&navigator.userAgent.indexOf(t[e])>=0)return 1;return 0}();var Wl=jl&&window.Promise?function(t){var e=!1;return function(){e||(e=!0,window.Promise.resolve().then((function(){e=!1,t()})))}}:function(t){var e=!1;return function(){e||(e=!0,setTimeout((function(){e=!1,t()}),Gl))}};function Yl(t){return t&&"[object Function]"==={}.toString.call(t)}function Ul(t,e){if(1!==t.nodeType)return[];var i=t.ownerDocument.defaultView.getComputedStyle(t,null);return e?i[e]:i}function ql(t){return"HTML"===t.nodeName?t:t.parentNode||t.host}function Kl(t){if(!t)return document.body;switch(t.nodeName){case"HTML":case"BODY":return t.ownerDocument.body;case"#document":return t.body}var e=Ul(t),i=e.overflow,n=e.overflowX,o=e.overflowY;return/(auto|scroll|overlay)/.test(i+o+n)?t:Kl(ql(t))}function Xl(t){return t&&t.referenceNode?t.referenceNode:t}var Zl=jl&&!(!window.MSInputMethodContext||!document.documentMode),Jl=jl&&/MSIE 10/.test(navigator.userAgent);function Ql(t){return 11===t?Zl:10===t?Jl:Zl||Jl}function tu(t){if(!t)return document.documentElement;for(var e=Ql(10)?document.body:null,i=t.offsetParent||null;i===e&&t.nextElementSibling;)i=(t=t.nextElementSibling).offsetParent;var n=i&&i.nodeName;return n&&"BODY"!==n&&"HTML"!==n?-1!==["TH","TD","TABLE"].indexOf(i.nodeName)&&"static"===Ul(i,"position")?tu(i):i:t?t.ownerDocument.documentElement:document.documentElement}function eu(t){return null!==t.parentNode?eu(t.parentNode):t}function iu(t,e){if(!(t&&t.nodeType&&e&&e.nodeType))return document.documentElement;var i=t.compareDocumentPosition(e)&Node.DOCUMENT_POSITION_FOLLOWING,n=i?t:e,o=i?e:t,r=document.createRange();r.setStart(n,0),r.setEnd(o,0);var a,s,l=r.commonAncestorContainer;if(t!==l&&e!==l||n.contains(o))return"BODY"===(s=(a=l).nodeName)||"HTML"!==s&&tu(a.firstElementChild)!==a?tu(l):l;var u=eu(t);return u.host?iu(u.host,e):iu(t,eu(e).host)}function nu(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"top",i="top"===e?"scrollTop":"scrollLeft",n=t.nodeName;if("BODY"===n||"HTML"===n){var o=t.ownerDocument.documentElement,r=t.ownerDocument.scrollingElement||o;return r[i]}return t[i]}function ou(t,e){var i=arguments.length>2&&void 0!==arguments[2]&&arguments[2],n=nu(e,"top"),o=nu(e,"left"),r=i?-1:1;return t.top+=n*r,t.bottom+=n*r,t.left+=o*r,t.right+=o*r,t}function ru(t,e){var i="x"===e?"Left":"Top",n="Left"===i?"Right":"Bottom";return parseFloat(t["border"+i+"Width"])+parseFloat(t["border"+n+"Width"])}function au(t,e,i,n){return Math.max(e["offset"+t],e["scroll"+t],i["client"+t],i["offset"+t],i["scroll"+t],Ql(10)?parseInt(i["offset"+t])+parseInt(n["margin"+("Height"===t?"Top":"Left")])+parseInt(n["margin"+("Height"===t?"Bottom":"Right")]):0)}function su(t){var e=t.body,i=t.documentElement,n=Ql(10)&&getComputedStyle(i);return{height:au("Height",e,i,n),width:au("Width",e,i,n)}}var lu=function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")},uu=function(){function t(t,e){for(var i=0;i<e.length;i++){var n=e[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(e,i,n){return i&&t(e.prototype,i),n&&t(e,n),e}}(),cu=function(t,e,i){return e in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t},du=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var i=arguments[e];for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(t[n]=i[n])}return t};function hu(t){return du({},t,{right:t.left+t.width,bottom:t.top+t.height})}function fu(t){var e={};try{if(Ql(10)){e=t.getBoundingClientRect();var i=nu(t,"top"),n=nu(t,"left");e.top+=i,e.left+=n,e.bottom+=i,e.right+=n}else e=t.getBoundingClientRect()}catch(t){}var o={left:e.left,top:e.top,width:e.right-e.left,height:e.bottom-e.top},r="HTML"===t.nodeName?su(t.ownerDocument):{},a=r.width||t.clientWidth||o.width,s=r.height||t.clientHeight||o.height,l=t.offsetWidth-a,u=t.offsetHeight-s;if(l||u){var c=Ul(t);l-=ru(c,"x"),u-=ru(c,"y"),o.width-=l,o.height-=u}return hu(o)}function pu(t,e){var i=arguments.length>2&&void 0!==arguments[2]&&arguments[2],n=Ql(10),o="HTML"===e.nodeName,r=fu(t),a=fu(e),s=Kl(t),l=Ul(e),u=parseFloat(l.borderTopWidth),c=parseFloat(l.borderLeftWidth);i&&o&&(a.top=Math.max(a.top,0),a.left=Math.max(a.left,0));var d=hu({top:r.top-a.top-u,left:r.left-a.left-c,width:r.width,height:r.height});if(d.marginTop=0,d.marginLeft=0,!n&&o){var h=parseFloat(l.marginTop),f=parseFloat(l.marginLeft);d.top-=u-h,d.bottom-=u-h,d.left-=c-f,d.right-=c-f,d.marginTop=h,d.marginLeft=f}return(n&&!i?e.contains(s):e===s&&"BODY"!==s.nodeName)&&(d=ou(d,e)),d}function mu(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],i=t.ownerDocument.documentElement,n=pu(t,i),o=Math.max(i.clientWidth,window.innerWidth||0),r=Math.max(i.clientHeight,window.innerHeight||0),a=e?0:nu(i),s=e?0:nu(i,"left"),l={top:a-n.top+n.marginTop,left:s-n.left+n.marginLeft,width:o,height:r};return hu(l)}function gu(t){var e=t.nodeName;if("BODY"===e||"HTML"===e)return!1;if("fixed"===Ul(t,"position"))return!0;var i=ql(t);return!!i&&gu(i)}function vu(t){if(!t||!t.parentElement||Ql())return document.documentElement;for(var e=t.parentElement;e&&"none"===Ul(e,"transform");)e=e.parentElement;return e||document.documentElement}function bu(t,e,i,n){var o=arguments.length>4&&void 0!==arguments[4]&&arguments[4],r={top:0,left:0},a=o?vu(t):iu(t,Xl(e));if("viewport"===n)r=mu(a,o);else{var s=void 0;"scrollParent"===n?"BODY"===(s=Kl(ql(e))).nodeName&&(s=t.ownerDocument.documentElement):s="window"===n?t.ownerDocument.documentElement:n;var l=pu(s,a,o);if("HTML"!==s.nodeName||gu(a))r=l;else{var u=su(t.ownerDocument),c=u.height,d=u.width;r.top+=l.top-l.marginTop,r.bottom=c+l.top,r.left+=l.left-l.marginLeft,r.right=d+l.left}}var h="number"==typeof(i=i||0);return r.left+=h?i:i.left||0,r.top+=h?i:i.top||0,r.right-=h?i:i.right||0,r.bottom-=h?i:i.bottom||0,r}function yu(t){return t.width*t.height}function Su(t,e,i,n,o){var r=arguments.length>5&&void 0!==arguments[5]?arguments[5]:0;if(-1===t.indexOf("auto"))return t;var a=bu(i,n,r,o),s={top:{width:a.width,height:e.top-a.top},right:{width:a.right-e.right,height:a.height},bottom:{width:a.width,height:a.bottom-e.bottom},left:{width:e.left-a.left,height:a.height}},l=Object.keys(s).map((function(t){return du({key:t},s[t],{area:yu(s[t])})})).sort((function(t,e){return e.area-t.area})),u=l.filter((function(t){var e=t.width,n=t.height;return e>=i.clientWidth&&n>=i.clientHeight})),c=u.length>0?u[0].key:l[0].key,d=t.split("-")[1];return c+(d?"-"+d:"")}function wu(t,e,i){var n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,o=n?vu(e):iu(e,Xl(i));return pu(i,o,n)}function Tu(t){var e=t.ownerDocument.defaultView.getComputedStyle(t),i=parseFloat(e.marginTop||0)+parseFloat(e.marginBottom||0),n=parseFloat(e.marginLeft||0)+parseFloat(e.marginRight||0);return{width:t.offsetWidth+n,height:t.offsetHeight+i}}function Cu(t){var e={left:"right",right:"left",bottom:"top",top:"bottom"};return t.replace(/left|right|bottom|top/g,(function(t){return e[t]}))}function ku(t,e,i){i=i.split("-")[0];var n=Tu(t),o={width:n.width,height:n.height},r=-1!==["right","left"].indexOf(i),a=r?"top":"left",s=r?"left":"top",l=r?"height":"width",u=r?"width":"height";return o[a]=e[a]+e[l]/2-n[l]/2,o[s]=i===s?e[s]-n[u]:e[Cu(s)],o}function Bu(t,e){return Array.prototype.find?t.find(e):t.filter(e)[0]}function xu(t,e,i){return(void 0===i?t:t.slice(0,function(t,e,i){if(Array.prototype.findIndex)return t.findIndex((function(t){return t[e]===i}));var n=Bu(t,(function(t){return t[e]===i}));return t.indexOf(n)}(t,"name",i))).forEach((function(t){t.function&&console.warn("`modifier.function` is deprecated, use `modifier.fn`!");var i=t.function||t.fn;t.enabled&&Yl(i)&&(e.offsets.popper=hu(e.offsets.popper),e.offsets.reference=hu(e.offsets.reference),e=i(e,t))})),e}function $u(){if(!this.state.isDestroyed){var t={instance:this,styles:{},arrowStyles:{},attributes:{},flipped:!1,offsets:{}};t.offsets.reference=wu(this.state,this.popper,this.reference,this.options.positionFixed),t.placement=Su(this.options.placement,t.offsets.reference,this.popper,this.reference,this.options.modifiers.flip.boundariesElement,this.options.modifiers.flip.padding),t.originalPlacement=t.placement,t.positionFixed=this.options.positionFixed,t.offsets.popper=ku(this.popper,t.offsets.reference,t.placement),t.offsets.popper.position=this.options.positionFixed?"fixed":"absolute",t=xu(this.modifiers,t),this.state.isCreated?this.options.onUpdate(t):(this.state.isCreated=!0,this.options.onCreate(t))}}function _u(t,e){return t.some((function(t){var i=t.name;return t.enabled&&i===e}))}function Du(t){for(var e=[!1,"ms","Webkit","Moz","O"],i=t.charAt(0).toUpperCase()+t.slice(1),n=0;n<e.length;n++){var o=e[n],r=o?""+o+i:t;if("undefined"!=typeof document.body.style[r])return r}return null}function Fu(){return this.state.isDestroyed=!0,_u(this.modifiers,"applyStyle")&&(this.popper.removeAttribute("x-placement"),this.popper.style.position="",this.popper.style.top="",this.popper.style.left="",this.popper.style.right="",this.popper.style.bottom="",this.popper.style.willChange="",this.popper.style[Du("transform")]=""),this.disableEventListeners(),this.options.removeOnDestroy&&this.popper.parentNode.removeChild(this.popper),this}function Iu(t){var e=t.ownerDocument;return e?e.defaultView:window}function Ou(t,e,i,n){var o="BODY"===t.nodeName,r=o?t.ownerDocument.defaultView:t;r.addEventListener(e,i,{passive:!0}),o||Ou(Kl(r.parentNode),e,i,n),n.push(r)}function Pu(t,e,i,n){i.updateBound=n,Iu(t).addEventListener("resize",i.updateBound,{passive:!0});var o=Kl(t);return Ou(o,"scroll",i.updateBound,i.scrollParents),i.scrollElement=o,i.eventsEnabled=!0,i}function Au(){this.state.eventsEnabled||(this.state=Pu(this.reference,this.options,this.state,this.scheduleUpdate))}function Vu(){var t,e;this.state.eventsEnabled&&(cancelAnimationFrame(this.scheduleUpdate),this.state=(t=this.reference,e=this.state,Iu(t).removeEventListener("resize",e.updateBound),e.scrollParents.forEach((function(t){t.removeEventListener("scroll",e.updateBound)})),e.updateBound=null,e.scrollParents=[],e.scrollElement=null,e.eventsEnabled=!1,e))}function Eu(t){return""!==t&&!isNaN(parseFloat(t))&&isFinite(t)}function Lu(t,e){Object.keys(e).forEach((function(i){var n="";-1!==["width","height","top","right","bottom","left"].indexOf(i)&&Eu(e[i])&&(n="px"),t.style[i]=e[i]+n}))}var Ru=jl&&/Firefox/i.test(navigator.userAgent);function Nu(t,e,i){var n=Bu(t,(function(t){return t.name===e})),o=!!n&&t.some((function(t){return t.name===i&&t.enabled&&t.order<n.order}));if(!o){var r="`"+e+"`",a="`"+i+"`";console.warn(a+" modifier is required by "+r+" modifier in order to work, be sure to include it before "+r+"!")}return o}var Mu=["auto-start","auto","auto-end","top-start","top","top-end","right-start","right","right-end","bottom-end","bottom","bottom-start","left-end","left","left-start"],Hu=Mu.slice(3);function zu(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],i=Hu.indexOf(t),n=Hu.slice(i+1).concat(Hu.slice(0,i));return e?n.reverse():n}var ju="flip",Gu="clockwise",Wu="counterclockwise";function Yu(t,e,i,n){var o=[0,0],r=-1!==["right","left"].indexOf(n),a=t.split(/(\+|\-)/).map((function(t){return t.trim()})),s=a.indexOf(Bu(a,(function(t){return-1!==t.search(/,|\s/)})));a[s]&&-1===a[s].indexOf(",")&&console.warn("Offsets separated by white space(s) are deprecated, use a comma (,) instead.");var l=/\s*,\s*|\s+/,u=-1!==s?[a.slice(0,s).concat([a[s].split(l)[0]]),[a[s].split(l)[1]].concat(a.slice(s+1))]:[a];return(u=u.map((function(t,n){var o=(1===n?!r:r)?"height":"width",a=!1;return t.reduce((function(t,e){return""===t[t.length-1]&&-1!==["+","-"].indexOf(e)?(t[t.length-1]=e,a=!0,t):a?(t[t.length-1]+=e,a=!1,t):t.concat(e)}),[]).map((function(t){return function(t,e,i,n){var o=t.match(/((?:\-|\+)?\d*\.?\d*)(.*)/),r=+o[1],a=o[2];if(!r)return t;if(0===a.indexOf("%")){var s=void 0;switch(a){case"%p":s=i;break;case"%":case"%r":default:s=n}return hu(s)[e]/100*r}if("vh"===a||"vw"===a)return("vh"===a?Math.max(document.documentElement.clientHeight,window.innerHeight||0):Math.max(document.documentElement.clientWidth,window.innerWidth||0))/100*r;return r}(t,o,e,i)}))}))).forEach((function(t,e){t.forEach((function(i,n){Eu(i)&&(o[e]+=i*("-"===t[n-1]?-1:1))}))})),o}var Uu={placement:"bottom",positionFixed:!1,eventsEnabled:!0,removeOnDestroy:!1,onCreate:function(){},onUpdate:function(){},modifiers:{shift:{order:100,enabled:!0,fn:function(t){var e=t.placement,i=e.split("-")[0],n=e.split("-")[1];if(n){var o=t.offsets,r=o.reference,a=o.popper,s=-1!==["bottom","top"].indexOf(i),l=s?"left":"top",u=s?"width":"height",c={start:cu({},l,r[l]),end:cu({},l,r[l]+r[u]-a[u])};t.offsets.popper=du({},a,c[n])}return t}},offset:{order:200,enabled:!0,fn:function(t,e){var i=e.offset,n=t.placement,o=t.offsets,r=o.popper,a=o.reference,s=n.split("-")[0],l=void 0;return l=Eu(+i)?[+i,0]:Yu(i,r,a,s),"left"===s?(r.top+=l[0],r.left-=l[1]):"right"===s?(r.top+=l[0],r.left+=l[1]):"top"===s?(r.left+=l[0],r.top-=l[1]):"bottom"===s&&(r.left+=l[0],r.top+=l[1]),t.popper=r,t},offset:0},preventOverflow:{order:300,enabled:!0,fn:function(t,e){var i=e.boundariesElement||tu(t.instance.popper);t.instance.reference===i&&(i=tu(i));var n=Du("transform"),o=t.instance.popper.style,r=o.top,a=o.left,s=o[n];o.top="",o.left="",o[n]="";var l=bu(t.instance.popper,t.instance.reference,e.padding,i,t.positionFixed);o.top=r,o.left=a,o[n]=s,e.boundaries=l;var u=e.priority,c=t.offsets.popper,d={primary:function(t){var i=c[t];return c[t]<l[t]&&!e.escapeWithReference&&(i=Math.max(c[t],l[t])),cu({},t,i)},secondary:function(t){var i="right"===t?"left":"top",n=c[i];return c[t]>l[t]&&!e.escapeWithReference&&(n=Math.min(c[i],l[t]-("right"===t?c.width:c.height))),cu({},i,n)}};return u.forEach((function(t){var e=-1!==["left","top"].indexOf(t)?"primary":"secondary";c=du({},c,d[e](t))})),t.offsets.popper=c,t},priority:["left","right","top","bottom"],padding:5,boundariesElement:"scrollParent"},keepTogether:{order:400,enabled:!0,fn:function(t){var e=t.offsets,i=e.popper,n=e.reference,o=t.placement.split("-")[0],r=Math.floor,a=-1!==["top","bottom"].indexOf(o),s=a?"right":"bottom",l=a?"left":"top",u=a?"width":"height";return i[s]<r(n[l])&&(t.offsets.popper[l]=r(n[l])-i[u]),i[l]>r(n[s])&&(t.offsets.popper[l]=r(n[s])),t}},arrow:{order:500,enabled:!0,fn:function(t,e){var i;if(!Nu(t.instance.modifiers,"arrow","keepTogether"))return t;var n=e.element;if("string"==typeof n){if(!(n=t.instance.popper.querySelector(n)))return t}else if(!t.instance.popper.contains(n))return console.warn("WARNING: `arrow.element` must be child of its popper element!"),t;var o=t.placement.split("-")[0],r=t.offsets,a=r.popper,s=r.reference,l=-1!==["left","right"].indexOf(o),u=l?"height":"width",c=l?"Top":"Left",d=c.toLowerCase(),h=l?"left":"top",f=l?"bottom":"right",p=Tu(n)[u];s[f]-p<a[d]&&(t.offsets.popper[d]-=a[d]-(s[f]-p)),s[d]+p>a[f]&&(t.offsets.popper[d]+=s[d]+p-a[f]),t.offsets.popper=hu(t.offsets.popper);var m=s[d]+s[u]/2-p/2,g=Ul(t.instance.popper),v=parseFloat(g["margin"+c]),b=parseFloat(g["border"+c+"Width"]),y=m-t.offsets.popper[d]-v-b;return y=Math.max(Math.min(a[u]-p,y),0),t.arrowElement=n,t.offsets.arrow=(cu(i={},d,Math.round(y)),cu(i,h,""),i),t},element:"[x-arrow]"},flip:{order:600,enabled:!0,fn:function(t,e){if(_u(t.instance.modifiers,"inner"))return t;if(t.flipped&&t.placement===t.originalPlacement)return t;var i=bu(t.instance.popper,t.instance.reference,e.padding,e.boundariesElement,t.positionFixed),n=t.placement.split("-")[0],o=Cu(n),r=t.placement.split("-")[1]||"",a=[];switch(e.behavior){case ju:a=[n,o];break;case Gu:a=zu(n);break;case Wu:a=zu(n,!0);break;default:a=e.behavior}return a.forEach((function(s,l){if(n!==s||a.length===l+1)return t;n=t.placement.split("-")[0],o=Cu(n);var u=t.offsets.popper,c=t.offsets.reference,d=Math.floor,h="left"===n&&d(u.right)>d(c.left)||"right"===n&&d(u.left)<d(c.right)||"top"===n&&d(u.bottom)>d(c.top)||"bottom"===n&&d(u.top)<d(c.bottom),f=d(u.left)<d(i.left),p=d(u.right)>d(i.right),m=d(u.top)<d(i.top),g=d(u.bottom)>d(i.bottom),v="left"===n&&f||"right"===n&&p||"top"===n&&m||"bottom"===n&&g,b=-1!==["top","bottom"].indexOf(n),y=!!e.flipVariations&&(b&&"start"===r&&f||b&&"end"===r&&p||!b&&"start"===r&&m||!b&&"end"===r&&g),S=!!e.flipVariationsByContent&&(b&&"start"===r&&p||b&&"end"===r&&f||!b&&"start"===r&&g||!b&&"end"===r&&m),w=y||S;(h||v||w)&&(t.flipped=!0,(h||v)&&(n=a[l+1]),w&&(r=function(t){return"end"===t?"start":"start"===t?"end":t}(r)),t.placement=n+(r?"-"+r:""),t.offsets.popper=du({},t.offsets.popper,ku(t.instance.popper,t.offsets.reference,t.placement)),t=xu(t.instance.modifiers,t,"flip"))})),t},behavior:"flip",padding:5,boundariesElement:"viewport",flipVariations:!1,flipVariationsByContent:!1},inner:{order:700,enabled:!1,fn:function(t){var e=t.placement,i=e.split("-")[0],n=t.offsets,o=n.popper,r=n.reference,a=-1!==["left","right"].indexOf(i),s=-1===["top","left"].indexOf(i);return o[a?"left":"top"]=r[i]-(s?o[a?"width":"height"]:0),t.placement=Cu(e),t.offsets.popper=hu(o),t}},hide:{order:800,enabled:!0,fn:function(t){if(!Nu(t.instance.modifiers,"hide","preventOverflow"))return t;var e=t.offsets.reference,i=Bu(t.instance.modifiers,(function(t){return"preventOverflow"===t.name})).boundaries;if(e.bottom<i.top||e.left>i.right||e.top>i.bottom||e.right<i.left){if(!0===t.hide)return t;t.hide=!0,t.attributes["x-out-of-boundaries"]=""}else{if(!1===t.hide)return t;t.hide=!1,t.attributes["x-out-of-boundaries"]=!1}return t}},computeStyle:{order:850,enabled:!0,fn:function(t,e){var i=e.x,n=e.y,o=t.offsets.popper,r=Bu(t.instance.modifiers,(function(t){return"applyStyle"===t.name})).gpuAcceleration;void 0!==r&&console.warn("WARNING: `gpuAcceleration` option moved to `computeStyle` modifier and will not be supported in future versions of Popper.js!");var a=void 0!==r?r:e.gpuAcceleration,s=tu(t.instance.popper),l=fu(s),u={position:o.position},c=function(t,e){var i=t.offsets,n=i.popper,o=i.reference,r=Math.round,a=Math.floor,s=function(t){return t},l=r(o.width),u=r(n.width),c=-1!==["left","right"].indexOf(t.placement),d=-1!==t.placement.indexOf("-"),h=e?c||d||l%2==u%2?r:a:s,f=e?r:s;return{left:h(l%2==1&&u%2==1&&!d&&e?n.left-1:n.left),top:f(n.top),bottom:f(n.bottom),right:h(n.right)}}(t,window.devicePixelRatio<2||!Ru),d="bottom"===i?"top":"bottom",h="right"===n?"left":"right",f=Du("transform"),p=void 0,m=void 0;if(m="bottom"===d?"HTML"===s.nodeName?-s.clientHeight+c.bottom:-l.height+c.bottom:c.top,p="right"===h?"HTML"===s.nodeName?-s.clientWidth+c.right:-l.width+c.right:c.left,a&&f)u[f]="translate3d("+p+"px, "+m+"px, 0)",u[d]=0,u[h]=0,u.willChange="transform";else{var g="bottom"===d?-1:1,v="right"===h?-1:1;u[d]=m*g,u[h]=p*v,u.willChange=d+", "+h}var b={"x-placement":t.placement};return t.attributes=du({},b,t.attributes),t.styles=du({},u,t.styles),t.arrowStyles=du({},t.offsets.arrow,t.arrowStyles),t},gpuAcceleration:!0,x:"bottom",y:"right"},applyStyle:{order:900,enabled:!0,fn:function(t){var e,i;return Lu(t.instance.popper,t.styles),e=t.instance.popper,i=t.attributes,Object.keys(i).forEach((function(t){!1!==i[t]?e.setAttribute(t,i[t]):e.removeAttribute(t)})),t.arrowElement&&Object.keys(t.arrowStyles).length&&Lu(t.arrowElement,t.arrowStyles),t},onLoad:function(t,e,i,n,o){var r=wu(o,e,t,i.positionFixed),a=Su(i.placement,r,e,t,i.modifiers.flip.boundariesElement,i.modifiers.flip.padding);return e.setAttribute("x-placement",a),Lu(e,{position:i.positionFixed?"fixed":"absolute"}),i},gpuAcceleration:void 0}}},qu=function(){function t(e,i){var n=this,o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};lu(this,t),this.scheduleUpdate=function(){return requestAnimationFrame(n.update)},this.update=Wl(this.update.bind(this)),this.options=du({},t.Defaults,o),this.state={isDestroyed:!1,isCreated:!1,scrollParents:[]},this.reference=e&&e.jquery?e[0]:e,this.popper=i&&i.jquery?i[0]:i,this.options.modifiers={},Object.keys(du({},t.Defaults.modifiers,o.modifiers)).forEach((function(e){n.options.modifiers[e]=du({},t.Defaults.modifiers[e]||{},o.modifiers?o.modifiers[e]:{})})),this.modifiers=Object.keys(this.options.modifiers).map((function(t){return du({name:t},n.options.modifiers[t])})).sort((function(t,e){return t.order-e.order})),this.modifiers.forEach((function(t){t.enabled&&Yl(t.onLoad)&&t.onLoad(n.reference,n.popper,n.options,t,n.state)})),this.update();var r=this.options.eventsEnabled;r&&this.enableEventListeners(),this.state.eventsEnabled=r}return uu(t,[{key:"update",value:function(){return $u.call(this)}},{key:"destroy",value:function(){return Fu.call(this)}},{key:"enableEventListeners",value:function(){return Au.call(this)}},{key:"disableEventListeners",value:function(){return Vu.call(this)}}]),t}();qu.Utils=("undefined"!=typeof window?window:global).PopperUtils,qu.placements=Mu,qu.Defaults=Uu;var BvEvent=function(){function BvEvent(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(o(this,BvEvent),!t)throw new TypeError("Failed to construct '".concat(this.constructor.name,"'. 1 argument required, ").concat(arguments.length," given."));ft(this,BvEvent.Defaults,this.constructor.Defaults,e,{type:t}),mt(this,{type:{enumerable:!0,configurable:!1,writable:!1},cancelable:{enumerable:!0,configurable:!1,writable:!1},nativeEvent:{enumerable:!0,configurable:!1,writable:!1},target:{enumerable:!0,configurable:!1,writable:!1},relatedTarget:{enumerable:!0,configurable:!1,writable:!1},vueTarget:{enumerable:!0,configurable:!1,writable:!1},componentId:{enumerable:!0,configurable:!1,writable:!1}});var i=!1;this.preventDefault=function(){this.cancelable&&(i=!0)},gt(this,"defaultPrevented",{enumerable:!0,get:function(){return i}})}return a(BvEvent,null,[{key:"Defaults",get:function(){return{type:"",cancelable:!0,nativeEvent:null,target:null,relatedTarget:null,vueTarget:null,componentId:null}}}]),BvEvent}(),Ku={data:function(){return{listenForClickOut:!1}},watch:{listenForClickOut:function(t,e){t!==e&&(qo(this.clickOutElement,this.clickOutEventName,this._clickOutHandler,el),t&&Uo(this.clickOutElement,this.clickOutEventName,this._clickOutHandler,el))}},beforeCreate:function(){this.clickOutElement=null,this.clickOutEventName=null},mounted:function(){this.clickOutElement||(this.clickOutElement=document),this.clickOutEventName||(this.clickOutEventName="click"),this.listenForClickOut&&Uo(this.clickOutElement,this.clickOutEventName,this._clickOutHandler,el)},beforeDestroy:function(){qo(this.clickOutElement,this.clickOutEventName,this._clickOutHandler,el)},methods:{isClickOut:function(t){return!uo(this.$el,t.target)},_clickOutHandler:function(t){this.clickOutHandler&&this.isClickOut(t)&&this.clickOutHandler(t)}}},Xu={data:function(){return{listenForFocusIn:!1}},watch:{listenForFocusIn:function(t,e){t!==e&&(qo(this.focusInElement,"focusin",this._focusInHandler,el),t&&Uo(this.focusInElement,"focusin",this._focusInHandler,el))}},beforeCreate:function(){this.focusInElement=null},mounted:function(){this.focusInElement||(this.focusInElement=document),this.listenForFocusIn&&Uo(this.focusInElement,"focusin",this._focusInHandler,el)},beforeDestroy:function(){qo(this.focusInElement,"focusin",this._focusInHandler,el)},methods:{_focusInHandler:function(t){this.focusInHandler&&this.focusInHandler(t)}}},Zu="bv::dropdown::",Ju="".concat(Zu,"shown"),Qu="".concat(Zu,"hidden"),tc=[".dropdown-item",".b-dropdown-form"].map((function(t){return"".concat(t,":not(.disabled):not([disabled])")})).join(", "),ec=xn({dropup:{type:Boolean,default:!1},dropright:{type:Boolean,default:!1},dropleft:{type:Boolean,default:!1},right:{type:Boolean,default:!1},offset:{type:[Number,String],default:0},noFlip:{type:Boolean,default:!1},popperOpts:{type:Object,default:function(){}},boundary:{type:[String,HTMLElement],default:"scrollParent"}},Ge),ic=u(u({},ec),xn({disabled:{type:Boolean,default:!1}},Ge)),nc={mixins:[vs,Ku,Xu],provide:function(){return{bvDropdown:this}},inject:{bvNavbar:{default:null}},props:ic,data:function(){return{visible:!1,visibleChangePrevented:!1}},computed:{inNavbar:function(){return!tt(this.bvNavbar)},toggler:function(){var t=this.$refs.toggle;return t?t.$el||t:null},directionClass:function(){return this.dropup?"dropup":this.dropright?"dropright":this.dropleft?"dropleft":""},boundaryClass:function(){return"scrollParent"===this.boundary||this.inNavbar?"":"position-static"}},watch:{visible:function(t,e){if(this.visibleChangePrevented)this.visibleChangePrevented=!1;else if(t!==e){var i=t?"show":"hide",n=new BvEvent(i,{cancelable:!0,vueTarget:this,target:this.$refs.menu,relatedTarget:null,componentId:this.safeId?this.safeId():this.id||null});if(this.emitEvent(n),n.defaultPrevented)return this.visibleChangePrevented=!0,this.visible=e,void this.$off("hidden",this.focusToggler);"show"===i?this.showMenu():this.hideMenu()}},disabled:function(t,e){t!==e&&t&&this.visible&&(this.visible=!1)}},created:function(){this.$_popper=null,this.$_hideTimeout=null},deactivated:function(){this.visible=!1,this.whileOpenListen(!1),this.destroyPopper()},beforeDestroy:function(){this.visible=!1,this.whileOpenListen(!1),this.destroyPopper(),this.clearHideTimeout()},methods:{emitEvent:function(t){var e=t.type;this.$emit(e,t),this.$root.$emit("".concat(Zu).concat(e),t)},showMenu:function(){var t=this;if(!this.disabled){if(!this.inNavbar)if("undefined"==typeof qu)oe("Popper.js not found. Falling back to CSS positioning",Ge);else{var e=this.dropup&&this.right||this.split?this.$el:this.$refs.toggle;e=e.$el||e,this.createPopper(e)}this.$root.$emit(Ju,this),this.whileOpenListen(!0),this.$nextTick((function(){t.focusMenu(),t.$emit("shown")}))}},hideMenu:function(){this.whileOpenListen(!1),this.$root.$emit(Qu,this),this.$emit("hidden"),this.destroyPopper()},createPopper:function(t){this.destroyPopper(),this.$_popper=new qu(t,this.$refs.menu,this.getPopperConfig())},destroyPopper:function(){this.$_popper&&this.$_popper.destroy(),this.$_popper=null},updatePopper:function(){try{this.$_popper.scheduleUpdate()}catch(t){}},clearHideTimeout:function(){clearTimeout(this.$_hideTimeout),this.$_hideTimeout=null},getPopperConfig:function(){var t="bottom-start";this.dropup?t=this.right?"top-end":"top-start":this.dropright?t="right-start":this.dropleft?t="left-start":this.right&&(t="bottom-end");var e={placement:t,modifiers:{offset:{offset:this.offset||0},flip:{enabled:!this.noFlip}}},i=this.boundary;return i&&(e.modifiers.preventOverflow={boundariesElement:i}),Ct(e,this.popperOpts||{})},whileOpenListen:function(t){this.listenForClickOut=t,this.listenForFocusIn=t;var e=t?"$on":"$off";this.$root[e](Ju,this.rootCloseListener)},rootCloseListener:function(t){t!==this&&(this.visible=!1)},show:function(){var t=this;this.disabled||Xn((function(){t.visible=!0}))},hide:function(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.disabled||(this.visible=!1,t&&this.$once("hidden",this.focusToggler))},toggle:function(t){var e=t=t||{},i=e.type,n=e.keyCode;("click"===i||"keydown"===i&&-1!==[$r,Ir,Br].indexOf(n))&&(this.disabled?this.visible=!1:(this.$emit("toggle",t),Xo(t),this.visible?this.hide(!0):this.show()))},onMousedown:function(t){Xo(t,{propagation:!1})},onKeydown:function(t){var e=t.keyCode;27===e?this.onEsc(t):e===Br?this.focusNext(t,!1):e===Or&&this.focusNext(t,!0)},onEsc:function(t){this.visible&&(this.visible=!1,Xo(t),this.$once("hidden",this.focusToggler))},onSplitClick:function(t){this.disabled?this.visible=!1:this.$emit("click",t)},hideHandler:function(t){var e=this,i=t.target;!this.visible||uo(this.$refs.menu,i)||uo(this.toggler,i)||(this.clearHideTimeout(),this.$_hideTimeout=setTimeout((function(){return e.hide()}),this.inNavbar?300:0))},clickOutHandler:function(t){this.hideHandler(t)},focusInHandler:function(t){this.hideHandler(t)},focusNext:function(t,e){var i=this,n=t.target;!this.visible||t&&lo(".dropdown form",n)||(Xo(t),this.$nextTick((function(){var t=i.getItems();if(!(t.length<1)){var o=t.indexOf(n);e&&o>0?o--:!e&&o<t.length-1&&o++,o<0&&(o=0),i.focusItem(o,t)}})))},focusItem:function(t,e){var i=e.find((function(e,i){return i===t}));_o(i)},getItems:function(){return(ro(tc,this.$refs.menu)||[]).filter(io)},focusMenu:function(){_o(this.$refs.menu)},focusToggler:function(){var t=this;this.$nextTick((function(){_o(t.toggler)}))}}},oc=xn(u(u({},ic),{},{text:{type:String},html:{type:String},variant:{type:String,default:"secondary"},size:{type:String},block:{type:Boolean,default:!1},menuClass:{type:[String,Array,Object]},toggleTag:{type:String,default:"button"},toggleText:{type:String,default:"Toggle dropdown"},toggleClass:{type:[String,Array,Object]},noCaret:{type:Boolean,default:!1},split:{type:Boolean,default:!1},splitHref:{type:String},splitTo:{type:[String,Object]},splitVariant:{type:String},splitClass:{type:[String,Array,Object]},splitButtonType:{type:String,default:"button",validator:function(t){return _n(["button","submit","reset"],t)}},lazy:{type:Boolean,default:!1},role:{type:String,default:"menu"}}),Ge),rc=i.default.extend({name:Ge,mixins:[vs,nc,Wo],props:oc,computed:{dropdownClasses:function(){var t=this.block,e=this.split;return[this.directionClass,this.boundaryClass,{show:this.visible,"btn-group":e||!t,"d-flex":t&&e}]},menuClasses:function(){return[this.menuClass,{"dropdown-menu-right":this.right,show:this.visible}]},toggleClasses:function(){var t=this.split;return[this.toggleClass,{"dropdown-toggle-split":t,"dropdown-toggle-no-caret":this.noCaret&&!t}]}},render:function(t){var e=this.visible,i=this.variant,n=this.size,o=this.block,r=this.disabled,a=this.split,s=this.role,l=this.hide,c=this.toggle,d={variant:i,size:n,block:o,disabled:r},h="button-content",f=this.normalizeSlot(h),p=this.hasNormalizedSlot(h)?{}:Ea(this.html,this.text),m=t();if(a){var g=this.splitTo,v=this.splitHref,b=this.splitButtonType,y=u(u({},d),{},{variant:this.splitVariant||i});g?y.to=g:v?y.href=v:b&&(y.type=b),m=t(ta,{class:this.splitClass,attrs:{id:this.safeId("_BV_button_")},props:y,domProps:p,on:{click:this.onSplitClick},ref:"button"},f),f=[t("span",{class:["sr-only"]},[this.toggleText])],p={}}var S=t(ta,{staticClass:"dropdown-toggle",class:this.toggleClasses,attrs:{id:this.safeId("_BV_toggle_"),"aria-haspopup":"true","aria-expanded":Hn(e)},props:u(u({},d),{},{tag:this.toggleTag,block:o&&!a}),domProps:p,on:{mousedown:this.onMousedown,click:c,keydown:c},ref:"toggle"},f),w=t("ul",{staticClass:"dropdown-menu",class:this.menuClasses,attrs:{role:s,tabindex:"-1","aria-labelledby":this.safeId(a?"_BV_button_":"_BV_toggle_")},on:{keydown:this.onKeydown},ref:"menu"},[!this.lazy||e?this.normalizeSlot(Vo,{hide:l}):t()]);return t("div",{staticClass:"dropdown b-dropdown",class:this.dropdownClasses,attrs:{id:this.safeId()}},[m,S,w])}}),ac=Tt(zr,["event","routerTag"]),sc=i.default.extend({name:Ke,mixins:[Rr,Wo],inject:{bvDropdown:{default:null}},inheritAttrs:!1,props:xn(u(u({},ac),{},{linkClass:{type:[String,Array,Object]},variant:{type:String}}),Ke),computed:{computedAttrs:function(){return u(u({},this.bvAttrs),{},{role:"menuitem"})}},methods:{closeDropdown:function(){var t=this;Xn((function(){t.bvDropdown&&t.bvDropdown.hide(!0)}))},onClick:function(t){this.$emit("click",t),this.closeDropdown()}},render:function(t){var e=this.linkClass,i=this.variant,n=this.active,o=this.disabled,r=this.onClick;return t("li",{attrs:{role:"presentation"}},[t(jr,{staticClass:"dropdown-item",class:[e,s({},"text-".concat(i),i&&!(n||o))],props:this.$props,attrs:this.computedAttrs,on:{click:r},ref:"item"},this.normalizeSlot())])}}),lc=xn({active:{type:Boolean,default:!1},activeClass:{type:String,default:"active"},buttonClass:{type:[String,Array,Object]},disabled:{type:Boolean,default:!1},variant:{type:String}},Xe),uc=i.default.extend({name:Xe,mixins:[Rr,Wo],inject:{bvDropdown:{default:null}},inheritAttrs:!1,props:lc,computed:{computedAttrs:function(){return u(u({},this.bvAttrs),{},{role:"menuitem",type:"button",disabled:this.disabled})}},methods:{closeDropdown:function(){this.bvDropdown&&this.bvDropdown.hide(!0)},onClick:function(t){this.$emit("click",t),this.closeDropdown()}},render:function(t){var e;return t("li",{attrs:{role:"presentation"}},[t("button",{staticClass:"dropdown-item",class:[this.buttonClass,(e={},s(e,this.activeClass,this.active),s(e,"text-".concat(this.variant),this.variant&&!(this.active||this.disabled)),e)],attrs:this.computedAttrs,on:{click:this.onClick},ref:"button"},this.normalizeSlot())])}}),cc=xn({id:{type:String},tag:{type:String,default:"header"},variant:{type:String}},qe),dc=i.default.extend({name:qe,functional:!0,props:cc,render:function(t,e){var i=e.props,n=e.data,o=e.children,r=n.attrs||{};return n.attrs={},t("li",F(n,{attrs:{role:"presentation"}}),[t(i.tag,{staticClass:"dropdown-header",class:s({},"text-".concat(i.variant),i.variant),attrs:u(u({},r),{},{id:i.id||null,role:"heading"}),ref:"header"},o)])}}),hc=xn({tag:{type:String,default:"hr"}},We),fc=i.default.extend({name:We,functional:!0,props:hc,render:function(t,e){var i=e.props,n=e.data,o=n.attrs||{};return n.attrs={},t("li",F(n,{attrs:{role:"presentation"}}),[t(i.tag,{staticClass:"dropdown-divider",attrs:u(u({},o),{},{role:"separator","aria-orientation":"horizontal"}),ref:"divider"})])}}),pc=xn({id:{type:String},inline:{type:Boolean,default:!1},novalidate:{type:Boolean,default:!1},validated:{type:Boolean,default:!1}},Qe),mc=i.default.extend({name:Qe,functional:!0,props:pc,render:function(t,e){var i=e.props,n=e.data,o=e.children;return t("form",F(n,{class:{"form-inline":i.inline,"was-validated":i.validated},attrs:{id:i.id,novalidate:i.novalidate}}),o)}}),gc=i.default.extend({name:Ye,functional:!0,props:xn(u(u({},pc),{},{disabled:{type:Boolean,default:!1},formClass:{type:[String,Object,Array]}}),Ye),render:function(t,e){var i=e.props,n=e.data,o=e.children,r=n.attrs||{},a=n.on||{};return n.attrs={},n.on={},t("li",F(n,{attrs:{role:"presentation"}}),[t(mc,{ref:"form",staticClass:"b-dropdown-form",class:[i.formClass,{disabled:i.disabled}],props:i,attrs:u(u({},r),{},{disabled:i.disabled,tabindex:i.disabled?null:"-1"}),on:a},o)])}}),vc=i.default.extend({name:Ze,functional:!0,props:xn({tag:{type:String,default:"p"},textClass:{type:[String,Array,Object]},variant:{type:String}},Ze),render:function(t,e){var i=e.props,n=e.data,o=e.children,r=i.tag,a=i.textClass,l=i.variant,u=n.attrs||{};return n.attrs={},t("li",F(n,{attrs:{role:"presentation"}}),[t(r,{staticClass:"b-dropdown-text",class:[a,s({},"text-".concat(l),l)],props:i,attrs:u,ref:"text"},o)])}}),bc=xn({id:{type:String},header:{type:String},headerTag:{type:String,default:"header"},headerVariant:{type:String},headerClasses:{type:[String,Array,Object]},ariaDescribedby:{type:String}},Ue),yc=i.default.extend({name:Ue,functional:!0,props:bc,render:function(t,e){var i,n=e.props,o=e.data,r=e.slots,a=e.scopedSlots,l=r(),c=a||{},d=o.attrs||{};o.attrs={};var h=null;(jo(Ro,c,l)||n.header)&&(h=n.id?"_bv_".concat(n.id,"_group_dd_header"):null,i=t(n.headerTag,{staticClass:"dropdown-header",class:[n.headerClasses,s({},"text-".concat(n.variant),n.variant)],attrs:{id:h,role:"heading"}},Go(Ro,{},c,l)||n.header));var f=[h,n.ariaDescribedBy].filter(ee).join(" ").trim();return t("li",F(o,{attrs:{role:"presentation"}}),[i||t(),t("ul",{staticClass:"list-unstyled",attrs:u(u({},d),{},{id:n.id||null,role:"group","aria-describedby":f||null})},Go(Vo,{},c,l))])}}),Sc=de({components:{BDropdown:rc,BDd:rc,BDropdownItem:sc,BDdItem:sc,BDropdownItemButton:uc,BDropdownItemBtn:uc,BDdItemButton:uc,BDdItemBtn:uc,BDropdownHeader:dc,BDdHeader:dc,BDropdownDivider:fc,BDdDivider:fc,BDropdownForm:gc,BDdForm:gc,BDropdownText:vc,BDdText:vc,BDropdownGroup:yc,BDdGroup:yc}}),wc=["iframe","embed","video","object","img","b-img","b-img-lazy"],Tc=xn({type:{type:String,default:"iframe",validator:function(t){return _n(wc,t)}},tag:{type:String,default:"div"},aspect:{type:String,default:"16by9"}},Je),Cc=de({components:{BEmbed:i.default.extend({name:Je,functional:!0,props:Tc,render:function(t,e){var i=e.props,n=e.data,o=e.children;return t(i.tag,{ref:n.ref,staticClass:"embed-responsive",class:s({},"embed-responsive-".concat(i.aspect),i.aspect)},[t(i.type,F(n,{ref:"",staticClass:"embed-responsive-item"}),o)])}})}}),kc=xn({options:{type:[Array,Object],default:function(){return[]}},valueField:{type:String,default:"value"},textField:{type:String,default:"text"},htmlField:{type:String,default:"html"},disabledField:{type:String,default:"disabled"}},"formOptionControls"),Bc={props:kc,computed:{formOptions:function(){return this.normalizeOptions(this.options)}},methods:{normalizeOption:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if(ut(t)){var i=ne(t,this.valueField),n=ne(t,this.textField);return{value:Q(i)?e||n:i,text:Va(String(Q(n)?e:n)),html:ne(t,this.htmlField),disabled:Boolean(ne(t,this.disabledField))}}return{value:e||t,text:Va(String(t)),disabled:!1}},normalizeOptions:function(t){var e=this;return st(t)?t.map((function(t){return e.normalizeOption(t)})):ut(t)?(oe('Setting prop "options" to an object is deprecated. Use the array format instead.',this.$options.name),bt(t).map((function(i){return e.normalizeOption(t[i]||{},i)}))):[]}}},xc=i.default.extend({name:ii,mixins:[Bc,Wo],props:xn(u(u({},kc),{},{id:{type:String,required:!0}}),ii),render:function(t){var e=this.formOptions.map((function(e,i){var n=e.value,o=e.text,r=e.html,a=e.disabled;return t("option",{attrs:{value:n,disabled:a},domProps:Ea(r,o),key:"option_".concat(i)})}));return t("datalist",{attrs:{id:this.id}},[e,this.normalizeSlot()])}}),$c=xn({id:{type:String},tag:{type:String,default:"small"},textVariant:{type:String,default:"muted"},inline:{type:Boolean,default:!1}},bi),_c=i.default.extend({name:bi,functional:!0,props:$c,render:function(t,e){var i=e.props,n=e.data,o=e.children;return t(i.tag,F(n,{class:s({"form-text":!i.inline},"text-".concat(i.textVariant),i.textVariant),attrs:{id:i.id}}),o)}}),Dc=xn({id:{type:String},tag:{type:String,default:"div"},tooltip:{type:Boolean,default:!1},forceShow:{type:Boolean,default:!1},state:{type:Boolean,default:null},ariaLive:{type:String},role:{type:String}},si),Fc=i.default.extend({name:si,functional:!0,props:Dc,render:function(t,e){var i=e.props,n=e.data,o=e.children,r=!0===i.forceShow||!1===i.state;return t(i.tag,F(n,{class:{"invalid-feedback":!i.tooltip,"invalid-tooltip":i.tooltip,"d-block":r},attrs:{id:i.id||null,role:i.role||null,"aria-live":i.ariaLive||null,"aria-atomic":i.ariaLive?"true":null}}),o)}}),Ic=xn({id:{type:String},tag:{type:String,default:"div"},tooltip:{type:Boolean,default:!1},forceShow:{type:Boolean,default:!1},state:{type:Boolean,default:null},ariaLive:{type:String},role:{type:String}},wi),Oc=i.default.extend({name:wi,functional:!0,props:Ic,render:function(t,e){var i=e.props,n=e.data,o=e.children,r=!0===i.forceShow||!0===i.state;return t(i.tag,F(n,{class:{"valid-feedback":!i.tooltip,"valid-tooltip":i.tooltip,"d-block":r},attrs:{id:i.id||null,role:i.role||null,"aria-live":i.ariaLive||null,"aria-atomic":i.ariaLive?"true":null}}),o)}}),Pc=xn({tag:{type:String,default:"div"}},di),Ac=i.default.extend({name:di,functional:!0,props:Pc,render:function(t,e){var i=e.props,n=e.data,o=e.children;return t(i.tag,F(n,{staticClass:"form-row"}),o)}}),Vc=de({components:{BForm:mc,BFormDatalist:xc,BDatalist:xc,BFormText:_c,BFormInvalidFeedback:Fc,BFormFeedback:Fc,BFormValidFeedback:Oc,BFormRow:Ac}}),Ec=function(t,e){for(var i=0;i<t.length;i++)if(Ar(t[i],e))return i;return-1},Lc="input, textarea, select",Rc=u({id:{type:String},name:{type:String}},xn({disabled:{type:Boolean,default:!1},required:{type:Boolean,default:!1},form:{type:String},autofocus:{type:Boolean,default:!1}},"formControls")),Nc={props:Rc,mounted:function(){this.handleAutofocus()},activated:function(){this.handleAutofocus()},methods:{handleAutofocus:function(){var t=this;this.$nextTick((function(){Xn((function(){var e=t.$el;t.autofocus&&io(e)&&(so(e,Lc)||(e=ao(Lc,e)),_o(e))}))}))}}},Mc=xn({plain:{type:Boolean,default:!1}},"formControls"),Hc={props:Mc,computed:{custom:function(){return!this.plain}}},zc=xn(u(u({},Mc),{},{value:{},checked:{},inline:{type:Boolean,default:!1},button:{type:Boolean,default:!1},buttonVariant:{type:String},ariaLabel:{type:String},ariaLabelledby:{type:String}}),"formRadioCheckControls"),jc={mixins:[Rr,Hc,Wo],inheritAttrs:!1,model:{prop:"checked",event:"input"},props:zc,data:function(){return{localChecked:this.isGroup?this.bvGroup.checked:this.checked,hasFocus:!1}},computed:{computedLocalChecked:{get:function(){return this.isGroup?this.bvGroup.localChecked:this.localChecked},set:function(t){this.isGroup?this.bvGroup.localChecked=t:this.localChecked=t}},isGroup:function(){return Boolean(this.bvGroup)},isBtnMode:function(){return this.isGroup?this.bvGroup.buttons:this.button},isPlain:function(){return!this.isBtnMode&&(this.isGroup?this.bvGroup.plain:this.plain)},isCustom:function(){return!this.isBtnMode&&!this.isPlain},isSwitch:function(){return!(this.isBtnMode||this.isRadio||this.isPlain)&&(this.isGroup?this.bvGroup.switches:this.switch)},isInline:function(){return this.isGroup?this.bvGroup.inline:this.inline},isDisabled:function(){return this.isGroup&&this.bvGroup.disabled||this.disabled},isRequired:function(){return this.getName&&(this.isGroup?this.bvGroup.required:this.required)},getName:function(){return(this.isGroup?this.bvGroup.groupName:this.name)||null},getForm:function(){return(this.isGroup?this.bvGroup.form:this.form)||null},getSize:function(){return(this.isGroup?this.bvGroup.size:this.size)||""},getState:function(){return this.isGroup?this.bvGroup.computedState:this.computedState},getButtonVariant:function(){return this.buttonVariant?this.buttonVariant:this.isGroup&&this.bvGroup.buttonVariant?this.bvGroup.buttonVariant:"secondary"},buttonClasses:function(){var t;return["btn","btn-".concat(this.getButtonVariant),(t={},s(t,"btn-".concat(this.getSize),this.getSize),s(t,"disabled",this.isDisabled),s(t,"active",this.isChecked),s(t,"focus",this.hasFocus),t)]},computedAttrs:function(){return u(u({},this.bvAttrs),{},{id:this.safeId(),type:this.isRadio?"radio":"checkbox",name:this.getName,form:this.getForm,disabled:this.isDisabled,required:this.isRequired,"aria-required":this.isRequired||null,"aria-label":this.ariaLabel||null,"aria-labelledby":this.ariaLabelledby||null})}},watch:{checked:function(t){Ar(t,this.computedLocalChecked)||(this.computedLocalChecked=t)}},methods:{handleFocus:function(t){t.target&&("focus"===t.type?this.hasFocus=!0:"blur"===t.type&&(this.hasFocus=!1))},focus:function(){this.isDisabled||_o(this.$refs.input)},blur:function(){this.isDisabled||Do(this.$refs.input)}},render:function(t){var e=this.normalizeSlot(),i={change:this.handleChange};this.isBtnMode&&(i.focus=i.blur=this.handleFocus);var n=t("input",{ref:"input",key:"input",on:i,class:{"form-check-input":this.isPlain,"custom-control-input":this.isCustom,"is-valid":!0===this.getState&&!this.isBtnMode,"is-invalid":!1===this.getState&&!this.isBtnMode,"position-static":this.isPlain&&!e},directives:[{name:"model",rawName:"v-model",value:this.computedLocalChecked,expression:"computedLocalChecked"}],attrs:this.computedAttrs,domProps:{value:this.value,checked:this.isChecked}});if(this.isBtnMode){var o=t("label",{class:this.buttonClasses},[n,e]);return this.isGroup||(o=t("div",{class:["btn-group-toggle","d-inline-block"]},[o])),o}var r=t();return this.isPlain&&!e||(r=t("label",{class:{"form-check-label":this.isPlain,"custom-control-label":this.isCustom},attrs:{for:this.safeId()}},e)),t("div",{class:s({"form-check":this.isPlain,"form-check-inline":this.isPlain&&this.isInline,"custom-control":this.isCustom,"custom-control-inline":this.isCustom&&this.isInline,"custom-checkbox":this.isCustom&&this.isCheck&&!this.isSwitch,"custom-switch":this.isSwitch,"custom-radio":this.isCustom&&this.isRadio},"b-custom-control-".concat(this.getSize),Boolean(this.getSize&&!this.isBtnMode))},[n,r])}},Gc=xn({size:{type:String}},"formControls"),Wc={props:Gc,computed:{sizeFormClass:function(){return[this.size?"form-control-".concat(this.size):null]}}},Yc=xn({state:{type:Boolean,default:null}},"formState"),Uc={props:Yc,computed:{computedState:function(){return nt(this.state)?this.state:null},stateClass:function(){var t=this.computedState;return!0===t?"is-valid":!1===t?"is-invalid":null}}},qc=i.default.extend({name:ti,mixins:[jc,vs,Nc,Wc,Uc],inject:{bvGroup:{from:"bvCheckGroup",default:!1}},props:xn(u(u(u(u(u({},Rc),zc),Gc),Yc),{},{value:{default:!0},uncheckedValue:{default:!1},indeterminate:{type:Boolean,default:!1},switch:{type:Boolean,default:!1},checked:{default:null}}),ti),computed:{isChecked:function(){var t=this.value,e=this.computedLocalChecked;return st(e)?Ec(e,t)>-1:Ar(e,t)},isRadio:function(){return!1},isCheck:function(){return!0}},watch:{computedLocalChecked:function(t,e){if(!Ar(t,e)){this.$emit("input",t);var i=this.$refs.input;i&&this.$emit("update:indeterminate",i.indeterminate)}},indeterminate:function(t){this.setIndeterminate(t)}},mounted:function(){this.setIndeterminate(this.indeterminate)},methods:{handleChange:function(t){var e=this,i=t.target,n=i.checked,o=i.indeterminate,r=this.value,a=this.uncheckedValue,s=this.computedLocalChecked;if(st(s)){var l=Ec(s,r);n&&l<0?s=s.concat(r):!n&&l>-1&&(s=s.slice(0,l).concat(s.slice(l+1)))}else s=n?r:a;this.computedLocalChecked=s,this.$nextTick((function(){e.$emit("change",s),e.isGroup&&e.bvGroup.$emit("change",s),e.$emit("update:indeterminate",o)}))},setIndeterminate:function(t){st(this.computedLocalChecked)&&(t=!1);var e=this.$refs.input;e&&(e.indeterminate=t,this.$emit("update:indeterminate",t))}}}),Kc=i.default.extend({name:li,mixins:[vs,jc,Nc,Wc,Uc],inject:{bvGroup:{from:"bvRadioGroup",default:!1}},props:xn(u(u(u(u(u({},Rc),zc),Gc),Yc),{},{checked:{default:null}}),li),computed:{isChecked:function(){return Ar(this.value,this.computedLocalChecked)},isRadio:function(){return!0},isCheck:function(){return!1}},watch:{computedLocalChecked:function(t,e){Ar(t,e)||this.$emit("input",t)}},methods:{handleChange:function(t){var e=this,i=t.target.checked,n=this.value,o=i?n:null;this.computedLocalChecked=n,this.$nextTick((function(){e.$emit("change",o),e.isGroup&&e.bvGroup.$emit("change",o)}))}}}),Xc=xn(u(u({},Mc),{},{validated:{type:Boolean,default:!1},ariaInvalid:{type:[Boolean,String],default:!1},stacked:{type:Boolean,default:!1},buttons:{type:Boolean,default:!1},buttonVariant:{type:String}}),"formRadioCheckGroups"),Zc={mixins:[Hc,Wo],model:{prop:"checked",event:"input"},props:Xc,computed:{inline:function(){return!this.stacked},groupName:function(){return this.name||this.safeId()},groupClasses:function(){return this.buttons?["btn-group-toggle",this.inline?"btn-group":"btn-group-vertical",this.size?"btn-group-".concat(this.size):"",this.validated?"was-validated":""]:[this.validated?"was-validated":""]},computedAriaInvalid:function(){var t=this.ariaInvalid;return!0===t||"true"===t||""===t||!1===this.computedState?"true":null}},watch:{checked:function(t){Ar(t,this.localChecked)||(this.localChecked=t)},localChecked:function(t,e){Ar(t,e)||this.$emit("input",t)}},render:function(t){var e=this,i=this.formOptions.map((function(i,n){var o="BV_option_".concat(n);return t(e.isRadioGroup?Kc:qc,{props:{id:e.safeId(o),value:i.value,disabled:i.disabled||!1},key:o},[t("span",{domProps:Ea(i.html,i.text)})])}));return t("div",{class:[this.groupClasses,"bv-no-focus-ring"],attrs:{id:this.safeId(),role:this.isRadioGroup?"radiogroup":"group",tabindex:"-1","aria-required":this.required?"true":null,"aria-invalid":this.computedAriaInvalid}},[this.normalizeSlot(Eo),i,this.normalizeSlot()])}},Jc=xn(u(u(u(u(u(u({},Rc),kc),Xc),Gc),Yc),{},{switches:{type:Boolean,default:!1},checked:{type:Array,default:null}}),ei),Qc=i.default.extend({name:ei,mixins:[vs,Nc,Zc,Bc,Wc,Uc],provide:function(){return{bvCheckGroup:this}},props:Jc,data:function(){return{localChecked:this.checked||[]}},computed:{isRadioGroup:function(){return!1}}}),td=de({components:{BFormCheckbox:qc,BCheckbox:qc,BCheck:qc,BFormCheckboxGroup:Qc,BCheckboxGroup:Qc,BCheckGroup:Qc}}),ed="__BV_hover_handler__",id="mouseenter",nd=function(t,e,i){Ko(t,e,id,i,el),Ko(t,e,"mouseleave",i,el)},od=function(t,e){var i=e.value,n=void 0===i?null:i;if(M){var o=t[ed],r=it(o),a=!(r&&o.fn===n);r&&a&&(nd(!1,t,o),delete t[ed]),it(n)&&a&&(t[ed]=function(t){var e=function(e){t(e.type===id,e)};return e.fn=t,e}(n),nd(!0,t,t[ed]))}},rd={bind:od,componentUpdated:od,unbind:function(t){od(t,{value:null})}},ad=u(u(u(u(u({},Tt(Rc,["autofocus"])),Gc),ec),Yc),{},{value:{type:String,default:""},formattedValue:{type:String},placeholder:{type:String},labelSelected:{type:String},readonly:{type:Boolean,default:!1},lang:{type:String},rtl:{type:Boolean,default:null},buttonOnly:{type:Boolean,default:!1},buttonVariant:{type:String,default:"secondary"},menuClass:{type:[String,Array,Object]}}),sd=i.default.extend({name:"BVFormBtnLabelControl",directives:{BHover:rd},mixins:[vs,Wc,Uc,nc,Wo],props:ad,data:function(){return{isHovered:!1,hasFocus:!1}},computed:{idButton:function(){return this.safeId()},idLabel:function(){return this.safeId("_value_")},idMenu:function(){return this.safeId("_dialog_")},idWrapper:function(){return this.safeId("_outer_")},computedDir:function(){return!0===this.rtl?"rtl":!1===this.rtl?"ltr":null}},methods:{focus:function(){this.disabled||_o(this.$refs.toggle)},blur:function(){this.disabled||Do(this.$refs.toggle)},setFocus:function(t){this.hasFocus="focus"===t.type},handleHover:function(t){this.isHovered=t}},render:function(t){var e,i=this.idButton,n=this.idLabel,o=this.idMenu,r=this.idWrapper,a=this.disabled,l=this.readonly,u=this.required,c=this.name,d=this.state,h=this.visible,f=this.size,p=this.isHovered,m=this.hasFocus,g=this.labelSelected,v=this.buttonVariant,b=Hn(this.value)||"",y=!!this.buttonOnly,S=!1===d||u&&!b,w={isHovered:p,hasFocus:m,state:d,opened:h},T=t("button",{ref:"toggle",staticClass:"btn",class:(e={},s(e,"btn-".concat(v),y),s(e,"btn-".concat(f),!!f),s(e,"h-auto",!y),s(e,"dropdown-toggle",y),s(e,"dropdown-toggle-no-caret",y),e),attrs:{id:i,type:"button",disabled:a,"aria-haspopup":"dialog","aria-expanded":h?"true":"false","aria-invalid":S?"true":null,"aria-required":u?"true":null},directives:[{name:"b-hover",value:this.handleHover}],on:{mousedown:this.onMousedown,click:this.toggle,keydown:this.toggle,"!focus":this.setFocus,"!blur":this.setFocus}},[this.hasNormalizedSlot(Ao)?this.normalizeSlot(Ao,w):t(da,{props:{scale:1.25}})]),C=t();c&&!a&&(C=t("input",{attrs:{type:"hidden",name:c||null,form:this.form||null,value:b}}));var k=t("div",{ref:"menu",staticClass:"dropdown-menu",class:[this.menuClass,{show:h,"dropdown-menu-right":this.right}],attrs:{id:o,role:"dialog",tabindex:"-1","aria-modal":"false","aria-labelledby":n},on:{keydown:this.onKeydown}},[this.normalizeSlot(Vo,{opened:h})]),B=t("label",{staticClass:"form-control text-break text-wrap bg-transparent h-auto",class:[{"sr-only":y,"text-muted":!b},this.stateClass,this.sizeFormClass],attrs:{id:n,for:i,"aria-invalid":S?"true":null,"aria-required":u?"true":null},directives:[{name:"b-hover",value:this.handleHover}],on:{"!click":function(t){Xo(t,{preventDefault:!1})}}},[b?this.formattedValue||b:this.placeholder||"",b&&g?t("bdi",{staticClass:"sr-only"},g):""]);return t("div",{staticClass:"b-form-btn-label-control dropdown",class:[this.directionClass,this.boundaryClass,[{"btn-group":y,"form-control":!y,"d-flex":!y,"h-auto":!y,"align-items-stretch":!y,focus:m&&!y,show:h,"is-valid":!0===d,"is-invalid":!1===d},y?null:this.sizeFormClass]],attrs:{id:r,role:y?null:"group",lang:this.lang||null,dir:this.computedDir,"aria-disabled":a,"aria-readonly":l&&!a,"aria-labelledby":n,"aria-invalid":!1===d||u&&!b?"true":null,"aria-required":u?"true":null}},[T,C,k,B])}}),ld=i.default.extend({name:ni,mixins:[vs],model:{prop:"value",event:"input"},props:xn(u(u(u({},bs),Tt(ad,["id","value","formattedValue","rtl","lang"])),{},{resetValue:{type:[String,Date]},noCloseOnSelect:{type:Boolean,default:!1},buttonOnly:{type:Boolean,default:!1},buttonVariant:{type:String,default:"secondary"},calendarWidth:{type:String,default:"270px"},todayButton:{type:Boolean,default:!1},labelTodayButton:{type:String,default:"Select today"},todayButtonVariant:{type:String,default:"outline-primary"},resetButton:{type:Boolean,default:!1},labelResetButton:{type:String,default:"Reset"},resetButtonVariant:{type:String,default:"outline-danger"},closeButton:{type:Boolean,default:!1},labelCloseButton:{type:String,default:"Close"},closeButtonVariant:{type:String,default:"outline-secondary"},dark:{type:Boolean,default:!1}}),ni),data:function(){return{localYMD:is(this.value)||"",isVisible:!1,localLocale:null,isRTL:!1,formattedValue:"",activeYMD:""}},computed:{calendarYM:function(){return this.activeYMD.slice(0,-3)},computedLang:function(){return(this.localLocale||"").replace(/-u-.*$/i,"")||null},computedResetValue:function(){return is(ps(this.resetValue))||""}},watch:{value:function(t){this.localYMD=is(t)||""},localYMD:function(t){this.isVisible&&this.$emit("input",this.valueAsDate?es(t)||null:t||"")},calendarYM:function(t,e){if(t!==e&&e)try{this.$refs.control.updatePopper()}catch(t){}}},methods:{focus:function(){this.disabled||_o(this.$refs.control)},blur:function(){this.disabled||Do(this.$refs.control)},setAndClose:function(t){var e=this;this.localYMD=t,this.noCloseOnSelect||this.$nextTick((function(){e.$refs.control.hide(!0)}))},onSelected:function(t){var e=this;this.$nextTick((function(){e.setAndClose(t)}))},onInput:function(t){this.localYMD!==t&&(this.localYMD=t)},onContext:function(t){var e=t.activeYMD,i=t.isRTL,n=t.locale,o=t.selectedYMD,r=t.selectedFormatted;this.isRTL=i,this.localLocale=n,this.formattedValue=r,this.localYMD=o,this.activeYMD=e,this.$emit("context",t)},onTodayButton:function(){this.setAndClose(is(ps(ts(),this.min,this.max)))},onResetButton:function(){this.setAndClose(this.computedResetValue)},onCloseButton:function(){this.$refs.control.hide(!0)},onShow:function(){this.isVisible=!0},onShown:function(){var t=this;this.$nextTick((function(){_o(t.$refs.calendar),t.$emit("shown")}))},onHidden:function(){this.isVisible=!1,this.$emit("hidden")},defaultButtonFn:function(t){var e=t.isHovered,i=t.hasFocus;return this.$createElement(e||i?la:sa,{attrs:{"aria-hidden":"true"}})}},render:function(t){var e=this.localYMD,i=this.disabled,n=this.readonly,o=this.dark,r=this.$props,a=this.$scopedSlots,s=et(this.placeholder)?this.labelNoDateSelected:this.placeholder,l=[];if(this.todayButton){var c=this.labelTodayButton;l.push(t(ta,{props:{size:"sm",disabled:i||n,variant:this.todayButtonVariant},attrs:{"aria-label":c||null},on:{click:this.onTodayButton}},c))}if(this.resetButton){var d=this.labelResetButton;l.push(t(ta,{props:{size:"sm",disabled:i||n,variant:this.resetButtonVariant},attrs:{"aria-label":d||null},on:{click:this.onResetButton}},d))}if(this.closeButton){var h=this.labelCloseButton;l.push(t(ta,{props:{size:"sm",disabled:i,variant:this.closeButtonVariant},attrs:{"aria-label":h||null},on:{click:this.onCloseButton}},h))}l.length>0&&(l=[t("div",{staticClass:"b-form-date-controls d-flex flex-wrap",class:{"justify-content-between":l.length>1,"justify-content-end":l.length<2}},l)]);var f=t(ys,{key:"calendar",ref:"calendar",staticClass:"b-form-date-calendar w-100",props:u(u({},gr(bs,r)),{},{value:e,hidden:!this.isVisible}),on:{selected:this.onSelected,input:this.onInput,context:this.onContext},scopedSlots:wt(a,["nav-prev-decade","nav-prev-year","nav-prev-month","nav-this-month","nav-next-month","nav-next-year","nav-next-decade"])},l);return t(sd,{ref:"control",staticClass:"b-form-datepicker",props:u(u({},gr(ad,r)),{},{id:this.safeId(),value:e,formattedValue:e?this.formattedValue:"",placeholder:s,rtl:this.isRTL,lang:this.computedLang,menuClass:[{"bg-dark":!!o,"text-light":!!o},this.menuClass]}),on:{show:this.onShow,shown:this.onShown,hidden:this.onHidden},scopedSlots:{"button-content":a["button-content"]||this.defaultButtonFn}},[f])}}),ud=de({components:{BFormDatepicker:ld,BDatepicker:ld}}),cd=function t(e){return e instanceof Z||st(e)&&e.every((function(e){return t(e)}))},dd=function(t){return it(t.getAsEntry)?t.getAsEntry():it(t.webkitGetAsEntry)?t.webkitGetAsEntry():null},hd=function t(e){var i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return new Promise((function(n){var o=[];!function r(){e.readEntries((function(e){0===e.length?n(Promise.all(o).then((function(t){return In(t)}))):(o.push(Promise.all(e.map((function(e){if(e){if(e.isDirectory)return t(e.createReader(),"".concat(i).concat(e.name,"/"));if(e.isFile)return new Promise((function(t){e.file((function(e){e.$path="".concat(i).concat(e.name),t(e)}))}))}return null})).filter(ee))),r())}))}()}))},fd=i.default.extend({name:oi,mixins:[Rr,vs,Nc,Uc,Hc,Wo],inheritAttrs:!1,model:{prop:"value",event:"input"},props:xn(u(u(u(u(u({},Rc),Mc),Yc),Gc),{},{value:{type:[Z,Array],default:null,validator:function(t){return""===t?(oe('Setting "value"/"v-model" to an empty string for reset is deprecated. Set to "null" instead.',oi),!0):et(t)||cd(t)}},accept:{type:String,default:""},capture:{type:Boolean,default:!1},placeholder:{type:String,default:"No file chosen"},browseText:{type:String,default:"Browse"},dropPlaceholder:{type:String,default:"Drop files here"},noDropPlaceholder:{type:String,default:"Not allowed"},multiple:{type:Boolean,default:!1},directory:{type:Boolean,default:!1},noTraverse:{type:Boolean,default:!1},noDrop:{type:Boolean,default:!1},fileNameFormatter:{type:Function}}),oi),data:function(){return{files:[],dragging:!1,dropAllowed:!this.noDrop,hasFocus:!1}},computed:{computedAccept:function(){var t=this.accept;return 0===(t=(t||"").trim().split(/[,\s]+/).filter(Boolean)).length?null:t.map((function(t){var e="name",i="^",n="$";return $t.test(t)?i="":(e="type",Rt.test(t)&&(n=".+$",t=t.slice(0,-1))),t=Mn(t),{rx:new RegExp("".concat(i).concat(t).concat(n)),prop:e}}))},computedCapture:function(){var t=this.capture;return!0===t||""===t||(t||null)},computedAttrs:function(){var t=this.name,e=this.disabled,i=this.required,n=this.form,o=this.computedCapture,r=this.accept,a=this.multiple,s=this.directory;return u(u({},this.bvAttrs),{},{type:"file",id:this.safeId(),name:t,disabled:e,required:i,form:n||null,capture:o,accept:r||null,multiple:a,directory:s,webkitdirectory:s,"aria-required":i?"true":null})},computedFileNameFormatter:function(){var t=this.fileNameFormatter,e=null;try{e=t()}catch(t){}return Q(e)?this.defaultFileNameFormatter:t},clonedFiles:function(){return kt(this.files)},flattenedFiles:function(){return On(this.files)},fileNames:function(){return this.flattenedFiles.map((function(t){return t.name}))},labelContent:function(){var t=this.$createElement;if(this.dragging&&!this.noDrop)return this.normalizeSlot("drop-placeholder",{allowed:this.dropAllowed})||(this.dropAllowed?this.dropPlaceholder:t("span",{staticClass:"text-danger"},this.noDropPlaceholder));if(0===this.files.length)return this.normalizeSlot("placeholder")||this.placeholder;var e=this.flattenedFiles,i=this.clonedFiles,n=this.fileNames,o=this.computedFileNameFormatter;return this.hasNormalizedSlot("file-name")?this.normalizeSlot("file-name",{files:e,filesTraversed:i,names:n}):o(e,i,n)}},watch:{value:function(t){(!t||st(t)&&0===t.length)&&this.reset()},files:function(t,e){if(!Ar(t,e)){var i=this.multiple,n=this.noTraverse,o=!i||n?On(t):t;this.$emit("input",i?o:o[0]||null)}}},mounted:function(){var t=this,e=lo("form",this.$el);e&&(Uo(e,"reset",this.reset,tl),this.$on("hook:beforeDestroy",(function(){qo(e,"reset",t.reset,tl)})))},methods:{isFileValid:function(t){if(!t)return!1;var e=this.computedAccept;return!e||e.some((function(e){return e.rx.test(t[e.prop])}))},isFilesArrayValid:function(t){var e=this;return st(t)?t.every((function(t){return e.isFileValid(t)})):this.isFileValid(t)},defaultFileNameFormatter:function(t,e,i){return i.join(", ")},setFiles:function(t){this.dropAllowed=!this.noDrop,this.dragging=!1,this.files=this.multiple?this.directory?t:On(t):On(t).slice(0,1)},setInputFiles:function(t){try{var e=new ClipboardEvent("").clipboardData||new DataTransfer;On(kt(t)).forEach((function(t){delete t.$path,e.items.add(t)})),this.$refs.input.files=e.files}catch(t){}},reset:function(){try{var t=this.$refs.input;t.value="",t.type="",t.type="file"}catch(t){}this.files=[]},handleFiles:function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(e){var i=t.filter(this.isFilesArrayValid);i.length>0&&(this.setFiles(i),this.setInputFiles(i))}else this.setFiles(t)},focusHandler:function(t){this.plain||"focusout"===t.type?this.hasFocus=!1:this.hasFocus=!0},onChange:function(t){var e=this,i=t.type,n=t.target,o=t.dataTransfer,r=void 0===o?{}:o,a="drop"===i;this.$emit("change",t);var s=$n(r.items||[]);if(R&&s.length>0&&!tt(dd(s[0])))(function(t){var e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];return Promise.all($n(t).filter((function(t){return"file"===t.kind})).map((function(t){var i=dd(t);if(i){if(i.isDirectory&&e)return hd(i.createReader(),"".concat(i.name,"/"));if(i.isFile)return new Promise((function(t){i.file((function(e){e.$path="",t(e)}))}))}return null})).filter(ee))})(s,this.directory).then((function(t){return e.handleFiles(t,a)}));else{var l=$n(n.files||r.files||[]).map((function(t){return t.$path=t.webkitRelativePath||"",t}));this.handleFiles(l,a)}},onDragenter:function(t){Xo(t),this.dragging=!0;var e=t.dataTransfer,i=void 0===e?{}:e;if(this.noDrop||this.disabled||!this.dropAllowed)return i.dropEffect="none",void(this.dropAllowed=!1);i.dropEffect="copy"},onDragover:function(t){Xo(t),this.dragging=!0;var e=t.dataTransfer,i=void 0===e?{}:e;if(this.noDrop||this.disabled||!this.dropAllowed)return i.dropEffect="none",void(this.dropAllowed=!1);i.dropEffect="copy"},onDragleave:function(t){var e=this;Xo(t),this.$nextTick((function(){e.dragging=!1,e.dropAllowed=!e.noDrop}))},onDrop:function(t){var e=this;Xo(t),this.dragging=!1,this.noDrop||this.disabled||!this.dropAllowed?this.$nextTick((function(){e.dropAllowed=!e.noDrop})):this.onChange(t)}},render:function(t){var e=this.custom,i=this.plain,n=this.size,o=this.dragging,r=this.stateClass,a=t("input",{ref:"input",class:[{"form-control-file":i,"custom-file-input":e,focus:e&&this.hasFocus},r],style:e?{zIndex:-5}:{},attrs:this.computedAttrs,on:{change:this.onChange,focusin:this.focusHandler,focusout:this.focusHandler,reset:this.reset}});if(i)return a;var l=t("label",{staticClass:"custom-file-label",class:{dragging:o},attrs:{for:this.safeId(),"data-browse":this.browseText||null}},[t("span",{staticClass:"d-block form-file-text",style:{pointerEvents:"none"}},[this.labelContent])]);return t("div",{staticClass:"custom-file b-form-file",class:[s({},"b-custom-control-".concat(n),n),r],attrs:{id:this.safeId("_BV_file_outer_")},on:{dragenter:this.onDragenter,dragover:this.onDragover,dragleave:this.onDragleave,drop:this.onDrop}},[a,l])}}),pd=de({components:{BFormFile:fd,BFile:fd}}),md=function(t){return"\\"+t},gd=function(t){var e=(t=Hn(t)).length,i=t.charCodeAt(0);return t.split("").reduce((function(n,o,r){var a=t.charCodeAt(r);return 0===a?n+"�":127===a||a>=1&&a<=31||0===r&&a>=48&&a<=57||1===r&&a>=48&&a<=57&&45===i?n+md("".concat(a.toString(16)," ")):0===r&&45===a&&1===e?n+md(o):a>=128||45===a||95===a||a>=48&&a<=57||a>=65&&a<=90||a>=97&&a<=122?n+o:n+md(o)}),"")},vd=["auto","start","end","center","baseline","stretch"],bd=function(){return{type:[String,Number],default:null}},yd=yn((function(t,e,i){var n=t;if(!et(i)&&!1!==i)return e&&(n+="-".concat(e)),"col"!==t||""!==i&&!0!==i?(n+="-".concat(i),jn(n)):jn(n)})),Sd=pt(null),wd=function(){var t=Bn().filter(ee),e=t.reduce((function(t,e){return e&&(t[e]={type:[Boolean,String,Number],default:!1}),t}),pt(null)),i=t.reduce((function(t,e){return t[pr(e,"offset")]=bd(),t}),pt(null)),n=t.reduce((function(t,e){return t[pr(e,"order")]=bd(),t}),pt(null));return Sd=ft(pt(null),{col:bt(e),offset:bt(i),order:bt(n)}),u(u(u(u({col:{type:Boolean,default:!1},cols:bd()},e),{},{offset:bd()},i),{},{order:bd()},n),{},{alignSelf:{type:String,default:null,validator:function(t){return _n(vd,t)}},tag:{type:String,default:"div"}})},Td={name:"BCol",functional:!0,get props(){return delete this.props,this.props=wd()},render:function(t,e){var i,n=e.props,o=e.data,r=e.children,a=[];for(var l in Sd)for(var u=Sd[l],c=0;c<u.length;c++){var d=yd(l,u[c].replace(l,""),n[u[c]]);d&&a.push(d)}var h=a.some((function(t){return Jt.test(t)}));return a.push((s(i={col:n.col||!h&&!n.cols},"col-".concat(n.cols),n.cols),s(i,"offset-".concat(n.offset),n.offset),s(i,"order-".concat(n.order),n.order),s(i,"align-self-".concat(n.alignSelf),n.alignSelf),i)),t(n.tag,F(o,{class:a}),r)}},Cd=["input","select","textarea","label","button","a"],kd=yn((function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return"".concat(e).concat(Nn(t))})),Bd={name:ri,mixins:[vs,Uc,Wo],get props(){return delete this.props,this.props=(t=Bn(),e=t.reduce((function(t,e){return t[kd(e,"labelCols")]={type:[Number,String,Boolean],default:!e&&null},t}),pt(null)),i=t.reduce((function(t,e){return t[kd(e,"labelAlign")]={type:String},t}),pt(null)),xn(u(u(u(u({},Yc),{},{label:{type:String},labelFor:{type:String},labelSize:{type:String},labelSrOnly:{type:Boolean,default:!1}},e),i),{},{labelClass:{type:[String,Array,Object]},description:{type:String},invalidFeedback:{type:String},validFeedback:{type:String},tooltip:{type:Boolean,default:!1},feedbackAriaLive:{type:String,default:"assertive"},validated:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1}}),ri));var t,e,i},data:function(){return{describedByIds:""}},computed:{labelColProps:function(){var t=this,e={};return Bn().forEach((function(i){var n=t[kd(i,"labelCols")];nt(n=""===n||(n||!1))||"auto"===n||(n=(n=Pn(n,0))>0&&n),n&&(e[i||(nt(n)?"col":"cols")]=n)})),e},labelAlignClasses:function(){var t=this,e=[];return Bn().forEach((function(i){var n=t[kd(i,"labelAlign")]||null;if(n){var o=i?"text-".concat(i,"-").concat(n):"text-".concat(n);e.push(o)}})),e},isHorizontal:function(){return bt(this.labelColProps).length>0}},watch:{describedByIds:function(t,e){t!==e&&this.setInputDescribedBy(t,e)}},mounted:function(){var t=this;this.$nextTick((function(){t.setInputDescribedBy(t.describedByIds)}))},methods:{legendClick:function(t){if(!this.labelFor){var e=t.target,i=e?e.tagName:"";if(-1===Cd.indexOf(i)){var n=ro("input:not([disabled]),textarea:not([disabled]),select:not([disabled])",this.$refs.content).filter(io);n&&1===n.length&&_o(n[0])}}},setInputDescribedBy:function(t,e){if(this.labelFor&&M){var i=ao("#".concat(gd(this.labelFor)),this.$refs.content);if(i){var n="aria-describedby",o=(vo(i,n)||"").split(/\s+/);t=(t||"").split(/\s+/),e=(e||"").split(/\s+/),o=o.filter((function(t){return!_n(e,t)})).concat(t).filter(Boolean),(o=bt(o.reduce((function(t,e){return u(u({},t),{},s({},e,!0))}),{})).join(" ").trim())?mo(i,n,o):go(i,n)}}}},render:function(t){var e=this.labelFor,i=this.tooltip,n=this.feedbackAriaLive,o=this.computedState,r=this.isHorizontal,a=this.normalizeSlot,s=!e,l=t(),c=a(No)||this.label,d=c?this.safeId("_BV_label_"):null;if(c||r){var h=this.labelSize,f=this.labelColProps,p=s,m=p?"legend":"label";this.labelSrOnly?(c&&(l=t(m,{class:"sr-only",attrs:{id:d,for:e||null}},[c])),l=t(r?Td:"div",{props:r?f:{}},[l])):l=t(r?Td:m,{on:p?{click:this.legendClick}:{},props:r?u({tag:m},f):{},attrs:{id:d,for:e||null,tabindex:p?"-1":null},class:[p?"bv-no-focus-ring":"",r||p?"col-form-label":"",!r&&p?"pt-0":"",r||p?"":"d-block",h?"col-form-label-".concat(h):"",this.labelAlignClasses,this.labelClass]},[c])}var g=t(),v=a("invalid-feedback")||this.invalidFeedback,b=v?this.safeId("_BV_feedback_invalid_"):null;v&&(g=t(Fc,{props:{id:b,state:o,tooltip:i,ariaLive:n,role:n?"alert":null},attrs:{tabindex:v?"-1":null}},[v]));var y=t(),S=a("valid-feedback")||this.validFeedback,w=S?this.safeId("_BV_feedback_valid_"):null;S&&(y=t(Oc,{props:{id:w,state:o,tooltip:i,ariaLive:n,role:n?"alert":null},attrs:{tabindex:S?"-1":null}},[S]));var T=t(),C=a("description")||this.description,k=C?this.safeId("_BV_description_"):null;C&&(T=t(_c,{attrs:{id:k,tabindex:C?"-1":null}},[C]));var B=t(r?Td:"div",{ref:"content",staticClass:"bv-no-focus-ring",attrs:{tabindex:s?"-1":null,role:s?"group":null,"aria-labelledby":s?d:null}},[a()||t(),g,y,T]);return this.describedByIds=[k,!1===o?b:null,!0===o?w:null].filter(Boolean).join(" "),t(s?"fieldset":r?Ac:"div",{staticClass:"form-group",class:[this.validated?"was-validated":null,this.stateClass],attrs:{id:this.safeId(),disabled:s?this.disabled:null,role:s?null:"group","aria-invalid":!1===o?"true":null,"aria-labelledby":s&&r?d:null,"aria-describedby":s?this.describedByIds:null}},r&&s?[t(Ac,[l,B])]:[l,B])}},xd=de({components:{BFormGroup:Bd,BFormFieldset:Bd}}),$d={computed:{selectionStart:{cache:!1,get:function(){return this.$refs.input.selectionStart},set:function(t){this.$refs.input.selectionStart=t}},selectionEnd:{cache:!1,get:function(){return this.$refs.input.selectionEnd},set:function(t){this.$refs.input.selectionEnd=t}},selectionDirection:{cache:!1,get:function(){return this.$refs.input.selectionDirection},set:function(t){this.$refs.input.selectionDirection=t}}},methods:{select:function(){var t;(t=this.$refs.input).select.apply(t,arguments)},setSelectionRange:function(){var t;(t=this.$refs.input).setSelectionRange.apply(t,arguments)},setRangeText:function(){var t;(t=this.$refs.input).setRangeText.apply(t,arguments)}}},_d=xn({value:{type:[String,Number],default:""},ariaInvalid:{type:[Boolean,String],default:!1},readonly:{type:Boolean,default:!1},plaintext:{type:Boolean,default:!1},autocomplete:{type:String},placeholder:{type:String},formatter:{type:Function},lazyFormatter:{type:Boolean,default:!1},trim:{type:Boolean,default:!1},number:{type:Boolean,default:!1},lazy:{type:Boolean,default:!1},debounce:{type:[Number,String],default:0}},"formTextControls"),Dd={model:{prop:"value",event:"update"},props:_d,data:function(){return{localValue:Hn(this.value),vModelValue:this.value}},computed:{computedClass:function(){return[{"custom-range":"range"===this.type,"form-control-plaintext":this.plaintext&&"range"!==this.type&&"color"!==this.type,"form-control":!this.plaintext&&"range"!==this.type||"color"===this.type},this.sizeFormClass,this.stateClass]},computedAriaInvalid:function(){return this.ariaInvalid&&"false"!==this.ariaInvalid?!0===this.ariaInvalid?"true":this.ariaInvalid:!1===this.computedState?"true":null},computedDebounce:function(){return nr(Pn(this.debounce,0),0)},hasFormatter:function(){var t=null;try{t=this.formatter()}catch(t){}return!Q(t)}},watch:{value:function(t){var e=Hn(t);e!==this.localValue&&t!==this.vModelValue&&(this.clearDebounce(),this.localValue=e,this.vModelValue=t)}},created:function(){this.$_inputDebounceTimer=null},mounted:function(){this.$on("hook:beforeDestroy",this.clearDebounce);var t=this.value,e=Hn(t);e!==this.localValue&&t!==this.vModelValue&&(this.localValue=e,this.vModelValue=t)},methods:{clearDebounce:function(){clearTimeout(this.$_inputDebounceTimer),this.$_inputDebounceTimer=null},formatValue:function(t,e){var i=arguments.length>2&&void 0!==arguments[2]&&arguments[2];return t=Hn(t),!this.hasFormatter||this.lazyFormatter&&!i||(t=this.formatter(t,e)),t},modifyValue:function(t){return this.trim&&(t=t.trim()),this.number&&(t=An(t,t)),t},updateValue:function(t){var e=this,i=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=this.lazy;if(!n||i){this.clearDebounce();var o=function(){if((t=e.modifyValue(t))!==e.vModelValue)e.vModelValue=t,e.$emit("update",t);else if(e.hasFormatter){var i=e.$refs.input;i&&t!==i.value&&(i.value=t)}},r=this.computedDebounce;r>0&&!n&&!i?this.$_inputDebounceTimer=setTimeout(o,r):o()}},onInput:function(t){if(!t.target.composing){var e=t.target.value,i=this.formatValue(e,t);!1===i||t.defaultPrevented?Xo(t,{propagation:!1}):(this.localValue=i,this.updateValue(i),this.$emit("input",i))}},onChange:function(t){var e=t.target.value,i=this.formatValue(e,t);!1===i||t.defaultPrevented?Xo(t,{propagation:!1}):(this.localValue=i,this.updateValue(i,!0),this.$emit("change",i))},onBlur:function(t){var e=t.target.value,i=this.formatValue(e,t,!0);!1!==i&&(this.localValue=Hn(this.modifyValue(i)),this.updateValue(i,!0)),this.$emit("blur",t)},focus:function(){this.disabled||_o(this.$el)},blur:function(){this.disabled||Do(this.$el)}}},Fd={computed:{validity:{cache:!1,get:function(){return this.$refs.input.validity}},validationMessage:{cache:!1,get:function(){return this.$refs.input.validationMessage}},willValidate:{cache:!1,get:function(){return this.$refs.input.willValidate}}},methods:{setCustomValidity:function(){var t;return(t=this.$refs.input).setCustomValidity.apply(t,arguments)},checkValidity:function(){var t;return(t=this.$refs.input).checkValidity.apply(t,arguments)},reportValidity:function(){var t;return(t=this.$refs.input).reportValidity.apply(t,arguments)}}},Id=["text","password","email","number","url","tel","search","range","color","date","time","datetime","datetime-local","month","week"],Od=i.default.extend({name:ai,mixins:[Nr,vs,Nc,Wc,Uc,Dd,$d,Fd],props:xn(u(u(u(u(u({},Rc),Gc),Yc),_d),{},{type:{type:String,default:"text",validator:function(t){return _n(Id,t)}},noWheel:{type:Boolean,default:!1},min:{type:[String,Number]},max:{type:[String,Number]},step:{type:[String,Number]},list:{type:String}}),ai),computed:{localType:function(){return _n(Id,this.type)?this.type:"text"},computedAttrs:function(){var t=this.localType,e=this.disabled,i=this.placeholder,n=this.required,o=this.min,r=this.max,a=this.step;return{id:this.safeId(),name:this.name||null,form:this.form||null,type:t,disabled:e,placeholder:i,required:n,autocomplete:this.autocomplete||null,readonly:this.readonly||this.plaintext,min:o,max:r,step:a,list:"password"!==t?this.list:null,"aria-required":n?"true":null,"aria-invalid":this.computedAriaInvalid}},computedListeners:function(){return u(u({},this.bvListeners),{},{input:this.onInput,change:this.onChange,blur:this.onBlur})}},watch:{noWheel:function(t){this.setWheelStopper(t)}},mounted:function(){this.setWheelStopper(this.noWheel)},deactivated:function(){this.setWheelStopper(!1)},activated:function(){this.setWheelStopper(this.noWheel)},beforeDestroy:function(){this.setWheelStopper(!1)},methods:{setWheelStopper:function(t){var e=this.$el;Ko(t,e,"focus",this.onWheelFocus),Ko(t,e,"blur",this.onWheelBlur),t||qo(document,"wheel",this.stopWheel)},onWheelFocus:function(){Uo(document,"wheel",this.stopWheel)},onWheelBlur:function(){qo(document,"wheel",this.stopWheel)},stopWheel:function(t){Xo(t,{propagation:!1}),Do(this.$el)}},render:function(t){return t("input",{ref:"input",class:this.computedClass,attrs:this.computedAttrs,domProps:{value:this.localValue},on:this.computedListeners})}}),Pd=de({components:{BFormInput:Od,BInput:Od}}),Ad=xn(u(u(u(u(u(u({},Rc),kc),Xc),Gc),Yc),{},{checked:{default:null}}),ui),Vd=i.default.extend({name:ui,mixins:[vs,Nc,Zc,Bc,Wc,Uc],provide:function(){return{bvRadioGroup:this}},props:Ad,data:function(){return{localChecked:this.checked}},computed:{isRadioGroup:function(){return!0}}}),Ed=de({components:{BFormRadio:Kc,BRadio:Kc,BFormRadioGroup:Vd,BRadioGroup:Vd}}),Ld=function(t){return nr(3,Pn(t,5))},Rd=function(t,e,i){return nr(ir(t,i),e)},Nd=i.default.extend({name:"BVFormRatingStar",mixins:[Wo],props:{rating:{type:Number,default:0},star:{type:Number,default:0},focused:{type:Boolean,default:!1},variant:{type:String},disabled:{type:Boolean,default:!1},readonly:{type:Boolean,default:!1},hasClear:{type:Boolean,default:!1}},methods:{onClick:function(t){this.disabled||this.readonly||(Xo(t,{propagation:!1}),this.$emit("selected",this.star))}},render:function(t){var e=this.rating,i=this.star,n=this.focused,o=this.hasClear,r=this.variant,a=this.disabled,s=this.readonly,l=o?0:1,u=e>=i?"full":e>=i-.5?"half":"empty",c={variant:r,disabled:a,readonly:s};return t("span",{staticClass:"b-rating-star",class:{focused:n&&e===i||!Pn(e)&&i===l,"b-rating-star-empty":"empty"===u,"b-rating-star-half":"half"===u,"b-rating-star-full":"full"===u},attrs:{tabindex:a||s?null:"-1"},on:{click:this.onClick}},[t("span",{staticClass:"b-rating-icon"},[this.normalizeSlot(u,c)])])}}),Md=i.default.extend({name:ci,components:{BIconStar:Sa,BIconStarHalf:Ta,BIconStarFill:wa,BIconX:Ca},mixins:[vs,Wc],model:{prop:"value",event:"change"},props:xn(u(u(u({},Tt(Rc,["required","autofocus"])),Gc),{},{value:{type:[Number,String],default:null},stars:{type:[Number,String],default:5,validator:function(t){return Pn(t)>=3}},variant:{type:String},color:{type:String},showValue:{type:Boolean,default:!1},showValueMax:{type:Boolean,default:!1},readonly:{type:Boolean,default:!1},noBorder:{type:Boolean,default:!1},inline:{type:Boolean,default:!1},precision:{type:[Number,String],default:null},iconEmpty:{type:String,default:"star"},iconHalf:{type:String,default:"star-half"},iconFull:{type:String,default:"star-fill"},iconClear:{type:String,default:"x"},locale:{type:[String,Array]},showClear:{type:Boolean,default:!1}}),ci),data:function(){var t=An(this.value,null),e=Ld(this.stars);return{localValue:tt(t)?null:Rd(t,0,e),hasFocus:!1}},computed:{computedStars:function(){return Ld(this.stars)},computedRating:function(){var t=An(this.localValue,0),e=Pn(this.precision,3);return Rd(An(t.toFixed(e)),0,this.computedStars)},computedLocale:function(){var t=Dn(this.locale).filter(ee);return new Intl.NumberFormat(t).resolvedOptions().locale},isInteractive:function(){return!this.disabled&&!this.readonly},isRTL:function(){return gs(this.computedLocale)},formattedRating:function(){var t=Pn(this.precision),e=this.showValueMax,i=this.computedLocale,n={notation:"standard",minimumFractionDigits:isNaN(t)?0:t,maximumFractionDigits:isNaN(t)?3:t},o=this.computedStars.toLocaleString(i),r=this.localValue;return r=tt(r)?e?"-":"":r.toLocaleString(i,n),e?"".concat(r,"/").concat(o):r}},watch:{value:function(t,e){if(t!==e){var i=An(t,null);this.localValue=tt(i)?null:Rd(i,0,this.computedStars)}},localValue:function(t,e){t!==e&&t!==(this.value||0)&&this.$emit("change",t||null)},disabled:function(t){t&&(this.hasFocus=!1,this.blur())}},methods:{focus:function(){this.disabled||_o(this.$el)},blur:function(){this.disabled||Do(this.$el)},onKeydown:function(t){var e=t.keyCode;if(this.isInteractive&&_n([Dr,Br,Fr,Or],e)){Xo(t,{propagation:!1});var i=Pn(this.localValue,0),n=this.showClear?0:1,o=this.computedStars,r=this.isRTL?-1:1;e===Dr?this.localValue=Rd(i-r,n,o)||null:e===Fr?this.localValue=Rd(i+r,n,o):e===Br?this.localValue=Rd(i-1,n,o)||null:e===Or&&(this.localValue=Rd(i+1,n,o))}},onSelected:function(t){this.isInteractive&&(this.localValue=t)},onFocus:function(t){this.hasFocus=!!this.isInteractive&&"focus"===t.type},renderIcon:function(t){return this.$createElement(Ba,{props:{icon:t,variant:this.disabled||this.color?null:this.variant||null}})},iconEmptyFn:function(){return this.renderIcon(this.iconEmpty)},iconHalfFn:function(){return this.renderIcon(this.iconHalf)},iconFullFn:function(){return this.renderIcon(this.iconFull)},iconClearFn:function(){return this.$createElement(Ba,{props:{icon:this.iconClear}})}},render:function(t){var e=this,i=this.disabled,n=this.readonly,o=this.name,r=this.form,a=this.inline,s=this.variant,l=this.color,u=this.noBorder,c=this.hasFocus,d=this.computedRating,h=this.computedStars,f=this.formattedRating,p=this.showClear,m=this.isRTL,g=this.isInteractive,v=this.$scopedSlots,b=[];if(p&&!i&&!n){var y=t("span",{staticClass:"b-rating-icon"},[(v["icon-clear"]||this.iconClearFn)()]);b.push(t("span",{staticClass:"b-rating-star b-rating-star-clear flex-grow-1",class:{focused:c&&0===d},attrs:{tabindex:g?"-1":null},on:{click:function(){return e.onSelected(null)}},key:"clear"},[y]))}for(var S=0;S<h;S++){var w=S+1;b.push(t(Nd,{staticClass:"flex-grow-1",style:l&&!i?{color:l}:{},props:{rating:d,star:w,variant:i?null:s||null,disabled:i,readonly:n,focused:c,hasClear:p},on:{selected:this.onSelected},scopedSlots:{empty:v["icon-empty"]||this.iconEmptyFn,half:v["icon-half"]||this.iconHalfFn,full:v["icon-full"]||this.iconFullFn},key:S}))}return o&&b.push(t("input",{attrs:{type:"hidden",value:tt(this.localValue)?"":d,name:o,form:r||null},key:"hidden"})),this.showValue&&b.push(t("b",{staticClass:"b-rating-value flex-grow-1",attrs:{"aria-hidden":"true"},key:"value"},Hn(f))),t("output",{staticClass:"b-rating form-control align-items-center",class:[{"d-inline-flex":a,"d-flex":!a,"border-0":u,disabled:i,readonly:!i&&n},this.sizeFormClass],attrs:{id:this.safeId(),dir:m?"rtl":"ltr",tabindex:i?null:"0",disabled:i,role:"slider","aria-disabled":i?"true":null,"aria-readonly":!i&&n?"true":null,"aria-live":"off","aria-valuemin":p?"0":"1","aria-valuemax":Hn(h),"aria-valuenow":d?Hn(d):null},on:{keydown:this.onKeydown,focus:this.onFocus,blur:this.onFocus}},b)}}),Hd=de({components:{BFormRating:Md,BRating:Md}}),zd={mixins:[Bc],props:xn(u(u({},kc),{},{labelField:{type:String,default:"label"},optionsField:{type:String,default:"options"}}),"formOptions"),methods:{normalizeOption:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if(ut(t)){var i=ne(t,this.valueField),n=ne(t,this.textField),o=ne(t,this.optionsField,null);return tt(o)?{value:Q(i)?e||n:i,text:String(Q(n)?e:n),html:ne(t,this.htmlField),disabled:Boolean(ne(t,this.disabledField))}:{label:String(ne(t,this.labelField)||n),options:this.normalizeOptions(o)}}return{value:e||t,text:String(t),disabled:!1}}}},jd=xn({value:{required:!0},disabled:{type:Boolean,default:!1}},fi),Gd=i.default.extend({name:fi,functional:!0,props:jd,render:function(t,e){var i=e.props,n=e.data,o=e.children,r=i.value;return t("option",F(n,{attrs:{disabled:i.disabled},domProps:{value:r}}),o)}}),Wd=i.default.extend({name:pi,mixins:[Wo,Bc],props:xn(u(u({},kc),{},{label:{type:String,required:!0}}),pi),render:function(t){var e=this.formOptions.map((function(e,i){var n=e.value,o=e.text,r=e.html,a=e.disabled;return t(Gd,{attrs:{value:n,disabled:a},domProps:Ea(r,o),key:"option_".concat(i)})}));return t("optgroup",{attrs:{label:this.label}},[this.normalizeSlot(Eo),e,this.normalizeSlot()])}}),Yd=i.default.extend({name:hi,mixins:[vs,Wo,Nc,Wc,Uc,Hc,zd],model:{prop:"value",event:"input"},props:xn(u(u(u(u(u({},Rc),Mc),Gc),Yc),{},{value:{},multiple:{type:Boolean,default:!1},selectSize:{type:Number,default:0},ariaInvalid:{type:[Boolean,String],default:!1}}),hi),data:function(){return{localValue:this.value}},computed:{computedSelectSize:function(){return this.plain||0!==this.selectSize?this.selectSize:null},inputClass:function(){return[this.plain?"form-control":"custom-select",this.size&&this.plain?"form-control-".concat(this.size):null,this.size&&!this.plain?"custom-select-".concat(this.size):null,this.stateClass]},computedAriaInvalid:function(){return!0===this.ariaInvalid||"true"===this.ariaInvalid||"is-invalid"===this.stateClass?"true":null}},watch:{value:function(t){this.localValue=t},localValue:function(){this.$emit("input",this.localValue)}},methods:{focus:function(){_o(this.$refs.input)},blur:function(){Do(this.$refs.input)},onChange:function(t){var e=this,i=t.target,n=$n(i.options).filter((function(t){return t.selected})).map((function(t){return"_value"in t?t._value:t.value}));this.localValue=i.multiple?n:n[0],this.$nextTick((function(){e.$emit("change",e.localValue)}))}},render:function(t){var e=this.name,i=this.disabled,n=this.required,o=this.computedSelectSize,r=this.localValue,a=this.formOptions.map((function(e,i){var n=e.value,o=e.label,r=e.options,a=e.disabled,s="option_".concat(i);return st(r)?t(Wd,{props:{label:o,options:r},key:s}):t(Gd,{props:{value:n,disabled:a},domProps:Ea(e.html,e.text),key:s})}));return t("select",{class:this.inputClass,attrs:{id:this.safeId(),name:e,form:this.form||null,multiple:this.multiple||null,size:o,disabled:i,required:n,"aria-required":n?"true":null,"aria-invalid":this.computedAriaInvalid},on:{change:this.onChange},directives:[{name:"model",value:r}],ref:"input"},[this.normalizeSlot(Eo),a,this.normalizeSlot()])}}),Ud=de({components:{BFormSelect:Yd,BFormSelectOption:Gd,BFormSelectOptionGroup:Wd,BSelect:Yd,BSelectOption:Gd,BSelectOptionGroup:Wd}}),qd=[Or,Br,_r,xr,33,34],Kd=xn(u(u(u(u({},Tt(Rc,["required","autofocus"])),Gc),Yc),{},{value:{type:Number,default:null},min:{type:[Number,String],default:1},max:{type:[Number,String],default:100},step:{type:[Number,String],default:1},wrap:{type:Boolean,default:!1},formatterFn:{type:Function},placeholder:{type:String},readonly:{type:Boolean,default:!1},inline:{type:Boolean,default:!1},vertical:{type:Boolean,default:!1},ariaLabel:{type:String},ariaControls:{type:String},labelDecrement:{type:String,default:"Decrement"},labelIncrement:{type:String,default:"Increment"},locale:{type:[String,Array]},repeatDelay:{type:[Number,String],default:500},repeatInterval:{type:[Number,String],default:100},repeatThreshold:{type:[Number,String],default:10},repeatStepMultiplier:{type:[Number,String],default:4}}),mi),Xd=i.default.extend({name:mi,mixins:[Rr,vs,Wc,Uc,Wo],inheritAttrs:!1,props:Kd,data:function(){return{localValue:An(this.value,null),hasFocus:!1}},computed:{spinId:function(){return this.safeId()},computedInline:function(){return this.inline&&!this.vertical},computedReadonly:function(){return this.readonly&&!this.disabled},computedRequired:function(){return this.required&&!this.computedReadonly&&!this.disabled},computedStep:function(){return An(this.step,1)},computedMin:function(){return An(this.min,1)},computedMax:function(){var t=An(this.max,100),e=this.computedStep,i=this.computedMin;return ar((t-i)/e)*e+i},computedDelay:function(){var t=Pn(this.repeatDelay,0);return t>0?t:500},computedInterval:function(){var t=Pn(this.repeatInterval,0);return t>0?t:100},computedThreshold:function(){return nr(Pn(this.repeatThreshold,10),1)},computedStepMultiplier:function(){return nr(Pn(this.repeatStepMultiplier,4),1)},computedPrecision:function(){var t=this.computedStep;return ar(t)===t?0:(t.toString().split(".")[1]||"").length},computedMultiplier:function(){return sr(10,this.computedPrecision||0)},valueAsFixed:function(){var t=this.localValue;return tt(t)?"":t.toFixed(this.computedPrecision)},computedLocale:function(){var t=Dn(this.locale).filter(ee);return new Intl.NumberFormat(t).resolvedOptions().locale},computedRTL:function(){return gs(this.computedLocale)},defaultFormatter:function(){var t=this.computedPrecision;return new Intl.NumberFormat(this.computedLocale,{style:"decimal",useGrouping:!1,minimumIntegerDigits:1,minimumFractionDigits:t,maximumFractionDigits:t,notation:"standard"}).format},computedFormatter:function(){var t=this.formatterFn,e=null;try{e=t()}catch(t){}return Q(e)?this.defaultFormatter:t},computedAttrs:function(){return u(u({},this.bvAttrs),{},{role:"group",lang:this.computedLocale,tabindex:this.disabled?null:"-1",title:this.ariaLabel})},computedSpinAttrs:function(){var t=this.spinId,e=this.localValue,i=this.computedRequired,n=this.disabled,o=this.state,r=this.computedFormatter,a=!tt(e);return u(u({dir:this.computedRTL?"rtl":"ltr"},this.bvAttrs),{},{id:t,role:"spinbutton",tabindex:n?null:"0","aria-live":"off","aria-label":this.ariaLabel||null,"aria-controls":this.ariaControls||null,"aria-invalid":!1===o||!a&&i?"true":null,"aria-required":i?"true":null,"aria-valuemin":Hn(this.computedMin),"aria-valuemax":Hn(this.computedMax),"aria-valuenow":a?e:null,"aria-valuetext":a?r(e):null})}},watch:{value:function(t){this.localValue=An(t,null)},localValue:function(t){this.$emit("input",t)},disabled:function(t){t&&this.clearRepeat()},readonly:function(t){t&&this.clearRepeat()}},created:function(){this.$_autoDelayTimer=null,this.$_autoRepeatTimer=null,this.$_keyIsDown=!1},beforeDestroy:function(){this.clearRepeat()},deactivated:function(){this.clearRepeat()},methods:{focus:function(){this.disabled||_o(this.$refs.spinner)},blur:function(){this.disabled||Do(this.$refs.spinner)},emitChange:function(){this.$emit("change",this.localValue)},stepValue:function(t){var e=this.localValue;if(!this.disabled&&!tt(e)){var i=this.computedStep*t,n=this.computedMin,o=this.computedMax,r=this.computedMultiplier,a=this.wrap;e=lr((e-n)/i)*i+n+i,e=lr(e*r)/r,this.localValue=e>o?a?n:o:e<n?a?o:n:e}},onFocusBlur:function(t){this.disabled?this.hasFocus=!1:this.hasFocus="focus"===t.type},stepUp:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,e=this.localValue;tt(e)?this.localValue=this.computedMin:this.stepValue(1*t)},stepDown:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,e=this.localValue;tt(e)?this.localValue=this.wrap?this.computedMax:this.computedMin:this.stepValue(-1*t)},onKeydown:function(t){var e=t.keyCode,i=t.altKey,n=t.ctrlKey,o=t.metaKey;if(!(this.disabled||this.readonly||i||n||o)&&_n(qd,e)){if(Xo(t,{propagation:!1}),this.$_keyIsDown)return;this.resetTimers(),_n([Or,Br],e)?(this.$_keyIsDown=!0,e===Or?this.handleStepRepeat(t,this.stepUp):e===Br&&this.handleStepRepeat(t,this.stepDown)):33===e?this.stepUp(this.computedStepMultiplier):34===e?this.stepDown(this.computedStepMultiplier):e===_r?this.localValue=this.computedMin:e===xr&&(this.localValue=this.computedMax)}},onKeyup:function(t){var e=t.keyCode,i=t.altKey,n=t.ctrlKey,o=t.metaKey;this.disabled||this.readonly||i||n||o||_n(qd,e)&&(Xo(t,{propagation:!1}),this.resetTimers(),this.$_keyIsDown=!1,this.emitChange())},handleStepRepeat:function(t,e){var i=this,n=t||{},o=n.type,r=n.button;if(!this.disabled&&!this.readonly){if("mousedown"===o&&r)return;this.resetTimers(),e(1);var a=this.computedThreshold,s=this.computedStepMultiplier,l=this.computedDelay,u=this.computedInterval;this.$_autoDelayTimer=setTimeout((function(){var t=0;i.$_autoRepeatTimer=setInterval((function(){e(t<a?1:s),t++}),u)}),l)}},onMouseup:function(t){var e=t||{},i=e.type,n=e.button;"mouseup"===i&&n||(Xo(t,{propagation:!1}),this.resetTimers(),this.setMouseup(!1),this.emitChange())},setMouseup:function(t){try{Ko(t,document.body,"mouseup",this.onMouseup,!1),Ko(t,document.body,"touchend",this.onMouseup,!1)}catch(t){}},resetTimers:function(){clearTimeout(this.$_autoDelayTimer),clearInterval(this.$_autoRepeatTimer),this.$_autoDelayTimer=null,this.$_autoRepeatTimer=null},clearRepeat:function(){this.resetTimers(),this.setMouseup(!1),this.$_keyIsDown=!1}},render:function(t){var e=this,i=this.spinId,n=this.localValue,o=this.computedInline,r=this.computedReadonly,a=this.vertical,s=this.disabled,l=this.computedFormatter,u=!tt(n),c=function(n,o,l,u,c,d,h){var f=t(l,{props:{scale:e.hasFocus?1.5:1.25},attrs:{"aria-hidden":"true"}}),p={hasFocus:e.hasFocus},m=function(t){s||r||(Xo(t,{propagation:!1}),e.setMouseup(!0),_o(t.currentTarget),e.handleStepRepeat(t,n))};return t("button",{key:u||null,ref:u,staticClass:"btn btn-sm border-0 rounded-0",class:{"py-0":!a},attrs:{tabindex:"-1",type:"button",disabled:s||r||d,"aria-disabled":s||r||d?"true":null,"aria-controls":i,"aria-label":o||null,"aria-keyshortcuts":c||null},on:{mousedown:m,touchstart:m}},[t("div",[e.normalizeSlot(h,p)||f])])},d=c(this.stepUp,this.labelIncrement,ya,"inc","ArrowUp",!1,"increment"),h=c(this.stepDown,this.labelDecrement,va,"dec","ArrowDown",!1,"decrement"),f=t();this.name&&!s&&(f=t("input",{key:"hidden",attrs:{type:"hidden",name:this.name,form:this.form||null,value:this.valueAsFixed}}));var p=t("output",{ref:"spinner",key:"output",staticClass:"flex-grow-1",class:{"d-flex":a,"align-self-center":!a,"align-items-center":a,"border-top":a,"border-bottom":a,"border-left":!a,"border-right":!a},attrs:this.computedSpinAttrs},[t("bdi",u?l(n):this.placeholder||"")]);return t("div",{staticClass:"b-form-spinbutton form-control",class:[{disabled:s,readonly:r,focus:this.hasFocus,"d-inline-flex":o||a,"d-flex":!o&&!a,"align-items-stretch":!a,"flex-column":a},this.sizeFormClass,this.stateClass],attrs:this.computedAttrs,on:{keydown:this.onKeydown,keyup:this.onKeyup,"!focus":this.onFocusBlur,"!blur":this.onFocusBlur}},a?[d,f,p,h]:[h,f,p,d])}}),Zd=de({components:{BFormSpinbutton:Xd,BSpinbutton:Xd}}),Jd=i.default.extend({name:gi,mixins:[vs,Wo],props:xn({variant:{type:String,default:"secondary"},disabled:{type:Boolean,default:!1},title:{type:String},pill:{type:Boolean,default:!1},removeLabel:{type:String,default:"Remove tag"},tag:{type:String,default:"span"}},gi),methods:{onDelete:function(t){var e=t.type,i=t.keyCode;this.disabled||"click"!==e&&("keydown"!==e||46!==i)||this.$emit("remove")}},render:function(t){var e=this.safeId(),i=this.safeId("_taglabel_"),n=t();this.disabled||(n=t(Jo,{staticClass:"b-form-tag-remove",props:{ariaLabel:this.removeLabel},attrs:{"aria-controls":e,"aria-describedby":i,"aria-keyshortcuts":"Delete"},on:{click:this.onDelete,keydown:this.onDelete}}));var o=t("span",{staticClass:"b-form-tag-content flex-grow-1 text-truncate",attrs:{id:i}},this.normalizeSlot()||this.title||[t()]);return t(Pa,{staticClass:"b-form-tag d-inline-flex align-items-baseline mw-100",class:{disabled:this.disabled},attrs:{id:e,title:this.title||null,"aria-labelledby":i},props:{tag:this.tag,variant:this.variant,pill:this.pill}},[o,n])}}),Qd=["text","email","tel","url","number"],th=function(t){return Dn(t).map((function(t){return zn(Hn(t))})).filter((function(t,e,i){return t.length>0&&i.indexOf(t)===e}))},eh=function(t){return ot(t)?t:dt(t)&&t.target.value||""},ih=i.default.extend({name:vi,mixins:[vs,Nc,Wc,Uc,Wo],model:{prop:"value",event:"input"},props:xn(u(u(u(u({},Rc),Gc),Yc),{},{value:{type:Array,default:function(){return[]}},placeholder:{type:String,default:"Add tag..."},inputId:{type:String},inputType:{type:String,default:"text",validator:function(t){return _n(Qd,t)}},inputClass:{type:[String,Array,Object]},inputAttrs:{type:Object,default:function(){return{}}},addButtonText:{type:String,default:"Add"},addButtonVariant:{type:String,default:"outline-secondary"},tagVariant:{type:String,default:"secondary"},tagClass:{type:[String,Array,Object]},tagPills:{type:Boolean,default:!1},tagRemoveLabel:{type:String,default:"Remove tag"},tagRemovedLabel:{type:String,default:"Tag removed"},tagValidator:{type:Function},duplicateTagText:{type:String,default:"Duplicate tag(s)"},invalidTagText:{type:String,default:"Invalid tag(s)"},limitTagsText:{type:String,default:"Tag limit reached"},limit:{type:Number},separator:{type:[String,Array]},removeOnDelete:{type:Boolean,default:!1},addOnChange:{type:Boolean,default:!1},noAddOnEnter:{type:Boolean,default:!1},noOuterFocus:{type:Boolean,default:!1},ignoreInputFocusSelector:{type:[Array,String],default:function(){return[".b-form-tag","button","input","select"]}}}),vi),data:function(){return{hasFocus:!1,newTag:"",tags:[],removedTags:[],tagsState:{all:[],valid:[],invalid:[],duplicate:[]}}},computed:{computedInputId:function(){return this.inputId||this.safeId("__input__")},computedInputType:function(){return _n(Qd,this.inputType)?this.inputType:"text"},computedInputAttrs:function(){return u(u({},this.inputAttrs),{},{id:this.computedInputId,value:this.newTag,disabled:this.disabled||null,form:this.form||null})},computedInputHandlers:function(){return{input:this.onInputInput,change:this.onInputChange,keydown:this.onInputKeydown}},computedSeparator:function(){return Dn(this.separator).filter(ot).filter(ee).join("")},computedSeparatorRegExp:function(){var t=this.computedSeparator;return t?new RegExp("[".concat(Mn(t).replace(Et,"\\s"),"]+")):null},computedJoiner:function(){var t=this.computedSeparator.charAt(0);return" "!==t?"".concat(t," "):t},computeIgnoreInputFocusSelector:function(){return Dn(this.ignoreInputFocusSelector).filter(ee).join(",").trim()},disableAddButton:function(){var t=this,e=zn(this.newTag);return""===e||!this.splitTags(e).some((function(e){return!_n(t.tags,e)&&t.validateTag(e)}))},duplicateTags:function(){return this.tagsState.duplicate},hasDuplicateTags:function(){return this.duplicateTags.length>0},invalidTags:function(){return this.tagsState.invalid},hasInvalidTags:function(){return this.invalidTags.length>0},isLimitReached:function(){var t=this.limit;return rt(t)&&t>=0&&this.tags.length>=t}},watch:{value:function(t){this.tags=th(t)},tags:function(t,e){Ar(t,this.value)||this.$emit("input",t),Ar(t,e)||(t=Dn(t).filter(ee),e=Dn(e).filter(ee),this.removedTags=e.filter((function(e){return!_n(t,e)})))},tagsState:function(t,e){Ar(t,e)||this.$emit("tag-state",t.valid,t.invalid,t.duplicate)}},created:function(){this.tags=th(this.value)},methods:{addTag:function(t){if(t=ot(t)?t:this.newTag,!this.disabled&&""!==zn(t)&&!this.isLimitReached){var e=this.parseTags(t);if(e.valid.length>0||0===e.all.length)if(so(this.getInput(),"select"))this.newTag="";else{var i=[].concat(w(e.invalid),w(e.duplicate));this.newTag=e.all.filter((function(t){return _n(i,t)})).join(this.computedJoiner).concat(i.length>0?this.computedJoiner.charAt(0):"")}e.valid.length>0&&(this.tags=Dn(this.tags,e.valid)),this.tagsState=e,this.focus()}},removeTag:function(t){var e=this;this.disabled||(this.tags=this.tags.filter((function(e){return e!==t})),this.$nextTick((function(){e.focus()})))},onInputInput:function(t){if(!(this.disabled||dt(t)&&t.target.composing)){var e=eh(t),i=this.computedSeparatorRegExp;this.newTag!==e&&(this.newTag=e),e=Hn(e).replace(Mt,""),i&&i.test(e.slice(-1))?this.addTag():this.tagsState=""===e?{all:[],valid:[],invalid:[],duplicate:[]}:this.parseTags(e)}},onInputChange:function(t){if(!this.disabled&&this.addOnChange){var e=eh(t);this.newTag!==e&&(this.newTag=e),this.addTag()}},onInputKeydown:function(t){if(!this.disabled&&dt(t)){var e=t.keyCode,i=t.target.value||"";this.noAddOnEnter||e!==$r?!this.removeOnDelete||8!==e&&46!==e||""!==i||(Xo(t,{propagation:!1}),this.tags=this.tags.slice(0,-1)):(Xo(t,{propagation:!1}),this.addTag())}},onClick:function(t){var e=this,i=this.computeIgnoreInputFocusSelector,n=t.target;this.disabled||eo(n)||i&&lo(i,n,!0)||this.$nextTick((function(){e.focus()}))},onFocusin:function(){this.hasFocus=!0},onFocusout:function(){this.hasFocus=!1},handleAutofocus:function(){var t=this;this.$nextTick((function(){Xn((function(){t.autofocus&&!t.disabled&&t.focus()}))}))},focus:function(){this.disabled||_o(this.getInput())},blur:function(){this.disabled||Do(this.getInput())},splitTags:function(t){t=Hn(t);var e=this.computedSeparatorRegExp;return(e?t.split(e):[t]).map(zn).filter(ee)},parseTags:function(t){var e=this,i=this.splitTags(t),n={all:i,valid:[],invalid:[],duplicate:[]};return i.forEach((function(t){_n(e.tags,t)||_n(n.valid,t)?_n(n.duplicate,t)||n.duplicate.push(t):e.validateTag(t)?n.valid.push(t):_n(n.invalid,t)||n.invalid.push(t)})),n},validateTag:function(t){var e=this.tagValidator,i=null;try{i=e()}catch(t){}return!!Q(i)||e(t)},getInput:function(){return ao("#".concat(gd(this.computedInputId)),this.$el)},defaultRender:function(t){var e=t.tags,i=t.inputAttrs,n=t.inputType,o=t.inputHandlers,r=t.removeTag,a=t.addTag,s=t.isInvalid,l=t.isDuplicate,c=t.isLimitReached,d=t.disableAddButton,h=t.disabled,f=t.placeholder,p=t.inputClass,m=t.tagRemoveLabel,g=t.tagVariant,v=t.tagPills,b=t.tagClass,y=t.addButtonText,S=t.addButtonVariant,w=t.invalidTagText,T=t.duplicateTagText,C=t.limitTagsText,k=this.$createElement,B=e.map((function(t){return t=Hn(t),k(Jd,{class:b,props:{tag:"li",title:t,disabled:h,variant:g,pill:v,removeLabel:m},on:{remove:function(){return r(t)}},key:"tags_".concat(t)},t)})),x=w&&s?this.safeId("__invalid_feedback__"):null,$=T&&l?this.safeId("__duplicate_feedback__"):null,_=C&&c?this.safeId("__limit_feedback__"):null,D=[i["aria-describedby"],x,$,_].filter(ee).join(" "),F=k("input",{ref:"input",directives:[{name:"model",value:i.value}],staticClass:"b-form-tags-input w-100 flex-grow-1 p-0 m-0 bg-transparent border-0",class:p,style:{outline:0,minWidth:"5rem"},attrs:u(u({},i),{},{"aria-describedby":D||null,type:n,placeholder:f||null}),domProps:{value:i.value},on:o}),I=k(ta,{ref:"button",staticClass:"b-form-tags-button py-0",class:{invisible:d},style:{fontSize:"90%"},props:{variant:S,disabled:d||c},on:{click:function(){return a()}}},[this.normalizeSlot("add-button-text")||y]),O=this.safeId("__tag_list__"),P=k("li",{staticClass:"b-from-tags-field flex-grow-1",attrs:{role:"none","aria-live":"off","aria-controls":O},key:"tags_field"},[k("div",{staticClass:"d-flex",attrs:{role:"group"}},[F,I])]),A=k("ul",{staticClass:"b-form-tags-list list-unstyled mb-0 d-flex flex-wrap align-items-center",attrs:{id:O},key:"tags_list"},[B,P]),V=k();if(w||T||C){var E=this.computedJoiner,L=k();x&&(L=k(Fc,{props:{id:x,forceShow:!0},key:"tags_invalid_feedback"},[this.invalidTagText,": ",this.invalidTags.join(E)]));var R=k();$&&(R=k(_c,{props:{id:$},key:"tags_duplicate_feedback"},[this.duplicateTagText,": ",this.duplicateTags.join(E)]));var N=k();_&&(N=k(_c,{props:{id:_},key:"tags_limit_feedback"},[C])),V=k("div",{attrs:{"aria-live":"polite","aria-atomic":"true"},key:"tags_feedback"},[L,R,N])}return[A,V]}},render:function(t){var e=this,i=this.name,n=this.disabled,o=this.tags,r=this.computedInputId,a=this.hasFocus,s=this.noOuterFocus,l=u({tags:o.slice(),inputAttrs:this.computedInputAttrs,inputType:this.computedInputType,inputHandlers:this.computedInputHandlers,removeTag:this.removeTag,addTag:this.addTag,inputId:r,isInvalid:this.hasInvalidTags,invalidTags:this.invalidTags.slice(),isDuplicate:this.hasDuplicateTags,duplicateTags:this.duplicateTags.slice(),isLimitReached:this.isLimitReached,disableAddButton:this.disableAddButton},wt(this.$props,["disabled","state","size","limit","separator","placeholder","inputClass","tagRemoveLabel","tagVariant","tagPills","tagClass","addButtonText","addButtonVariant","invalidTagText","duplicateTagText","limitTagsText"])),c=this.normalizeSlot(Vo,l)||this.defaultRender(l),d=t("output",{staticClass:"sr-only",attrs:{id:this.safeId("__selected_tags__"),role:"status",for:r,"aria-live":a?"polite":"off","aria-atomic":"true","aria-relevant":"additions text"}},this.tags.join(", ")),h=t("div",{staticClass:"sr-only",attrs:{id:this.safeId("__removed_tags__"),role:"status","aria-live":a?"assertive":"off","aria-atomic":"true"}},this.removedTags.length>0?"(".concat(this.tagRemovedLabel,") ").concat(this.removedTags.join(", ")):""),f=t();return i&&!n&&(f=o.map((function(n){return t("input",{attrs:{type:"hidden",value:n,name:i,form:e.form||null},key:"tag_input_".concat(n)})}))),t("div",{staticClass:"b-form-tags form-control h-auto",class:[{focus:a&&!s&&!n,disabled:n},this.sizeFormClass,this.stateClass],attrs:{id:this.safeId(),role:"group",tabindex:n||s?null:"-1","aria-describedby":this.safeId("__selected_tags__")},on:{click:this.onClick,focusin:this.onFocusin,focusout:this.onFocusout}},[d,h,c,f])}}),nh=de({components:{BFormTags:ih,BTags:ih,BFormTag:Jd,BTag:Jd}}),oh=i.default.extend({name:yi,directives:{"b-visible":Hs},mixins:[Nr,vs,pl,Nc,Wc,Uc,Dd,$d,Fd],props:xn(u(u(u(u(u({},Rc),Gc),Yc),_d),{},{rows:{type:[Number,String],default:2},maxRows:{type:[Number,String]},wrap:{type:String,default:"soft"},noResize:{type:Boolean,default:!1},noAutoShrink:{type:Boolean,default:!1}}),yi),data:function(){return{heightInPx:null}},computed:{computedStyle:function(){var t={resize:!this.computedRows||this.noResize?"none":null};return this.computedRows||(t.height=this.heightInPx,t.overflowY="scroll"),t},computedMinRows:function(){return nr(Pn(this.rows,2),2)},computedMaxRows:function(){return nr(this.computedMinRows,Pn(this.maxRows,0))},computedRows:function(){return this.computedMinRows===this.computedMaxRows?this.computedMinRows:null},computedAttrs:function(){var t=this.disabled,e=this.required;return{id:this.safeId(),name:this.name||null,form:this.form||null,disabled:t,placeholder:this.placeholder||null,required:e,autocomplete:this.autocomplete||null,readonly:this.readonly||this.plaintext,rows:this.computedRows,wrap:this.wrap||null,"aria-required":this.required?"true":null,"aria-invalid":this.computedAriaInvalid}},computedListeners:function(){return u(u({},this.bvListeners),{},{input:this.onInput,change:this.onChange,blur:this.onBlur})}},watch:{localValue:function(){this.setHeight()}},mounted:function(){this.setHeight()},methods:{visibleCallback:function(t){t&&this.$nextTick(this.setHeight)},setHeight:function(){var t=this;this.$nextTick((function(){Xn((function(){t.heightInPx=t.computeHeight()}))}))},computeHeight:function(){if(this.$isServer||!tt(this.computedRows))return null;var t=this.$el;if(!io(t))return null;var e=Co(t),i=An(e.lineHeight,1),n=An(e.borderTopWidth,0)+An(e.borderBottomWidth,0),o=An(e.paddingTop,0)+An(e.paddingBottom,0),r=n+o,a=i*this.computedMinRows+r,s=wo(t,"height")||e.height;yo(t,"height","auto");var l=t.scrollHeight;yo(t,"height",s);var u=nr((l-o)/i,2),c=ir(nr(u,this.computedMinRows),this.computedMaxRows),d=nr(rr(c*i+r),a);return this.noAutoShrink&&An(s,0)>d?s:"".concat(d,"px")}},render:function(t){return t("textarea",{ref:"input",class:this.computedClass,style:this.computedStyle,directives:[{name:"b-visible",value:this.visibleCallback,modifiers:{640:!0}}],attrs:this.computedAttrs,domProps:{value:this.localValue},on:this.computedListeners})}}),rh=de({components:{BFormTextarea:oh,BTextarea:oh}}),ah="numeric",sh=function(t){return"00".concat(t||"").slice(-2)},lh=function(t){t=Hn(t);var e=null,i=null,n=null;if(Wt.test(t)){var o=S(t.split(":").map((function(t){return Pn(t,null)})),3);e=o[0],i=o[1],n=o[2]}return{hours:et(e)?null:e,minutes:et(i)?null:i,seconds:et(n)?null:n,ampm:et(e)||e<12?0:1}},uh=xn(u(u({value:{type:String,default:""},showSeconds:{type:Boolean,default:!1},hour12:{type:Boolean,default:null},locale:{type:[String,Array]},ariaLabelledby:{type:String},secondsStep:{type:[Number,String],default:1},minutesStep:{type:[Number,String],default:1},disabled:{type:Boolean,default:!1},readonly:{type:Boolean,default:!1},hideHeader:{type:Boolean,default:!1},labelNoTimeSelected:{type:String,default:"No time selected"},labelSelected:{type:String,default:"Selected time"},labelHours:{type:String,default:"Hours"},labelMinutes:{type:String,default:"Minutes"},labelSeconds:{type:String,default:"Seconds"},labelAmpm:{type:String,default:"AM/PM"},labelAm:{type:String,default:"AM"},labelPm:{type:String,default:"PM"}},wt(Kd,["labelIncrement","labelDecrement"])),{},{hidden:{type:Boolean,default:!1}}),mn),ch=i.default.extend({name:mn,mixins:[vs,Wo],model:{prop:"value",event:"input"},props:uh,data:function(){var t=lh(this.value||"");return{modelHours:t.hours,modelMinutes:t.minutes,modelSeconds:t.seconds,modelAmpm:t.ampm,isLive:!1}},computed:{computedHMS:function(){return function(t){var e=t.hours,i=t.minutes,n=t.seconds,o=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return tt(e)||tt(i)||o&&tt(n)?"":[e,i,o?n:0].map(sh).join(":")}({hours:this.modelHours,minutes:this.modelMinutes,seconds:this.modelSeconds},this.showSeconds)},resolvedOptions:function(){var t=Dn(this.locale).filter(ee),e={hour:ah,minute:ah,second:ah};et(this.hour12)||(e.hour12=!!this.hour12);var i=new Intl.DateTimeFormat(t,e).resolvedOptions(),n=i.hour12||!1,o=i.hourCycle||(n?"h12":"h23");return{locale:i.locale,hour12:n,hourCycle:o}},computedLocale:function(){return this.resolvedOptions.locale},computedLang:function(){return(this.computedLocale||"").replace(/-u-.*$/,"")},computedRTL:function(){return gs(this.computedLang)},computedHourCycle:function(){return this.resolvedOptions.hourCycle},is12Hour:function(){return!!this.resolvedOptions.hour12},context:function(){return{locale:this.computedLocale,isRTL:this.computedRTL,hourCycle:this.computedHourCycle,hour12:this.is12Hour,hours:this.modelHours,minutes:this.modelMinutes,seconds:this.showSeconds?this.modelSeconds:0,value:this.computedHMS,formatted:this.formattedTimeString}},valueId:function(){return this.safeId()||null},computedAriaLabelledby:function(){return[this.ariaLabelledby,this.valueId].filter(ee).join(" ")||null},timeFormatter:function(){var t={hour12:this.is12Hour,hourCycle:this.computedHourCycle,hour:ah,minute:ah,timeZone:"UTC"};return this.showSeconds&&(t.second=ah),ns(this.computedLocale,t)},numberFormatter:function(){return new Intl.NumberFormat(this.computedLocale,{style:"decimal",minimumIntegerDigits:2,minimumFractionDigits:0,maximumFractionDigits:0,notation:"standard"}).format},formattedTimeString:function(){var t=this.modelHours,e=this.modelMinutes,i=this.showSeconds&&this.modelSeconds||0;return this.computedHMS?this.timeFormatter(ts(Date.UTC(0,0,1,t,e,i))):this.labelNoTimeSelected||" "},spinScopedSlots:function(){var t=this.$createElement;return{increment:function(e){var i=e.hasFocus;return t(fa,{props:{scale:i?1.5:1.25},attrs:{"aria-hidden":"true"}})},decrement:function(e){var i=e.hasFocus;return t(fa,{props:{flipV:!0,scale:i?1.5:1.25},attrs:{"aria-hidden":"true"}})}}}},watch:{value:function(t,e){if(t!==e&&!Ar(lh(t),lh(this.computedHMS))){var i=lh(t),n=i.hours,o=i.minutes,r=i.seconds,a=i.ampm;this.modelHours=n,this.modelMinutes=o,this.modelSeconds=r,this.modelAmpm=a}},computedHMS:function(t,e){t!==e&&this.$emit("input",t)},context:function(t,e){Ar(t,e)||this.$emit("context",t)},modelAmpm:function(t,e){var i=this;if(t!==e){var n=tt(this.modelHours)?0:this.modelHours;this.$nextTick((function(){0===t&&n>11?i.modelHours=n-12:1===t&&n<12&&(i.modelHours=n+12)}))}},modelHours:function(t,e){t!==e&&(this.modelAmpm=t>11?1:0)}},created:function(){var t=this;this.$nextTick((function(){t.$emit("context",t.context)}))},mounted:function(){this.setLive(!0)},activated:function(){this.setLive(!0)},deactivated:function(){this.setLive(!1)},beforeDestroy:function(){this.setLive(!1)},methods:{focus:function(){this.disabled||_o(this.$refs.spinners[0])},blur:function(){if(!this.disabled){var t=Qn();uo(this.$el,t)&&Do(t)}},formatHours:function(t){var e=this.computedHourCycle;return t=0===(t=this.is12Hour&&t>12?t-12:t)&&"h12"===e?12:0===t&&"h24"===e?24:12===t&&"h11"===e?0:t,this.numberFormatter(t)},formatMinutes:function(t){return this.numberFormatter(t)},formatSeconds:function(t){return this.numberFormatter(t)},formatAmpm:function(t){return 0===t?this.labelAm:1===t?this.labelPm:""},setHours:function(t){this.modelHours=t},setMinutes:function(t){this.modelMinutes=t},setSeconds:function(t){this.modelSeconds=t},setAmpm:function(t){this.modelAmpm=t},onSpinLeftRight:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.type,i=t.keyCode;if(!this.disabled&&"keydown"===e&&(i===Dr||i===Fr)){Xo(t);var n=this.$refs.spinners||[],o=n.map((function(t){return!!t.hasFocus})).indexOf(!0);o=(o+=i===Dr?-1:1)>=n.length?0:o<0?n.length-1:o,_o(n[o])}},setLive:function(t){var e=this;t?this.$nextTick((function(){Xn((function(){e.isLive=!0}))})):this.isLive=!1}},render:function(t){var e=this;if(this.hidden)return t();var i=this.valueId,n=this.computedAriaLabelledby,o=[],r=function(n,r,a){var s=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},l=e.safeId("_spinbutton_".concat(r,"_"))||null;return o.push(l),t(Xd,{key:r,ref:"spinners",refInFor:!0,class:a,props:u({id:l,placeholder:"--",vertical:!0,required:!0,disabled:e.disabled,readonly:e.readonly,locale:e.computedLocale,labelIncrement:e.labelIncrement,labelDecrement:e.labelDecrement,wrap:!0,ariaControls:i,min:0},s),scopedSlots:e.spinScopedSlots,on:{change:n}})},a=function(){return t("div",{staticClass:"d-flex flex-column",class:{"text-muted":e.disabled||e.readonly},attrs:{"aria-hidden":"true"}},[t(pa,{props:{shiftV:4,scale:.5}}),t(pa,{props:{shiftV:-4,scale:.5}})])},s=[];s.push(r(this.setHours,"hours","b-time-hours",{value:this.modelHours,max:23,step:1,formatterFn:this.formatHours,ariaLabel:this.labelHours})),s.push(a()),s.push(r(this.setMinutes,"minutes","b-time-minutes",{value:this.modelMinutes,max:59,step:this.minutesStep||1,formatterFn:this.formatMinutes,ariaLabel:this.labelMinutes})),this.showSeconds&&(s.push(a()),s.push(r(this.setSeconds,"seconds","b-time-seconds",{value:this.modelSeconds,max:59,step:this.secondsStep||1,formatterFn:this.formatSeconds,ariaLabel:this.labelSeconds}))),this.is12Hour&&s.push(r(this.setAmpm,"ampm","b-time-ampm",{value:this.modelAmpm,max:1,formatterFn:this.formatAmpm,ariaLabel:this.labelAmpm,required:!1})),s=t("div",{staticClass:"d-flex align-items-center justify-content-center mx-auto",attrs:{role:"group",tabindex:this.disabled||this.readonly?null:"-1","aria-labelledby":n},on:{keydown:this.onSpinLeftRight,click:function(t){t.target===t.currentTarget&&e.focus()}}},s);var l=t("output",{staticClass:"form-control form-control-sm text-center",class:{disabled:this.disabled||this.readonly},attrs:{id:i,role:"status",for:o.filter(ee).join(" ")||null,tabindex:this.disabled?null:"-1","aria-live":this.isLive?"polite":"off","aria-atomic":"true"},on:{click:this.focus,focus:this.focus}},[t("bdi",this.formattedTimeString),this.computedHMS?t("span",{staticClass:"sr-only"}," (".concat(this.labelSelected,") ")):""]),c=t("header",{staticClass:"b-time-header",class:{"sr-only":this.hideHeader}},[l]),d=this.normalizeSlot();return d=d?t("footer",{staticClass:"b-time-footer"},d):t(),t("div",{staticClass:"b-time d-inline-flex flex-column text-center",attrs:{role:"group",lang:this.computedLang||null,"aria-labelledby":n||null,"aria-disabled":this.disabled?"true":null,"aria-readonly":this.readonly&&!this.disabled?"true":null}},[c,s,d])}}),dh=i.default.extend({name:Si,mixins:[vs],model:{prop:"value",event:"input"},props:xn(u(u(u({},uh),Tt(ad,["id","value","formattedValue","rtl","lang"])),{},{resetValue:{type:String,default:""},buttonOnly:{type:Boolean,default:!1},buttonVariant:{type:String,default:"secondary"},nowButton:{type:Boolean,default:!1},labelNowButton:{type:String,default:"Select now"},nowButtonVariant:{type:String,default:"outline-primary"},resetButton:{type:Boolean,default:!1},labelResetButton:{type:String,default:"Reset"},resetButtonVariant:{type:String,default:"outline-danger"},noCloseButton:{type:Boolean,default:!1},labelCloseButton:{type:String,default:"Close"},closeButtonVariant:{type:String,default:"outline-secondary"}}),Si),data:function(){return{localHMS:this.value||"",localLocale:null,isRTL:!1,formattedValue:"",isVisible:!1}},computed:{computedLang:function(){return(this.localLocale||"").replace(/-u-.*$/i,"")||null}},watch:{value:function(t){this.localHMS=t||""},localHMS:function(t){this.isVisible&&this.$emit("input",t||"")}},methods:{focus:function(){this.disabled||_o(this.$refs.control)},blur:function(){this.disabled||Do(this.$refs.control)},setAndClose:function(t){var e=this;this.localHMS=t,this.$nextTick((function(){e.$refs.control.hide(!0)}))},onInput:function(t){this.localHMS!==t&&(this.localHMS=t)},onContext:function(t){var e=t.isRTL,i=t.locale,n=t.value,o=t.formatted;this.isRTL=e,this.localLocale=i,this.formattedValue=o,this.localHMS=n||"",this.$emit("context",t)},onNowButton:function(){var t=new Date,e=[t.getHours(),t.getMinutes(),this.showSeconds?t.getSeconds():0].map((function(t){return"00".concat(t||"").slice(-2)})).join(":");this.setAndClose(e)},onResetButton:function(){this.setAndClose(this.resetValue)},onCloseButton:function(){this.$refs.control.hide(!0)},onShow:function(){this.isVisible=!0},onShown:function(){var t=this;this.$nextTick((function(){_o(t.$refs.time),t.$emit("shown")}))},onHidden:function(){this.isVisible=!1,this.$emit("hidden")},defaultButtonFn:function(t){var e=t.isHovered,i=t.hasFocus;return this.$createElement(e||i?ga:ma,{attrs:{"aria-hidden":"true"}})}},render:function(t){var e=this.localHMS,i=this.disabled,n=this.readonly,o=this.$props,r=et(this.placeholder)?this.labelNoTimeSelected:this.placeholder,a=[];if(this.nowButton){var s=this.labelNowButton;a.push(t(ta,{key:"now-btn",props:{size:"sm",disabled:i||n,variant:this.nowButtonVariant},attrs:{"aria-label":s||null},on:{click:this.onNowButton}},s))}if(this.resetButton){a.length>0&&a.push(t("span"," "));var l=this.labelResetButton;a.push(t(ta,{key:"reset-btn",props:{size:"sm",disabled:i||n,variant:this.resetButtonVariant},attrs:{"aria-label":l||null},on:{click:this.onResetButton}},l))}if(!this.noCloseButton){a.length>0&&a.push(t("span"," "));var c=this.labelCloseButton;a.push(t(ta,{key:"close-btn",props:{size:"sm",disabled:i,variant:this.closeButtonVariant},attrs:{"aria-label":c||null},on:{click:this.onCloseButton}},c))}a.length>0&&(a=[t("div",{staticClass:"b-form-date-controls d-flex flex-wrap",class:{"justify-content-between":a.length>1,"justify-content-end":a.length<2}},a)]);var d=t(ch,{ref:"time",staticClass:"b-form-time-control",props:u(u({},gr(uh,o)),{},{value:e,hidden:!this.isVisible}),on:{input:this.onInput,context:this.onContext}},a);return t(sd,{ref:"control",staticClass:"b-form-timepicker",props:u(u({},gr(ad,o)),{},{id:this.safeId(),value:e,formattedValue:e?this.formattedValue:"",placeholder:r,rtl:this.isRTL,lang:this.computedLang}),on:{show:this.onShow,shown:this.onShown,hidden:this.onHidden},scopedSlots:{"button-content":this.$scopedSlots["button-content"]||this.defaultButtonFn}},[d])}}),hh=de({components:{BFormTimepicker:dh,BTimepicker:dh}}),fh=de({components:{BImg:Gs,BImgLazy:Ys}}),ph=xn({tag:{type:String,default:"div"}},Fi),mh=i.default.extend({name:Fi,functional:!0,props:ph,render:function(t,e){var i=e.props,n=e.data,o=e.children;return t(i.tag,F(n,{staticClass:"input-group-text"}),o)}}),gh={id:{type:String,default:null},tag:{type:String,default:"div"},isText:{type:Boolean,default:!1}},vh=i.default.extend({name:$i,functional:!0,props:xn(u(u({},gh),{},{append:{type:Boolean,default:!1}}),$i),render:function(t,e){var i=e.props,n=e.data,o=e.children;return t(i.tag,F(n,{class:{"input-group-append":i.append,"input-group-prepend":!i.append},attrs:{id:i.id}}),i.isText?[t(mh,o)]:o)}}),bh=i.default.extend({name:_i,functional:!0,props:xn(gh,_i),render:function(t,e){var i=e.props,n=e.data,o=e.children;return t(vh,F(n,{props:u(u({},i),{},{append:!0})}),o)}}),yh=i.default.extend({name:Di,functional:!0,props:xn(gh,Di),render:function(t,e){var i=e.props,n=e.data,o=e.children;return t(vh,F(n,{props:u(u({},i),{},{append:!1})}),o)}}),Sh=xn({id:{type:String},size:{type:String},prepend:{type:String},prependHtml:{type:String},append:{type:String},appendHtml:{type:String},tag:{type:String,default:"div"}},xi),wh=de({components:{BInputGroup:i.default.extend({name:xi,functional:!0,props:Sh,render:function(t,e){var i=e.props,n=e.data,o=e.slots,r=e.scopedSlots,a=i.prepend,l=i.prependHtml,u=i.append,c=i.appendHtml,d=i.size,h=r||{},f=o(),p={},m=t(),g=jo(Ho,h,f);(g||a||l)&&(m=t(yh,[g?Go(Ho,p,h,f):t(mh,{domProps:Ea(l,a)})]));var v=t(),b=jo(Po,h,f);return(b||u||c)&&(v=t(bh,[b?Go(Po,p,h,f):t(mh,{domProps:Ea(c,u)})])),t(i.tag,F(n,{staticClass:"input-group",class:s({},"input-group-".concat(d),d),attrs:{id:i.id||null,role:"group"}}),[m,Go(Vo,p,h,f),v])}}),BInputGroupAddon:vh,BInputGroupPrepend:yh,BInputGroupAppend:bh,BInputGroupText:mh}}),Th=xn({tag:{type:String,default:"div"},fluid:{type:[Boolean,String],default:!1}},je),Ch=i.default.extend({name:je,functional:!0,props:Th,render:function(t,e){var i=e.props,n=e.data,o=e.children;return t(i.tag,F(n,{class:s({container:!(i.fluid||""===i.fluid),"container-fluid":!0===i.fluid||""===i.fluid},"container-".concat(i.fluid),i.fluid&&!0!==i.fluid)}),o)}}),kh=xn({fluid:{type:Boolean,default:!1},containerFluid:{type:[Boolean,String],default:!1},header:{type:String},headerHtml:{type:String},headerTag:{type:String,default:"h1"},headerLevel:{type:[Number,String],default:"3"},lead:{type:String},leadHtml:{type:String},leadTag:{type:String,default:"p"},tag:{type:String,default:"div"},bgVariant:{type:String},borderVariant:{type:String},textVariant:{type:String}},Ii),Bh=de({components:{BJumbotron:i.default.extend({name:Ii,functional:!0,props:kh,render:function(t,e){var i,n=e.props,o=e.data,r=e.slots,a=e.scopedSlots,l=n.header,u=n.headerHtml,c=n.lead,d=n.leadHtml,h=n.textVariant,f=n.bgVariant,p=n.borderVariant,m=a||{},g=r(),v={},b=t(),y=jo(Ro,m,g);if(y||l||u){var S=n.headerLevel;b=t(n.headerTag,{class:s({},"display-".concat(S),S),domProps:y?{}:Ea(u,l)},Go(Ro,v,m,g))}var w=t(),T=jo(Mo,m,g);(T||c||d)&&(w=t(n.leadTag,{staticClass:"lead",domProps:T?{}:Ea(d,c)},Go(Mo,v,m,g)));var C=[b,w,Go(Vo,v,m,g)];return n.fluid&&(C=[t(Ch,{props:{fluid:n.containerFluid}},C)]),t(n.tag,F(o,{staticClass:"jumbotron",class:(i={"jumbotron-fluid":n.fluid},s(i,"text-".concat(h),h),s(i,"bg-".concat(f),f),s(i,"border-".concat(p),p),s(i,"border",p),i)}),C)}})}}),xh=["start","end","center"],$h=yn((function(t,e){return(e=zn(Hn(e)))?jn(["row-cols",t,e].filter(ee).join("-")):null})),_h=yn((function(t){return jn(t.replace("cols",""))})),Dh=[],Fh=function(){var t=Bn().reduce((function(t,e){return t[pr(e,"cols")]={type:[String,Number],default:null},t}),pt(null));return Dh=bt(t),xn(u({tag:{type:String,default:"div"},noGutters:{type:Boolean,default:!1},alignV:{type:String,default:null,validator:function(t){return _n(Dn(xh,"baseline","stretch"),t)}},alignH:{type:String,default:null,validator:function(t){return _n(Dn(xh,"between","around"),t)}},alignContent:{type:String,default:null,validator:function(t){return _n(Dn(xh,"between","around","stretch"),t)}}},t),Qi)},Ih=de({components:{BContainer:Ch,BRow:{name:Qi,functional:!0,get props(){return delete this.props,this.props=Fh(),this.props},render:function(t,e){var i,n=e.props,o=e.data,r=e.children,a=[];return Dh.forEach((function(t){var e=$h(_h(t),n[t]);e&&a.push(e)})),a.push((s(i={"no-gutters":n.noGutters},"align-items-".concat(n.alignV),n.alignV),s(i,"justify-content-".concat(n.alignH),n.alignH),s(i,"align-content-".concat(n.alignContent),n.alignContent),i)),t(n.tag,F(o,{staticClass:"row",class:a}),r)}},BCol:Td,BFormRow:Ac}}),Oh=de({components:{BLink:jr}}),Ph=xn({tag:{type:String,default:"div"},flush:{type:Boolean,default:!1},horizontal:{type:[Boolean,String],default:!1}},Pi),Ah=i.default.extend({name:Pi,functional:!0,props:Ph,render:function(t,e){var i=e.props,n=e.data,o=e.children,r=""===i.horizontal||i.horizontal;r=!i.flush&&r;var a={staticClass:"list-group",class:s({"list-group-flush":i.flush,"list-group-horizontal":!0===r},"list-group-horizontal-".concat(r),ot(r))};return t(i.tag,F(n,a),o)}}),Vh=["a","router-link","button","b-link"],Eh=Tt(zr,["event","routerTag"]);delete Eh.href.default,delete Eh.to.default;var Lh=xn(u(u({},Eh),{},{tag:{type:String,default:"div"},action:{type:Boolean,default:null},button:{type:Boolean,default:null},variant:{type:String}}),Ai),Rh=de({components:{BListGroup:Ah,BListGroupItem:i.default.extend({name:Ai,functional:!0,props:Lh,render:function(t,e){var i,n=e.props,o=e.data,r=e.children,a=n.button,l=n.variant,u=n.active,c=n.disabled,d=Tr(n),h=a?"button":d?jr:n.tag,f=!!(n.action||d||a||_n(Vh,n.tag)),p={},m={};return to(h,"button")?(o.attrs&&o.attrs.type||(p.type="button"),n.disabled&&(p.disabled=!0)):m=gr(Eh,n),t(h,F(o,{attrs:p,props:m,staticClass:"list-group-item",class:(i={},s(i,"list-group-item-".concat(l),l),s(i,"list-group-item-action",f),s(i,"active",u),s(i,"disabled",c),i)}),r)}})}}),Nh=xn({tag:{type:String,default:"div"},right:{type:Boolean,default:!1},verticalAlign:{type:String,default:"top"}},Ei),Mh=i.default.extend({name:Ei,functional:!0,props:Nh,render:function(t,e){var i=e.props,n=e.data,o=e.children,r=i.verticalAlign,a="top"===r?"start":"bottom"===r?"end":r;return t(i.tag,F(n,{staticClass:"media-aside",class:s({"media-aside-right":i.right},"align-self-".concat(a),a)}),o)}}),Hh=xn({tag:{type:String,default:"div"}},Li),zh=i.default.extend({name:Li,functional:!0,props:Hh,render:function(t,e){var i=e.props,n=e.data,o=e.children;return t(i.tag,F(n,{staticClass:"media-body"}),o)}}),jh=xn({tag:{type:String,default:"div"},noBody:{type:Boolean,default:!1},rightAlign:{type:Boolean,default:!1},verticalAlign:{type:String,default:"top"}},Vi),Gh=de({components:{BMedia:i.default.extend({name:Vi,functional:!0,props:jh,render:function(t,e){var i=e.props,n=e.data,o=e.slots,r=e.scopedSlots,a=e.children,s=i.noBody,l=i.rightAlign,u=i.verticalAlign,c=s?a:[];if(!s){var d={},h=o(),f=r||{};c.push(t(zh,Go(Vo,d,f,h)));var p=Go("aside",d,f,h);p&&c[l?"push":"unshift"](t(Mh,{props:{right:l,verticalAlign:u}},p))}return t(i.tag,F(n,{staticClass:"media"}),c)}}),BMediaAside:Mh,BMediaBody:zh}}),Wh=i.default.extend({abstract:!0,name:"BTransporterTargetSingle",props:{nodes:{type:[Array,Function]}},data:function(t){return{updatedNodes:t.nodes}},destroyed:function(){var t;(t=this.$el)&&t.parentNode&&t.parentNode.removeChild(t)},render:function(t){var e=it(this.updatedNodes)?this.updatedNodes({}):this.updatedNodes;return(e=Dn(e).filter(Boolean))&&e.length>0&&!e[0].text?e[0]:t()}}),Yh=i.default.extend({name:"BTransporterSingle",mixins:[Wo],props:{disabled:{type:Boolean,default:!1},container:{type:[String,HTMLElement],default:"body"},tag:{type:String,default:"div"}},watch:{disabled:{immediate:!0,handler:function(t){t?this.unmountTarget():this.$nextTick(this.mountTarget)}}},created:function(){this.$_defaultFn=null,this.$_target=null},beforeMount:function(){this.mountTarget()},updated:function(){this.updateTarget()},beforeDestroy:function(){this.unmountTarget(),this.$_defaultFn=null},methods:{getContainer:function(){if(M){var t=this.container;return ot(t)?ao(t):t}return null},mountTarget:function(){if(!this.$_target){var t=this.getContainer();if(t){var e=document.createElement("div");t.appendChild(e),this.$_target=new Wh({el:e,parent:this,propsData:{nodes:Dn(this.normalizeSlot())}})}}},updateTarget:function(){if(M&&this.$_target){var t=this.$scopedSlots.default;this.disabled||(t&&this.$_defaultFn!==t?this.$_target.updatedNodes=t:t||(this.$_target.updatedNodes=this.$slots.default)),this.$_defaultFn=t}},unmountTarget:function(){this.$_target&&this.$_target.$destroy(),this.$_target=null}},render:function(t){if(this.disabled){var e=Dn(this.normalizeSlot()).filter(ee);if(e.length>0&&!e[0].text)return e[0]}return t()}}),Uh="$_bv_documentHandlers_",qh={created:function(){var t=this;M&&(this[Uh]={},this.$once("hook:beforeDestroy",(function(){var e=t[Uh]||{};delete t[Uh],bt(e).forEach((function(t){(e[t]||[]).forEach((function(e){return qo(document,t,e,el)}))}))})))},methods:{listenDocument:function(t,e,i){t?this.listenOnDocument(e,i):this.listenOffDocument(e,i)},listenOnDocument:function(t,e){this[Uh]&&ot(t)&&it(e)&&(this[Uh][t]=this[Uh][t]||[],_n(this[Uh][t],e)||(this[Uh][t].push(e),Uo(document,t,e,el)))},listenOffDocument:function(t,e){this[Uh]&&ot(t)&&it(e)&&(qo(document,t,e,el),this[Uh][t]=(this[Uh][t]||[]).filter((function(t){return t!==e})))}}},Kh="$_bv_windowHandlers_",Xh={beforeCreate:function(){this[Kh]={}},beforeDestroy:function(){if(M){var t=this[Kh];delete this[Kh],bt(t).forEach((function(e){(t[e]||[]).forEach((function(t){return qo(window,e,t,el)}))}))}},methods:{listenWindow:function(t,e,i){t?this.listenOnWindow(e,i):this.listenOffWindow(e,i)},listenOnWindow:function(t,e){M&&this[Kh]&&ot(t)&&it(e)&&(this[Kh][t]=this[Kh][t]||[],_n(this[Kh][t],e)||(this[Kh][t].push(e),Uo(window,t,e,el)))},listenOffWindow:function(t,e){M&&this[Kh]&&ot(t)&&it(e)&&(qo(window,t,e,el),this[Kh][t]=(this[Kh][t]||[]).filter((function(t){return t!==e})))}}},Zh=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return t&&t.$options._scopeId||e},Jh={computed:{scopedStyleAttrs:function(){var t=Zh(this.$parent);return t?s({},t,""):{}}}},Qh=".fixed-top, .fixed-bottom, .is-fixed, .sticky-top",tf=".sticky-top",ef=".navbar-toggler",nf=new(i.default.extend({data:function(){return{modals:[],baseZIndex:null,scrollbarWidth:null,isBodyOverflowing:!1}},computed:{modalCount:function(){return this.modals.length},modalsAreOpen:function(){return this.modalCount>0}},watch:{modalCount:function(t,e){M&&(this.getScrollbarWidth(),t>0&&0===e?(this.checkScrollbar(),this.setScrollbar(),ho(document.body,"modal-open")):0===t&&e>0&&(this.resetScrollbar(),fo(document.body,"modal-open")),mo(document.body,"data-modal-open-count",String(t)))},modals:function(t){var e=this;this.checkScrollbar(),Xn((function(){e.updateModals(t||[])}))}},methods:{registerModal:function(t){var e=this;t&&-1===this.modals.indexOf(t)&&(this.modals.push(t),t.$once("hook:beforeDestroy",(function(){e.unregisterModal(t)})))},unregisterModal:function(t){var e=this.modals.indexOf(t);e>-1&&(this.modals.splice(e,1),t._isBeingDestroyed||t._isDestroyed||this.resetModal(t))},getBaseZIndex:function(){if(tt(this.baseZIndex)&&M){var t=document.createElement("div");ho(t,"modal-backdrop"),ho(t,"d-none"),yo(t,"display","none"),document.body.appendChild(t),this.baseZIndex=Pn(Co(t).zIndex,1040),document.body.removeChild(t)}return this.baseZIndex||1040},getScrollbarWidth:function(){if(tt(this.scrollbarWidth)&&M){var t=document.createElement("div");ho(t,"modal-scrollbar-measure"),document.body.appendChild(t),this.scrollbarWidth=To(t).width-t.clientWidth,document.body.removeChild(t)}return this.scrollbarWidth||0},updateModals:function(t){var e=this,i=this.getBaseZIndex(),n=this.getScrollbarWidth();t.forEach((function(t,o){t.zIndex=i+o,t.scrollbarWidth=n,t.isTop=o===e.modals.length-1,t.isBodyOverflowing=e.isBodyOverflowing}))},resetModal:function(t){t&&(t.zIndex=this.getBaseZIndex(),t.isTop=!0,t.isBodyOverflowing=!1)},checkScrollbar:function(){var t=To(document.body),e=t.left,i=t.right;this.isBodyOverflowing=e+i<window.innerWidth},setScrollbar:function(){var t=document.body;if(t._paddingChangedForModal=t._paddingChangedForModal||[],t._marginChangedForModal=t._marginChangedForModal||[],this.isBodyOverflowing){var e=this.scrollbarWidth;ro(Qh).forEach((function(i){var n=wo(i,"paddingRight")||"";mo(i,"data-padding-right",n),yo(i,"paddingRight","".concat(An(Co(i).paddingRight,0)+e,"px")),t._paddingChangedForModal.push(i)})),ro(tf).forEach((function(i){var n=wo(i,"marginRight")||"";mo(i,"data-margin-right",n),yo(i,"marginRight","".concat(An(Co(i).marginRight,0)-e,"px")),t._marginChangedForModal.push(i)})),ro(ef).forEach((function(i){var n=wo(i,"marginRight")||"";mo(i,"data-margin-right",n),yo(i,"marginRight","".concat(An(Co(i).marginRight,0)+e,"px")),t._marginChangedForModal.push(i)}));var i=wo(t,"paddingRight")||"";mo(t,"data-padding-right",i),yo(t,"paddingRight","".concat(An(Co(t).paddingRight,0)+e,"px"))}},resetScrollbar:function(){var t=document.body;t._paddingChangedForModal&&t._paddingChangedForModal.forEach((function(t){bo(t,"data-padding-right")&&(yo(t,"paddingRight",vo(t,"data-padding-right")||""),go(t,"data-padding-right"))})),t._marginChangedForModal&&t._marginChangedForModal.forEach((function(t){bo(t,"data-margin-right")&&(yo(t,"marginRight",vo(t,"data-margin-right")||""),go(t,"data-margin-right"))})),t._paddingChangedForModal=null,t._marginChangedForModal=null,bo(t,"data-padding-right")&&(yo(t,"paddingRight",vo(t,"data-padding-right")||""),go(t,"data-padding-right"))}}})),BvModalEvent=function(t){c(BvModalEvent,t);var e=b(BvModalEvent);function BvModalEvent(t){var i,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return o(this,BvModalEvent),i=e.call(this,t,n),mt(g(i),{trigger:{enumerable:!0,configurable:!1,writable:!1}}),i}return a(BvModalEvent,null,[{key:"Defaults",get:function(){return u(u({},y(d(BvModalEvent),"Defaults",this)),{},{trigger:null})}}]),BvModalEvent}(BvEvent),of={subtree:!0,childList:!0,characterData:!0,attributes:!0,attributeFilter:["style","class"]},rf=xn({size:{type:String,default:"md"},centered:{type:Boolean,default:!1},scrollable:{type:Boolean,default:!1},buttonSize:{type:String},noStacking:{type:Boolean,default:!1},noFade:{type:Boolean,default:!1},noCloseOnBackdrop:{type:Boolean,default:!1},noCloseOnEsc:{type:Boolean,default:!1},noEnforceFocus:{type:Boolean,default:!1},ignoreEnforceFocusSelector:{type:[Array,String],default:""},title:{type:String,default:""},titleHtml:{type:String},titleTag:{type:String,default:"h5"},titleClass:{type:[String,Array,Object]},titleSrOnly:{type:Boolean,default:!1},ariaLabel:{type:String},headerBgVariant:{type:String},headerBorderVariant:{type:String},headerTextVariant:{type:String},headerCloseVariant:{type:String},headerClass:{type:[String,Array,Object]},bodyBgVariant:{type:String},bodyTextVariant:{type:String},modalClass:{type:[String,Array,Object]},dialogClass:{type:[String,Array,Object]},contentClass:{type:[String,Array,Object]},bodyClass:{type:[String,Array,Object]},footerBgVariant:{type:String},footerBorderVariant:{type:String},footerTextVariant:{type:String},footerClass:{type:[String,Array,Object]},hideHeader:{type:Boolean,default:!1},hideFooter:{type:Boolean,default:!1},hideHeaderClose:{type:Boolean,default:!1},hideBackdrop:{type:Boolean,default:!1},okOnly:{type:Boolean,default:!1},okDisabled:{type:Boolean,default:!1},cancelDisabled:{type:Boolean,default:!1},visible:{type:Boolean,default:!1},returnFocus:{type:[HTMLElement,String,Object],default:null},headerCloseContent:{type:String,default:"&times;"},headerCloseLabel:{type:String,default:"Close"},cancelTitle:{type:String,default:"Cancel"},cancelTitleHtml:{type:String},okTitle:{type:String,default:"OK"},okTitleHtml:{type:String},cancelVariant:{type:String,default:"secondary"},okVariant:{type:String,default:"primary"},lazy:{type:Boolean,default:!1},busy:{type:Boolean,default:!1},static:{type:Boolean,default:!1},autoFocusButton:{type:String,default:null,validator:function(t){return et(t)||_n(["ok","cancel","close"],t)}}},Ri),af=i.default.extend({name:Ri,mixins:[Rr,vs,qh,pl,Xh,Wo,Jh],inheritAttrs:!1,model:{prop:"visible",event:"change"},props:rf,data:function(){return{isHidden:!0,isVisible:!1,isTransitioning:!1,isShow:!1,isBlock:!1,isOpening:!1,isClosing:!1,ignoreBackdropClick:!1,isModalOverflowing:!1,return_focus:this.returnFocus||null,scrollbarWidth:0,zIndex:nf.getBaseZIndex(),isTop:!0,isBodyOverflowing:!1}},computed:{modalId:function(){return this.safeId()},modalOuterId:function(){return this.safeId("__BV_modal_outer_")},modalHeaderId:function(){return this.safeId("__BV_modal_header_")},modalBodyId:function(){return this.safeId("__BV_modal_body_")},modalTitleId:function(){return this.safeId("__BV_modal_title_")},modalContentId:function(){return this.safeId("__BV_modal_content_")},modalFooterId:function(){return this.safeId("__BV_modal_footer_")},modalBackdropId:function(){return this.safeId("__BV_modal_backdrop_")},modalClasses:function(){return[{fade:!this.noFade,show:this.isShow},this.modalClass]},modalStyles:function(){var t="".concat(this.scrollbarWidth,"px");return{paddingLeft:!this.isBodyOverflowing&&this.isModalOverflowing?t:"",paddingRight:this.isBodyOverflowing&&!this.isModalOverflowing?t:"",display:this.isBlock?"block":"none"}},dialogClasses:function(){var t;return[(t={},s(t,"modal-".concat(this.size),this.size),s(t,"modal-dialog-centered",this.centered),s(t,"modal-dialog-scrollable",this.scrollable),t),this.dialogClass]},headerClasses:function(){var t;return[(t={},s(t,"bg-".concat(this.headerBgVariant),this.headerBgVariant),s(t,"text-".concat(this.headerTextVariant),this.headerTextVariant),s(t,"border-".concat(this.headerBorderVariant),this.headerBorderVariant),t),this.headerClass]},titleClasses:function(){return[{"sr-only":this.titleSrOnly},this.titleClass]},bodyClasses:function(){var t;return[(t={},s(t,"bg-".concat(this.bodyBgVariant),this.bodyBgVariant),s(t,"text-".concat(this.bodyTextVariant),this.bodyTextVariant),t),this.bodyClass]},footerClasses:function(){var t;return[(t={},s(t,"bg-".concat(this.footerBgVariant),this.footerBgVariant),s(t,"text-".concat(this.footerTextVariant),this.footerTextVariant),s(t,"border-".concat(this.footerBorderVariant),this.footerBorderVariant),t),this.footerClass]},modalOuterStyle:function(){return{position:"absolute",zIndex:this.zIndex}},slotScope:function(){return{ok:this.onOk,cancel:this.onCancel,close:this.onClose,hide:this.hide,visible:this.isVisible}},computeIgnoreEnforceFocusSelector:function(){return Dn(this.ignoreEnforceFocusSelector).filter(ee).join(",").trim()},computedAttrs:function(){return u(u(u({},this.static?{}:this.scopedStyleAttrs),this.bvAttrs),{},{id:this.modalOuterId})},computedModalAttrs:function(){var t=this.isVisible,e=this.ariaLabel;return{id:this.modalId,role:"dialog","aria-hidden":t?null:"true","aria-modal":t?"true":null,"aria-label":e,"aria-labelledby":this.hideHeader||e||!(this.hasNormalizedSlot("modal-title")||this.titleHtml||this.title)?null:this.modalTitleId,"aria-describedby":this.modalBodyId}}},watch:{visible:function(t,e){t!==e&&this[t?"show":"hide"]()}},created:function(){this.$_observer=null},mounted:function(){this.zIndex=nf.getBaseZIndex(),this.listenOnRoot("bv::show::modal",this.showHandler),this.listenOnRoot("bv::hide::modal",this.hideHandler),this.listenOnRoot("bv::toggle::modal",this.toggleHandler),this.listenOnRoot("bv::modal::show",this.modalListener),!0===this.visible&&this.$nextTick(this.show)},beforeDestroy:function(){this.setObserver(!1),this.isVisible&&(this.isVisible=!1,this.isShow=!1,this.isTransitioning=!1)},methods:{setObserver:function(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.$_observer&&this.$_observer.disconnect(),this.$_observer=null,t&&(this.$_observer=nl(this.$refs.content,this.checkModalOverflow.bind(this),of))},updateModel:function(t){t!==this.visible&&this.$emit("change",t)},buildEvent:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return new BvModalEvent(t,u(u({cancelable:!1,target:this.$refs.modal||this.$el||null,relatedTarget:null,trigger:null},e),{},{vueTarget:this,componentId:this.modalId}))},show:function(){if(!this.isVisible&&!this.isOpening)if(this.isClosing)this.$once("hidden",this.show);else{this.isOpening=!0,this.return_focus=this.return_focus||this.getActiveElement();var t=this.buildEvent("show",{cancelable:!0});if(this.emitEvent(t),t.defaultPrevented||this.isVisible)return this.isOpening=!1,void this.updateModel(!1);this.doShow()}},hide:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";if(this.isVisible&&!this.isClosing){this.isClosing=!0;var e=this.buildEvent("hide",{cancelable:"FORCE"!==t,trigger:t||null});if("ok"===t?this.$emit("ok",e):"cancel"===t?this.$emit("cancel",e):"headerclose"===t&&this.$emit("close",e),this.emitEvent(e),e.defaultPrevented||!this.isVisible)return this.isClosing=!1,void this.updateModel(!0);this.setObserver(!1),this.isVisible=!1,this.updateModel(!1)}},toggle:function(t){t&&(this.return_focus=t),this.isVisible?this.hide("toggle"):this.show()},getActiveElement:function(){var t=Qn(M?[document.body]:[]);return t&&t.focus?t:null},doShow:function(){var t=this;nf.modalsAreOpen&&this.noStacking?this.listenOnRootOnce("bv::modal::hidden",this.doShow):(nf.registerModal(this),this.isHidden=!1,this.$nextTick((function(){t.isVisible=!0,t.isOpening=!1,t.updateModel(!0),t.$nextTick((function(){t.setObserver(!0)}))})))},onBeforeEnter:function(){this.isTransitioning=!0,this.setResizeEvent(!0)},onEnter:function(){var t=this;this.isBlock=!0,Xn((function(){Xn((function(){t.isShow=!0}))}))},onAfterEnter:function(){var t=this;this.checkModalOverflow(),this.isTransitioning=!1,Xn((function(){t.emitEvent(t.buildEvent("shown")),t.setEnforceFocus(!0),t.$nextTick((function(){t.focusFirst()}))}))},onBeforeLeave:function(){this.isTransitioning=!0,this.setResizeEvent(!1),this.setEnforceFocus(!1)},onLeave:function(){this.isShow=!1},onAfterLeave:function(){var t=this;this.isBlock=!1,this.isTransitioning=!1,this.isModalOverflowing=!1,this.isHidden=!0,this.$nextTick((function(){t.isClosing=!1,nf.unregisterModal(t),t.returnFocusTo(),t.emitEvent(t.buildEvent("hidden"))}))},emitEvent:function(t){var e=t.type;this.emitOnRoot("bv::modal::".concat(e),t,t.componentId),this.$emit(e,t)},onDialogMousedown:function(){var t=this,e=this.$refs.modal;Uo(e,"mouseup",(function i(n){qo(e,"mouseup",i,el),n.target===e&&(t.ignoreBackdropClick=!0)}),el)},onClickOut:function(t){this.ignoreBackdropClick?this.ignoreBackdropClick=!1:this.isVisible&&!this.noCloseOnBackdrop&&uo(document.body,t.target)&&(uo(this.$refs.content,t.target)||this.hide("backdrop"))},onOk:function(){this.hide("ok")},onCancel:function(){this.hide("cancel")},onClose:function(){this.hide("headerclose")},onEsc:function(t){27===t.keyCode&&this.isVisible&&!this.noCloseOnEsc&&this.hide("esc")},focusHandler:function(t){var e=this.$refs.content,i=t.target;if(!(this.noEnforceFocus||!this.isTop||!this.isVisible||!e||document===i||uo(e,i)||this.computeIgnoreEnforceFocusSelector&&lo(this.computeIgnoreEnforceFocusSelector,i,!0))){var n=$o(this.$refs.content),o=this.$refs,r=o.bottomTrap,a=o.topTrap;if(r&&i===r){if(_o(n[0]))return}else if(a&&i===a&&_o(n[n.length-1]))return;_o(e,{preventScroll:!0})}},setEnforceFocus:function(t){this.listenDocument(t,"focusin",this.focusHandler)},setResizeEvent:function(t){this.listenWindow(t,"resize",this.checkModalOverflow),this.listenWindow(t,"orientationchange",this.checkModalOverflow)},showHandler:function(t,e){t===this.modalId&&(this.return_focus=e||this.getActiveElement(),this.show())},hideHandler:function(t){t===this.modalId&&this.hide("event")},toggleHandler:function(t,e){t===this.modalId&&this.toggle(e)},modalListener:function(t){this.noStacking&&t.vueTarget!==this&&this.hide()},focusFirst:function(){var t=this;M&&Xn((function(){var e=t.$refs.modal,i=t.$refs.content,n=t.getActiveElement();if(e&&i&&(!n||!uo(i,n))){var o=t.$refs["ok-button"],r=t.$refs["cancel-button"],a=t.$refs["close-button"],s=t.autoFocusButton,l="ok"===s&&o?o.$el||o:"cancel"===s&&r?r.$el||r:"close"===s&&a?a.$el||a:i;_o(l),l===i&&t.$nextTick((function(){e.scrollTop=0}))}}))},returnFocusTo:function(){var t=this.returnFocus||this.return_focus||null;this.return_focus=null,this.$nextTick((function(){(t=ot(t)?ao(t):t)&&(t=t.$el||t,_o(t))}))},checkModalOverflow:function(){if(this.isVisible){var t=this.$refs.modal;this.isModalOverflowing=t.scrollHeight>document.documentElement.clientHeight}},makeModal:function(t){var e=t();if(!this.hideHeader){var i=this.normalizeSlot("modal-header",this.slotScope);if(!i){var n=t();this.hideHeaderClose||(n=t(Jo,{props:{content:this.headerCloseContent,disabled:this.isTransitioning,ariaLabel:this.headerCloseLabel,textVariant:this.headerCloseVariant||this.headerTextVariant},on:{click:this.onClose},ref:"close-button"},[this.normalizeSlot("modal-header-close")])),i=[t(this.titleTag,{staticClass:"modal-title",class:this.titleClasses,attrs:{id:this.modalTitleId},domProps:this.hasNormalizedSlot("modal-title")?{}:Ea(this.titleHtml,this.title)},this.normalizeSlot("modal-title",this.slotScope)),n]}e=t("header",{staticClass:"modal-header",class:this.headerClasses,attrs:{id:this.modalHeaderId},ref:"header"},[i])}var o=t("div",{staticClass:"modal-body",class:this.bodyClasses,attrs:{id:this.modalBodyId},ref:"body"},this.normalizeSlot(Vo,this.slotScope)),r=t();if(!this.hideFooter){var a=this.normalizeSlot("modal-footer",this.slotScope);if(!a){var s=t();this.okOnly||(s=t(ta,{props:{variant:this.cancelVariant,size:this.buttonSize,disabled:this.cancelDisabled||this.busy||this.isTransitioning},domProps:this.hasNormalizedSlot("modal-cancel")?{}:Ea(this.cancelTitleHtml,this.cancelTitle),on:{click:this.onCancel},ref:"cancel-button"},this.normalizeSlot("modal-cancel"))),a=[s,t(ta,{props:{variant:this.okVariant,size:this.buttonSize,disabled:this.okDisabled||this.busy||this.isTransitioning},domProps:this.hasNormalizedSlot("modal-ok")?{}:Ea(this.okTitleHtml,this.okTitle),on:{click:this.onOk},ref:"ok-button"},this.normalizeSlot("modal-ok"))]}r=t("footer",{staticClass:"modal-footer",class:this.footerClasses,attrs:{id:this.modalFooterId},ref:"footer"},[a])}var l=t("div",{staticClass:"modal-content",class:this.contentClass,attrs:{id:this.modalContentId,tabindex:"-1"},ref:"content"},[e,o,r]),u=t(),c=t();this.isVisible&&!this.noEnforceFocus&&(u=t("span",{ref:"topTrap",attrs:{tabindex:"0"}}),c=t("span",{ref:"bottomTrap",attrs:{tabindex:"0"}}));var d=t("div",{staticClass:"modal-dialog",class:this.dialogClasses,on:{mousedown:this.onDialogMousedown},ref:"dialog"},[u,l,c]),h=t("div",{staticClass:"modal",class:this.modalClasses,style:this.modalStyles,attrs:this.computedModalAttrs,on:{keydown:this.onEsc,click:this.onClickOut},directives:[{name:"show",value:this.isVisible}],ref:"modal"},[d]);h=t("transition",{props:{enterClass:"",enterToClass:"",enterActiveClass:"",leaveClass:"",leaveActiveClass:"",leaveToClass:""},on:{beforeEnter:this.onBeforeEnter,enter:this.onEnter,afterEnter:this.onAfterEnter,beforeLeave:this.onBeforeLeave,leave:this.onLeave,afterLeave:this.onAfterLeave}},[h]);var f=t();return!this.hideBackdrop&&this.isVisible&&(f=t("div",{staticClass:"modal-backdrop",attrs:{id:this.modalBackdropId}},this.normalizeSlot("modal-backdrop"))),f=t(Oo,{props:{noFade:this.noFade}},[f]),t("div",{style:this.modalOuterStyle,attrs:this.computedAttrs,key:"modal-outer-".concat(this._uid)},[h,f])}},render:function(t){return this.static?this.lazy&&this.isHidden?t():this.makeModal(t):this.isHidden?t():t(Yh,[this.makeModal(t)])}}),sf="__bv_modal_directive__",lf=function(t){var e=t.modifiers,i=void 0===e?{}:e,n=t.arg,o=t.value;return ot(o)?o:ot(n)?n:bt(i).reverse()[0]},uf=function(t){return t&&so(t,".dropdown-menu > li, li.nav-item")&&ao("a, button",t)||t},cf=function(t){t&&"BUTTON"!==t.tagName&&(bo(t,"role")||mo(t,"role","button"),"A"===t.tagName||bo(t,"tabindex")||mo(t,"tabindex","0"))},df=function(t){var e=t[sf]||{},i=e.trigger,n=e.handler;i&&n&&(qo(i,"click",n,tl),qo(i,"keydown",n,tl),qo(t,"click",n,tl),qo(t,"keydown",n,tl)),delete t[sf]},hf=function(t,e,i){var n=t[sf]||{},o=lf(e),r=uf(t);o===n.target&&r===n.trigger||(df(t),function(t,e,i){var n=lf(e),o=uf(t);if(n&&o){var r=function(t){var e=t.currentTarget;if(!no(e)){var o=t.type,r=t.keyCode;"click"!==o&&("keydown"!==o||r!==$r&&r!==Ir)||i.context.$root.$emit("bv::show::modal",n,e)}};t[sf]={handler:r,target:n,trigger:o},cf(o),Uo(o,"click",r,tl),"BUTTON"!==o.tagName&&"button"===vo(o,"role")&&Uo(o,"keydown",r,tl)}}(t,e,i)),cf(r)},ff={inserted:hf,updated:function(){},componentUpdated:hf,unbind:df},pf="$bvModal",mf=["id"].concat(w(bt(Tt(rf,["busy","lazy","noStacking","static","visible"])))),gf=function(){},vf={msgBoxContent:"default",title:"modal-title",okTitle:"modal-ok",cancelTitle:"modal-cancel"},bf=function(t){return mf.reduce((function(e,i){return Q(t[i])||(e[i]=t[i]),e}),{})},yf=de({components:{BModal:af},directives:{VBModal:ff},plugins:{BVModalPlugin:de({plugins:{plugin:function(t){var e=t.extend({name:"BMsgBox",extends:af,destroyed:function(){this.$el&&this.$el.parentNode&&this.$el.parentNode.removeChild(this.$el)},mounted:function(){var t=this,e=function(){t.$nextTick((function(){Xn((function(){t.$destroy()}))}))};this.$parent.$once("hook:destroyed",e),this.$once("hidden",e),this.$router&&this.$route&&this.$once("hook:beforeDestroy",this.$watch("$router",e)),this.show()}}),i=function(t,i){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:gf;if(!re(pf)&&!ae(pf)){var o=new e({parent:t,propsData:u(u(u({},bf(Tn(Ri))),{},{hideHeaderClose:!0,hideHeader:!(i.title||i.titleHtml)},Tt(i,bt(vf))),{},{lazy:!1,busy:!1,visible:!1,noStacking:!1,noEnforceFocus:!1})});return bt(vf).forEach((function(t){Q(i[t])||(o.$slots[vf[t]]=Dn(i[t]))})),new Promise((function(t,e){var i=!1;o.$once("hook:destroyed",(function(){i||e(new Error("BootstrapVue MsgBox destroyed before resolve"))})),o.$on("hide",(function(e){if(!e.defaultPrevented){var o=n(e);e.defaultPrevented||(i=!0,t(o))}}));var r=document.createElement("div");document.body.appendChild(r),o.$mount(r)}))}},n=function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null;if(e&&!ae(pf)&&!re(pf)&&it(o))return i(t,u(u({},bf(n)),{},{msgBoxContent:e}),o)},r=function(){function t(e){o(this,t),ft(this,{_vm:e,_root:e.$root}),mt(this,{_vm:{enumerable:!0,configurable:!1,writable:!1},_root:{enumerable:!0,configurable:!1,writable:!1}})}return a(t,[{key:"show",value:function(t){if(t&&this._root){for(var e,i=arguments.length,n=new Array(i>1?i-1:0),o=1;o<i;o++)n[o-1]=arguments[o];(e=this._root).$emit.apply(e,["bv::show::modal",t].concat(n))}}},{key:"hide",value:function(t){if(t&&this._root){for(var e,i=arguments.length,n=new Array(i>1?i-1:0),o=1;o<i;o++)n[o-1]=arguments[o];(e=this._root).$emit.apply(e,["bv::hide::modal",t].concat(n))}}},{key:"msgBoxOk",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=u(u({},e),{},{okOnly:!0,okDisabled:!1,hideFooter:!1,msgBoxContent:t});return n(this._vm,t,i,(function(){return!0}))}},{key:"msgBoxConfirm",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=u(u({},e),{},{okOnly:!1,okDisabled:!1,cancelDisabled:!1,hideFooter:!1});return n(this._vm,t,i,(function(t){var e=t.trigger;return"ok"===e||"cancel"!==e&&null}))}}]),t}();t.mixin({beforeCreate:function(){this._bv__modal=new r(this)}}),yt(t.prototype,pf)||gt(t.prototype,pf,{get:function(){return this&&this._bv__modal||oe('"'.concat(pf,'" must be accessed from a Vue instance "this" context.'),Ri),this._bv__modal}})}}})}}),Sf=xn({tag:{type:String,default:"ul"},fill:{type:Boolean,default:!1},justified:{type:Boolean,default:!1},align:{type:String},tabs:{type:Boolean,default:!1},pills:{type:Boolean,default:!1},vertical:{type:Boolean,default:!1},small:{type:Boolean,default:!1},cardHeader:{type:Boolean,default:!1}},Ni),wf=i.default.extend({name:Ni,functional:!0,props:Sf,render:function(t,e){var i,n,o=e.props,r=e.data,a=e.children;return t(o.tag,F(r,{staticClass:"nav",class:(i={"nav-tabs":o.tabs,"nav-pills":o.pills&&!o.tabs,"card-header-tabs":!o.vertical&&o.cardHeader&&o.tabs,"card-header-pills":!o.vertical&&o.cardHeader&&o.pills&&!o.tabs,"flex-column":o.vertical,"nav-fill":!o.vertical&&o.fill,"nav-justified":!o.vertical&&o.justified},s(i,(n=o.align,"justify-content-".concat(n="left"===n?"start":"right"===n?"end":n)),!o.vertical&&o.align),s(i,"small",o.small),i)}),a)}}),Tf=xn(u(u({},Tt(zr,["event","routerTag"])),{},{linkAttrs:{type:Object,default:function(){}},linkClasses:{type:[String,Object,Array],default:null}}),Wi),Cf=i.default.extend({name:Wi,functional:!0,props:Tf,render:function(t,e){var i=e.props,n=e.data,o=e.listeners,r=e.children;return delete n.on,t("li",F(n,{staticClass:"nav-item"}),[t(jr,{staticClass:"nav-link",class:i.linkClasses,attrs:i.linkAttrs,props:i,on:o},r)])}}),kf=i.default.extend({name:"BNavText",functional:!0,props:{},render:function(t,e){var i=e.data,n=e.children;return t("li",F(i,{staticClass:"navbar-text"}),n)}}),Bf=xn(u(u({},Tt(pc,["inline"])),{},{formClass:{type:[String,Array,Object]}}),Gi),xf=i.default.extend({name:Gi,functional:!0,props:Bf,render:function(t,e){var i=e.props,n=e.data,o=e.children,r=e.listeners,a=void 0===r?{}:r,s=n.attrs;n.attrs={},n.on={};var l=t(mc,{class:i.formClass,props:u(u({},i),{},{inline:!0}),attrs:s,on:a},o);return t("li",F(n,{staticClass:"form-inline"}),[l])}}),$f=xn(gr(["text","html","menuClass","toggleClass","noCaret","role","lazy"],oc),Yi),_f=i.default.extend({name:Yi,mixins:[vs,nc,Wo],props:$f,computed:{toggleId:function(){return this.safeId("_BV_toggle_")},dropdownClasses:function(){return[this.directionClass,this.boundaryClass,{show:this.visible}]},menuClasses:function(){return[this.menuClass,{"dropdown-menu-right":this.right,show:this.visible}]},toggleClasses:function(){return[this.toggleClass,{"dropdown-toggle-no-caret":this.noCaret}]}},render:function(t){var e=this.toggleId,i=this.visible,n=t(jr,{staticClass:"nav-link dropdown-toggle",class:this.toggleClasses,props:{href:"#".concat(this.id||""),disabled:this.disabled},attrs:{id:e,role:"button","aria-haspopup":"true","aria-expanded":i?"true":"false"},on:{mousedown:this.onMousedown,click:this.toggle,keydown:this.toggle},ref:"toggle"},[this.normalizeSlot([Ao,"text"])||t("span",{domProps:Ea(this.html,this.text)})]),o=t("ul",{staticClass:"dropdown-menu",class:this.menuClasses,attrs:{tabindex:"-1","aria-labelledby":e},on:{keydown:this.onKeydown},ref:"menu"},!this.lazy||i?this.normalizeSlot(Vo,{hide:this.hide}):[t()]);return t("li",{staticClass:"nav-item b-nav-dropdown dropdown",class:this.dropdownClasses,attrs:{id:this.safeId()}},[n,o])}}),Df=de({components:{BNav:wf,BNavItem:Cf,BNavText:kf,BNavForm:xf,BNavItemDropdown:_f,BNavItemDd:_f,BNavDropdown:_f,BNavDd:_f},plugins:{DropdownPlugin:Sc}}),Ff=xn({tag:{type:String,default:"nav"},type:{type:String,default:"light"},variant:{type:String},toggleable:{type:[Boolean,String],default:!1},fixed:{type:String},sticky:{type:Boolean,default:!1},print:{type:Boolean,default:!1}},Mi),If=i.default.extend({name:Mi,mixins:[Wo],provide:function(){return{bvNavbar:this}},props:Ff,computed:{breakpointClass:function(){var t=null,e=Cn()[0],i=this.toggleable;return i&&ot(i)&&i!==e?t="navbar-expand-".concat(i):!1===i&&(t="navbar-expand"),t}},render:function(t){var e;return t(this.tag,{staticClass:"navbar",class:[(e={"d-print":this.print,"sticky-top":this.sticky},s(e,"navbar-".concat(this.type),this.type),s(e,"bg-".concat(this.variant),this.variant),s(e,"fixed-".concat(this.fixed),this.fixed),e),this.breakpointClass],attrs:{role:to(this.tag,"nav")?null:"navigation"}},[this.normalizeSlot()])}}),Of=xn(gr(["tag","fill","justified","align","small"],Sf),zi),Pf=i.default.extend({name:zi,functional:!0,props:Of,render:function(t,e){var i,n,o=e.props,r=e.data,a=e.children;return t(o.tag,F(r,{staticClass:"navbar-nav",class:(i={"nav-fill":o.fill,"nav-justified":o.justified},s(i,(n=o.align,"justify-content-".concat(n="left"===n?"start":"right"===n?"end":n)),o.align),s(i,"small",o.small),i)}),a)}}),Af=Tt(zr,["event","routerTag"]);Af.href.default=void 0,Af.to.default=void 0;var Vf=xn(u({tag:{type:String,default:"div"}},Af),Hi),Ef=i.default.extend({name:Hi,functional:!0,props:Vf,render:function(t,e){var i=e.props,n=e.data,o=e.children,r=i.to||i.href;return t(r?jr:i.tag,F(n,{staticClass:"navbar-brand",props:r?gr(Af,i):{}}),o)}}),Lf="navbar-toggler",Rf=i.default.extend({name:ji,directives:{VBToggle:Rl},mixins:[pl,Wo],props:xn({label:{type:String,default:"Toggle navigation"},target:{type:[Array,String],required:!0},disabled:{type:Boolean,default:!1}},ji),data:function(){return{toggleState:!1}},created:function(){this.listenOnRoot(_l,this.handleStateEvt),this.listenOnRoot(Dl,this.handleStateEvt)},methods:{onClick:function(t){this.disabled||this.$emit("click",t)},handleStateEvt:function(t,e){t===this.target&&(this.toggleState=e)}},render:function(t){var e=this.disabled;return t("button",{staticClass:Lf,class:{disabled:e},directives:[{name:"VBToggle",value:this.target}],attrs:{type:"button",disabled:e,"aria-label":this.label},on:{click:this.onClick}},[this.normalizeSlot(Vo,{expanded:this.toggleState})||t("span",{staticClass:"".concat(Lf,"-icon")})])}}),Nf=de({components:{BNavbar:If,BNavbarNav:Pf,BNavbarBrand:Ef,BNavbarToggle:Rf,BNavToggle:Rf},plugins:{NavPlugin:Df,CollapsePlugin:zl,DropdownPlugin:Sc}}),Mf=i.default.extend({name:sn,functional:!0,props:xn({type:{type:String,default:"border"},label:{type:String},variant:{type:String},small:{type:Boolean,default:!1},role:{type:String,default:"status"},tag:{type:String,default:"span"}},sn),render:function(t,e){var i,n=e.props,o=e.data,r=e.slots,a=e.scopedSlots,l=r(),u=Go(No,{},a||{},l)||n.label;return u&&(u=t("span",{staticClass:"sr-only"},u)),t(n.tag,F(o,{attrs:{role:u?n.role||"status":null,"aria-hidden":u?null:"true"},class:(i={},s(i,"spinner-".concat(n.type),n.type),s(i,"spinner-".concat(n.type,"-sm"),n.small),s(i,"text-".concat(n.variant),n.variant),i)}),[u||t()])}}),Hf={top:0,left:0,bottom:0,right:0},zf=de({components:{BOverlay:i.default.extend({name:Ui,mixins:[Wo],props:xn({show:{type:Boolean,default:!1},variant:{type:String,default:"light"},bgColor:{type:String},opacity:{type:[Number,String],default:.85,validator:function(t){var e=An(t,0);return e>=0&&e<=1}},blur:{type:String,default:"2px"},rounded:{type:[Boolean,String],default:!1},noCenter:{type:Boolean,default:!1},noFade:{type:Boolean,default:!1},spinnerType:{type:String,default:"border"},spinnerVariant:{type:String},spinnerSmall:{type:Boolean,default:!1},overlayTag:{type:String,default:"div"},wrapTag:{type:String,default:"div"},noWrap:{type:Boolean,default:!1},fixed:{type:Boolean,default:!1},zIndex:{type:[Number,String],default:10}},Ui),computed:{computedRounded:function(){var t=this.rounded;return!0===t||""===t?"rounded":t?"rounded-".concat(t):""},computedVariant:function(){return this.variant&&!this.bgColor?"bg-".concat(this.variant):""},overlayScope:function(){return{spinnerType:this.spinnerType||null,spinnerVariant:this.spinnerVariant||null,spinnerSmall:this.spinnerSmall}}},methods:{defaultOverlayFn:function(t){var e=t.spinnerType,i=t.spinnerVariant,n=t.spinnerSmall;return this.$createElement(Mf,{props:{type:e,variant:i,small:n}})}},render:function(t){var e=this,i=t();if(this.show){var n=this.overlayScope,o=t("div",{staticClass:"position-absolute",class:[this.computedVariant,this.computedRounded],style:u(u({},Hf),{},{opacity:this.opacity,backgroundColor:this.bgColor||null,backdropFilter:this.blur?"blur(".concat(this.blur,")"):null})}),r=t("div",{staticClass:"position-absolute",style:this.noCenter?u({},Hf):{top:"50%",left:"50%",transform:"translateX(-50%) translateY(-50%)"}},[this.normalizeSlot("overlay",n)||this.defaultOverlayFn(n)]);i=t(this.overlayTag,{key:"overlay",staticClass:"b-overlay",class:{"position-absolute":!this.noWrap||this.noWrap&&!this.fixed,"position-fixed":this.noWrap&&this.fixed},style:u(u({},Hf),{},{zIndex:this.zIndex||10}),on:{click:function(t){return e.$emit("click",t)}}},[o,r])}return i=t(Oo,{props:{noFade:this.noFade,appear:!0},on:{"after-enter":function(){return e.$emit("shown")},"after-leave":function(){return e.$emit("hidden")}}},[i]),this.noWrap?i:t(this.wrapTag,{staticClass:"b-overlay-wrap position-relative",attrs:{"aria-busy":this.show?"true":null}},this.noWrap?[i]:[this.normalizeSlot(),i])}})}}),jf=function(t){return Array.apply(null,{length:t})},Gf=function(t){var e=Pn(t)||1;return e<1?5:e},Wf=function(t,e){var i=Pn(t)||1;return i>e?e:i<1?1:i},Yf=function(t){if(t.keyCode===Ir)return Xo(t,{immediatePropagation:!0}),t.currentTarget.click(),!1},Uf=xn({disabled:{type:Boolean,default:!1},value:{type:[Number,String],default:null,validator:function(t){return!(!tt(t)&&Pn(t,0)<1)||(oe('"v-model" value must be a number greater than "0"',qi),!1)}},limit:{type:[Number,String],default:5,validator:function(t){return!(Pn(t,0)<1)||(oe('Prop "limit" must be a number greater than "0"',qi),!1)}},align:{type:String,default:"left"},pills:{type:Boolean,default:!1},hideGotoEndButtons:{type:Boolean,default:!1},ariaLabel:{type:String,default:"Pagination"},labelFirstPage:{type:String,default:"Go to first page"},firstText:{type:String,default:"«"},firstNumber:{type:Boolean,default:!1},firstClass:{type:[String,Array,Object],default:null},labelPrevPage:{type:String,default:"Go to previous page"},prevText:{type:String,default:"‹"},prevClass:{type:[String,Array,Object],default:null},labelNextPage:{type:String,default:"Go to next page"},nextText:{type:String,default:"›"},nextClass:{type:[String,Array,Object]},labelLastPage:{type:String,default:"Go to last page"},lastText:{type:String,default:"»"},lastNumber:{type:Boolean,default:!1},lastClass:{type:[String,Array,Object]},labelPage:{type:[String,Function],default:"Go to page"},pageClass:{type:[String,Array,Object]},hideEllipsis:{type:Boolean,default:!1},ellipsisText:{type:String,default:"…"},ellipsisClass:{type:[String,Array,Object]}},qi),qf={mixins:[Wo],model:{prop:"value",event:"input"},props:Uf,data:function(){var t=Pn(this.value,0);return{currentPage:t=t>0?t:-1,localNumberOfPages:1,localLimit:5}},computed:{btnSize:function(){return this.size?"pagination-".concat(this.size):""},alignment:function(){var t=this.align;return"center"===t?"justify-content-center":"end"===t||"right"===t?"justify-content-end":"fill"===t?"text-center":""},styleClass:function(){return this.pills?"b-pagination-pills":""},computedCurrentPage:function(){return Wf(this.currentPage,this.localNumberOfPages)},paginationParams:function(){var t=this.localLimit,e=this.localNumberOfPages,i=this.computedCurrentPage,n=this.hideEllipsis,o=this.firstNumber,r=this.lastNumber,a=!1,s=!1,l=t,u=1;e<=t?l=e:i<t-1&&t>3?(n&&!r||(s=!0,l=t-(o?0:1)),l=ir(l,t)):e-i+2<t&&t>3?(n&&!o||(a=!0,l=t-(r?0:1)),u=e-l+1):(t>3&&(l=t-(n?0:2),a=!(n&&!o),s=!(n&&!r)),u=i-ar(l/2)),u<1?(u=1,a=!1):u>e-l&&(u=e-l+1,s=!1),a&&o&&u<4&&(l+=2,u=1,a=!1);var c=u+l-1;return s&&r&&c>e-3&&(l+=c===e-2?2:3,s=!1),t<=3&&(o&&1===u?l=ir(l+1,e,t+1):r&&e===u+l-1&&(u=nr(u-1,1),l=ir(e-u+1,e,t+1))),{showFirstDots:a,showLastDots:s,numberOfLinks:l=ir(l,e-u+1),startNumber:u}},pageList:function(){var t=this.paginationParams,e=t.numberOfLinks,i=t.startNumber,n=this.computedCurrentPage,o=function(t,e){return jf(e).map((function(e,i){return{number:t+i,classes:null}}))}(i,e);if(o.length>3){var r=n-i,a="bv-d-xs-down-none";if(0===r)for(var s=3;s<o.length;s++)o[s].classes=a;else if(r===o.length-1)for(var l=0;l<o.length-3;l++)o[l].classes=a;else{for(var u=0;u<r-1;u++)o[u].classes=a;for(var c=o.length-1;c>r+1;c--)o[c].classes=a}}return o}},watch:{value:function(t,e){t!==e&&(this.currentPage=Wf(t,this.localNumberOfPages))},currentPage:function(t,e){t!==e&&this.$emit("input",t>0?t:null)},limit:function(t,e){t!==e&&(this.localLimit=Gf(t))}},created:function(){var t=this;this.localLimit=Gf(this.limit),this.$nextTick((function(){t.currentPage=t.currentPage>t.localNumberOfPages?t.localNumberOfPages:t.currentPage}))},methods:{handleKeyNav:function(t){var e=t.keyCode,i=t.shiftKey;this.isNav||(e===Dr||e===Or?(Xo(t,{propagation:!1}),i?this.focusFirst():this.focusPrev()):e!==Fr&&e!==Br||(Xo(t,{propagation:!1}),i?this.focusLast():this.focusNext()))},getButtons:function(){return ro("button.page-link, a.page-link",this.$el).filter((function(t){return io(t)}))},focusCurrent:function(){var t=this;this.$nextTick((function(){var e=t.getButtons().find((function(e){return Pn(vo(e,"aria-posinset"),0)===t.computedCurrentPage}));_o(e)||t.focusFirst()}))},focusFirst:function(){var t=this;this.$nextTick((function(){var e=t.getButtons().find((function(t){return!no(t)}));_o(e)}))},focusLast:function(){var t=this;this.$nextTick((function(){var e=t.getButtons().reverse().find((function(t){return!no(t)}));_o(e)}))},focusPrev:function(){var t=this;this.$nextTick((function(){var e=t.getButtons(),i=e.indexOf(Qn());i>0&&!no(e[i-1])&&_o(e[i-1])}))},focusNext:function(){var t=this;this.$nextTick((function(){var e=t.getButtons(),i=e.indexOf(Qn());i<e.length-1&&!no(e[i+1])&&_o(e[i+1])}))}},render:function(t){var e=this,i=[],n=this.localNumberOfPages,o=this.pageList.map((function(t){return t.number})),r=this.disabled,a=this.paginationParams,s=a.showFirstDots,l=a.showLastDots,u=this.computedCurrentPage,c="fill"===this.align,d=this.isNav,h=function(t){return t===u},f=this.currentPage<1,p=function(i,o,a,s,l,u,p){var m=r||h(u)||f||i<1||i>n,g=i<1?1:i>n?n:i,v={disabled:m,page:g,index:g-1},b=e.normalizeSlot(a,v)||Hn(s)||t(),y=t(m?"span":d?jr:"button",{staticClass:"page-link",class:{"flex-grow-1":!d&&!m&&c},props:m||!d?{}:e.linkProps(i),attrs:{role:d?null:"menuitem",type:d||m?null:"button",tabindex:m||d?null:"-1","aria-label":o,"aria-controls":e.ariaControls||null,"aria-disabled":m?"true":null},on:m?{}:{"!click":function(t){e.onClick(t,i)},keydown:Yf}},[b]);return t("li",{key:p,staticClass:"page-item",class:[{disabled:m,"flex-fill":c,"d-flex":c&&!d&&!m},l],attrs:{role:d?null:"presentation","aria-hidden":m?"true":null}},[y])},m=function(i){return t("li",{key:"ellipsis-".concat(i?"last":"first"),staticClass:"page-item",class:["disabled","bv-d-xs-down-none",c?"flex-fill":"",e.ellipsisClass],attrs:{role:"separator"}},[t("span",{staticClass:"page-link"},[e.normalizeSlot("ellipsis-text")||Hn(e.ellipsisText)||t()])])},g=function(i,o){var a=h(i.number)&&!f,s=r?null:a||f&&0===o?"0":"-1",l={role:d?null:"menuitemradio",type:d||r?null:"button","aria-disabled":r?"true":null,"aria-controls":e.ariaControls||null,"aria-label":it(e.labelPage)&&!Q(e.labelPage(i.number))?e.labelPage(i.number):"".concat(e.labelPage," ").concat(i.number),"aria-checked":d?null:a?"true":"false","aria-current":d&&a?"page":null,"aria-posinset":d?null:i.number,"aria-setsize":d?null:n,tabindex:d?null:s},u=Hn(e.makePage(i.number)),p={page:i.number,index:i.number-1,content:u,active:a,disabled:r},m=t(r?"span":d?jr:"button",{props:r||!d?{}:e.linkProps(i.number),staticClass:"page-link",class:{"flex-grow-1":!d&&!r&&c},attrs:l,on:r?{}:{"!click":function(t){e.onClick(t,i.number)},keydown:Yf}},[e.normalizeSlot("page",p)||u]);return t("li",{key:"page-".concat(i.number),staticClass:"page-item",class:[{disabled:r,active:a,"flex-fill":c,"d-flex":c&&!d&&!r},i.classes,e.pageClass],attrs:{role:d?null:"presentation"}},[m])},v=t();this.firstNumber||this.hideGotoEndButtons||(v=p(1,this.labelFirstPage,"first-text",this.firstText,this.firstClass,1,"pagination-goto-first")),i.push(v),i.push(p(u-1,this.labelPrevPage,"prev-text",this.prevText,this.prevClass,1,"pagination-goto-prev")),i.push(this.firstNumber&&1!==o[0]?g({number:1},0):t()),i.push(s?m(!1):t()),this.pageList.forEach((function(t,n){var r=s&&e.firstNumber&&1!==o[0]?1:0;i.push(g(t,n+r))})),i.push(l?m(!0):t()),i.push(this.lastNumber&&o[o.length-1]!==n?g({number:n},-1):t()),i.push(p(u+1,this.labelNextPage,"next-text",this.nextText,this.nextClass,n,"pagination-goto-next"));var b=t();this.lastNumber||this.hideGotoEndButtons||(b=p(n,this.labelLastPage,"last-text",this.lastText,this.lastClass,n,"pagination-goto-last")),i.push(b);var y=t("ul",{ref:"ul",staticClass:"pagination",class:["b-pagination",this.btnSize,this.alignment,this.styleClass],attrs:{role:d?null:"menubar","aria-disabled":r?"true":"false","aria-label":d?null:this.ariaLabel||null},on:d?{}:{keydown:this.handleKeyNav}},i);return d?t("nav",{attrs:{"aria-disabled":r?"true":null,"aria-hidden":r?"true":"false","aria-label":d&&this.ariaLabel||null}},[y]):y}},Kf=function(t){return nr(Pn(t)||20,1)},Xf=function(t){return nr(Pn(t)||0,0)},Zf=de({components:{BPagination:i.default.extend({name:qi,mixins:[qf],props:xn({size:{type:String},perPage:{type:[Number,String],default:20},totalRows:{type:[Number,String],default:0},ariaControls:{type:String}},qi),computed:{numberOfPages:function(){var t=rr(Xf(this.totalRows)/Kf(this.perPage));return t<1?1:t},pageSizeNumberOfPages:function(){return{perPage:Kf(this.perPage),totalRows:Xf(this.totalRows),numberOfPages:this.numberOfPages}}},watch:{pageSizeNumberOfPages:function(t,e){et(e)||(t.perPage!==e.perPage&&t.totalRows===e.totalRows||t.numberOfPages!==e.numberOfPages&&this.currentPage>t.numberOfPages)&&(this.currentPage=1),this.localNumberOfPages=t.numberOfPages}},created:function(){var t=this;this.localNumberOfPages=this.numberOfPages;var e=Pn(this.value,0);e>0?this.currentPage=e:this.$nextTick((function(){t.currentPage=0}))},mounted:function(){this.localNumberOfPages=this.numberOfPages},methods:{onClick:function(t,e){var i=this;if(e!==this.currentPage){var n=t.target,o=new BvEvent("page-click",{cancelable:!0,vueTarget:this,target:n});this.$emit(o.type,o,e),o.defaultPrevented||(this.currentPage=e,this.$emit("change",this.currentPage),this.$nextTick((function(){io(n)&&i.$el.contains(n)?_o(n):i.focusCurrent()})))}},makePage:function(t){return t},linkProps:function(){return{}}}})}}),Jf=Tt(zr,["event","routerTag"]),Qf=de({components:{BPaginationNav:i.default.extend({name:Ki,mixins:[qf],props:xn(u(u(u({},Uf),Jf),{},{size:{type:String},numberOfPages:{type:[Number,String],default:1,validator:function(t){return!(Pn(t,0)<1)||(oe('Prop "number-of-pages" must be a number greater than "0"',Ki),!1)}},baseUrl:{type:String,default:"/"},useRouter:{type:Boolean,default:!1},linkGen:{type:Function},pageGen:{type:Function},pages:{type:Array},noPageDetect:{type:Boolean,default:!1}}),Ki),computed:{isNav:function(){return!0},computedValue:function(){var t=Pn(this.value,0);return t<1?null:t}},watch:{numberOfPages:function(){var t=this;this.$nextTick((function(){t.setNumberOfPages()}))},pages:function(){var t=this;this.$nextTick((function(){t.setNumberOfPages()}))}},created:function(){this.setNumberOfPages()},mounted:function(){var t=this;this.$router&&this.$watch("$route",(function(){t.$nextTick((function(){Xn((function(){t.guessCurrentPage()}))}))}))},methods:{setNumberOfPages:function(){var t,e=this;st(this.pages)&&this.pages.length>0?this.localNumberOfPages=this.pages.length:this.localNumberOfPages=(t=this.numberOfPages,nr(Pn(t,0),1)),this.$nextTick((function(){e.guessCurrentPage()}))},onClick:function(t,e){var i=this;if(e!==this.currentPage){var n=t.currentTarget||t.target,o=new BvEvent("page-click",{cancelable:!0,vueTarget:this,target:n});this.$emit(o.type,o,e),o.defaultPrevented||(Xn((function(){i.currentPage=e,i.$emit("change",e)})),this.$nextTick((function(){Do(n)})))}},getPageInfo:function(t){if(!st(this.pages)||0===this.pages.length||Q(this.pages[t-1])){var e="".concat(this.baseUrl).concat(t);return{link:this.useRouter?{path:e}:e,text:Hn(t)}}var i=this.pages[t-1];if(lt(i)){var n=i.link;return{link:lt(n)?n:this.useRouter?{path:n}:n,text:Hn(i.text||t)}}return{link:Hn(i),text:Hn(t)}},makePage:function(t){var e=this.pageGen,i=this.getPageInfo(t);if(e&&it(e)){var n=e(t,i);if(!Q(n))return n}return i.text},makeLink:function(t){var e=this.linkGen,i=this.getPageInfo(t);if(e&&it(e)){var n=e(t,i);if(!Q(n))return n}return i.link},linkProps:function(t){var e=gr(Jf,this),i=this.makeLink(t);return this.useRouter||lt(i)?e.to=i:e.href=i,e},resolveLink:function(){var t,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";try{(t=document.createElement("a")).href=kr({to:e},"a","/","/"),document.body.appendChild(t);var i=t,n=i.pathname,o=i.hash,r=i.search;return document.body.removeChild(t),{path:n,hash:o,query:wr(r)}}catch(e){try{t&&t.parentNode&&t.parentNode.removeChild(t)}catch(t){}return{}}},resolveRoute:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";try{var e=this.$router.resolve(t,this.$route).route;return{path:e.path,hash:e.hash,query:e.query}}catch(t){return{}}},guessCurrentPage:function(){var t=this.computedValue,e=this.$router,i=this.$route;if(!this.noPageDetect&&!t&&(M||!M&&e))for(var n=e&&i?{path:i.path,hash:i.hash,query:i.query}:{},o=M?window.location||document.location:null,r=o?{path:o.pathname,hash:o.hash,query:wr(o.search)}:{},a=1;!t&&a<=this.localNumberOfPages;a++){var s=this.makeLink(a);t=e&&(lt(s)||this.useRouter)?Ar(this.resolveRoute(s),n)?a:null:M?Ar(this.resolveLink(s),r)?a:null:-1}this.currentPage=t>0?t:0}}})}}),tp={AUTO:"auto",TOP:"top",RIGHT:"right",BOTTOM:"bottom",LEFT:"left",TOPLEFT:"top",TOPRIGHT:"top",RIGHTTOP:"right",RIGHTBOTTOM:"right",BOTTOMLEFT:"bottom",BOTTOMRIGHT:"bottom",LEFTTOP:"left",LEFTBOTTOM:"left"},ep={AUTO:0,TOPLEFT:-1,TOP:0,TOPRIGHT:1,RIGHTTOP:-1,RIGHT:0,RIGHTBOTTOM:1,BOTTOMLEFT:-1,BOTTOM:0,BOTTOMRIGHT:1,LEFTTOP:-1,LEFT:0,LEFTBOTTOM:1},ip=i.default.extend({name:"BVPopper",props:{target:{type:[HTMLElement,SVGElement]},placement:{type:String,default:"top"},fallbackPlacement:{type:[String,Array],default:"flip"},offset:{type:Number,default:0},boundary:{type:[String,HTMLElement],default:"scrollParent"},boundaryPadding:{type:Number,default:5},arrowPadding:{type:Number,default:6}},data:function(){return{noFade:!1,localShow:!0,attachment:this.getAttachment(this.placement)}},computed:{templateType:function(){return"unknown"},popperConfig:function(){var t=this,e=this.placement;return{placement:this.getAttachment(e),modifiers:{offset:{offset:this.getOffset(e)},flip:{behavior:this.fallbackPlacement},arrow:{element:".arrow"},preventOverflow:{padding:this.boundaryPadding,boundariesElement:this.boundary}},onCreate:function(e){e.originalPlacement!==e.placement&&t.popperPlacementChange(e)},onUpdate:function(e){t.popperPlacementChange(e)}}}},created:function(){var t=this;this.$_popper=null,this.localShow=!0,this.$on("show",(function(e){t.popperCreate(e)}));var e=function(){t.$nextTick((function(){Xn((function(){t.$destroy()}))}))};this.$parent.$once("hook:destroyed",e),this.$once("hidden",e)},beforeMount:function(){this.attachment=this.getAttachment(this.placement)},updated:function(){this.updatePopper()},beforeDestroy:function(){this.destroyPopper()},destroyed:function(){var t=this.$el;t&&t.parentNode&&t.parentNode.removeChild(t)},methods:{hide:function(){this.localShow=!1},getAttachment:function(t){return tp[String(t).toUpperCase()]||"auto"},getOffset:function(t){if(!this.offset){var e=this.$refs.arrow||ao(".arrow",this.$el),i=An(Co(e).width,0)+An(this.arrowPadding,0);switch(ep[String(t).toUpperCase()]||0){case 1:return"+50%p - ".concat(i,"px");case-1:return"-50%p + ".concat(i,"px");default:return 0}}return this.offset},popperCreate:function(t){this.destroyPopper(),this.$_popper=new qu(this.target,t,this.popperConfig)},destroyPopper:function(){this.$_popper&&this.$_popper.destroy(),this.$_popper=null},updatePopper:function(){this.$_popper&&this.$_popper.scheduleUpdate()},popperPlacementChange:function(t){this.attachment=this.getAttachment(t.placement)},renderTemplate:function(t){return t("div")}},render:function(t){var e=this;return t(Oo,{props:{appear:!0,noFade:this.noFade},on:{beforeEnter:function(t){return e.$emit("show",t)},afterEnter:function(t){return e.$emit("shown",t)},beforeLeave:function(t){return e.$emit("hide",t)},afterLeave:function(t){return e.$emit("hidden",t)}}},[this.localShow?this.renderTemplate(t):t()])}}),np=i.default.extend({name:"BVTooltipTemplate",extends:ip,mixins:[Jh],props:{id:{type:String},html:{type:Boolean}},data:function(){return{title:"",content:"",variant:null,customClass:null,interactive:!0}},computed:{templateType:function(){return"tooltip"},templateClasses:function(){var t;return[(t={noninteractive:!this.interactive},s(t,"b-".concat(this.templateType,"-").concat(this.variant),this.variant),s(t,"bs-".concat(this.templateType,"-").concat(this.attachment),this.attachment),t),this.customClass]},templateAttributes:function(){return u(u({},this.$parent.$parent.$attrs),{},{id:this.id,role:"tooltip",tabindex:"-1"},this.scopedStyleAttrs)},templateListeners:function(){var t=this;return{mouseenter:function(e){t.$emit("mouseenter",e)},mouseleave:function(e){t.$emit("mouseleave",e)},focusin:function(e){t.$emit("focusin",e)},focusout:function(e){t.$emit("focusout",e)}}}},methods:{renderTemplate:function(t){var e=it(this.title)?this.title({}):et(this.title)?t():this.title,i=this.html&&!it(this.title)?{innerHTML:this.title}:{};return t("div",{staticClass:"tooltip b-tooltip",class:this.templateClasses,attrs:this.templateAttributes,on:this.templateListeners},[t("div",{ref:"arrow",staticClass:"arrow"}),t("div",{staticClass:"tooltip-inner",domProps:i},[e])])}}}),op=".modal-content",rp=[op,".b-sidebar"].join(", "),ap="data-original-title",sp={title:"",content:"",variant:null,customClass:null,triggers:"",placement:"auto",fallbackPlacement:"flip",target:null,container:null,noFade:!1,boundary:"scrollParent",boundaryPadding:5,offset:0,delay:0,arrowPadding:6,interactive:!0,disabled:!1,id:null,html:!1},lp=i.default.extend({name:"BVTooltip",data:function(){return u(u({},sp),{},{activeTrigger:{hover:!1,click:!1,focus:!1},localShow:!1})},computed:{templateType:function(){return"tooltip"},computedId:function(){return this.id||"__bv_".concat(this.templateType,"_").concat(this._uid,"__")},computedDelay:function(){var t={show:0,hide:0};return ut(this.delay)?(t.show=nr(Pn(this.delay.show,0),0),t.hide=nr(Pn(this.delay.hide,0),0)):(rt(this.delay)||ot(this.delay))&&(t.show=t.hide=nr(Pn(this.delay,0),0)),t},computedTriggers:function(){return Dn(this.triggers).filter(Boolean).join(" ").trim().toLowerCase().split(/\s+/).sort()},isWithActiveTrigger:function(){for(var t in this.activeTrigger)if(this.activeTrigger[t])return!0;return!1},computedTemplateData:function(){return{title:this.title,content:this.content,variant:this.variant,customClass:this.customClass,noFade:this.noFade,interactive:this.interactive}}},watch:{computedTriggers:function(t,e){var i=this;Ar(t,e)||this.$nextTick((function(){i.unListen(),e.forEach((function(e){_n(t,e)||i.activeTrigger[e]&&(i.activeTrigger[e]=!1)})),i.listen()}))},computedTemplateData:function(){this.handleTemplateUpdate()},title:function(t,e){t===e||t||this.hide()},disabled:function(t){t?this.disable():this.enable()}},created:function(){var t=this;this.$_tip=null,this.$_hoverTimeout=null,this.$_hoverState="",this.$_visibleInterval=null,this.$_enabled=!this.disabled,this.$_noop=il.bind(this),this.$parent&&this.$parent.$once("hook:beforeDestroy",(function(){t.$nextTick((function(){Xn((function(){t.$destroy()}))}))})),this.$nextTick((function(){var e=t.getTarget();e&&uo(document.body,e)?(t.scopeId=Zh(t.$parent),t.listen()):oe(ot(t.target)?'Unable to find target element by ID "#'.concat(t.target,'" in document.'):"The provided target is no valid HTML element.",t.templateType)}))},updated:function(){this.$nextTick(this.handleTemplateUpdate)},deactivated:function(){this.forceHide()},beforeDestroy:function(){this.unListen(),this.setWhileOpenListeners(!1),this.clearHoverTimeout(),this.clearVisibilityInterval(),this.destroyTemplate(),this.$_noop=null},methods:{getTemplate:function(){return np},updateData:function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},i=!1;bt(sp).forEach((function(n){Q(e[n])||t[n]===e[n]||(t[n]=e[n],"title"===n&&(i=!0))})),i&&this.localShow&&this.fixTitle()},createTemplateAndShow:function(){var t=this.getContainer(),e=this.getTemplate(),i=this.$_tip=new e({parent:this,propsData:{id:this.computedId,html:this.html,placement:this.placement,fallbackPlacement:this.fallbackPlacement,target:this.getPlacementTarget(),boundary:this.getBoundary(),offset:Pn(this.offset,0),arrowPadding:Pn(this.arrowPadding,0),boundaryPadding:Pn(this.boundaryPadding,0)}});this.handleTemplateUpdate(),i.$once("show",this.onTemplateShow),i.$once("shown",this.onTemplateShown),i.$once("hide",this.onTemplateHide),i.$once("hidden",this.onTemplateHidden),i.$once("hook:destroyed",this.destroyTemplate),i.$on("focusin",this.handleEvent),i.$on("focusout",this.handleEvent),i.$on("mouseenter",this.handleEvent),i.$on("mouseleave",this.handleEvent),i.$mount(t.appendChild(document.createElement("div")))},hideTemplate:function(){this.$_tip&&this.$_tip.hide(),this.clearActiveTriggers(),this.$_hoverState=""},destroyTemplate:function(){this.setWhileOpenListeners(!1),this.clearHoverTimeout(),this.$_hoverState="",this.clearActiveTriggers(),this.localPlacementTarget=null;try{this.$_tip.$destroy()}catch(t){}this.$_tip=null,this.removeAriaDescribedby(),this.restoreTitle(),this.localShow=!1},getTemplateElement:function(){return this.$_tip?this.$_tip.$el:null},handleTemplateUpdate:function(){var t=this,e=this.$_tip;if(e){["title","content","variant","customClass","noFade","interactive"].forEach((function(i){e[i]!==t[i]&&(e[i]=t[i])}))}},show:function(){var t=this.getTarget();if(t&&uo(document.body,t)&&io(t)&&!this.dropdownOpen()&&(!et(this.title)&&""!==this.title||!et(this.content)&&""!==this.content)&&!this.$_tip&&!this.localShow){this.localShow=!0;var e=this.buildEvent("show",{cancelable:!0});this.emitEvent(e),e.defaultPrevented?this.destroyTemplate():(this.fixTitle(),this.addAriaDescribedby(),this.createTemplateAndShow())}},hide:function(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0],e=this.getTemplateElement();if(e&&this.localShow){var i=this.buildEvent("hide",{cancelable:!t});this.emitEvent(i),i.defaultPrevented||this.hideTemplate()}else this.restoreTitle()},forceHide:function(){this.getTemplateElement()&&this.localShow&&(this.setWhileOpenListeners(!1),this.clearHoverTimeout(),this.$_hoverState="",this.clearActiveTriggers(),this.$_tip&&(this.$_tip.noFade=!0),this.hide(!0))},enable:function(){this.$_enabled=!0,this.emitEvent(this.buildEvent("enabled"))},disable:function(){this.$_enabled=!1,this.emitEvent(this.buildEvent("disabled"))},onTemplateShow:function(){this.setWhileOpenListeners(!0)},onTemplateShown:function(){var t=this.$_hoverState;this.$_hoverState="","out"===t&&this.leave(null),this.emitEvent(this.buildEvent("shown"))},onTemplateHide:function(){this.setWhileOpenListeners(!1)},onTemplateHidden:function(){this.destroyTemplate(),this.emitEvent(this.buildEvent("hidden"))},getTarget:function(){var t=this.target;return ot(t)?t=co(t.replace(/^#/,"")):it(t)?t=t():t&&(t=t.$el||t),Jn(t)?t:null},getPlacementTarget:function(){return this.getTarget()},getTargetId:function(){var t=this.getTarget();return t&&t.id?t.id:null},getContainer:function(){var t=!!this.container&&(this.container.$el||this.container),e=document.body,i=this.getTarget();return!1===t?lo(rp,i)||e:ot(t)&&co(t.replace(/^#/,""))||e},getBoundary:function(){return this.boundary?this.boundary.$el||this.boundary:"scrollParent"},isInModal:function(){var t=this.getTarget();return t&&lo(op,t)},isDropdown:function(){var t=this.getTarget();return t&&po(t,"dropdown")},dropdownOpen:function(){var t=this.getTarget();return this.isDropdown()&&t&&ao(".dropdown-menu.show",t)},clearHoverTimeout:function(){clearTimeout(this.$_hoverTimeout),this.$_hoverTimeout=null},clearVisibilityInterval:function(){clearInterval(this.$_visibleInterval),this.$_visibleInterval=null},clearActiveTriggers:function(){for(var t in this.activeTrigger)this.activeTrigger[t]=!1},addAriaDescribedby:function(){var t=this.getTarget(),e=vo(t,"aria-describedby")||"";e=e.split(/\s+/).concat(this.computedId).join(" ").trim(),mo(t,"aria-describedby",e)},removeAriaDescribedby:function(){var t=this,e=this.getTarget(),i=vo(e,"aria-describedby")||"";(i=i.split(/\s+/).filter((function(e){return e!==t.computedId})).join(" ").trim())?mo(e,"aria-describedby",i):go(e,"aria-describedby")},fixTitle:function(){var t=this.getTarget();if(bo(t,"title")){var e=vo(t,"title");mo(t,"title",""),e&&mo(t,ap,e)}},restoreTitle:function(){var t=this.getTarget();if(bo(t,ap)){var e=vo(t,ap);go(t,ap),e&&mo(t,"title",e)}},buildEvent:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return new BvEvent(t,u({cancelable:!1,target:this.getTarget(),relatedTarget:this.getTemplateElement()||null,componentId:this.computedId,vueTarget:this},e))},emitEvent:function(t){var e=t.type,i=this.$root;i&&i.$emit&&i.$emit("bv::".concat(this.templateType,"::").concat(e),t),this.$emit(e,t)},listen:function(){var t=this,e=this.getTarget();e&&(this.setRootListener(!0),this.computedTriggers.forEach((function(i){"click"===i?Uo(e,"click",t.handleEvent,el):"focus"===i?(Uo(e,"focusin",t.handleEvent,el),Uo(e,"focusout",t.handleEvent,el)):"blur"===i?Uo(e,"focusout",t.handleEvent,el):"hover"===i&&(Uo(e,"mouseenter",t.handleEvent,el),Uo(e,"mouseleave",t.handleEvent,el))}),this))},unListen:function(){var t=this,e=this.getTarget();this.setRootListener(!1),["click","focusin","focusout","mouseenter","mouseleave"].forEach((function(i){e&&qo(e,i,t.handleEvent,el)}),this)},setRootListener:function(t){var e=this.$root;if(e){var i=t?"$on":"$off",n=this.templateType;e[i]("bv::hide::".concat(n),this.doHide),e[i]("bv::show::".concat(n),this.doShow),e[i]("bv::disable::".concat(n),this.doDisable),e[i]("bv::enable::".concat(n),this.doEnable)}},setWhileOpenListeners:function(t){this.setModalListener(t),this.setDropdownListener(t),this.visibleCheck(t),this.setOnTouchStartListener(t)},visibleCheck:function(t){var e=this;this.clearVisibilityInterval();var i=this.getTarget(),n=this.getTemplateElement();t&&(this.$_visibleInterval=setInterval((function(){!n||!e.localShow||i.parentNode&&io(i)||e.forceHide()}),100))},setModalListener:function(t){this.isInModal()&&this.$root[t?"$on":"$off"]("bv::modal::hidden",this.forceHide)},setOnTouchStartListener:function(t){var e=this;"ontouchstart"in document.documentElement&&$n(document.body.children).forEach((function(i){Ko(t,i,"mouseover",e.$_noop)}))},setDropdownListener:function(t){var e=this.getTarget();e&&this.$root&&this.isDropdown&&e.__vue__&&e.__vue__[t?"$on":"$off"]("shown",this.forceHide)},handleEvent:function(t){var e=this.getTarget();if(e&&!no(e)&&this.$_enabled&&!this.dropdownOpen()){var i=t.type,n=this.computedTriggers;if("click"===i&&_n(n,"click"))this.click(t);else if("mouseenter"===i&&_n(n,"hover"))this.enter(t);else if("focusin"===i&&_n(n,"focus"))this.enter(t);else if("focusout"===i&&(_n(n,"focus")||_n(n,"blur"))||"mouseleave"===i&&_n(n,"hover")){var o=this.getTemplateElement(),r=t.target,a=t.relatedTarget;if(o&&uo(o,r)&&uo(e,a)||o&&uo(e,r)&&uo(o,a)||o&&uo(o,r)&&uo(o,a)||uo(e,r)&&uo(e,a))return;this.leave(t)}}},doHide:function(t){t&&this.getTargetId()!==t&&this.computedId!==t||this.forceHide()},doShow:function(t){t&&this.getTargetId()!==t&&this.computedId!==t||this.show()},doDisable:function(t){t&&this.getTargetId()!==t&&this.computedId!==t||this.disable()},doEnable:function(t){t&&this.getTargetId()!==t&&this.computedId!==t||this.enable()},click:function(t){this.$_enabled&&!this.dropdownOpen()&&(_o(t.currentTarget),this.activeTrigger.click=!this.activeTrigger.click,this.isWithActiveTrigger?this.enter(null):this.leave(null))},toggle:function(){this.$_enabled&&!this.dropdownOpen()&&(this.localShow?this.leave(null):this.enter(null))},enter:function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;e&&(this.activeTrigger["focusin"===e.type?"focus":"hover"]=!0),this.localShow||"in"===this.$_hoverState?this.$_hoverState="in":(this.clearHoverTimeout(),this.$_hoverState="in",this.computedDelay.show?(this.fixTitle(),this.$_hoverTimeout=setTimeout((function(){"in"===t.$_hoverState?t.show():t.localShow||t.restoreTitle()}),this.computedDelay.show)):this.show())},leave:function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;e&&(this.activeTrigger["focusout"===e.type?"focus":"hover"]=!1,"focusout"===e.type&&_n(this.computedTriggers,"blur")&&(this.activeTrigger.click=!1,this.activeTrigger.hover=!1)),this.isWithActiveTrigger||(this.clearHoverTimeout(),this.$_hoverState="out",this.computedDelay.hide?this.$_hoverTimeout=setTimeout((function(){"out"===t.$_hoverState&&t.hide()}),this.computedDelay.hide):this.hide())}}}),up=i.default.extend({name:bn,inheritAttrs:!1,props:xn({title:{type:String},target:{type:[String,HTMLElement,SVGElement,Function,Object],required:!0},triggers:{type:[String,Array],default:"hover focus"},placement:{type:String,default:"top"},fallbackPlacement:{type:[String,Array],default:"flip",validator:function(t){return st(t)&&t.every((function(t){return ot(t)}))||_n(["flip","clockwise","counterclockwise"],t)}},variant:{type:String},customClass:{type:String},delay:{type:[Number,Object,String],default:50},boundary:{type:[String,HTMLElement,Object],default:"scrollParent"},boundaryPadding:{type:[Number,String],default:5},offset:{type:[Number,String],default:0},noFade:{type:Boolean,default:!1},container:{type:[String,HTMLElement,Object]},show:{type:Boolean,default:!1},noninteractive:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},id:{type:String}},bn),data:function(){return{localShow:this.show,localTitle:"",localContent:""}},computed:{templateData:function(){return{title:this.localTitle,content:this.localContent,target:this.target,triggers:this.triggers,placement:this.placement,fallbackPlacement:this.fallbackPlacement,variant:this.variant,customClass:this.customClass,container:this.container,boundary:this.boundary,boundaryPadding:this.boundaryPadding,delay:this.delay,offset:this.offset,noFade:this.noFade,interactive:!this.noninteractive,disabled:this.disabled,id:this.id}},templateTitleContent:function(){return{title:this.title,content:this.content}}},watch:{show:function(t,e){t!==e&&t!==this.localShow&&this.$_toolpop&&(t?this.$_toolpop.show():this.$_toolpop.forceHide())},disabled:function(t){t?this.doDisable():this.doEnable()},localShow:function(t){this.$emit("update:show",t)},templateData:function(){var t=this;this.$nextTick((function(){t.$_toolpop&&t.$_toolpop.updateData(t.templateData)}))},templateTitleContent:function(){this.$nextTick(this.updateContent)}},created:function(){this.$_toolpop=null},updated:function(){this.$nextTick(this.updateContent)},beforeDestroy:function(){this.$off("open",this.doOpen),this.$off("close",this.doClose),this.$off("disable",this.doDisable),this.$off("enable",this.doEnable),this.$_toolpop&&(this.$_toolpop.$destroy(),this.$_toolpop=null)},mounted:function(){var t=this;this.$nextTick((function(){var e=t.getComponent();t.updateContent();var i=Zh(t)||Zh(t.$parent),n=t.$_toolpop=new e({parent:t,_scopeId:i||void 0});n.updateData(t.templateData),n.$on("show",t.onShow),n.$on("shown",t.onShown),n.$on("hide",t.onHide),n.$on("hidden",t.onHidden),n.$on("disabled",t.onDisabled),n.$on("enabled",t.onEnabled),t.disabled&&t.doDisable(),t.$on("open",t.doOpen),t.$on("close",t.doClose),t.$on("disable",t.doDisable),t.$on("enable",t.doEnable),t.localShow&&n.show()}))},methods:{getComponent:function(){return lp},updateContent:function(){this.setTitle(this.$scopedSlots.default||this.title)},setTitle:function(t){t=et(t)?"":t,this.localTitle!==t&&(this.localTitle=t)},setContent:function(t){t=et(t)?"":t,this.localContent!==t&&(this.localContent=t)},onShow:function(t){this.$emit("show",t),t&&(this.localShow=!t.defaultPrevented)},onShown:function(t){this.localShow=!0,this.$emit("shown",t)},onHide:function(t){this.$emit("hide",t)},onHidden:function(t){this.$emit("hidden",t),this.localShow=!1},onDisabled:function(t){t&&"disabled"===t.type&&(this.$emit("update:disabled",!0),this.$emit("disabled",t))},onEnabled:function(t){t&&"enabled"===t.type&&(this.$emit("update:disabled",!1),this.$emit("enabled",t))},doOpen:function(){!this.localShow&&this.$_toolpop&&this.$_toolpop.show()},doClose:function(){this.localShow&&this.$_toolpop&&this.$_toolpop.hide()},doDisable:function(){this.$_toolpop&&this.$_toolpop.disable()},doEnable:function(){this.$_toolpop&&this.$_toolpop.enable()}},render:function(t){return t()}}),cp=i.default.extend({name:"BVPopoverTemplate",extends:np,computed:{templateType:function(){return"popover"}},methods:{renderTemplate:function(t){var e=it(this.title)?this.title({}):this.title,i=it(this.content)?this.content({}):this.content,n=this.html&&!it(this.title)?{innerHTML:this.title}:{},o=this.html&&!it(this.content)?{innerHTML:this.content}:{};return t("div",{staticClass:"popover b-popover",class:this.templateClasses,attrs:this.templateAttributes,on:this.templateListeners},[t("div",{ref:"arrow",staticClass:"arrow"}),et(e)||""===e?t():t("h3",{staticClass:"popover-header",domProps:n},[e]),et(i)||""===i?t():t("div",{staticClass:"popover-body",domProps:o},[i])])}}}),dp=i.default.extend({name:"BVPopover",extends:lp,computed:{templateType:function(){return"popover"}},methods:{getTemplate:function(){return cp}}}),hp=i.default.extend({name:Xi,extends:up,inheritAttrs:!1,props:xn({title:{type:String},content:{type:String},triggers:{type:[String,Array],default:"click"},placement:{type:String,default:"right"},variant:{type:String,default:void 0},customClass:{type:String,default:void 0},delay:{type:[Number,Object,String],default:50},boundary:{type:[String,HTMLElement,Object],default:"scrollParent"},boundaryPadding:{type:[Number,String],default:5}},Xi),methods:{getComponent:function(){return dp},updateContent:function(){this.setContent(this.$scopedSlots.default||this.content),this.setTitle(this.$scopedSlots.title||this.title)}}}),fp="__BV_Popover__",pp={focus:!0,hover:!0,click:!0,blur:!0,manual:!0},mp=/^html$/i,gp=/^nofade$/i,vp=/^(auto|top(left|right)?|bottom(left|right)?|left(top|bottom)?|right(top|bottom)?)$/i,bp=/^(window|viewport|scrollParent)$/i,yp=/^d\d+$/i,Sp=/^ds\d+$/i,wp=/^dh\d+$/i,Tp=/^o-?\d+$/i,Cp=/^v-.+$/i,kp=/\s+/,Bp=function(t,e,i){if(M){var n=function(t,e){var i={title:void 0,content:void 0,trigger:"",placement:"right",fallbackPlacement:"flip",container:!1,animation:!0,offset:0,disabled:!1,id:null,html:!1,delay:Tn(Xi,"delay",50),boundary:String(Tn(Xi,"boundary","scrollParent")),boundaryPadding:Pn(Tn(Xi,"boundaryPadding",5),0),variant:Tn(Xi,"variant"),customClass:Tn(Xi,"customClass")};if(ot(t.value)||rt(t.value)||it(t.value)?i.content=t.value:ut(t.value)&&(i=u(u({},i),t.value)),t.arg&&(i.container="#".concat(t.arg)),Q(i.title)){var n=e.data||{};i.title=n.attrs&&!et(n.attrs.title)?n.attrs.title:void 0}ut(i.delay)||(i.delay={show:Pn(i.delay,0),hide:Pn(i.delay,0)}),bt(t.modifiers).forEach((function(t){if(mp.test(t))i.html=!0;else if(gp.test(t))i.animation=!1;else if(vp.test(t))i.placement=t;else if(bp.test(t))t="scrollparent"===t?"scrollParent":t,i.boundary=t;else if(yp.test(t)){var e=Pn(t.slice(1),0);i.delay.show=e,i.delay.hide=e}else Sp.test(t)?i.delay.show=Pn(t.slice(2),0):wp.test(t)?i.delay.hide=Pn(t.slice(2),0):Tp.test(t)?i.offset=Pn(t.slice(1),0):Cp.test(t)&&(i.variant=t.slice(2)||null)}));var o={};return Dn(i.trigger||"").filter(ee).join(" ").trim().toLowerCase().split(kp).forEach((function(t){pp[t]&&(o[t]=!0)})),bt(t.modifiers).forEach((function(t){t=t.toLowerCase(),pp[t]&&(o[t]=!0)})),i.trigger=bt(o).join(" "),"blur"===i.trigger&&(i.trigger="focus"),i.trigger||(i.trigger="click"),i}(e,i);if(!t[fp]){var o=i.context;t[fp]=new dp({parent:o,_scopeId:Zh(o,void 0)}),t[fp].__bv_prev_data__={},t[fp].$on("show",(function(){var e={};it(n.title)&&(e.title=n.title(t)),it(n.content)&&(e.content=n.content(t)),bt(e).length>0&&t[fp].updateData(e)}))}var r={title:n.title,content:n.content,triggers:n.trigger,placement:n.placement,fallbackPlacement:n.fallbackPlacement,variant:n.variant,customClass:n.customClass,container:n.container,boundary:n.boundary,delay:n.delay,offset:n.offset,noFade:!n.animation,id:n.id,disabled:n.disabled,html:n.html},a=t[fp].__bv_prev_data__;if(t[fp].__bv_prev_data__=r,!Ar(r,a)){var s={target:t};bt(r).forEach((function(e){r[e]!==a[e]&&(s[e]="title"!==e&&"content"!==e||!it(r[e])?r[e]:r[e](t))})),t[fp].updateData(s)}}},xp=de({directives:{VBPopover:{bind:function(t,e,i){Bp(t,e,i)},componentUpdated:function(t,e,i){i.context.$nextTick((function(){Bp(t,e,i)}))},unbind:function(t){!function(t){t[fp]&&(t[fp].$destroy(),t[fp]=null),delete t[fp]}(t)}}}}),$p=de({components:{BPopover:hp},plugins:{VBPopoverPlugin:xp}}),_p=i.default.extend({name:Ji,mixins:[Wo],inject:{bvProgress:{default:function(){return{}}}},props:xn({value:{type:[Number,String],default:0},label:{type:String},labelHtml:{type:String},max:{type:[Number,String],default:null},precision:{type:[Number,String],default:null},variant:{type:String},striped:{type:Boolean,default:null},animated:{type:Boolean,default:null},showProgress:{type:Boolean,default:null},showValue:{type:Boolean,default:null}},Ji),computed:{progressBarClasses:function(){return[this.computedVariant?"bg-".concat(this.computedVariant):"",this.computedStriped||this.computedAnimated?"progress-bar-striped":"",this.computedAnimated?"progress-bar-animated":""]},progressBarStyles:function(){return{width:this.computedValue/this.computedMax*100+"%"}},computedValue:function(){return An(this.value,0)},computedMax:function(){var t=An(this.max)||An(this.bvProgress.max,0);return t>0?t:100},computedPrecision:function(){return nr(Pn(this.precision,Pn(this.bvProgress.precision,0)),0)},computedProgress:function(){var t=this.computedPrecision,e=sr(10,t);return Vn(100*e*this.computedValue/this.computedMax/e,t)},computedVariant:function(){return this.variant||this.bvProgress.variant},computedStriped:function(){return nt(this.striped)?this.striped:this.bvProgress.striped||!1},computedAnimated:function(){return nt(this.animated)?this.animated:this.bvProgress.animated||!1},computedShowProgress:function(){return nt(this.showProgress)?this.showProgress:this.bvProgress.showProgress||!1},computedShowValue:function(){return nt(this.showValue)?this.showValue:this.bvProgress.showValue||!1}},render:function(t){var e,i=this.label,n=this.labelHtml,o=this.computedValue,r=this.computedPrecision,a={};return this.hasNormalizedSlot()?e=this.normalizeSlot():i||n?a=Ea(n,i):this.computedShowProgress?e=this.computedProgress:this.computedShowValue&&(e=Vn(o,r)),t("div",{staticClass:"progress-bar",class:this.progressBarClasses,style:this.progressBarStyles,attrs:{role:"progressbar","aria-valuemin":"0","aria-valuemax":Hn(this.computedMax),"aria-valuenow":Vn(o,r)},domProps:a},e)}}),Dp=de({components:{BProgress:i.default.extend({name:Zi,mixins:[Wo],provide:function(){return{bvProgress:this}},props:xn({variant:{type:String},striped:{type:Boolean,default:!1},animated:{type:Boolean,default:!1},height:{type:String},precision:{type:[Number,String],default:0},showProgress:{type:Boolean,default:!1},showValue:{type:Boolean,default:!1},max:{type:[Number,String],default:100},value:{type:[Number,String],default:0}},Zi),computed:{progressHeight:function(){return{height:this.height||null}}},render:function(t){var e=this.normalizeSlot();return e||(e=t(_p,{props:{value:this.value,max:this.max,precision:this.precision,variant:this.variant,animated:this.animated,striped:this.striped,showProgress:this.showProgress,showValue:this.showValue}})),t("div",{class:["progress"],style:this.progressHeight},[e])}}),BProgressBar:_p}}),Fp="b-sidebar",Ip=function(t,e){if(e.noHeader)return t();var i=function(t,e){var i=e.computedTile;return i?t("strong",{attrs:{id:e.safeId("__title__")}},[i]):t("span")}(t,e),n=function(t,e){if(e.noHeaderClose)return t();var i=e.closeLabel,n=e.textVariant,o=e.hide;return t(Jo,{ref:"close-button",props:{ariaLabel:i,textVariant:n},on:{click:o}},[e.normalizeSlot("header-close")||t(Ca)])}(t,e);return t("header",{key:"header",staticClass:"".concat(Fp,"-header"),class:e.headerClass},e.right?[n,i]:[i,n])},Op=function(t,e){return t("div",{key:"body",staticClass:"".concat(Fp,"-body"),class:e.bodyClass},[e.normalizeSlot(Vo,e.slotScope)])},Pp=function(t,e){var i=e.normalizeSlot(Lo,e.slotScope);return i?t("footer",{key:"footer",staticClass:"".concat(Fp,"-footer"),class:e.footerClass},[i]):t()},Ap=function(t,e){var i=Ip(t,e);return e.lazy&&!e.isOpen?i:[i,Op(t,e),Pp(t,e)]},Vp=function(t,e){if(!e.backdrop)return t();var i=e.backdropVariant;return t("div",{directives:[{name:"show",value:e.localShow}],staticClass:"b-sidebar-backdrop",class:s({},"bg-".concat(i),!!i),on:{click:e.onBackdropClick}})},Ep=de({components:{BSidebar:i.default.extend({name:tn,mixins:[Rr,vs,pl,Wo],inheritAttrs:!1,model:{prop:"visible",event:"change"},props:xn({title:{type:String},right:{type:Boolean,default:!1},bgVariant:{type:String,default:"light"},textVariant:{type:String,default:"dark"},shadow:{type:[Boolean,String],default:!1},width:{type:String},zIndex:{type:[Number,String]},ariaLabel:{type:String},ariaLabelledby:{type:String},closeLabel:{type:String},tag:{type:String,default:"div"},sidebarClass:{type:[String,Array,Object]},headerClass:{type:[String,Array,Object]},bodyClass:{type:[String,Array,Object]},footerClass:{type:[String,Array,Object]},backdrop:{type:Boolean,default:!1},backdropVariant:{type:String,default:"dark"},noSlide:{type:Boolean,default:!1},noHeader:{type:Boolean,default:!1},noHeaderClose:{type:Boolean,default:!1},noCloseOnEsc:{type:Boolean,default:!1},noCloseOnBackdrop:{type:Boolean,default:!1},noCloseOnRouteChange:{type:Boolean,default:!1},noEnforceFocus:{type:Boolean,default:!1},lazy:{type:Boolean,default:!1},visible:{type:Boolean,default:!1}},tn),data:function(){return{localShow:!!this.visible,isOpen:!!this.visible}},computed:{transitionProps:function(){return this.noSlide?{css:!0}:{css:!0,enterClass:"",enterActiveClass:"slide",enterToClass:"show",leaveClass:"show",leaveActiveClass:"slide",leaveToClass:""}},slotScope:function(){return{visible:this.localShow,right:this.right,hide:this.hide}},computedTile:function(){return this.normalizeSlot(zo,this.slotScope)||Hn(this.title)||null},titleId:function(){return this.computedTile?this.safeId("__title__"):null},computedAttrs:function(){return u(u({},this.bvAttrs),{},{id:this.safeId(),tabindex:"-1",role:"dialog","aria-modal":this.backdrop?"true":"false","aria-hidden":this.localShow?null:"true","aria-label":this.ariaLabel||null,"aria-labelledby":this.ariaLabelledby||this.titleId||null})}},watch:{visible:function(t,e){t!==e&&(this.localShow=t)},localShow:function(t,e){t!==e&&(this.emitState(t),this.$emit("change",t))},$route:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.noCloseOnRouteChange||t.fullPath===e.fullPath||this.hide()}},created:function(){this.$_returnFocusEl=null},mounted:function(){var t=this;this.listenOnRoot($l,this.handleToggle),this.listenOnRoot(Fl,this.handleSync),this.$nextTick((function(){t.emitState(t.localShow)}))},activated:function(){this.emitSync()},beforeDestroy:function(){this.localShow=!1,this.$_returnFocusEl=null},methods:{hide:function(){this.localShow=!1},emitState:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.localShow;this.emitOnRoot(_l,this.safeId(),t)},emitSync:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.localShow;this.emitOnRoot(Dl,this.safeId(),t)},handleToggle:function(t){t&&t===this.safeId()&&(this.localShow=!this.localShow)},handleSync:function(t){var e=this;t&&t===this.safeId()&&this.$nextTick((function(){e.emitSync(e.localShow)}))},onKeydown:function(t){var e=t.keyCode;!this.noCloseOnEsc&&27===e&&this.localShow&&this.hide()},onBackdropClick:function(){this.localShow&&!this.noCloseOnBackdrop&&this.hide()},onTopTrapFocus:function(){var t=$o(this.$refs.content);this.enforceFocus(t.reverse()[0])},onBottomTrapFocus:function(){var t=$o(this.$refs.content);this.enforceFocus(t[0])},onBeforeEnter:function(){this.$_returnFocusEl=Qn(M?[document.body]:[]),this.isOpen=!0},onAfterEnter:function(t){uo(t,Qn())||this.enforceFocus(t),this.$emit("shown")},onAfterLeave:function(){this.enforceFocus(this.$_returnFocusEl),this.$_returnFocusEl=null,this.isOpen=!1,this.$emit("hidden")},enforceFocus:function(t){this.noEnforceFocus||_o(t)}},render:function(t){var e,i=this.localShow,n=""===this.shadow||this.shadow,o=t(this.tag,{ref:"content",directives:[{name:"show",value:i}],staticClass:Fp,class:[(e={shadow:!0===n},s(e,"shadow-".concat(n),n&&!0!==n),s(e,"".concat(Fp,"-right"),this.right),s(e,"bg-".concat(this.bgVariant),!!this.bgVariant),s(e,"text-".concat(this.textVariant),!!this.textVariant),e),this.sidebarClass],attrs:this.computedAttrs,style:{width:this.width}},[Ap(t,this)]);o=t("transition",{props:this.transitionProps,on:{beforeEnter:this.onBeforeEnter,afterEnter:this.onAfterEnter,afterLeave:this.onAfterLeave}},[o]);var r=t(Oo,{props:{noFade:this.noSlide}},[Vp(t,this)]),a=t(),l=t();return this.backdrop&&this.localShow&&(a=t("div",{attrs:{tabindex:"0"},on:{focus:this.onTopTrapFocus}}),l=t("div",{attrs:{tabindex:"0"},on:{focus:this.onBottomTrapFocus}})),t("div",{staticClass:"b-sidebar-outer",style:{zIndex:this.zIndex},attrs:{tabindex:"-1"},on:{keydown:this.onKeydown}},[a,o,l,r])}})},plugins:{VBTogglePlugin:Hl}}),Lp=i.default.extend({name:en,functional:!0,props:xn({animation:{type:String,default:"wave"},type:{type:String,default:"text"},width:{type:String},height:{type:String},size:{type:String},variant:{type:String}},en),render:function(t,e){var i,n=e.data,o=e.props,r=o.size,a=o.animation,l=o.variant;return t("div",F(n,{staticClass:"b-skeleton",style:{width:r||o.width,height:r||o.height},class:(i={},s(i,"b-skeleton-".concat(o.type),!0),s(i,"b-skeleton-animate-".concat(a),a),s(i,"bg-".concat(l),l),i)}))}}),Rp=(xn(ea,Ci),i.default.extend({name:nn,functional:!0,props:xn({animation:{type:String,default:"wave"},icon:{type:String},iconProps:{type:Object,default:function(){}}},nn),render:function(t,e){var i=e.props,n=i.icon,o=i.animation,r=t(Ba,{props:u({icon:n},i.iconProps),staticClass:"b-skeleton-icon"});return t("div",{staticClass:"b-skeleton-icon-wrapper position-relative d-inline-block overflow-hidden",class:s({},"b-skeleton-animate-".concat(o),o)},[r])}})),Np=i.default.extend({name:on,functional:!0,props:xn({animation:{type:String},aspect:{type:String,default:"16:9"},noAspect:{type:Boolean,default:!1},height:{type:String},width:{type:String},variant:{type:String},cardImg:{type:String}},on),render:function(t,e){var i=e.props,n=i.aspect,o=i.width,r=i.height,a=i.animation,l=i.variant,u=i.cardImg,c=t(Lp,{props:{type:"img",width:o,height:r,animation:a,variant:l},class:s({},"card-img-".concat(u),u)});return i.noAspect?c:t(cr,{props:{aspect:n}},[c])}}),Mp={methods:{hasListener:function(t){var e=this.$listeners||{},i=this._events||{};return!Q(e[t])||st(i[t])&&i[t].length>0}}},Hp=xn({variant:{type:String,default:null}},"BTr"),zp="light",jp="dark",Gp=i.default.extend({name:"BTr",mixins:[Rr,Nr,Wo],provide:function(){return{bvTableTr:this}},inject:{bvTableRowGroup:{default:function(){return{}}}},inheritAttrs:!1,props:Hp,computed:{inTbody:function(){return this.bvTableRowGroup.isTbody},inThead:function(){return this.bvTableRowGroup.isThead},inTfoot:function(){return this.bvTableRowGroup.isTfoot},isDark:function(){return this.bvTableRowGroup.isDark},isStacked:function(){return this.bvTableRowGroup.isStacked},isResponsive:function(){return this.bvTableRowGroup.isResponsive},isStickyHeader:function(){return this.bvTableRowGroup.isStickyHeader},hasStickyHeader:function(){return!this.isStacked&&this.bvTableRowGroup.hasStickyHeader},tableVariant:function(){return this.bvTableRowGroup.tableVariant},headVariant:function(){return this.inThead?this.bvTableRowGroup.headVariant:null},footVariant:function(){return this.inTfoot?this.bvTableRowGroup.footVariant:null},isRowDark:function(){return this.headVariant!==zp&&this.footVariant!==zp&&(this.headVariant===jp||this.footVariant===jp||this.isDark)},trClasses:function(){return[this.variant?"".concat(this.isRowDark?"bg":"table","-").concat(this.variant):null]},trAttrs:function(){return u({role:"row"},this.bvAttrs)}},render:function(t){return t("tr",{class:this.trClasses,attrs:this.trAttrs,on:this.bvListeners},this.normalizeSlot())}}),Wp="bottom-row",Yp={methods:{renderBottomRow:function(){var t=this.$createElement;if(!this.hasNormalizedSlot(Wp)||!0===this.stacked||""===this.stacked)return t();var e=this.computedFields;return t(Gp,{key:"b-bottom-row",staticClass:"b-table-bottom-row",class:[it(this.tbodyTrClass)?this.tbodyTrClass(null,"row-bottom"):this.tbodyTrClass],attrs:it(this.tbodyTrAttr)?this.tbodyTrAttr(null,"row-bottom"):this.tbodyTrAttr},this.normalizeSlot(Wp,{columns:e.length,fields:e}))}}},Up=function(t){return(t=Pn(t,0))>0?t:null},qp=function(t){return et(t)||Up(t)>0},Kp=xn({variant:{type:String,default:null},colspan:{type:[Number,String],default:null,validator:qp},rowspan:{type:[Number,String],default:null,validator:qp},stackedHeading:{type:String,default:null},stickyColumn:{type:Boolean,default:!1}},cn),Xp=i.default.extend({name:cn,mixins:[Rr,Nr,Wo],inject:{bvTableTr:{default:function(){return{}}}},inheritAttrs:!1,props:Kp,computed:{tag:function(){return"td"},inTbody:function(){return this.bvTableTr.inTbody},inThead:function(){return this.bvTableTr.inThead},inTfoot:function(){return this.bvTableTr.inTfoot},isDark:function(){return this.bvTableTr.isDark},isStacked:function(){return this.bvTableTr.isStacked},isStackedCell:function(){return this.inTbody&&this.isStacked},isResponsive:function(){return this.bvTableTr.isResponsive},isStickyHeader:function(){return this.bvTableTr.isStickyHeader},hasStickyHeader:function(){return this.bvTableTr.hasStickyHeader},isStickyColumn:function(){return!this.isStacked&&(this.isResponsive||this.hasStickyHeader)&&this.stickyColumn},rowVariant:function(){return this.bvTableTr.variant},headVariant:function(){return this.bvTableTr.headVariant},footVariant:function(){return this.bvTableTr.footVariant},tableVariant:function(){return this.bvTableTr.tableVariant},computedColspan:function(){return Up(this.colspan)},computedRowspan:function(){return Up(this.rowspan)},cellClasses:function(){var t=this.variant;return(!t&&this.isStickyHeader&&!this.headVariant||!t&&this.isStickyColumn&&this.inTfoot&&!this.footVariant||!t&&this.isStickyColumn&&this.inThead&&!this.headVariant||!t&&this.isStickyColumn&&this.inTbody)&&(t=this.rowVariant||this.tableVariant||"b-table-default"),[t?"".concat(this.isDark?"bg":"table","-").concat(t):null,this.isStickyColumn?"b-table-sticky-column":null]},cellAttrs:function(){var t=this.inThead||this.inTfoot,e=this.computedColspan,i=this.computedRowspan,n="cell",o=null;return t?(n="columnheader",o=e>0?"colspan":"col"):to(this.tag,"th")&&(n="rowheader",o=i>0?"rowgroup":"row"),u(u({colspan:e,rowspan:i,role:n,scope:o},this.bvAttrs),{},{"data-label":this.isStackedCell&&!et(this.stackedHeading)?Hn(this.stackedHeading):null})}},render:function(t){var e=[this.normalizeSlot()];return t(this.tag,{class:this.cellClasses,attrs:this.cellAttrs,on:this.bvListeners},[this.isStackedCell?t("div",[e]):e])}}),Zp="table-busy",Jp={props:xn({busy:{type:Boolean,default:!1}},un),data:function(){return{localBusy:!1}},computed:{computedBusy:function(){return this.busy||this.localBusy}},watch:{localBusy:function(t,e){t!==e&&this.$emit("update:busy",t)}},methods:{stopIfBusy:function(t){return!!this.computedBusy&&(Xo(t),!0)},renderBusy:function(){var t=this.$createElement;return this.computedBusy&&this.hasNormalizedSlot(Zp)?t(Gp,{key:"table-busy-slot",staticClass:"b-table-busy-slot",class:[it(this.tbodyTrClass)?this.tbodyTrClass(null,Zp):this.tbodyTrClass],attrs:it(this.tbodyTrAttr)?this.tbodyTrAttr(null,Zp):this.tbodyTrAttr},[t(Xp,{props:{colspan:this.computedFields.length||null}},[this.normalizeSlot(Zp)])]):null}}},Qp={props:xn({caption:{type:String},captionHtml:{type:String}},un),computed:{captionId:function(){return this.isStacked?this.safeId("_caption_"):null}},methods:{renderCaption:function(){var t=this.caption,e=this.captionHtml,i=this.$createElement,n=i(),o=this.hasNormalizedSlot("table-caption");return(o||t||e)&&(n=i("caption",{key:"caption",attrs:{id:this.captionId},domProps:o?{}:Ea(e,t)},this.normalizeSlot("table-caption"))),n}}},tm={methods:{renderColgroup:function(){var t=this.$createElement,e=this.computedFields,i=t();return this.hasNormalizedSlot("table-colgroup")&&(i=t("colgroup",{key:"colgroup"},[this.normalizeSlot("table-colgroup",{columns:e.length,fields:e})])),i}}},em={props:xn({showEmpty:{type:Boolean,default:!1},emptyText:{type:String,default:"There are no records to show"},emptyHtml:{type:String},emptyFilteredText:{type:String,default:"There are no records matching your request"},emptyFilteredHtml:{type:String}},un),methods:{renderEmpty:function(){var t=this.$createElement,e=this.computedItems,i=t();if(this.showEmpty&&(!e||0===e.length)&&(!this.computedBusy||!this.hasNormalizedSlot("table-busy"))){var n=this.isFiltered,o=this.emptyText,r=this.emptyHtml,a=this.emptyFilteredText,s=this.emptyFilteredHtml,l=this.computedFields,u=this.tbodyTrClass,c=this.tbodyTrAttr;(i=this.normalizeSlot(this.isFiltered?"emptyfiltered":"empty",{emptyFilteredHtml:s,emptyFilteredText:a,emptyHtml:r,emptyText:o,fields:l,items:this.computedItems}))||(i=t("div",{class:["text-center","my-2"],domProps:n?Ea(s,a):Ea(r,o)})),i=t(Xp,{props:{colspan:l.length||null}},[t("div",{attrs:{role:"alert","aria-live":"polite"}},[i])]),i=t(Gp,{staticClass:"b-table-empty-row",class:[it(u)?this.tbodyTrClass(null,"row-empty"):u],attrs:it(c)?this.tbodyTrAttr(null,"row-empty"):c,key:n?"b-empty-filtered-row":"b-empty-row"},[i])}return i}}},im={_rowVariant:!0,_cellVariants:!0,_showDetails:!0},nm=["a","a *","button","button *","input:not(.disabled):not([disabled])","select:not(.disabled):not([disabled])","textarea:not(.disabled):not([disabled])",'[role="link"]','[role="link"] *','[role="button"]','[role="button"] *',"[tabindex]:not(.disabled):not([disabled])"].join(","),om=function(t,e,i){var n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},o=bt(n).reduce((function(e,i){var o=n[i],r=o.filterByFormatted,a=it(r)?r:r?o.formatter:null;return it(a)&&(e[i]=a(t[i],i,t)),e}),St(t)),r=bt(o).filter((function(t){return!(im[t]||st(e)&&e.length>0&&_n(e,t)||st(i)&&i.length>0&&!_n(i,t))}));return wt(o,r)},rm=function t(e){return et(e)?"":lt(e)&&!ct(e)?bt(e).sort().filter((function(t){return!et(t)})).map((function(i){return t(e[i])})).join(" "):Hn(e)},am='Prop "filter-debounce" is deprecated. Use the debounce feature of "<b-form-input>" instead.',sm={props:xn({filter:{type:[String,RegExp,Object,Array],default:null},filterFunction:{type:Function},filterIgnoredFields:{type:Array},filterIncludedFields:{type:Array},filterDebounce:{type:[Number,String],deprecated:am,default:0,validator:function(t){return/^\d+/.test(String(t))}}},un),data:function(){return{isFiltered:!1,localFilter:this.filterSanitize(this.filter)}},computed:{computedFilterIgnored:function(){return Dn(this.filterIgnoredFields||[]).filter(ee)},computedFilterIncluded:function(){return Dn(this.filterIncludedFields||[]).filter(ee)},computedFilterDebounce:function(){var t=Pn(this.filterDebounce,0);return t>0&&oe(am,un),t},localFiltering:function(){return!this.hasProvider||!!this.noProviderFiltering},filteredCheck:function(){return{filteredItems:this.filteredItems,localItems:this.localItems,localFilter:this.localFilter}},localFilterFn:function(){var t=this.filterFunction,e=null;try{e=t()}catch(t){}return Q(e)?null:t},filteredItems:function(){var t=this.localItems||[],e=this.localFilter,i=this.localFiltering?this.filterFnFactory(this.localFilterFn,e)||this.defaultFilterFnFactory(e):null;return i&&t.length>0?t.filter(i):t}},watch:{computedFilterDebounce:function(t){!t&&this.$_filterTimer&&(this.clearFilterTimer(),this.localFilter=this.filterSanitize(this.filter))},filter:{deep:!0,handler:function(t){var e=this,i=this.computedFilterDebounce;this.clearFilterTimer(),i&&i>0?this.$_filterTimer=setTimeout((function(){e.localFilter=e.filterSanitize(t)}),i):this.localFilter=this.filterSanitize(t)}},filteredCheck:function(t){var e=t.filteredItems,i=t.localFilter,n=!1;i?Ar(i,[])||Ar(i,{})?n=!1:i&&(n=!0):n=!1,n&&this.$emit("filtered",e,e.length),this.isFiltered=n},isFiltered:function(t,e){!1===t&&!0===e&&this.$emit("filtered",this.localItems,this.localItems.length)}},created:function(){var t=this;this.$_filterTimer=null,this.$nextTick((function(){t.isFiltered=Boolean(t.localFilter)}))},beforeDestroy:function(){this.clearFilterTimer()},methods:{clearFilterTimer:function(){clearTimeout(this.$_filterTimer),this.$_filterTimer=null},filterSanitize:function(t){return!this.localFiltering||this.localFilterFn||ot(t)||ht(t)?kt(t):""},filterFnFactory:function(t,e){if(!t||!it(t)||!e||Ar(e,[])||Ar(e,{}))return null;return function(i){return t(i,e)}},defaultFilterFnFactory:function(t){var e=this;if(!t||!ot(t)&&!ht(t))return null;var i=t;if(ot(i)){var n=Mn(t).replace(Et,"\\s+");i=new RegExp(".*".concat(n,".*"),"i")}return function(t){return i.lastIndex=0,i.test((n=t,o=e.computedFilterIgnored,r=e.computedFilterIncluded,a=e.computedFieldsObj,lt(n)?rm(om(n,o,r,a)):""));var n,o,r,a}}}},lm=function(t,e){var i=[];if(st(t)&&t.filter(ee).forEach((function(t){if(ot(t))i.push({key:t,label:Rn(t)});else if(lt(t)&&t.key&&ot(t.key))i.push(St(t));else if(lt(t)&&1===bt(t).length){var e=bt(t)[0],n=function(t,e){var i=null;return ot(e)?i={key:t,label:e}:it(e)?i={key:t,formatter:e}:lt(e)?(i=St(e)).key=i.key||t:!1!==e&&(i={key:t}),i}(e,t[e]);n&&i.push(n)}})),0===i.length&&st(e)&&e.length>0){var n=e[0];bt(n).forEach((function(t){im[t]||i.push({key:t,label:Rn(t)})}))}var o={};return i.filter((function(t){return!o[t.key]&&(o[t.key]=!0,t.label=ot(t.label)?t.label:Rn(t.key),!0)}))},um={props:xn({items:{type:Array,default:function(){return[]}},fields:{type:Array,default:null},primaryKey:{type:String},value:{type:Array,default:function(){return[]}}},un),data:function(){return{localItems:st(this.items)?this.items.slice():[]}},computed:{computedFields:function(){return lm(this.fields,this.localItems)},computedFieldsObj:function(){var t=this.$parent;return this.computedFields.reduce((function(e,i){if(e[i.key]=St(i),i.formatter){var n=i.formatter;ot(n)&&it(t[n])?n=t[n]:it(n)||(n=void 0),e[i.key].formatter=n}return e}),{})},computedItems:function(){return(this.paginatedItems||this.sortedItems||this.filteredItems||this.localItems||[]).slice()},context:function(){return{filter:this.localFilter,sortBy:this.localSortBy,sortDesc:this.localSortDesc,perPage:nr(Pn(this.perPage,0),0),currentPage:nr(Pn(this.currentPage,0),1),apiUrl:this.apiUrl}}},watch:{items:function(t){st(t)?this.localItems=t.slice():et(t)&&(this.localItems=[])},computedItems:function(t,e){Ar(t,e)||this.$emit("input",t)},context:function(t,e){Ar(t,e)||this.$emit("context-changed",t)}},mounted:function(){this.$emit("input",this.computedItems)},methods:{getFieldFormatter:function(t){var e=this.computedFieldsObj[t];return e?e.formatter:void 0}}},cm={props:xn({perPage:{type:[Number,String],default:0},currentPage:{type:[Number,String],default:1}},un),computed:{localPaging:function(){return!this.hasProvider||!!this.noProviderPaging},paginatedItems:function(){var t=this.sortedItems||this.filteredItems||this.localItems||[],e=nr(Pn(this.currentPage,1),1),i=nr(Pn(this.perPage,0),0);return this.localPaging&&i&&(t=t.slice((e-1)*i,e*i)),t}}},dm={mixins:[pl],props:xn({items:{type:[Array,Function],default:function(){return[]}},noProviderPaging:{type:Boolean,default:!1},noProviderSorting:{type:Boolean,default:!1},noProviderFiltering:{type:Boolean,default:!1},apiUrl:{type:String,default:""}},un),computed:{hasProvider:function(){return it(this.items)},providerTriggerContext:function(){var t={apiUrl:this.apiUrl,filter:null,sortBy:null,sortDesc:null,perPage:null,currentPage:null};return this.noProviderFiltering||(t.filter=this.localFilter),this.noProviderSorting||(t.sortBy=this.localSortBy,t.sortDesc=this.localSortDesc),this.noProviderPaging||(t.perPage=this.perPage,t.currentPage=this.currentPage),St(t)}},watch:{items:function(t){(this.hasProvider||it(t))&&this.$nextTick(this._providerUpdate)},providerTriggerContext:function(t,e){Ar(t,e)||this.$nextTick(this._providerUpdate)}},mounted:function(){var t=this;!this.hasProvider||this.localItems&&0!==this.localItems.length||this._providerUpdate(),this.listenOnRoot("bv::refresh::table",(function(e){e!==t.id&&e!==t||t.refresh()}))},methods:{refresh:function(){this.$off("refreshed",this.refresh),this.computedBusy?this.localBusy&&this.hasProvider&&this.$on("refreshed",this.refresh):(this.clearSelected(),this.hasProvider?this.$nextTick(this._providerUpdate):this.localItems=st(this.items)?this.items.slice():[])},_providerSetLocal:function(t){this.localItems=st(t)?t.slice():[],this.localBusy=!1,this.$emit("refreshed"),this.id&&this.emitOnRoot("bv::table::refreshed",this.id)},_providerUpdate:function(){var t=this;this.hasProvider&&(this.computedBusy?this.$nextTick(this.refresh):(this.localBusy=!0,this.$nextTick((function(){try{var e=t.items(t.context,t._providerSetLocal);!et(i=e)&&it(i.then)&&it(i.catch)?e.then((function(e){t._providerSetLocal(e)})):st(e)?t._providerSetLocal(e):2!==t.items.length&&(oe("Provider function didn't request callback and did not return a promise or data.",un),t.localBusy=!1)}catch(e){oe("Provider function error [".concat(e.name,"] ").concat(e.message,"."),un),t.localBusy=!1,t.$off("refreshed",t.refresh)}var i}))))}}},hm=["range","multi","single"],fm={props:xn({selectable:{type:Boolean,default:!1},selectMode:{type:String,default:"multi",validator:function(t){return _n(hm,t)}},selectedVariant:{type:String,default:"active"},noSelectOnClick:{type:Boolean,default:!1}},un),data:function(){return{selectedRows:[],selectedLastRow:-1}},computed:{isSelectable:function(){return this.selectable&&this.selectMode},hasSelectableRowClick:function(){return this.isSelectable&&!this.noSelectOnClick},supportsSelectableRows:function(){return!0},selectableHasSelection:function(){return this.isSelectable&&this.selectedRows&&this.selectedRows.length>0&&this.selectedRows.some(ee)},selectableIsMultiSelect:function(){return this.isSelectable&&_n(["range","multi"],this.selectMode)},selectableTableClasses:function(){var t;return s(t={"b-table-selectable":this.isSelectable},"b-table-select-".concat(this.selectMode),this.isSelectable),s(t,"b-table-selecting",this.selectableHasSelection),s(t,"b-table-selectable-no-click",this.isSelectable&&!this.hasSelectableRowClick),t},selectableTableAttrs:function(){return{"aria-multiselectable":this.isSelectable?this.selectableIsMultiSelect?"true":"false":null}}},watch:{computedItems:function(t,e){var i=!1;if(this.isSelectable&&this.selectedRows.length>0){i=st(t)&&st(e)&&t.length===e.length;for(var n=0;i&&n<t.length;n++)i=Ar(om(t[n]),om(e[n]))}i||this.clearSelected()},selectable:function(t){this.clearSelected(),this.setSelectionHandlers(t)},selectMode:function(){this.clearSelected()},hasSelectableRowClick:function(t){this.clearSelected(),this.setSelectionHandlers(!t)},selectedRows:function(t,e){var i=this;if(this.isSelectable&&!Ar(t,e)){var n=[];t.forEach((function(t,e){t&&n.push(i.computedItems[e])})),this.$emit("row-selected",n)}}},beforeMount:function(){this.isSelectable&&this.setSelectionHandlers(!0)},methods:{selectRow:function(t){if(this.isSelectable&&rt(t)&&t>=0&&t<this.computedItems.length&&!this.isRowSelected(t)){var e=this.selectableIsMultiSelect?this.selectedRows.slice():[];e[t]=!0,this.selectedLastClicked=-1,this.selectedRows=e}},unselectRow:function(t){if(this.isSelectable&&rt(t)&&this.isRowSelected(t)){var e=this.selectedRows.slice();e[t]=!1,this.selectedLastClicked=-1,this.selectedRows=e}},selectAllRows:function(){var t=this.computedItems.length;this.isSelectable&&t>0&&(this.selectedLastClicked=-1,this.selectedRows=this.selectableIsMultiSelect?jf(t).map((function(){return!0})):[!0])},isRowSelected:function(t){return!(!rt(t)||!this.selectedRows[t])},clearSelected:function(){this.selectedLastClicked=-1,this.selectedRows=[]},selectableRowClasses:function(t){if(this.isSelectable&&this.isRowSelected(t)){var e=this.selectedVariant;return s({"b-table-row-selected":!0},"".concat(this.dark?"bg":"table","-").concat(e),e)}return{}},selectableRowAttrs:function(t){return{"aria-selected":this.isSelectable?this.isRowSelected(t)?"true":"false":null}},setSelectionHandlers:function(t){var e=t&&!this.noSelectOnClick?"$on":"$off";this[e]("row-clicked",this.selectionHandler),this[e]("filtered",this.clearSelected),this[e]("context-changed",this.clearSelected)},selectionHandler:function(t,e,i){if(this.isSelectable&&!this.noSelectOnClick){var n=this.selectMode,o=this.selectedRows.slice(),r=!o[e];if("single"===n)o=[];else if("range"===n)if(this.selectedLastRow>-1&&i.shiftKey){for(var a=ir(this.selectedLastRow,e);a<=nr(this.selectedLastRow,e);a++)o[a]=!0;r=!0}else i.ctrlKey||i.metaKey||(o=[],r=!0),this.selectedLastRow=r?e:-1;o[e]=r,this.selectedRows=o}else this.clearSelected()}}},pm=function(t,e){return t.map((function(t,e){return[e,t]})).sort(function(t,e){return this(t[1],e[1])||t[0]-e[0]}.bind(e)).map((function(t){return t[1]}))},mm=["asc","desc","last"],gm={props:xn({sortBy:{type:String,default:""},sortDesc:{type:Boolean,default:!1},sortDirection:{type:String,default:"asc",validator:function(t){return _n(mm,t)}},sortCompare:{type:Function},sortCompareOptions:{type:Object,default:function(){return{numeric:!0}}},sortCompareLocale:{type:[String,Array]},sortNullLast:{type:Boolean,default:!1},noSortReset:{type:Boolean,default:!1},labelSortAsc:{type:String,default:"Click to sort Ascending"},labelSortDesc:{type:String,default:"Click to sort Descending"},labelSortClear:{type:String,default:"Click to clear sorting"},noLocalSorting:{type:Boolean,default:!1},noFooterSorting:{type:Boolean,default:!1},sortIconLeft:{type:Boolean,default:!1}},un),data:function(){return{localSortBy:this.sortBy||"",localSortDesc:this.sortDesc||!1}},computed:{localSorting:function(){return this.hasProvider?!!this.noProviderSorting:!this.noLocalSorting},isSortable:function(){return this.computedFields.some((function(t){return t.sortable}))},sortedItems:function(){var t=(this.filteredItems||this.localItems||[]).slice(),e=this.localSortBy,i=this.localSortDesc,n=this.sortCompare,o=this.localSorting,r=u(u({},this.sortCompareOptions),{},{usage:"sort"}),a=this.sortCompareLocale||void 0,s=this.sortNullLast;if(e&&o){var l=(this.computedFieldsObj[e]||{}).sortByFormatted,c=it(l)?l:l?this.getFieldFormatter(e):void 0;return pm(t,(function(t,o){var l=null;return it(n)&&(l=n(t,o,e,i,c,r,a)),(et(l)||!1===l)&&(l=function(t,e,i,n,o,r,a,s){var l=ne(t,i,null),u=ne(e,i,null);return it(o)&&(l=o(l,i,t),u=o(u,i,e)),l=et(l)?"":l,u=et(u)?"":u,ct(l)&&ct(u)||rt(l)&&rt(u)?l<u?-1:l>u?1:0:s&&""===l&&""!==u?1:s&&""!==l&&""===u?-1:rm(l).localeCompare(rm(u),a,r)}(t,o,e,0,c,r,a,s)),(l||0)*(i?-1:1)}))}return t}},watch:{isSortable:function(t){t?this.isSortable&&this.$on("head-clicked",this.handleSort):this.$off("head-clicked",this.handleSort)},sortDesc:function(t){t!==this.localSortDesc&&(this.localSortDesc=t||!1)},sortBy:function(t){t!==this.localSortBy&&(this.localSortBy=t||"")},localSortDesc:function(t,e){t!==e&&this.$emit("update:sortDesc",t)},localSortBy:function(t,e){t!==e&&this.$emit("update:sortBy",t)}},created:function(){this.isSortable&&this.$on("head-clicked",this.handleSort)},methods:{handleSort:function(t,e,i,n){var o=this;if(this.isSortable&&(!n||!this.noFooterSorting)){var r=!1,a=function(){var t=e.sortDirection||o.sortDirection;"asc"===t?o.localSortDesc=!1:"desc"===t&&(o.localSortDesc=!0)};if(e.sortable){var s=!this.localSorting&&e.sortKey?e.sortKey:t;this.localSortBy===s?this.localSortDesc=!this.localSortDesc:(this.localSortBy=s,a()),r=!0}else this.localSortBy&&!this.noSortReset&&(this.localSortBy="",a(),r=!0);r&&this.$emit("sort-changed",this.context)}},sortTheadThClasses:function(t,e,i){return{"b-table-sort-icon-left":e.sortable&&this.sortIconLeft&&!(i&&this.noFooterSorting)}},sortTheadThAttrs:function(t,e,i){if(!this.isSortable||i&&this.noFooterSorting)return{};var n=e.sortable;return{"aria-sort":n&&this.localSortBy===t?this.localSortDesc?"descending":"ascending":n?"none":null}},sortTheadThLabel:function(t,e,i){if(!this.isSortable||i&&this.noFooterSorting)return null;var n="";if(e.sortable)if(this.localSortBy===t)n=this.localSortDesc?this.labelSortAsc:this.labelSortDesc;else{n=this.localSortDesc?this.labelSortDesc:this.labelSortAsc;var o=this.sortDirection||e.sortDirection;"asc"===o?n=this.labelSortAsc:"desc"===o&&(n=this.labelSortDesc)}else this.noSortReset||(n=this.localSortBy?this.labelSortClear:"");return zn(n)||null}}},vm={props:xn({stacked:{type:[Boolean,String],default:!1}},un),computed:{isStacked:function(){return""===this.stacked||this.stacked},isStackedAlways:function(){return!0===this.isStacked},stackedTableClasses:function(){return s({"b-table-stacked":this.isStackedAlways},"b-table-stacked-".concat(this.stacked),!this.isStackedAlways&&this.isStacked)}}},bm={inheritAttrs:!1,mixins:[Rr],provide:function(){return{bvTable:this}},props:xn({striped:{type:Boolean,default:!1},bordered:{type:Boolean,default:!1},borderless:{type:Boolean,default:!1},outlined:{type:Boolean,default:!1},dark:{type:Boolean,default:!1},hover:{type:Boolean,default:!1},small:{type:Boolean,default:!1},fixed:{type:Boolean,default:!1},responsive:{type:[Boolean,String],default:!1},stickyHeader:{type:[Boolean,String],default:!1},noBorderCollapse:{type:Boolean,default:!1},captionTop:{type:Boolean,default:!1},tableVariant:{type:String},tableClass:{type:[String,Array,Object]}},un),computed:{isResponsive:function(){var t=""===this.responsive||this.responsive;return!this.isStacked&&t},isStickyHeader:function(){var t=""===this.stickyHeader||this.stickyHeader;return!this.isStacked&&t},wrapperClasses:function(){return[this.isStickyHeader?"b-table-sticky-header":"",!0===this.isResponsive?"table-responsive":this.isResponsive?"table-responsive-".concat(this.responsive):""].filter(ee)},wrapperStyles:function(){return this.isStickyHeader&&!nt(this.isStickyHeader)?{maxHeight:this.isStickyHeader}:{}},tableClasses:function(){var t=this.isTableSimple?this.hover:this.hover&&this.computedItems.length>0&&!this.computedBusy;return[this.tableClass,{"table-striped":this.striped,"table-hover":t,"table-dark":this.dark,"table-bordered":this.bordered,"table-borderless":this.borderless,"table-sm":this.small,border:this.outlined,"b-table-fixed":this.fixed,"b-table-caption-top":this.captionTop,"b-table-no-border-collapse":this.noBorderCollapse},this.tableVariant?"".concat(this.dark?"bg":"table","-").concat(this.tableVariant):"",this.stackedTableClasses,this.selectableTableClasses]},tableAttrs:function(){var t=[(this.bvAttrs||{})["aria-describedby"],this.captionId].filter(ee).join(" ")||null,e=this.computedItems,i=this.filteredItems,n=this.computedFields,o=this.selectableTableAttrs||{},r=this.isTableSimple?{}:{"aria-busy":this.computedBusy?"true":"false","aria-colcount":Hn(n.length),"aria-describedby":t};return u(u(u({"aria-rowcount":e&&i&&i.length>e.length?Hn(i.length):null},this.bvAttrs),{},{id:this.safeId(),role:"table"},r),o)}},render:function(t){var e=[];this.isTableSimple?e.push(this.normalizeSlot()):(e.push(this.renderCaption?this.renderCaption():null),e.push(this.renderColgroup?this.renderColgroup():null),e.push(this.renderThead?this.renderThead():null),e.push(this.renderTbody?this.renderTbody():null),e.push(this.renderTfoot?this.renderTfoot():null));var i=t("table",{key:"b-table",staticClass:"table b-table",class:this.tableClasses,attrs:this.tableAttrs},e.filter(ee));return this.wrapperClasses.length>0?t("div",{key:"wrap",class:this.wrapperClasses,style:this.wrapperStyles},[i]):i}},ym=xn({tbodyTransitionProps:{type:Object},tbodyTransitionHandlers:{type:Object}},hn),Sm=i.default.extend({name:hn,mixins:[Rr,Nr,Wo],provide:function(){return{bvTableRowGroup:this}},inject:{bvTable:{default:function(){return{}}}},inheritAttrs:!1,props:ym,computed:{isTbody:function(){return!0},isDark:function(){return this.bvTable.dark},isStacked:function(){return this.bvTable.isStacked},isResponsive:function(){return this.bvTable.isResponsive},isStickyHeader:function(){return!1},hasStickyHeader:function(){return!this.isStacked&&this.bvTable.stickyHeader},tableVariant:function(){return this.bvTable.tableVariant},isTransitionGroup:function(){return this.tbodyTransitionProps||this.tbodyTransitionHandlers},tbodyAttrs:function(){return u({role:"rowgroup"},this.bvAttrs)},tbodyProps:function(){return this.tbodyTransitionProps?u(u({},this.tbodyTransitionProps),{},{tag:"tbody"}):{}}},render:function(t){var e={props:this.tbodyProps,attrs:this.tbodyAttrs};return this.isTransitionGroup?(e.on=this.tbodyTransitionHandlers||{},e.nativeOn=this.bvListeners):e.on=this.bvListeners,t(this.isTransitionGroup?"transition-group":"tbody",e,this.normalizeSlot())}}),wm=["TD","TH","TR"],Tm=function(t){if(!t||!t.target)return!1;var e=t.target;if(e.disabled||-1!==wm.indexOf(e.tagName))return!1;if(lo(".dropdown-menu",e))return!0;var i="LABEL"===e.tagName?e:lo("label",e);if(i){var n=vo(i,"for"),o=n?co(n):ao("input, select, textarea",i);if(o&&!o.disabled)return!0}return so(e,nm)},Cm=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:document,e=ko();return!!(e&&""!==e.toString().trim()&&e.containsNode&&Jn(t))&&e.containsNode(t,!0)},km=i.default.extend({name:"BTh",extends:Xp,computed:{tag:function(){return"th"}}}),Bm="row-details",xm={mixins:[{props:xn({tbodyTrClass:{type:[String,Array,Object,Function]},tbodyTrAttr:{type:[Object,Function]},detailsTdClass:{type:[String,Array,Object]}},un),methods:{getTdValues:function(t,e,i,n){var o=this.$parent;if(i){var r=ne(t,e,"");return it(i)?i(r,e,t):ot(i)&&it(o[i])?o[i](r,e,t):i}return n},getThValues:function(t,e,i,n,o){var r=this.$parent;if(i){var a=ne(t,e,"");return it(i)?i(a,e,t,n):ot(i)&&it(r[i])?r[i](a,e,t,n):i}return o},getFormattedValue:function(t,e){var i=e.key,n=this.getFieldFormatter(i),o=ne(t,i,null);return it(n)&&(o=n(o,i,t)),et(o)?"":o},toggleDetailsFactory:function(t,e){var i=this;return function(){t&&i.$set(e,"_showDetails",!e._showDetails)}},rowHovered:function(t){this.tbodyRowEvtStopped(t)||this.emitTbodyRowEvent("row-hovered",t)},rowUnhovered:function(t){this.tbodyRowEvtStopped(t)||this.emitTbodyRowEvent("row-unhovered",t)},renderTbodyRowCell:function(t,e,i,n){var o=this,r=this.$createElement,a=this.hasNormalizedSlot(Bm),s=this.getFormattedValue(i,t),l=t.key,c=!this.isStacked&&(this.isResponsive||this.stickyHeader)&&t.stickyColumn,d=c?t.isRowHeader?km:Xp:t.isRowHeader?"th":"td",h=i._cellVariants&&i._cellVariants[l]?i._cellVariants[l]:t.variant||null,f={key:"row-".concat(n,"-cell-").concat(e,"-").concat(l),class:[t.class?t.class:"",this.getTdValues(i,l,t.tdClass,"")],props:{},attrs:u({"aria-colindex":String(e+1)},t.isRowHeader?this.getThValues(i,l,t.thAttr,"row",{}):this.getTdValues(i,l,t.tdAttr,{}))};c?f.props={stackedHeading:this.isStacked?t.label:null,stickyColumn:!0,variant:h}:(f.attrs["data-label"]=this.isStacked&&!et(t.label)?Hn(t.label):null,f.attrs.role=t.isRowHeader?"rowheader":"cell",f.attrs.scope=t.isRowHeader?"row":null,h&&f.class.push("".concat(this.dark?"bg":"table","-").concat(h)));var p={item:i,index:n,field:t,unformatted:ne(i,l,""),value:s,toggleDetails:this.toggleDetailsFactory(a,i),detailsShowing:Boolean(i._showDetails)};this.supportsSelectableRows&&(p.rowSelected=this.isRowSelected(n),p.selectRow=function(){return o.selectRow(n)},p.unselectRow=function(){return o.unselectRow(n)});var m=this.$_bodyFieldSlotNameCache[l],g=m?this.normalizeSlot(m,p):Hn(s);return this.isStacked&&(g=[r("div",[g])]),r(d,f,[g])},renderTbodyRow:function(t,e){var i=this,n=this.$createElement,o=this.computedFields,r=this.striped,a=this.hasNormalizedSlot(Bm),s=t._showDetails&&a,l=this.$listeners["row-clicked"]||this.hasSelectableRowClick,c=[],d=s?this.safeId("_details_".concat(e,"_")):null,h=o.map((function(n,o){return i.renderTbodyRowCell(n,o,t,e)})),f=null;this.currentPage&&this.perPage&&this.perPage>0&&(f=String((this.currentPage-1)*this.perPage+e+1));var p=this.primaryKey,m=Hn(ne(t,p))||null,g=m||Hn(e),v=m?this.safeId("_row_".concat(m)):null,b=this.selectableRowClasses?this.selectableRowClasses(e):{},y=this.selectableRowAttrs?this.selectableRowAttrs(e):{},S=it(this.tbodyTrClass)?this.tbodyTrClass(t,"row"):this.tbodyTrClass,w=it(this.tbodyTrAttr)?this.tbodyTrAttr(t,"row"):this.tbodyTrAttr;if(c.push(n(Gp,{key:"__b-table-row-".concat(g,"__"),ref:"itemRows",refInFor:!0,class:[S,b,s?"b-table-has-details":""],props:{variant:t._rowVariant||null},attrs:u(u({id:v},w),{},{tabindex:l?"0":null,"data-pk":m||null,"aria-details":d,"aria-owns":d,"aria-rowindex":f},y),on:{mouseenter:this.rowHovered,mouseleave:this.rowUnhovered}},h)),s){var T={item:t,index:e,fields:o,toggleDetails:this.toggleDetailsFactory(a,t)};this.supportsSelectableRows&&(T.rowSelected=this.isRowSelected(e),T.selectRow=function(){return i.selectRow(e)},T.unselectRow=function(){return i.unselectRow(e)});var C=n(Xp,{props:{colspan:o.length},class:this.detailsTdClass},[this.normalizeSlot(Bm,T)]);r&&c.push(n("tr",{key:"__b-table-details-stripe__".concat(g),staticClass:"d-none",attrs:{"aria-hidden":"true",role:"presentation"}}));var k=it(this.tbodyTrClass)?this.tbodyTrClass(t,Bm):this.tbodyTrClass,B=it(this.tbodyTrAttr)?this.tbodyTrAttr(t,Bm):this.tbodyTrAttr;c.push(n(Gp,{key:"__b-table-details__".concat(g),staticClass:"b-table-details",class:[k],props:{variant:t._rowVariant||null},attrs:u(u({},B),{},{id:d,tabindex:"-1"})},[C]))}else a&&(c.push(n()),r&&c.push(n()));return c}}}],props:u(u({},ym),{},{tbodyClass:{type:[String,Array,Object]}}),beforeDestroy:function(){this.$_bodyFieldSlotNameCache=null},methods:{getTbodyTrs:function(){var t=this.$refs||{},e=t.tbody?t.tbody.$el||t.tbody:null,i=(t.itemRows||[]).map((function(t){return t.$el||t}));return e&&e.children&&e.children.length>0&&i&&i.length>0?$n(e.children).filter((function(t){return _n(i,t)})):[]},getTbodyTrIndex:function(t){if(!Jn(t))return-1;var e="TR"===t.tagName?t:lo("tr",t,!0);return e?this.getTbodyTrs().indexOf(e):-1},emitTbodyRowEvent:function(t,e){if(t&&this.hasListener(t)&&e&&e.target){var i=this.getTbodyTrIndex(e.target);if(i>-1){var n=this.computedItems[i];this.$emit(t,n,i,e)}}},tbodyRowEvtStopped:function(t){return this.stopIfBusy&&this.stopIfBusy(t)},onTbodyRowKeydown:function(t){var e=t.target;if(!this.tbodyRowEvtStopped(t)&&"TR"===e.tagName&&eo(e)&&0===e.tabIndex){var i=t.keyCode;if(_n([$r,Ir],i))Xo(t),this.onTBodyRowClicked(t);else if(_n([Or,Br,_r,xr],i)){var n=this.getTbodyTrIndex(e);if(n>-1){Xo(t);var o=this.getTbodyTrs(),r=t.shiftKey;i===_r||r&&i===Or?_o(o[0]):i===xr||r&&i===Br?_o(o[o.length-1]):i===Or&&n>0?_o(o[n-1]):i===Br&&n<o.length-1&&_o(o[n+1])}}}},onTBodyRowClicked:function(t){this.tbodyRowEvtStopped(t)||Tm(t)||Cm(this.$el)||this.emitTbodyRowEvent("row-clicked",t)},onTbodyRowMiddleMouseRowClicked:function(t){this.tbodyRowEvtStopped(t)||2!==t.which||this.emitTbodyRowEvent("row-middle-clicked",t)},onTbodyRowContextmenu:function(t){this.tbodyRowEvtStopped(t)||this.emitTbodyRowEvent("row-contextmenu",t)},onTbodyRowDblClicked:function(t){this.tbodyRowEvtStopped(t)||Tm(t)||this.emitTbodyRowEvent("row-dblclicked",t)},renderTbody:function(){var t=this,e=this.computedItems,i=this.$createElement,n=this.hasListener("row-clicked")||this.hasSelectableRowClick,o=[],r=this.renderBusy?this.renderBusy():null;if(r)o.push(r);else{var a={},s=this.hasNormalizedSlot("cell()")?"cell()":null;this.computedFields.forEach((function(e){var i=e.key,n="cell(".concat(i,")"),o="cell(".concat(i.toLowerCase(),")");a[i]=t.hasNormalizedSlot(n)?n:t.hasNormalizedSlot(o)?o:s})),this.$_bodyFieldSlotNameCache=a,o.push(this.renderTopRow?this.renderTopRow():i()),e.forEach((function(e,i){o.push(t.renderTbodyRow(e,i))})),o.push(this.renderEmpty?this.renderEmpty():i()),o.push(this.renderBottomRow?this.renderBottomRow():i())}var l={auxclick:this.onTbodyRowMiddleMouseRowClicked,contextmenu:this.onTbodyRowContextmenu,dblclick:this.onTbodyRowDblClicked};return n&&(l.click=this.onTBodyRowClicked,l.keydown=this.onTbodyRowKeydown),i(Sm,{ref:"tbody",class:this.tbodyClass||null,props:{tbodyTransitionProps:this.tbodyTransitionProps,tbodyTransitionHandlers:this.tbodyTransitionHandlers},on:l},o)}}},$m=xn({footVariant:{type:String,default:null}},fn),_m=i.default.extend({name:fn,mixins:[Rr,Nr,Wo],provide:function(){return{bvTableRowGroup:this}},inject:{bvTable:{default:function(){return{}}}},inheritAttrs:!1,props:$m,computed:{isTfoot:function(){return!0},isDark:function(){return this.bvTable.dark},isStacked:function(){return this.bvTable.isStacked},isResponsive:function(){return this.bvTable.isResponsive},isStickyHeader:function(){return!1},hasStickyHeader:function(){return!this.isStacked&&this.bvTable.stickyHeader},tableVariant:function(){return this.bvTable.tableVariant},tfootClasses:function(){return[this.footVariant?"thead-".concat(this.footVariant):null]},tfootAttrs:function(){return u({role:"rowgroup"},this.bvAttrs)}},render:function(t){return t("tfoot",{class:this.tfootClasses,attrs:this.tfootAttrs,on:this.bvListeners},this.normalizeSlot())}}),Dm={props:xn({footClone:{type:Boolean,default:!1},footVariant:{type:String},footRowVariant:{type:String},tfootClass:{type:[String,Array,Object]},tfootTrClass:{type:[String,Array,Object]}},un),methods:{renderTFootCustom:function(){var t=this.$createElement;return this.hasNormalizedSlot("custom-foot")?t(_m,{key:"bv-tfoot-custom",class:this.tfootClass||null,props:{footVariant:this.footVariant||this.headVariant||null}},this.normalizeSlot("custom-foot",{items:this.computedItems.slice(),fields:this.computedFields.slice(),columns:this.computedFields.length})):t()},renderTfoot:function(){return this.footClone?this.renderThead(!0):this.renderTFootCustom()}}},Fm=xn({headVariant:{type:String,default:null}},pn),Im=i.default.extend({name:pn,mixins:[Rr,Nr,Wo],provide:function(){return{bvTableRowGroup:this}},inject:{bvTable:{default:function(){return{}}}},inheritAttrs:!1,props:Fm,computed:{isThead:function(){return!0},isDark:function(){return this.bvTable.dark},isStacked:function(){return this.bvTable.isStacked},isResponsive:function(){return this.bvTable.isResponsive},isStickyHeader:function(){return!this.isStacked&&this.bvTable.stickyHeader},hasStickyHeader:function(){return!this.isStacked&&this.bvTable.stickyHeader},tableVariant:function(){return this.bvTable.tableVariant},theadClasses:function(){return[this.headVariant?"thead-".concat(this.headVariant):null]},theadAttrs:function(){return u({role:"rowgroup"},this.bvAttrs)}},render:function(t){return t("thead",{class:this.theadClasses,attrs:this.theadAttrs,on:this.bvListeners},this.normalizeSlot())}}),Om={props:xn({headVariant:{type:String},headRowVariant:{type:String},theadClass:{type:[String,Array,Object]},theadTrClass:{type:[String,Array,Object]}},un),methods:{fieldClasses:function(t){return[t.class?t.class:"",t.thClass?t.thClass:""]},headClicked:function(t,e,i){this.stopIfBusy&&this.stopIfBusy(t)||Tm(t)||Cm(this.$el)||(Xo(t),this.$emit("head-clicked",e.key,e,t,i))},renderThead:function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],i=this.$createElement,n=this.computedFields||[];if(this.isStackedAlways||0===n.length)return i();var o=this.isSortable,r=this.isSelectable,a=this.headVariant,s=this.footVariant,l=this.headRowVariant,c=this.footRowVariant,d=o||this.hasListener("head-clicked"),h=r?this.selectAllRows:il,f=r?this.clearSelected:il,p=function(n,r){var a=n.label,s=n.labelHtml,l=n.variant,c=n.stickyColumn,p=n.key,m=null;n.label.trim()||n.headerTitle||(m=Rn(n.key));var g={};d&&(g.click=function(i){t.headClicked(i,n,e)},g.keydown=function(i){var o=i.keyCode;o!==$r&&o!==Ir||t.headClicked(i,n,e)});var v=o?t.sortTheadThAttrs(p,n,e):{},b=o?t.sortTheadThClasses(p,n,e):null,y=o?t.sortTheadThLabel(p,n,e):null,S={class:[t.fieldClasses(n),b],props:{variant:l,stickyColumn:c},style:n.thStyle||{},attrs:u(u({tabindex:d?"0":null,abbr:n.headerAbbr||null,title:n.headerTitle||null,"aria-colindex":r+1,"aria-label":m},t.getThValues(null,p,n.thAttr,e?"foot":"head",{})),v),on:g,key:p},T=["head(".concat(p,")"),"head(".concat(p.toLowerCase(),")"),"head()"];e&&(T=["foot(".concat(p,")"),"foot(".concat(p.toLowerCase(),")"),"foot()"].concat(w(T)));var C={label:a,column:p,field:n,isFoot:e,selectAllRows:h,clearSelected:f},k=t.normalizeSlot(T,C)||i("div",{domProps:Ea(s,a)}),B=y?i("span",{staticClass:"sr-only"}," (".concat(y,")")):null;return i(km,S,[k,B].filter(ee))},m=n.map(p).filter(ee),g=[];if(e)g.push(i(Gp,{class:this.tfootTrClass,props:{variant:et(c)?l:c}},m));else{var v={columns:n.length,fields:n,selectAllRows:h,clearSelected:f};g.push(this.normalizeSlot("thead-top",v)||i()),g.push(i(Gp,{class:this.theadTrClass,props:{variant:l}},m))}return i(e?_m:Im,{key:e?"bv-tfoot":"bv-thead",class:(e?this.tfootClass:this.theadClass)||null,props:e?{footVariant:s||a||null}:{headVariant:a||null}},g)}}},Pm="top-row",Am={methods:{renderTopRow:function(){var t=this.$createElement;if(!this.hasNormalizedSlot(Pm)||!0===this.stacked||""===this.stacked)return t();var e=this.computedFields;return t(Gp,{key:"b-top-row",staticClass:"b-table-top-row",class:[it(this.tbodyTrClass)?this.tbodyTrClass(null,"row-top"):this.tbodyTrClass],attrs:it(this.tbodyTrAttr)?this.tbodyTrAttr(null,"row-top"):this.tbodyTrAttr},[this.normalizeSlot(Pm,{columns:e.length,fields:e})])}}},Vm=i.default.extend({name:un,mixins:[Rr,Mp,vs,Wo,um,bm,vm,Om,Dm,xm,vm,sm,gm,cm,Qp,tm,fm,em,Am,Yp,Jp,dm]}),Em=i.default.extend({name:"BTableLite",mixins:[Mp,vs,Wo,um,bm,vm,Om,Dm,xm,Qp,tm]}),Lm=i.default.extend({name:"BTableSimple",mixins:[vs,Wo,bm,vm],computed:{isTableSimple:function(){return!0}}}),Rm=de({components:{BTable:Vm},plugins:{TableLitePlugin:de({components:{BTableLite:Em}}),TableSimplePlugin:de({components:{BTableSimple:Lm,BTbody:Sm,BThead:Im,BTfoot:_m,BTr:Gp,BTd:Xp,BTh:km}})}}),Nm=i.default.extend({name:rn,functional:!0,props:xn({animation:{type:String},rows:{type:Number,default:3,validator:function(t){return t>0}},columns:{type:Number,default:5,validator:function(t){return t>0}},hideHeader:{type:Boolean,default:!1},showFooter:{type:Boolean,default:!1},tableProps:{type:Object,default:function(){}}},rn),render:function(t,e){var i=e.props,n=i.animation,o=i.columns,r=t("th",[t(Lp,{props:{animation:n}})]),a=t("tr",Fn(o,r)),s=t("td",[t(Lp,{props:{width:"75%",animation:n}})]),l=t("tr",Fn(o,s)),c=t("tbody",Fn(i.rows,l)),d=i.hideHeader?t():t("thead",[a]),h=i.showFooter?t("tfoot",[a]):t();return t(Lm,{props:u({},i.tableProps)},[d,c,h])}}),Mm=i.default.extend({name:an,functional:!0,props:xn({loading:{type:Boolean,default:!1}},an),render:function(t,e){var i=e.data,n=e.props,o=e.slots,r=e.scopedSlots,a=o(),s=r||{},l={};return n.loading?t("div",F(i,{attrs:{role:"alert","aria-live":"polite","aria-busy":!0},staticClass:"b-skeleton-wrapper",key:"loading"}),[Go("loading",l,s,a)||t()]):Go(Vo,l,s,a)||t()}}),Hm=de({components:{BSkeleton:Lp,BSkeletonIcon:Rp,BSkeletonImg:Np,BSkeletonTable:Nm,BSkeletonWrapper:Mm}}),zm=de({components:{BSpinner:Mf}}),jm=Tt(Sf,["tabs","isNavBar","cardHeader"]),Gm=function(t){return!t.disabled},Wm=i.default.extend({name:"BVTabButton",inject:{bvTabs:{default:function(){return{}}}},props:xn({tab:{default:null},tabs:{type:Array,default:function(){return[]}},id:{type:String,default:null},controls:{type:String,default:null},tabIndex:{type:Number,default:null},posInSet:{type:Number,default:null},setSize:{type:Number,default:null},noKeyNav:{type:Boolean,default:!1}},dn),methods:{focus:function(){_o(this.$refs.link)},handleEvt:function(t){if(!this.tab.disabled){var e=t.type,i=t.keyCode,n=t.shiftKey;"click"===e||"keydown"===e&&i===Ir?(Xo(t),this.$emit("click",t)):"keydown"!==e||this.noKeyNav||(-1!==[Or,Dr,_r].indexOf(i)?(Xo(t),n||i===_r?this.$emit("first",t):this.$emit("prev",t)):-1!==[Br,Fr,xr].indexOf(i)&&(Xo(t),n||i===xr?this.$emit("last",t):this.$emit("next",t)))}}},render:function(t){var e=this.id,i=this.tabIndex,n=this.setSize,o=this.posInSet,r=this.controls,a=this.handleEvt,s=this.tab,l=s.title,c=s.localActive,d=s.disabled,h=s.titleItemClass,f=s.titleLinkClass,p=s.titleLinkAttributes,m=t(jr,{ref:"link",staticClass:"nav-link",class:[{active:c&&!d,disabled:d},f,c?this.bvTabs.activeNavItemClass:null],props:{disabled:d},attrs:u(u({},p),{},{role:"tab",id:e,tabindex:i,"aria-selected":c&&!d?"true":"false","aria-setsize":n,"aria-posinset":o,"aria-controls":r}),on:{click:a,keydown:a}},[this.tab.normalizeSlot(zo)||l]);return t("li",{staticClass:"nav-item",class:[h],attrs:{role:"presentation"}},[m])}}),Ym=de({components:{BTabs:i.default.extend({name:dn,mixins:[vs,Wo],provide:function(){return{bvTabs:this}},model:{prop:"value",event:"input"},props:u(u({},jm),{},{tag:{type:String,default:"div"},card:{type:Boolean,default:!1},end:{type:Boolean,default:!1},noFade:{type:Boolean,default:!1},noNavStyle:{type:Boolean,default:!1},noKeyNav:{type:Boolean,default:!1},lazy:{type:Boolean,default:!1},contentClass:{type:[String,Array,Object]},navClass:{type:[String,Array,Object]},navWrapperClass:{type:[String,Array,Object]},activeNavItemClass:{type:[String,Array,Object]},activeTabClass:{type:[String,Array,Object]},value:{type:Number,default:null}}),data:function(){return{currentTab:Pn(this.value,-1),tabs:[],registeredTabs:[],isMounted:!1}},computed:{fade:function(){return!this.noFade},localNavClass:function(){var t=[];return this.card&&this.vertical&&t.push("card-header","h-100","border-bottom-0","rounded-0"),[].concat(t,[this.navClass])}},watch:{currentTab:function(t){var e=-1;this.tabs.forEach((function(i,n){t!==n||i.disabled?i.localActive=!1:(i.localActive=!0,e=n)})),this.$emit("input",e)},value:function(t,e){if(t!==e){t=Pn(t,-1),e=Pn(e,0);var i=this.tabs;i[t]&&!i[t].disabled?this.activateTab(i[t]):t<e?this.previousTab():this.nextTab()}},registeredTabs:function(){var t=this;this.$nextTick((function(){Xn((function(){t.updateTabs()}))}))},tabs:function(t,e){var i=this;Ar(t.map((function(t){return t._uid})),e.map((function(t){return t._uid})))||this.$nextTick((function(){i.$emit("changed",t.slice(),e.slice())}))},isMounted:function(t){var e=this;t&&Xn((function(){e.updateTabs()})),this.setObserver(t)}},created:function(){var t=this;this.$_observer=null,this.currentTab=Pn(this.value,-1),this.$nextTick((function(){t.updateTabs()}))},mounted:function(){var t=this;this.updateTabs(),this.$nextTick((function(){t.isMounted=!0}))},deactivated:function(){this.isMounted=!1},activated:function(){var t=this;this.currentTab=Pn(this.value,-1),this.$nextTick((function(){t.updateTabs(),t.isMounted=!0}))},beforeDestroy:function(){this.isMounted=!1},destroyed:function(){this.tabs=[]},methods:{registerTab:function(t){var e=this;_n(this.registeredTabs,t)||(this.registeredTabs.push(t),t.$once("hook:destroyed",(function(){e.unregisterTab(t)})))},unregisterTab:function(t){this.registeredTabs=this.registeredTabs.slice().filter((function(e){return e!==t}))},setObserver:function(t){if(this.$_observer&&this.$_observer.disconnect(),this.$_observer=null,t){var e=this;this.$_observer=nl(this.$refs.tabsContainer,(function(){e.$nextTick((function(){Xn((function(){e.updateTabs()}))}))}),{childList:!0,subtree:!1,attributes:!0,attributeFilter:["id"]})}},getTabs:function(){var t=this.registeredTabs.filter((function(t){return 0===t.$children.filter((function(t){return t._isTab})).length})),e=[];if(this.isMounted&&t.length>0){var i=t.map((function(t){return"#".concat(t.safeId())})).join(", ");e=ro(i,this.$el).map((function(t){return t.id})).filter(ee)}return pm(t,(function(t,i){return e.indexOf(t.safeId())-e.indexOf(i.safeId())}))},updateTabs:function(){var t=this.getTabs(),e=t.indexOf(t.slice().reverse().find((function(t){return t.localActive&&!t.disabled})));if(e<0){var i=this.currentTab;i>=t.length?e=t.indexOf(t.slice().reverse().find(Gm)):t[i]&&!t[i].disabled&&(e=i)}e<0&&(e=t.indexOf(t.find(Gm))),t.forEach((function(t){t.localActive=!1})),t[e]&&(t[e].localActive=!0),this.tabs=t,this.currentTab=e},getButtonForTab:function(t){return(this.$refs.buttons||[]).find((function(e){return e.tab===t}))},updateButton:function(t){var e=this.getButtonForTab(t);e&&e.$forceUpdate&&e.$forceUpdate()},activateTab:function(t){var e=!1;if(t){var i=this.tabs.indexOf(t);if(!t.disabled&&i>-1&&i!==this.currentTab){var n=new BvEvent("activate-tab",{cancelable:!0,vueTarget:this,componentId:this.safeId()});this.$emit(n.type,i,this.currentTab,n),n.defaultPrevented||(e=!0,this.currentTab=i)}}return e||this.currentTab===this.value||this.$emit("input",this.currentTab),e},deactivateTab:function(t){return!!t&&this.activateTab(this.tabs.filter((function(e){return e!==t})).find(Gm))},focusButton:function(t){var e=this;this.$nextTick((function(){_o(e.getButtonForTab(t))}))},emitTabClick:function(t,e){dt(e)&&t&&t.$emit&&!t.disabled&&t.$emit("click",e)},clickTab:function(t,e){this.activateTab(t),this.emitTabClick(t,e)},firstTab:function(t){var e=this.tabs.find(Gm);this.activateTab(e)&&t&&(this.focusButton(e),this.emitTabClick(e,t))},previousTab:function(t){var e=nr(this.currentTab,0),i=this.tabs.slice(0,e).reverse().find(Gm);this.activateTab(i)&&t&&(this.focusButton(i),this.emitTabClick(i,t))},nextTab:function(t){var e=nr(this.currentTab,-1),i=this.tabs.slice(e+1).find(Gm);this.activateTab(i)&&t&&(this.focusButton(i),this.emitTabClick(i,t))},lastTab:function(t){var e=this.tabs.slice().reverse().find(Gm);this.activateTab(e)&&t&&(this.focusButton(e),this.emitTabClick(e,t))}},render:function(t){var e=this,i=this.tabs,n=this.noKeyNav,o=this.firstTab,r=this.previousTab,a=this.nextTab,s=this.lastTab,l=i.find((function(t){return t.localActive&&!t.disabled})),u=i.find((function(t){return!t.disabled})),c=i.map((function(c,d){var h=null;return n||(h=-1,(l===c||!l&&u===c)&&(h=null)),t(Wm,{key:c._uid||d,ref:"buttons",refInFor:!0,props:{tab:c,tabs:i,id:c.controlledBy||(c.safeId?c.safeId("_BV_tab_button_"):null),controls:c.safeId?c.safeId():null,tabIndex:h,setSize:i.length,posInSet:d+1,noKeyNav:n},on:{click:function(t){e.clickTab(c,t)},first:o,prev:r,next:a,last:s}})})),d=t(wf,{ref:"nav",class:this.localNavClass,attrs:{role:"tablist",id:this.safeId("_BV_tab_controls_")},props:{fill:this.fill,justified:this.justified,align:this.align,tabs:!this.noNavStyle&&!this.pills,pills:!this.noNavStyle&&this.pills,vertical:this.vertical,small:this.small,cardHeader:this.card&&!this.vertical}},[this.normalizeSlot("tabs-start")||t(),c,this.normalizeSlot("tabs-end")||t()]);d=t("div",{key:"bv-tabs-nav",class:[{"card-header":this.card&&!this.vertical&&!this.end,"card-footer":this.card&&!this.vertical&&this.end,"col-auto":this.vertical},this.navWrapperClass]},[d]);var h=t();i&&0!==i.length||(h=t("div",{key:"bv-empty-tab",class:["tab-pane","active",{"card-body":this.card}]},this.normalizeSlot("empty")));var f=t("div",{ref:"tabsContainer",key:"bv-tabs-container",staticClass:"tab-content",class:[{col:this.vertical},this.contentClass],attrs:{id:this.safeId("_BV_tab_container_")}},Dn(this.normalizeSlot(),h));return t(this.tag,{staticClass:"tabs",class:{row:this.vertical,"no-gutters":this.vertical&&this.card},attrs:{id:this.safeId()}},[this.end?f:t(),[d],this.end?t():f])}}),BTab:i.default.extend({name:ln,mixins:[vs,Wo],inject:{bvTabs:{default:function(){return{}}}},props:xn({active:{type:Boolean,default:!1},tag:{type:String,default:"div"},buttonId:{type:String},title:{type:String,default:""},titleItemClass:{type:[String,Array,Object]},titleLinkClass:{type:[String,Array,Object]},titleLinkAttributes:{type:Object},disabled:{type:Boolean,default:!1},noBody:{type:Boolean,default:!1},lazy:{type:Boolean,default:!1}},ln),data:function(){return{localActive:this.active&&!this.disabled,show:!1}},computed:{tabClasses:function(){return[{active:this.localActive,disabled:this.disabled,"card-body":this.bvTabs.card&&!this.noBody},this.localActive?this.bvTabs.activeTabClass:null]},controlledBy:function(){return this.buttonId||this.safeId("__BV_tab_button__")},computedNoFade:function(){return!this.bvTabs.fade},computedLazy:function(){return this.bvTabs.lazy||this.lazy},_isTab:function(){return!0}},watch:{localActive:function(t){this.$emit("update:active",t)},active:function(t,e){t!==e&&(t?this.activate():this.deactivate()||this.$emit("update:active",this.localActive))},disabled:function(t,e){if(t!==e){var i=this.bvTabs.firstTab;t&&this.localActive&&i&&(this.localActive=!1,i())}}},mounted:function(){this.registerTab(),this.show=this.localActive},updated:function(){var t=this.bvTabs.updateButton;t&&this.hasNormalizedSlot(zo)&&t(this)},destroyed:function(){this.unregisterTab()},methods:{registerTab:function(){var t=this.bvTabs.registerTab;t&&t(this)},unregisterTab:function(){var t=this.bvTabs.unregisterTab;t&&t(this)},activate:function(){var t=this.bvTabs.activateTab;return!(!t||this.disabled)&&t(this)},deactivate:function(){var t=this.bvTabs.deactivateTab;return!(!t||!this.localActive)&&t(this)}},render:function(t){var e=this.localActive,i=t(this.tag,{ref:"panel",staticClass:"tab-pane",class:this.tabClasses,directives:[{name:"show",rawName:"v-show",value:e,expression:"localActive"}],attrs:{role:"tabpanel",id:this.safeId(),"aria-hidden":e?"false":"true","aria-labelledby":this.controlledBy||null}},[e||!this.computedLazy?this.normalizeSlot():t()]);return t(Oo,{props:{mode:"out-in",noFade:this.computedNoFade}},[i])}})}}),Um=de({components:{BTime:ch}});function qm(t){return(qm="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function Km(t){return function(t){if(Array.isArray(t)){for(var e=0,i=new Array(t.length);e<t.length;e++)i[e]=t[e];return i}}(t)||function(t){if(Symbol.iterator in Object(t)||"[object Arguments]"===Object.prototype.toString.call(t))return Array.from(t)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance")}()}var Xm="undefined"!=typeof window;function Zm(t,e){return e.reduce((function(e,i){return t.hasOwnProperty(i)&&(e[i]=t[i]),e}),{})}var Jm,Qm={},tg={},eg={},ig=new(i.default.extend({data:function(){return{transports:Qm,targets:tg,sources:eg,trackInstances:Xm}},methods:{open:function(t){if(Xm){var e=t.to,n=t.from,o=t.passengers,r=t.order,a=void 0===r?1/0:r;if(e&&n&&o){var s,l={to:e,from:n,passengers:(s=o,Array.isArray(s)||"object"===qm(s)?Object.freeze(s):s),order:a};-1===Object.keys(this.transports).indexOf(e)&&i.default.set(this.transports,e,[]);var u,c=this.$_getTransportIndex(l),d=this.transports[e].slice(0);-1===c?d.push(l):d[c]=l,this.transports[e]=(u=function(t,e){return t.order-e.order},d.map((function(t,e){return[e,t]})).sort((function(t,e){return u(t[1],e[1])||t[0]-e[0]})).map((function(t){return t[1]})))}}},close:function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],i=t.to,n=t.from;if(i&&(n||!1!==e)&&this.transports[i])if(e)this.transports[i]=[];else{var o=this.$_getTransportIndex(t);if(o>=0){var r=this.transports[i].slice(0);r.splice(o,1),this.transports[i]=r}}},registerTarget:function(t,e,i){Xm&&(this.trackInstances&&!i&&this.targets[t]&&console.warn("[portal-vue]: Target ".concat(t," already exists")),this.$set(this.targets,t,Object.freeze([e])))},unregisterTarget:function(t){this.$delete(this.targets,t)},registerSource:function(t,e,i){Xm&&(this.trackInstances&&!i&&this.sources[t]&&console.warn("[portal-vue]: source ".concat(t," already exists")),this.$set(this.sources,t,Object.freeze([e])))},unregisterSource:function(t){this.$delete(this.sources,t)},hasTarget:function(t){return!(!this.targets[t]||!this.targets[t][0])},hasSource:function(t){return!(!this.sources[t]||!this.sources[t][0])},hasContentFor:function(t){return!!this.transports[t]&&!!this.transports[t].length},$_getTransportIndex:function(t){var e=t.to,i=t.from;for(var n in this.transports[e])if(this.transports[e][n].from===i)return+n;return-1}}}))(Qm),ng=1,og=i.default.extend({name:"portal",props:{disabled:{type:Boolean},name:{type:String,default:function(){return String(ng++)}},order:{type:Number,default:0},slim:{type:Boolean},slotProps:{type:Object,default:function(){return{}}},tag:{type:String,default:"DIV"},to:{type:String,default:function(){return String(Math.round(1e7*Math.random()))}}},created:function(){var t=this;this.$nextTick((function(){ig.registerSource(t.name,t)}))},mounted:function(){this.disabled||this.sendUpdate()},updated:function(){this.disabled?this.clear():this.sendUpdate()},beforeDestroy:function(){ig.unregisterSource(this.name),this.clear()},watch:{to:function(t,e){e&&e!==t&&this.clear(e),this.sendUpdate()}},methods:{clear:function(t){var e={from:this.name,to:t||this.to};ig.close(e)},normalizeSlots:function(){return this.$scopedSlots.default?[this.$scopedSlots.default]:this.$slots.default},normalizeOwnChildren:function(t){return"function"==typeof t?t(this.slotProps):t},sendUpdate:function(){var t=this.normalizeSlots();if(t){var e={from:this.name,to:this.to,passengers:Km(t),order:this.order};ig.open(e)}else this.clear()}},render:function(t){var e=this.$slots.default||this.$scopedSlots.default||[],i=this.tag;return e&&this.disabled?e.length<=1&&this.slim?this.normalizeOwnChildren(e)[0]:t(i,[this.normalizeOwnChildren(e)]):this.slim?t():t(i,{class:{"v-portal":!0},style:{display:"none"},key:"v-portal-placeholder"})}}),rg=i.default.extend({name:"portalTarget",props:{multiple:{type:Boolean,default:!1},name:{type:String,required:!0},slim:{type:Boolean,default:!1},slotProps:{type:Object,default:function(){return{}}},tag:{type:String,default:"div"},transition:{type:[String,Object,Function]}},data:function(){return{transports:ig.transports,firstRender:!0}},created:function(){var t=this;this.$nextTick((function(){ig.registerTarget(t.name,t)}))},watch:{ownTransports:function(){this.$emit("change",this.children().length>0)},name:function(t,e){ig.unregisterTarget(e),ig.registerTarget(t,this)}},mounted:function(){var t=this;this.transition&&this.$nextTick((function(){t.firstRender=!1}))},beforeDestroy:function(){ig.unregisterTarget(this.name)},computed:{ownTransports:function(){var t=this.transports[this.name]||[];return this.multiple?t:0===t.length?[]:[t[t.length-1]]},passengers:function(){return function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return t.reduce((function(t,i){var n=i.passengers[0],o="function"==typeof n?n(e):i.passengers;return t.concat(o)}),[])}(this.ownTransports,this.slotProps)}},methods:{children:function(){return 0!==this.passengers.length?this.passengers:this.$scopedSlots.default?this.$scopedSlots.default(this.slotProps):this.$slots.default||[]},noWrapper:function(){var t=this.slim&&!this.transition;return t&&this.children().length>1&&console.warn("[portal-vue]: PortalTarget with `slim` option received more than one child element."),t}},render:function(t){var e=this.noWrapper(),i=this.children(),n=this.transition||this.tag;return e?i[0]:this.slim&&!n?t():t(n,{props:{tag:this.transition&&this.tag?this.tag:void 0},class:{"vue-portal-target":!0}},i)}}),ag=0,sg=["disabled","name","order","slim","slotProps","tag","to"],lg=["multiple","transition"],ug=(i.default.extend({name:"MountingPortal",inheritAttrs:!1,props:{append:{type:[Boolean,String]},bail:{type:Boolean},mountTo:{type:String,required:!0},disabled:{type:Boolean},name:{type:String,default:function(){return"mounted_"+String(ag++)}},order:{type:Number,default:0},slim:{type:Boolean},slotProps:{type:Object,default:function(){return{}}},tag:{type:String,default:"DIV"},to:{type:String,default:function(){return String(Math.round(1e7*Math.random()))}},multiple:{type:Boolean,default:!1},targetSlim:{type:Boolean},targetSlotProps:{type:Object,default:function(){return{}}},targetTag:{type:String,default:"div"},transition:{type:[String,Object,Function]}},created:function(){if("undefined"!=typeof document){var t=document.querySelector(this.mountTo);if(t){var e=this.$props;if(ig.targets[e.name])e.bail?console.warn("[portal-vue]: Target ".concat(e.name," is already mounted.\n        Aborting because 'bail: true' is set")):this.portalTarget=ig.targets[e.name];else{var i=e.append;if(i){var n="string"==typeof i?i:"DIV",o=document.createElement(n);t.appendChild(o),t=o}var r=Zm(this.$props,lg);r.slim=this.targetSlim,r.tag=this.targetTag,r.slotProps=this.targetSlotProps,r.name=this.to,this.portalTarget=new rg({el:t,parent:this.$parent||this,propsData:r})}}else console.error("[portal-vue]: Mount Point '".concat(this.mountTo,"' not found in document"))}},beforeDestroy:function(){var t=this.portalTarget;if(this.append){var e=t.$el;e.parentNode.removeChild(e)}t.$destroy()},render:function(t){if(!this.portalTarget)return console.warn("[portal-vue] Target wasn't mounted"),t();if(!this.$scopedSlots.manual){var e=Zm(this.$props,sg);return t(og,{props:e,attrs:this.$attrs,on:this.$listeners,scopedSlots:this.$scopedSlots},this.$slots.default)}var i=this.$scopedSlots.manual({to:this.to});return Array.isArray(i)&&(i=i[0]),i||t()}}),xn({name:{type:String,required:!0},ariaLive:{type:String,default:void 0},ariaAtomic:{type:String},role:{type:String}},vn)),cg=i.default.extend({data:function(){return{name:"b-toaster"}},methods:{onAfterEnter:function(t){var e=this;Xn((function(){fo(t,"".concat(e.name,"-enter-to"))}))}},render:function(t){return t("transition-group",{props:{tag:"div",name:this.name},on:{afterEnter:this.onAfterEnter}},this.$slots.default)}}),dg=i.default.extend({name:vn,props:ug,data:function(){return{doRender:!1,dead:!1,staticName:this.name}},beforeMount:function(){var t=this;this.staticName=this.name,ig.hasTarget(this.staticName)?(oe('A "<portal-target>" with name "'.concat(this.name,'" already exists in the document.'),vn),this.dead=!0):(this.doRender=!0,this.$once("hook:beforeDestroy",(function(){t.$root.$emit("bv::toaster::destroyed",t.staticName)})))},destroyed:function(){this.$el&&this.$el.parentNode&&this.$el.parentNode.removeChild(this.$el)},render:function(t){var e=t("div",{class:["d-none",{"b-dead-toaster":this.dead}]});if(this.doRender){var i=t(rg,{staticClass:"b-toaster-slot",props:{name:this.staticName,multiple:!0,tag:"div",slim:!1,transition:cg}});e=t("div",{staticClass:"b-toaster",class:[this.staticName],attrs:{id:this.staticName,role:this.role||null,"aria-live":this.ariaLive,"aria-atomic":this.ariaAtomic}},[i])}return e}}),hg=wt(zr,["href","to"]),fg=xn(u({id:{type:String},title:{type:String},toaster:{type:String,default:"b-toaster-top-right"},visible:{type:Boolean,default:!1},variant:{type:String},isStatus:{type:Boolean,default:!1},appendToast:{type:Boolean,default:!1},noAutoHide:{type:Boolean,default:!1},autoHideDelay:{type:[Number,String],default:5e3},noCloseButton:{type:Boolean,default:!1},noFade:{type:Boolean,default:!1},noHoverPause:{type:Boolean,default:!1},solid:{type:Boolean,default:!1},toastClass:{type:[String,Object,Array]},headerClass:{type:[String,Object,Array]},bodyClass:{type:[String,Object,Array]},static:{type:Boolean,default:!1}},hg),gn),pg=i.default.extend({name:gn,mixins:[Rr,vs,pl,Wo,Jh],inheritAttrs:!1,model:{prop:"visible",event:"change"},props:fg,data:function(){return{isMounted:!1,doRender:!1,localShow:!1,isTransitioning:!1,isHiding:!1,order:0,dismissStarted:0,resumeDismiss:0}},computed:{bToastClasses:function(){return s({"b-toast-solid":this.solid,"b-toast-append":this.appendToast,"b-toast-prepend":!this.appendToast},"b-toast-".concat(this.variant),this.variant)},slotScope:function(){return{hide:this.hide}},computedDuration:function(){return nr(Pn(this.autoHideDelay,0),1e3)},computedToaster:function(){return String(this.toaster)},transitionHandlers:function(){return{beforeEnter:this.onBeforeEnter,afterEnter:this.onAfterEnter,beforeLeave:this.onBeforeLeave,afterLeave:this.onAfterLeave}},computedAttrs:function(){return u(u({},this.bvAttrs),{},{id:this.safeId(),tabindex:"0"})}},watch:{visible:function(t){t?this.show():this.hide()},localShow:function(t){t!==this.visible&&this.$emit("change",t)},toaster:function(){this.$nextTick(this.ensureToaster)},static:function(t){t&&this.localShow&&this.ensureToaster()}},created:function(){this.$_dismissTimer=null},mounted:function(){var t=this;this.isMounted=!0,this.$nextTick((function(){t.visible&&Xn((function(){t.show()}))})),this.listenOnRoot("bv::show::toast",(function(e){e===t.safeId()&&t.show()})),this.listenOnRoot("bv::hide::toast",(function(e){e&&e!==t.safeId()||t.hide()})),this.listenOnRoot("bv::toaster::destroyed",(function(e){e===t.computedToaster&&t.hide()}))},beforeDestroy:function(){this.clearDismissTimer()},methods:{show:function(){var t=this;if(!this.localShow){this.ensureToaster();var e=this.buildEvent("show");this.emitEvent(e),this.dismissStarted=this.resumeDismiss=0,this.order=Date.now()*(this.appendToast?1:-1),this.isHiding=!1,this.doRender=!0,this.$nextTick((function(){Xn((function(){t.localShow=!0}))}))}},hide:function(){var t=this;if(this.localShow){var e=this.buildEvent("hide");this.emitEvent(e),this.setHoverHandler(!1),this.dismissStarted=this.resumeDismiss=0,this.clearDismissTimer(),this.isHiding=!0,Xn((function(){t.localShow=!1}))}},buildEvent:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return new BvEvent(t,u(u({cancelable:!1,target:this.$el||null,relatedTarget:null},e),{},{vueTarget:this,componentId:this.safeId()}))},emitEvent:function(t){var e=t.type;this.emitOnRoot("bv::toast:".concat(e),t),this.$emit(e,t)},ensureToaster:function(){if(!this.static&&!ig.hasTarget(this.computedToaster)){var t=document.createElement("div");document.body.appendChild(t),new dg({parent:this.$root,propsData:{name:this.computedToaster}}).$mount(t)}},startDismissTimer:function(){this.clearDismissTimer(),this.noAutoHide||(this.$_dismissTimer=setTimeout(this.hide,this.resumeDismiss||this.computedDuration),this.dismissStarted=Date.now(),this.resumeDismiss=0)},clearDismissTimer:function(){clearTimeout(this.$_dismissTimer),this.$_dismissTimer=null},setHoverHandler:function(t){var e=this.$refs["b-toast"];Ko(t,e,"mouseenter",this.onPause,el),Ko(t,e,"mouseleave",this.onUnPause,el)},onPause:function(){if(!this.noAutoHide&&!this.noHoverPause&&this.$_dismissTimer&&!this.resumeDismiss){var t=Date.now()-this.dismissStarted;t>0&&(this.clearDismissTimer(),this.resumeDismiss=nr(this.computedDuration-t,1e3))}},onUnPause:function(){this.noAutoHide||this.noHoverPause||!this.resumeDismiss?this.resumeDismiss=this.dismissStarted=0:this.startDismissTimer()},onLinkClick:function(){var t=this;this.$nextTick((function(){Xn((function(){t.hide()}))}))},onBeforeEnter:function(){this.isTransitioning=!0},onAfterEnter:function(){this.isTransitioning=!1;var t=this.buildEvent("shown");this.emitEvent(t),this.startDismissTimer(),this.setHoverHandler(!0)},onBeforeLeave:function(){this.isTransitioning=!0},onAfterLeave:function(){this.isTransitioning=!1,this.order=0,this.resumeDismiss=this.dismissStarted=0;var t=this.buildEvent("hidden");this.emitEvent(t),this.doRender=!1},makeToast:function(t){var e=this,i=[],n=this.normalizeSlot("toast-title",this.slotScope);n?i.push(n):this.title&&i.push(t("strong",{staticClass:"mr-2"},this.title)),this.noCloseButton||i.push(t(Jo,{staticClass:"ml-auto mb-1",on:{click:function(){e.hide()}}}));var o=t();i.length>0&&(o=t("header",{staticClass:"toast-header",class:this.headerClass},i));var r=Tr(this),a=t(r?jr:"div",{staticClass:"toast-body",class:this.bodyClass,props:r?gr(hg,this):{},on:r?{click:this.onLinkClick}:{}},[this.normalizeSlot(Vo,this.slotScope)||t()]);return t("div",{key:"toast-".concat(this._uid),ref:"toast",staticClass:"toast",class:this.toastClass,attrs:this.computedAttrs},[o,a])}},render:function(t){if(!this.doRender||!this.isMounted)return t();var e="b-toast-".concat(this._uid),i=this.static?{}:this.scopedStyleAttrs;return t(og,{props:{name:e,to:this.computedToaster,order:this.order,slim:!0,disabled:this.static}},[t("div",{key:e,ref:"b-toast",staticClass:"b-toast",class:this.bToastClasses,attrs:u(u({},i),{},{id:this.safeId("_toast_outer"),role:this.isHiding?null:this.isStatus?"status":"alert","aria-live":this.isHiding?null:this.isStatus?"polite":"assertive","aria-atomic":this.isHiding?null:"true"})},[t(Oo,{props:{noFade:this.noFade},on:this.transitionHandlers},[this.localShow?this.makeToast(t):t()])])])}}),mg="$bvToast",gg=["id"].concat(w(bt(Tt(fg,["static","visible"])))),vg={toastContent:"default",title:"toast-title"},bg=function(t){return gg.reduce((function(e,i){return Q(t[i])||(e[i]=t[i]),e}),{})},yg=de({components:{BToast:pg,BToaster:dg},plugins:{BVToastPlugin:de({plugins:{plugin:function(t){var e=t.extend({name:"BVToastPop",extends:pg,destroyed:function(){this.$el&&this.$el.parentNode&&this.$el.parentNode.removeChild(this.$el)},mounted:function(){var t=this,e=function(){t.localShow=!1,t.doRender=!1,t.$nextTick((function(){t.$nextTick((function(){Xn((function(){t.$destroy()}))}))}))};this.$parent.$once("hook:destroyed",e),this.$once("hidden",e),this.listenOnRoot("bv::toaster::destroyed",(function(i){i===t.toaster&&e()}))}}),i=function(t,i){if(!re(mg)){var n=new e({parent:i,propsData:u(u(u({},bg(Tn(gn))),Tt(t,bt(vg))),{},{static:!1,visible:!0})});bt(vg).forEach((function(e){var o=t[e];Q(o)||("title"===e&&ot(o)&&(o=[i.$createElement("strong",{class:"mr-2"},o)]),n.$slots[vg[e]]=Dn(o))}));var o=document.createElement("div");document.body.appendChild(o),n.$mount(o)}},n=function(){function t(e){o(this,t),ft(this,{_vm:e,_root:e.$root}),mt(this,{_vm:{enumerable:!0,configurable:!1,writable:!1},_root:{enumerable:!0,configurable:!1,writable:!1}})}return a(t,[{key:"toast",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};t&&!re(mg)&&i(u(u({},bg(e)),{},{toastContent:t}),this._vm)}},{key:"show",value:function(t){t&&this._root.$emit("bv::show::toast",t)}},{key:"hide",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;this._root.$emit("bv::hide::toast",t)}}]),t}();t.mixin({beforeCreate:function(){this._bv__toast=new n(this)}}),yt(t.prototype,mg)||gt(t.prototype,mg,{get:function(){return this&&this._bv__toast||oe('"'.concat(mg,'" must be accessed from a Vue instance "this" context.'),gn),this._bv__toast}})}}})}}),Sg="__BV_Tooltip__",wg={focus:!0,hover:!0,click:!0,blur:!0,manual:!0},Tg=/^html$/i,Cg=/^noninteractive$/i,kg=/^nofade$/i,Bg=/^(auto|top(left|right)?|bottom(left|right)?|left(top|bottom)?|right(top|bottom)?)$/i,xg=/^(window|viewport|scrollParent)$/i,$g=/^d\d+$/i,_g=/^ds\d+$/i,Dg=/^dh\d+$/i,Fg=/^o-?\d+$/i,Ig=/^v-.+$/i,Og=/\s+/,Pg=function(t,e,i){if(M){var n=function(t,e){var i={title:void 0,trigger:"",placement:"top",fallbackPlacement:"flip",container:!1,animation:!0,offset:0,id:null,html:!1,interactive:!0,disabled:!1,delay:Tn(bn,"delay",50),boundary:String(Tn(bn,"boundary","scrollParent")),boundaryPadding:Pn(Tn(bn,"boundaryPadding",5),0),variant:Tn(bn,"variant"),customClass:Tn(bn,"customClass")};if(ot(t.value)||rt(t.value)||it(t.value)?i.title=t.value:ut(t.value)&&(i=u(u({},i),t.value)),Q(i.title)){var n=e.data||{};i.title=n.attrs&&!et(n.attrs.title)?n.attrs.title:void 0}ut(i.delay)||(i.delay={show:Pn(i.delay,0),hide:Pn(i.delay,0)}),t.arg&&(i.container="#".concat(t.arg)),bt(t.modifiers).forEach((function(t){if(Tg.test(t))i.html=!0;else if(Cg.test(t))i.interactive=!1;else if(kg.test(t))i.animation=!1;else if(Bg.test(t))i.placement=t;else if(xg.test(t))t="scrollparent"===t?"scrollParent":t,i.boundary=t;else if($g.test(t)){var e=Pn(t.slice(1),0);i.delay.show=e,i.delay.hide=e}else _g.test(t)?i.delay.show=Pn(t.slice(2),0):Dg.test(t)?i.delay.hide=Pn(t.slice(2),0):Fg.test(t)?i.offset=Pn(t.slice(1),0):Ig.test(t)&&(i.variant=t.slice(2)||null)}));var o={};return Dn(i.trigger||"").filter(ee).join(" ").trim().toLowerCase().split(Og).forEach((function(t){wg[t]&&(o[t]=!0)})),bt(t.modifiers).forEach((function(t){t=t.toLowerCase(),wg[t]&&(o[t]=!0)})),i.trigger=bt(o).join(" "),"blur"===i.trigger&&(i.trigger="focus"),i.trigger||(i.trigger="hover focus"),i}(e,i);if(!t[Sg]){var o=i.context;t[Sg]=new lp({parent:o,_scopeId:Zh(o,void 0)}),t[Sg].__bv_prev_data__={},t[Sg].$on("show",(function(){it(n.title)&&t[Sg].updateData({title:n.title(t)})}))}var r={title:n.title,triggers:n.trigger,placement:n.placement,fallbackPlacement:n.fallbackPlacement,variant:n.variant,customClass:n.customClass,container:n.container,boundary:n.boundary,delay:n.delay,offset:n.offset,noFade:!n.animation,id:n.id,interactive:n.interactive,disabled:n.disabled,html:n.html},a=t[Sg].__bv_prev_data__;if(t[Sg].__bv_prev_data__=r,!Ar(r,a)){var s={target:t};bt(r).forEach((function(e){r[e]!==a[e]&&(s[e]="title"===e&&it(r[e])?r[e](t):r[e])})),t[Sg].updateData(s)}}},Ag=de({directives:{VBTooltip:{bind:function(t,e,i){Pg(t,e,i)},componentUpdated:function(t,e,i){i.context.$nextTick((function(){Pg(t,e,i)}))},unbind:function(t){!function(t){t[Sg]&&(t[Sg].$destroy(),t[Sg]=null),delete t[Sg]}(t)}}}}),Vg=de({plugins:{AlertPlugin:er,AspectPlugin:dr,AvatarPlugin:Fa,BadgePlugin:Aa,BreadcrumbPlugin:Ha,ButtonPlugin:za,ButtonGroupPlugin:Wa,ButtonToolbarPlugin:qa,CalendarPlugin:Ss,CardPlugin:Qs,CarouselPlugin:cl,CollapsePlugin:zl,DropdownPlugin:Sc,EmbedPlugin:Cc,FormPlugin:Vc,FormCheckboxPlugin:td,FormDatepickerPlugin:ud,FormFilePlugin:pd,FormGroupPlugin:xd,FormInputPlugin:Pd,FormRadioPlugin:Ed,FormRatingPlugin:Hd,FormSelectPlugin:Ud,FormSpinbuttonPlugin:Zd,FormTagsPlugin:nh,FormTextareaPlugin:rh,FormTimepickerPlugin:hh,ImagePlugin:fh,InputGroupPlugin:wh,JumbotronPlugin:Bh,LayoutPlugin:Ih,LinkPlugin:Oh,ListGroupPlugin:Rh,MediaPlugin:Gh,ModalPlugin:yf,NavPlugin:Df,NavbarPlugin:Nf,OverlayPlugin:zf,PaginationPlugin:Zf,PaginationNavPlugin:Qf,PopoverPlugin:$p,ProgressPlugin:Dp,SidebarPlugin:Ep,SkeletonPlugin:Hm,SpinnerPlugin:zm,TablePlugin:Rm,TabsPlugin:Ym,TimePlugin:Um,ToastPlugin:yg,TooltipPlugin:de({components:{BTooltip:up},plugins:{VBTooltipPlugin:Ag}})}}),Eg=de({directives:{VBHover:rd}}),Lg=de({directives:{VBModal:ff}}),Rg="active",Ng=".nav-link",Mg=".nav-item",Hg=".list-group-item",zg=".dropdown-item",jg="position",Gg={element:"body",offset:10,method:"auto",throttle:75},Wg={element:"(string|element|component)",offset:"number",method:"string",throttle:"number"},Yg=["webkitTransitionEnd","transitionend","otransitionend","oTransitionEnd"],Ug=function(t){return function(t){return Object.prototype.toString.call(t)}(t).match(/\s([a-zA-Z]+)/)[1].toLowerCase()},qg=function(){function t(e,i,n){o(this,t),this.$el=e,this.$scroller=null,this.$selector=[Ng,Hg,zg].join(","),this.$offsets=[],this.$targets=[],this.$activeTarget=null,this.$scrollHeight=0,this.$resizeTimeout=null,this.$scrollerObserver=null,this.$targetsObserver=null,this.$root=n||null,this.$config=null,this.updateConfig(i)}return a(t,[{key:"updateConfig",value:function(t,e){this.$scroller&&(this.unlisten(),this.$scroller=null);var i=u(u({},this.constructor.Default),t);if(e&&(this.$root=e),function(t,e,i){for(var n in i)if(yt(i,n)){var o=i[n],r=e[n],a=r&&Jn(r)?"element":Ug(r);a=r&&r._isVue?"component":a,new RegExp(o).test(a)||oe("".concat(t,': Option "').concat(n,'" provided type "').concat(a,'" but expected type "').concat(o,'"'))}}(this.constructor.Name,i,this.constructor.DefaultType),this.$config=i,this.$root){var n=this;this.$root.$nextTick((function(){n.listen()}))}else this.listen()}},{key:"dispose",value:function(){this.unlisten(),clearTimeout(this.$resizeTimeout),this.$resizeTimeout=null,this.$el=null,this.$config=null,this.$scroller=null,this.$selector=null,this.$offsets=null,this.$targets=null,this.$activeTarget=null,this.$scrollHeight=null}},{key:"listen",value:function(){var t=this,e=this.getScroller();e&&"BODY"!==e.tagName&&Uo(e,"scroll",this,el),Uo(window,"scroll",this,el),Uo(window,"resize",this,el),Uo(window,"orientationchange",this,el),Yg.forEach((function(e){Uo(window,e,t,el)})),this.setObservers(!0),this.handleEvent("refresh")}},{key:"unlisten",value:function(){var t=this,e=this.getScroller();this.setObservers(!1),e&&"BODY"!==e.tagName&&qo(e,"scroll",this,el),qo(window,"scroll",this,el),qo(window,"resize",this,el),qo(window,"orientationchange",this,el),Yg.forEach((function(e){qo(window,e,t,el)}))}},{key:"setObservers",value:function(t){var e=this;this.$scrollerObserver&&this.$scrollerObserver.disconnect(),this.$targetsObserver&&this.$targetsObserver.disconnect(),this.$scrollerObserver=null,this.$targetsObserver=null,t&&(this.$targetsObserver=nl(this.$el,(function(){e.handleEvent("mutation")}),{subtree:!0,childList:!0,attributes:!0,attributeFilter:["href"]}),this.$scrollerObserver=nl(this.getScroller(),(function(){e.handleEvent("mutation")}),{subtree:!0,childList:!0,characterData:!0,attributes:!0,attributeFilter:["id","style","class"]}))}},{key:"handleEvent",value:function(t){var e=ot(t)?t:t.type,i=this;"scroll"===e?(this.$scrollerObserver||this.listen(),this.process()):/(resize|orientationchange|mutation|refresh)/.test(e)&&(i.$resizeTimeout||(i.$resizeTimeout=setTimeout((function(){i.refresh(),i.process(),i.$resizeTimeout=null}),i.$config.throttle)))}},{key:"refresh",value:function(){var t=this,e=this.getScroller();if(e){var i=e!==e.window?jg:"offset",n="auto"===this.$config.method?i:this.$config.method,o=n===jg?xo:Bo,r=n===jg?this.getScrollTop():0;return this.$offsets=[],this.$targets=[],this.$scrollHeight=this.getScrollHeight(),ro(this.$selector,this.$el).map((function(t){return vo(t,"href")})).filter((function(t){return t&&Yt.test(t||"")})).map((function(t){var i=t.replace(Yt,"$1").trim();if(!i)return null;var n=ao(i,e);return n&&io(n)?{offset:Pn(o(n).top,0)+r,target:i}:null})).filter(Boolean).sort((function(t,e){return t.offset-e.offset})).reduce((function(e,i){return e[i.target]||(t.$offsets.push(i.offset),t.$targets.push(i.target),e[i.target]=!0),e}),{}),this}}},{key:"process",value:function(){var t=this.getScrollTop()+this.$config.offset,e=this.getScrollHeight(),i=this.$config.offset+e-this.getOffsetHeight();if(this.$scrollHeight!==e&&this.refresh(),t>=i){var n=this.$targets[this.$targets.length-1];this.$activeTarget!==n&&this.activate(n)}else{if(this.$activeTarget&&t<this.$offsets[0]&&this.$offsets[0]>0)return this.$activeTarget=null,void this.clear();for(var o=this.$offsets.length;o--;){this.$activeTarget!==this.$targets[o]&&t>=this.$offsets[o]&&(Q(this.$offsets[o+1])||t<this.$offsets[o+1])&&this.activate(this.$targets[o])}}}},{key:"getScroller",value:function(){if(this.$scroller)return this.$scroller;var t=this.$config.element;return t?(Jn(t.$el)?t=t.$el:ot(t)&&(t=ao(t)),t?(this.$scroller="BODY"===t.tagName?window:t,this.$scroller):null):null}},{key:"getScrollTop",value:function(){var t=this.getScroller();return t===window?t.pageYOffset:t.scrollTop}},{key:"getScrollHeight",value:function(){return this.getScroller().scrollHeight||nr(document.body.scrollHeight,document.documentElement.scrollHeight)}},{key:"getOffsetHeight",value:function(){var t=this.getScroller();return t===window?window.innerHeight:To(t).height}},{key:"activate",value:function(t){var e=this;this.$activeTarget=t,this.clear();var i=ro(this.$selector.split(",").map((function(e){return"".concat(e,'[href$="').concat(t,'"]')})).join(","),this.$el);i.forEach((function(t){if(po(t,"dropdown-item")){var i=lo(".dropdown, .dropup",t);i&&e.setActiveState(ao(".dropdown-toggle",i),!0),e.setActiveState(t,!0)}else{e.setActiveState(t,!0),so(t.parentElement,Mg)&&e.setActiveState(t.parentElement,!0);for(var n=t;n;){var o=(n=lo(".nav, .list-group",n))?n.previousElementSibling:null;o&&so(o,"".concat(Ng,", ").concat(Hg))&&e.setActiveState(o,!0),o&&so(o,Mg)&&(e.setActiveState(ao(Ng,o),!0),e.setActiveState(o,!0))}}})),i&&i.length>0&&this.$root&&this.$root.$emit("bv::scrollspy::activate",t,i)}},{key:"clear",value:function(){var t=this;ro("".concat(this.$selector,", ").concat(Mg),this.$el).filter((function(t){return po(t,Rg)})).forEach((function(e){return t.setActiveState(e,!1)}))}},{key:"setActiveState",value:function(t,e){t&&(e?ho(t,Rg):fo(t,Rg))}}],[{key:"Name",get:function(){return"v-b-scrollspy"}},{key:"Default",get:function(){return Gg}},{key:"DefaultType",get:function(){return Wg}}]),t}(),Kg="__BV_ScrollSpy__",Xg=/^\d+$/,Zg=/^(auto|position|offset)$/,Jg=function(t,e,i){if(M){var n=function(t){var e={};return t.arg&&(e.element="#".concat(t.arg)),bt(t.modifiers).forEach((function(t){Xg.test(t)?e.offset=Pn(t,0):Zg.test(t)&&(e.method=t)})),ot(t.value)?e.element=t.value:rt(t.value)?e.offset=lr(t.value):lt(t.value)&&bt(t.value).filter((function(t){return!!qg.DefaultType[t]})).forEach((function(i){e[i]=t.value[i]})),e}(e);t[Kg]?t[Kg].updateConfig(n,i.context.$root):t[Kg]=new qg(t,n,i.context.$root)}},Qg={install:ce({plugins:{componentsPlugin:Vg,directivesPlugin:de({plugins:{VBHoverPlugin:Eg,VBModalPlugin:Lg,VBPopoverPlugin:xp,VBScrollspyPlugin:de({directives:{VBScrollspy:{bind:function(t,e,i){Jg(t,e,i)},inserted:function(t,e,i){Jg(t,e,i)},update:function(t,e,i){e.value!==e.oldValue&&Jg(t,e,i)},componentUpdated:function(t,e,i){e.value!==e.oldValue&&Jg(t,e,i)},unbind:function(t){!function(t){t[Kg]&&(t[Kg].dispose(),t[Kg]=null,delete t[Kg])}(t)}}}}),VBTogglePlugin:Hl,VBTooltipPlugin:Ag,VBVisiblePlugin:de({directives:{VBVisible:Hs}})}})}}),NAME:"BootstrapVue"};return Jm=Qg,V&&window.Vue&&window.Vue.use(Jm),V&&Jm.NAME&&(window[Jm.NAME]=Jm),Qg}));
//# sourceMappingURL=bootstrap-vue.min.js.map