<?php

namespace App\Roles;


use Illuminate\Contracts\Container\Container;
use Illuminate\Support\Facades\Auth;

class WebUserAuthentication extends \Buxus\WebUser\WebUserAuthentication
{

    public function __construct(Container $app)
    {
        parent::__construct($app);
        dd(4);
    }

    protected function init()
    {
        parent::init();

        dd(1);
        if($this->active_user_id) {
            Auth::onceUsingId($this->active_user_id);
        }
    }


}
