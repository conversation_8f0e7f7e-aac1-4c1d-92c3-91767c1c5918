<?php
namespace App\Roles;


use App\Roles\Generator\Role;
use Buxus\Util\PageIds;
use Buxus\Util\PropertyTag;
use Spatie\Permission\Models\Permission as SpatiePermission;
use Spatie\Permission\Models\Role as SpatieRole;
use Buxus\TemplateFunctions;
use Buxus\Util\PageTypeID;

class Generator
{
    static function generate()
    {

        $dbRoles = SpatieRole::all()->pluck('name')->toArray();
        $dbRights = SpatiePermission::all()->pluck('name')->toArray();
        $roles_root = new Role(PageIds::getPageId('roles'));
        $roles_data = $roles_root->getAllChilds();
        $roles = Role::getTags($roles_data);

        $rights = [];
        $rights_config = config('roles.rights');
        foreach($rights_config as $page_type => $page_type_rights) {
            foreach($page_type_rights as $right_tag => $right_label) {
                $rights[] = $page_type . '-' . $right_tag;
            }
        }

        // transform pages to rights
        foreach($roles_data as $role) {
            $allowed_pages = $role->getAllowedPages();
            foreach($allowed_pages as $allowed_page) {
                $rights[] = 'page_id-' . $allowed_page;
            }
            $forbidden_pages = $role->getForbiddenPages();
            foreach($forbidden_pages as $forbidden_page) {
                $rights[] = 'forbidden_id-' . $forbidden_page;
            }
        }
        /// dd($roles_data);
        /// dd($rights);

        // delete redundant roles
        foreach($dbRoles as $key => $role) {
            if(!in_array($role, $roles)) {
                /// delete role
                /// todo: delete role from db... delete from all tables
                SpatieRole::where('name', $role)->delete();
                unset($dbRoles[$key]);
            }
        }

        // delete redundant rights
        foreach($dbRights as $key => $right) {
            if(!in_array($right, $rights)) {
                /// delete right
                /// todo: delete right from db... delete from all tables
                SpatiePermission::where('name', $right)->delete();
                unset($dbRights[$key]);
            }
        }

        // create new rights
        foreach($rights as $right_tag) {
            if(!in_array($right_tag, $dbRights)) {
                SpatiePermission::create(['name' => $right_tag]);
                $dbRights[] = $right_tag;
            }
        }

        foreach($roles_data as $role) {
            /* @var $role Role */

            // create new roles
            $role_tag = $role->getPage()->getValue(PropertyTag::TAG_TAG());
            if(!in_array($role_tag, $dbRoles)) {
                SpatieRole::create(['name' => $role_tag]);
                $dbRoles[] = $role_tag;
            }

            // sync permissions with roles
            $spatie_role = SpatieRole::findByName($role_tag);
            $role_rights = $role->getRights();
            $spatie_role->syncPermissions($role_rights);

            // sync pages with roles
            $allowed_pages = $role->getAllowedPages();
            foreach ($allowed_pages as $allowed_page) {
                $spatie_role->givePermissionTo('page_id-' . $allowed_page);
            }
            $forbidden_pages = $role->getForbiddenPages();
            foreach ($forbidden_pages as $forbidden_page) {
                $spatie_role->givePermissionTo('forbidden_id-' . $forbidden_page);
            }

            // sync users with roles? from entities.
//            foreach (Entity::all() as $entity) {
//                $u_id = $entity->user_id;
//                $buxus_role_id = $entity->buxus_role_id;
//
//                $buxus_role = \PageFactory::get($buxus_role_id);
//
//            }

        }

    }

//    private static function generateRole($page_id)
//    {
//        $subrole_ids = collect(TemplateFunctions::SelectOneTypeChildren($page_id, PageTypeID::ROLE_ID()))->pluck('page_id');
//        foreach($subrole_ids as $subrole_id) {
//            self::generateRole($subrole_id);
//        }
//
//        if($page_id != PageIds::getPageId('roles')) {
//
//        }
//    }

}
